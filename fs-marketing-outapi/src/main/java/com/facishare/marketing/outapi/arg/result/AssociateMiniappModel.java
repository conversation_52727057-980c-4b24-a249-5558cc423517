package com.facishare.marketing.outapi.arg.result;

import com.facishare.marketing.common.util.ParameterValidateUtil;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 12/12/2018
 */
public class AssociateMiniappModel {
    @Data
    public static class AssociateMiniappArg implements Serializable{
        /**
         * 客脉小程序id
         */
        private String uid;
        /**
         * 企业ea
         */
        private String ea;
        /**
         * 手机号
         */
        private String phone;
        /**
         * 客户或者合作伙伴Id
         */
        private String crmObjectId;
        /**
         * 客户或者合作伙伴的apiName
         */
        private String crmObjectApiName;

        /**
         * 校验数据
         */
        public void validate() {
            StringBuilder errMsg = new StringBuilder();
            boolean isValid = true;
            if (this.uid == null){
                errMsg.append(" wxAppId is null .");
                isValid = false;
            }
            if (this.ea == null){
                errMsg.append(" ea is null .");
                isValid = false;
            }
            /**
             * correct format (400\\d{7})|(\\d{11})
             */
            if (this.phone != null && !this.phone.equals("") && !ParameterValidateUtil.validatePhone(this.phone)){
                errMsg.append(" Incorrect phone number format .");
                isValid = false;
            }
            if (!isValid){
                throw new IllegalArgumentException(errMsg.toString());
            }
        }
    }
    @Data
    public static class AssociateMiniappResult implements Serializable {
        /**
         * 用户营销账号id
         */
        private String userMarketingId;
        /**
         * 关联关系Id
         * @return
         */
        private transient String userMarketingMiniappAccountRelationId;
        public boolean isAssociation() {
            if (this.userMarketingId != null) {
                return true;
            }
            return false;

        }
        
    }
    @Data
    public static class AssociateMiniappAndLeadArg implements Serializable{
        /**
         * 客脉小程序id
         */
        private String uid;
        /**
         * 企业ea
         */
        private String ea;
        /**
         * CRM线索id
         */
        private String crmLeadId;
        /**
         * 手机号
         */
        private String phone;
        /**
         * 客户或者合作伙伴Id
         */
        private String crmObjectId;
        /**
         * 客户或者合作伙伴的apiName
         */
        private String crmObjectApiName = "";

        /**
         * 校验数据
         */
        public void validate(){
            StringBuilder errMsg = new StringBuilder();
            boolean isValid = true;
            if (this.uid == null){
                errMsg.append(" uid is null .");
                isValid = false;
            }
            if (this.ea == null){
                errMsg.append(" ea is null .");
                isValid = false;
            }
            if (this.crmLeadId == null){
                errMsg.append(" crmLeadId is null .");
                isValid = false;
            }
            /**
             * correct format (400\\d{7})|(\\d{11})
             */
            if (this.phone != null && !this.phone.equals("") && !ParameterValidateUtil.validatePhone(this.phone)){
                errMsg.append(" Incorrect phone number format .");
                isValid = false;
            }
            if (!isValid){
                throw new IllegalArgumentException(errMsg.toString());
            }
        }

    }
    @Data
    public static class AssociateMiniappAndLeadResult implements Serializable{
        /**
         * 小程序用户营销账号id
         */
        private String miniappUserMarketingId;
        /**
         * 线索用户营销账号id
         */
        private String leadUserMarketingId;
    }
    @Data
    public static class AssociateMiniappAndContactArg implements Serializable{
        /**
         * 客脉小程序id
         */
        private String uid;
        /**
         * 企业ea
         */
        private String ea;
        /**
         * CRM联系人id
         */
        private String crmContactId;
        /**
         * 手机号
         */
        private String phone;
        /**
         * 客户或者合作伙伴Id
         */
        private String crmObjectId;
        /**
         * 客户或者合作伙伴的apiName
         */
        private String crmObjectApiName = "";


        /**
         * 校验数据
         */
        public void validate() {
            StringBuilder errMsg = new StringBuilder();
            boolean isValid = true;
            if (this.uid == null){
                errMsg.append(" uid is null .");
                isValid = false;
            }
            if (this.ea == null){
                errMsg.append(" ea is null .");
                isValid = false;
            }
            if (this.crmContactId == null){
                errMsg.append(" crmContactId is null .");
                isValid = false;
            }
            /**
             * correct format (400\\d{7})|(\\d{11})
             */
            if (this.phone != null && !this.phone.equals("") && !ParameterValidateUtil.validatePhone(this.phone)){
                errMsg.append(" Incorrect phone number format .");
                isValid = false;
            }
            if (!isValid){
                throw new IllegalArgumentException(errMsg.toString());
            }
        }
    }
    @Data
    public static class AssociateMiniappAndContactResult implements Serializable{
        /**
         * 小程序用户营销账号id
         */
        private String miniappUserMarketingId;
        /**
         * 联系人用户营销账号id
         */
        private String contactUserMarketingId;
    }
}
