package com.facishare.marketing.qywx.arg.conference;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/04/03
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryInvitationDetailArg extends QYWXBaseArg {

    @ApiModelProperty("邀请函id")
    private String id;

    public boolean isWrongParam() {
        return StringUtils.isBlank(id);
    }
}
