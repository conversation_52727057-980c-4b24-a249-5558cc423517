package com.facishare.marketing.qywx.controller;

import com.facishare.marketing.api.arg.qywx.ListTagModelArg;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.arg.qywx.QywxAddFirstTagAndSubTagsArg;
import com.facishare.marketing.api.arg.usermarketingaccount.GetMarketingAccountArg;
import com.facishare.marketing.api.arg.usermarketingaccount.MarketingAccountByTargetArg;
import com.facishare.marketing.api.arg.usermarketingaccount.UserMarketingAccountDetailsArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.AddFirstTagAndSubTagsArg;
import com.facishare.marketing.api.result.CheckUserMarketingAccountResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountDetailsResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.LikeTagByNameResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.ListTagModelResult;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by zhengh on 2020/1/13.
 */
@RestController
@RequestMapping("/userMarketing")
@Slf4j
public class UserMarketingAccountController {
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private UserMarketingTagService userMarketingTagService;

    @ApiOperation(value = "访客权限校验", tags = {"4.0"})
    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/checkUserMarketingAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<CheckUserMarketingAccountResult> checkUserMarketingAccount(@RequestBody CheckUserMarketingAccountArg arg){
        return userMarketingAccountService.checkUserMarketingAccount(arg.getFsEa(), arg.getFsUserId(), arg.getUid(), arg.getNeedCheckApiName());
    }

    @RequestMapping(value = "/getUserMarketingAccountDetails", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @TokenCheckTrigger
    @ApiOperation(value = "营销用户详情", notes = "营销用户详情", tags = "8.4")
    public Result<UserMarketingAccountDetailsResult> getUserMarketingAccountDetails(@RequestBody UserMarketingAccountDetailsArg arg) {
        return userMarketingAccountService.getUserMarketingAccountDetails(arg.getFsEa(), arg.getFsUserId(), arg.getUserMarketingId());
    }

    @ApiOperation(value = "营销用户行为日志", tags = {"4.0"})
    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/pageUserMarketingActionStatistic", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<UserMarketingActionResult>> pageUserMarketingActionStatistic(@RequestBody PageUserMarketingActionStatisticArg arg){
        com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg innerArg = new com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg();
        innerArg.setPageNo(arg.getPageNum());
        innerArg.setPageSize(arg.getPageSize());
        innerArg.setUserMarketingId(arg.getUserMarketingId());
        innerArg.setMarketingActivityId(arg.getMarketingActivityId());
        return userMarketingAccountService.pageUserMarketingActionStatistic(arg.getFsEa(), arg.getFsUserId(), innerArg);
    }

    @ApiOperation(value = "通过标签名称模糊查标签", tags = "4.0")
    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/listAllTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<List<LikeTagByNameResult>> listAllTag(@RequestBody QYWXBaseArg arg){
        return userMarketingTagService.listAllTag(arg.getFsEa(), arg.getFsUserId());
    }

    @ResponseBody
    @TokenCheckTrigger
    @ApiOperation(value = "拉取标签模型", tags = "5.1")
    @RequestMapping(value = "/listTagModel", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<ListTagModelResult> listTagModel(@RequestBody ListTagModelArg arg){
        com.facishare.marketing.api.arg.usermarketingaccounttag.ListTagModelArg listTagModelArg = new com.facishare.marketing.api.arg.usermarketingaccounttag.ListTagModelArg();
        BeanUtils.copyProperties(arg, listTagModelArg);
        return userMarketingTagService.listTagModel(arg.getFsEa(), arg.getFsUserId(), listTagModelArg);
    }

    @TokenCheckTrigger
    @ResponseBody
    @RequestMapping(value = "addFirstTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建一级标签", tags = {"5.1"})
    public Result<NestedId> addFirstTag(@RequestBody QywxAddFirstTagAndSubTagsArg arg) {
        AddFirstTagAndSubTagsArg addFirstTagAndSubTagsArg = new AddFirstTagAndSubTagsArg();
        BeanUtils.copyProperties(arg, addFirstTagAndSubTagsArg);
        return userMarketingTagService.addFirstTag(arg.getFsEa(), arg.getFsUserId(), addFirstTagAndSubTagsArg);
    }

    @ApiOperation(value = "营销用户详情", tags = {"4.0"})
    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/getUserMarketingDetailsByAssociationId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<UserMarketingAccountDetailsResult> getUserMarketingDetailsByAssociationId(@RequestBody UserMarketingDetailsByAssociationIdArg arg){
        com.facishare.marketing.api.arg.UserMarketingDetailsByAssociationIdArg innerArg = new com.facishare.marketing.api.arg.UserMarketingDetailsByAssociationIdArg();
        innerArg.setAssociationId(arg.getAssociationId());
        innerArg.setType(arg.getType());
        innerArg.setWxAppId(arg.getWxAppId());
        Result<UserMarketingAccountDetailsResult> result = userMarketingAccountService.getUserMarketingDetailsByAssociationId(arg.getFsEa(), arg.getFsUserId(), innerArg);
        if (result.isSuccess() && result.getData() != null && CollectionUtils.isEmpty(result.getData().getTagNameList())) {
            result.getData().setTagNameList(new TagNameList());
        }
        return result;
    }

    @ApiOperation(value = "批量将用户用指定标签添加移除", tags = {"4.0"})
    @ResponseBody
    @TokenCheckTrigger
    @RequestMapping(value = "/batchMergeUserMarketingAccountsFromTags", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<Boolean> batchMergeUserMarketingAccountsFromTags(@RequestBody BatchMergeUserMarketingAccountsFromTagsArg arg){
        com.facishare.marketing.api.arg.usermarketingaccount.BatchMergeUserMarketingAccountsFromTagsArg innerArg = new com.facishare.marketing.api.arg.usermarketingaccount.BatchMergeUserMarketingAccountsFromTagsArg();
        innerArg.setTagNameList(arg.getTagNameList());
        innerArg.setTargetObjectApiNames(arg.getTargetObjectApiNames());
        innerArg.setUserMarketingAccountId(arg.getUserMarketingAccountId());
        return userMarketingAccountService.batchMergeUserMarketingAccountsFromTags(arg.getFsEa(), arg.getFsUserId(), innerArg);
    }

    @ApiOperation(value = "营销用户标签", tags = {"4.0"})
    @TokenCheckTrigger
    @RequestMapping(value = "/getTagsByUserMarketingAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<TagNameList> getTagsByUserMarketingAccount(@RequestBody GetTagsByUserMarketingAccountArg arg) {
        if (arg.isWrongParam()) {
            log.warn("UserMarketingAccountController.getTagsByUserMarketingAccount param error args{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.getTagsByUserMarketingId(arg.getFsEa(), arg.getFsUserId(), arg.getUserMarketingId());
    }

    @ApiOperation(value = "获取当前身份的营销用户id", tags = {"4.0"})
    @TokenCheckTrigger
    @RequestMapping(value = "/getMarketingAccount", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<String> getMarketingAccount(@RequestBody GetMarketingAccountIdArg arg) {
        GetMarketingAccountArg getMarketingAccountArg = BeanUtil.copy(arg,GetMarketingAccountArg.class);
        return userMarketingAccountService.getMarketingAccount(getMarketingAccountArg);
    }

    @ApiOperation(value = "获取关联id的营销用户id", tags = {"4.0"})
    @TokenCheckTrigger
    @RequestMapping(value = "/getMarketingAccountByTargetId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<String> getMarketingAccountByTargetId(@RequestBody MarketingAccountByTargetArg arg) {
        if (StringUtils.isBlank(arg.getTargetId()) || arg.getTargetType() == null) {
            log.warn("UserMarketingAccountController.getTagsByUserMarketingAccount param error args{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if(StringUtils.isBlank(arg.getEa())){
            arg.setEa(arg.getFsEa());
        }
        if (StringUtils.isBlank(arg.getEa()) ) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.getMarketingAccountByTargetId(arg);
    }
}
