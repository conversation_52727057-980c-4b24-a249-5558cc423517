package com.facishare.marketing.qywx.arg.distribute;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by zhengh on 2020/5/8.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QueryOperatorDistributionMaterialArg extends QYWXBaseArg{
    private String planId;      //分销计划id
    private String operatorId;  //运营人员id
    private Integer objectType; //物料类型
    @ApiModelProperty(value = "当前页码", required = true)
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "第一页请求时间戳")
    private Long time;
}
