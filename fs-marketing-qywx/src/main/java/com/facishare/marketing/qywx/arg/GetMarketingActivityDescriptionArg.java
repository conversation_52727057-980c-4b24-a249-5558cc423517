package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("获取推广宣传语参数")
public class GetMarketingActivityDescriptionArg extends QYWXBaseArg {

    @ApiModelProperty("推广ID")
    private String marketingActivityId;

    @ApiModelProperty("是否伙伴推广")
    private boolean partner;

}
