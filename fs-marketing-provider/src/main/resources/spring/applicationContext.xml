<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:c="http://www.springframework.org/schema/c" xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
    xmlns:task="http://www.springframework.org/schema/task" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
	            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
                http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

  <bean id="springContextUtil" class="com.facishare.eservice.common.utils.SpringContextUtil"/>
  <context:annotation-config/>
  <context:component-scan base-package="com.facishare.marketing.provider,com.facishare.paas.appframework"/>
  <context:component-scan base-package="com.facishare.marketing.api"/>
  <context:component-scan base-package="com.facishare.marketing.outapi"/>

  <!--配置中心 -->
  <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor" c:placeholderConfigurer-ref="autoConf"/>
  <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer" id="autoConf" p:configName="fs-marketing-provider" p:fileEncoding="UTF-8"
      p:ignoreResourceNotFound="true" p:ignoreUnresolvablePlaceholders="false" p:location="classpath:application.properties"/>

  <import resource="fs-redis.xml"/>
  <import resource="dubbo-consumer.xml"/>
  <import resource="dubbo-provider.xml"/>
  <import resource="spring-db.xml"/>
  <import resource="spring-aop.xml"/>
  <import resource="ei-ea-converter.xml"/>
  <import resource="fs-mq.xml"/>
  <import resource="fs-proxy.xml"/>
  <import resource="fs-rest-api.xml"/>
  <import resource="classpath*:spring/fs-change-set-api.xml"/>
  <import resource="classpath*:spring/fs-change-set-api-rest.xml"/>
  <import resource="classpath*:/wechatrest/wechatrest.xml"/>
  <import resource="classpath:/otherrest/otherrest.xml"/>
  <import resource="classpath:spring/license-client.xml"/>
  <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
  <import resource="classpath:/dubborestouterapi/fs-wechat-dubbo-rest-outer-api-client.xml"/>
<!--  <import resource="classpath:paasauthrest/paasauthrest.xml"/>-->
  <import resource="classpath:/enterpriserelation2/enterpriserelation-noappid.xml"/>
  <import resource="classpath:spring/open-ding-rest-client.xml"/>
  <import resource="classpath:spring/config.xml"/>
  <import resource="classpath:spring/fs-warehouse-rest-client.xml"/>
  <import resource="classpath:/miniprogramrest/miniprogramrest.xml"/>
  <import resource="classpath:META-INF/fs-paas-ai-client.xml"/>
  <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>
  <import resource="classpath:spring/fs-organization-api-spring-bean-others.xml"/>
  <!-- 短信中台rest接口 -->
  <import resource="classpath:rest/smsplatform-rest-api.xml"/>
  <!-- 邮件服务rest接口 -->
  <import resource="classpath:spring/email-rest.xml"/>

  <!--fs-orgainzation-adapter-api -->
  <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
  <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
  <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>
  <!-- JVM，logback 监控上报 -->
  <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

  <!-- http-spring-support -->
  <bean id="httpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-marketing-http-config"/>
  <!--<bean id="httpSupport4SelfApp" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-marketing-selfapp-http-config"/>-->
  <bean id="httpSupport4Qywx" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-marketing-selfapp-http-config"/>
<!--  直接走egress,目前专属云cpath走的是这个代理-->
  <bean id="httpSupportSvc" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-marketing-svc-http-config"/>

  <!-- sheduler -->
  <task:annotation-driven mode="proxy" scheduler="scheduler"/>
  <task:scheduler id="scheduler" pool-size="20"/>

  <!-- 灰度组件 -->
  <bean class="com.facishare.marketing.common.gray.GrayBeanPostProcessor"/>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.FieldDescribeService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>

  <bean id="marketingRestRetrofitFactory" class="com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-marketing-rest-api" init-method="init">
    <property name="okHttpSupport" ref="httpSupport"/>
    <property name="seializeNulls" value="true"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.ai.AiService">
    <property name="factory" ref="marketingRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.metadata.PublicMetadataService">
    <property name="factory" ref="marketingRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.udobjrest.PaasUdobjRestService">
    <property name="factory" ref="marketingRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.metadata.PublicConvertService">
    <property name="factory" ref="marketingRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.metadata.PublicPurgeService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>

  <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
    <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
  </bean>

  <bean id="webPageRestServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
        p:configName="fs-qixin-objgroup-rest-proxy-config" init-method="init"/>
  <bean id="tenantBrandColorService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.webpage.customer.api.service.TenantBrandColorService">
    <property name="factory" ref="webPageRestServiceProxyFactory"/>
  </bean>

  <bean id="eserviceRetrofitFactory" class="com.github.zhxing.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-eservice-rest" init-method="init"/>
  <bean class="com.github.zhxing.retrofitspring.RetrofitSpringFactoryBean"
        p:type="com.facishare.eservice.rest.online.service.KnowledgeService" p:factory-ref="eserviceRetrofitFactory"/>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.whatsapp.WhatsAppRestService">
    <property name="factory" ref="marketingRestRetrofitFactory"/>
  </bean>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.facishare.marketing.provider.remote.rest.DaiLiTongService">
    <property name="factory" ref="marketingRestRetrofitFactory"/>
  </bean>

  <bean id="systemPresetClient"  class="com.fxiaoke.stone.commons.impl.MarketingExclusiveClient">
    <constructor-arg index="0" ref="httpSupport"/>
  </bean>

  <import resource="classpath:fs-uc-rest-host.xml"/>
  <bean id="captchaService" class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
    <property name="objectType" value="com.facishare.uc.api.service.CaptchaService"/>
    <property name="serverHostProfile" ref="userCenterApiHostProfile"/>
  </bean>
</beans>
