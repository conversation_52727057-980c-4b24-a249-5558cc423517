<beans xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

  <dubbo:protocol id="dubbo" name="dubbo" payload="10485760" port="${dubbo.provider.port}"/>
  <dubbo:provider filter="tracerpc" timeout="18000"/>

  <dubbo:service interface="com.facishare.marketing.api.service.FileService" protocol="dubbo" ref="fileService" version="${dubbo.provider.version}" timeout="20000"/>

  <dubbo:service interface="com.facishare.marketing.api.service.UserService" protocol="dubbo" ref="userService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ProductService" protocol="dubbo" ref="productService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ArticleService" protocol="dubbo" ref="articleService"  timeout="120000" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ObjectGroupService" protocol="dubbo" ref="objectGroupService"  timeout="120000" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.distribution.MaterielService" protocol="dubbo" ref="materielService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.NoticeService" protocol="dubbo" ref="noticeService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.SettingService" protocol="dubbo" ref="settingService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.SystemNoticeService" protocol="dubbo" ref="systemNoticeService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ActivityService" protocol="dubbo" ref="activityService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MarketingEventService" protocol="dubbo" ref="marketingEventService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseStatisticService" protocol="dubbo" ref="enterpriseStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:service interface="com.facishare.marketing.api.service.distribution.DistributorService" protocol="dubbo" ref="distributorService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseObjectStatisticService" protocol="dubbo" ref="enterpriseObjectStatisticService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseEmployeeStatisticService" protocol="dubbo" ref="enterpriseEmployeeStatisticService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ActivityTemplateService" protocol="dubbo" ref="activityTemplateService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ObjectFieldService" protocol="dubbo" ref="objectFieldService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.FormTemplateService" protocol="dubbo" ref="formTemplateService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ObjectDescriptionService" protocol="dubbo" ref="objectDescriptionService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.CrmService" ref="crmService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.FormDataService" ref="formDataService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.FsBindService" ref="fsBindService" version="${dubbo.provider.version}" timeout="60000"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MemberService" ref="memberService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.CustomerService" ref="customerService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.CardTemplateService" ref="cardTemplateService" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.card.EnterpriseDefaultCardService" ref="enterpriseDefaultCardService" version="${dubbo.provider.version}"/>

  <!-- outer service here -->
  <dubbo:service interface="com.facishare.marketing.outapi.service.EnterpriseStatisticDataReportService" protocol="dubbo" ref="enterpriseStatisticDataReportService" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.FormDataService" protocol="dubbo" ref="formDataOutService" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.SettingService" ref="settingOutService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.CrmContactMarketingAccountAssociationService" ref="crmContactMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService" ref="crmLeadMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.CrmWxUserMarketingAccountAssociationService" ref="crmWxUserMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.MiniappMarketingAccountAssociationService" ref="miniappMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.WxServiceMarketingAccountAssociationService" ref="wxServiceMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.QRCodeService" ref="qRCodeService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.CustomizeFormDataService" ref="customizeFormDataOuterService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.OutShareContentService" ref="outShareContentService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.distribution.ClueService" ref="clueService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.distribution.OperatorService" ref="operatorService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.distribution.DistributionIndexService" ref="distributionIndexService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.sms.ApplyService" ref="applyService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sms.PayService" ref="payService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sms.QuotaService" ref="quotaService" protocol="dubbo" version="1.0"  timeout="60000" />
  <dubbo:service interface="com.facishare.marketing.api.service.sms.SendService" ref="sendService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sms.SmsVerCodeSettingService" ref="smsVerCodeSettingService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.enterpriseFeed.EnterpriseFeedService" ref="enterpriseFeedService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MarketingUserGroupService"  protocol="dubbo" ref="marketingUserGroupService" timeout="15000" version="${dubbo.provider.version}"/>
  <dubbo:service interface="com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService" ref="weChatServiceMarketingActivityService"   timeout="60000"  protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.marketingactivity.MarketingActivityExternalConfigService" ref="marketingActivityExternalConfigService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingReportService" ref="marketingReportService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.marketingactivity.MarketingActivityService" ref="marketingActivityService" protocol="dubbo"  timeout="60000" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService" ref="userMarketingTagService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService" ref="userMarketingAccountService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.UserMarketingOutAccountService" ref="userMarketingOutAccountService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.UserMarketingAccountService" ref="userMarketingAccountOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutAccountService" ref="outAccountService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.kis.SpreadTaskService" ref="spreadTaskService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.kis.AppVersionService" ref="appVersionService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.kis.ActivityService" ref="kisActivityService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.ArticleService" ref="kisArticleService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.ProductService" ref="kisProductService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseSpreadStatisticService" ref="enterpriseSpreadStatisticService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.SpreadWorkService" ref="spreadWorkService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.KisPermissionService" ref="kisPermissionService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.EmployeeSpreadStatisticService" ref="employeeSpreadStatisticService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.KisMarketingActivityService" ref="kisMarketingReportService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingUserGroupService"  protocol="dubbo" ref="marketingUserGroupService" timeout="13000" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MarketingUserGroupService" timeout="15000"  protocol="dubbo" ref="marketingUserGroupOutService" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.KisActionService" ref="kisActionService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MaterialSearchService" ref="materialSearchService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.AccountService" ref="accountService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MiniappService" ref="miniappService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.RedDotConfigService" ref="redDotConfigService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseMetaConfigService" ref="enterpriseMetaConfigService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseEmployeeObjectDayStatisticService" ref="enterpriseEmployeeObjectDayStatisticService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseSocialGroupService" ref="enterpriseSocialGroupService" protocol="dubbo" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.CustomizeFormDataService" ref="customizeFormDataService"  protocol="dubbo" version="${dubbo.provider.version}"/>

  <dubbo:service interface="com.facishare.marketing.api.service.GuideTaskService" ref="guideTaskService"  protocol="dubbo" version="${dubbo.provider.version}"/>


  <dubbo:service interface="com.facishare.marketing.api.service.distribution.DistributePlanGradeService" ref="distributePlanGradeService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MarketingFlowService" ref="marketingFlowService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingFlowInstanceService" ref="marketingFlowInstanceService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.distribution.DistributionPlanService" ref="distributionPlanService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MomentPosterService" ref="momentPosterService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.WxOfficialAccountsService" ref="wxOfficialAccountsService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.WxUserMkActionService" ref="wxUserMkActionService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.MigrateService" ref="migrateService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.ArticleService" ref="articleOutService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.ProductService" ref="productOutService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.conference.ConferenceService" ref="conferenceService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.connector.douyin.DouYinService" ref="douYinService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.qr.QRCodeService" ref="qrPosterCodeService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qr.QRPosterService" ref="qrPosterService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.ConferenceService" ref="conferenceOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MarketingFlowInstanceOuterService" ref="marketingFlowInstanceOuterService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.OutSpreadWorkService" ref="outSpreadWorkService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.CustomizeTicketService" ref="customizeTicketService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.WechatServiceTagService" ref="wechatServiceTagService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MaterialService" ref="materialOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MiniAppTagService" ref="miniAppTagService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.baidu.AdAccountService" ref="adAccountService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.baidu.BaiduCampaignService" ref="baiduCampaignService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.WxTicketService" ref="wxTicketService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.BrowserUserService" ref="browserUserService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.hexagon.HexagonService" ref="hexagonService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ContentMarketingEventService" ref="contentMarketingEventService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.outapi.service.OutHexagonService" ref="outHexagonService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.OfficialWebsiteService" ref="officialWebsiteService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.bd.BdSiteService" ref="bdSiteService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.ContentMarketingEventService" ref="contentMarketingEventService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.BrowserUserMarketingAccountAssociationService" ref="browserUserMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MemberService" ref="memberOutService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.counselor.CounselorService" ref="counselorService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QYWXInnerService" ref="qywxInnerService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QYWXThirdService" ref="qywxThirdService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.CardService" ref="qywxCardServiceImpl" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.MiniappLoginService" ref="miniappLoginService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.inner.InnerUserInfoService" ref="innerUserInfoService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QYWXContactService" ref="qywxContactService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.GroupSendMessageService" ref="groupSendMessageService" protocol="dubbo" version="1.0" timeout="30000"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.MiniAppProductService" ref="miniAppProductService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.MiniAppFeedService" ref="miniAppFeedServiceImpl" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QYWXCustomerGroupService" ref="qywxCustomerGroupService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.CustomizeFormDataService" ref="qywxCustomizeFormDataServiceImpl" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QYWXSettingService" ref="qywxSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService" ref="qywxSelfBuildAppCallbackService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdCallbackService" ref="wxThirdCallbackService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdPlatformInnerSupportService" ref="wxThirdPlatformInnerSupportService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdAuthService" ref="wxThirdAuthService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.WechatAccountOutService" ref="wechatAccountOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QywxUserService" ref="qywxUserService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QywxStaffService" ref="qywxStaffService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QyWxDepartmentService" ref="qyWxDepartmentService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.live.LiveService" ref="liveService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.pay.MerchantService" ref="merchantService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.pay.PayResultCallbackService" ref="payResultCallbackService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.VideoService" ref="videoService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.QywxMessageService" ref="qywxMessageService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MiniappSubscribeMessageService" ref="miniappSubscribeMessageService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ObjectBindDetailService" ref="objectBindDetailService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QyweixinAccountBindService" ref="qyweixinAccountBindService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.mail.MailService" ref="mailService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.StatisticService" ref="statisticService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.BoardService" ref="boardService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.WebHookService" ref="webHookService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.TaskCenterService" ref="taskCenterService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.PrivateMessageService" ref="privateMessageService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.SpreadChannelService" ref="spreadChannelService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ShareContentService" ref="shareContentService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.CustomizeMiniAppNavbarService" ref="customizeMiniAppNavbarService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.SafetyManagementService" ref="safetyManagementService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ClueManagementService" ref="clueManagementService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingTriggerService" ref="marketingTriggerService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingSceneService" ref="marketingSceneService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.baidu.AdObjectFieldMappingService" ref="adObjectFieldMappingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MiniAppSettingService" ref="miniAppSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingEventCommonSettingService" ref="marketingEventCommonSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.EnterpriseSelfDomainService" ref="enterpriseSelfDomainService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sms.SmsJobHandlerService" ref="smsJobHandlerService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.FsAddressBookOutService" ref="fsAddressBookOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.hexagon.HexagonTemplateService" ref="hexagonTemplateService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.CampaignMergeDataService" ref="campaignMergeDataService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.CustomizeMiniAuthorizeService" ref="customizeMiniAuthorizeService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutStatisticService" ref="outStatisticService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.fileLibrary.FileLibraryService" ref="fileLibraryService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService" ref="photoLibraryService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.CrmDashBoardService" ref="crmDashBoardService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.CustomizeMiniAppCardNavbarService" ref="customizeMiniAppCardNavbarService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.QywxGroupMessageService" ref="qywxGroupMessageService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingEventStatisticsService" ref="marketingEventStatisticsService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdCloudInnerService" ref="wxThirdCloudInnerService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.CustomizeContentService" ref="customizeContentService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutCustomizeContentService" ref="outCustomizeContentService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.advertiser.headlines.AuthCallbackService" ref="authCallbackService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.advertiser.headlines.HeadlinesService" ref="headlinesService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.DingMiniappCallBackService" ref="dingMiniappCallBackService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.DingMiniAppDepartmentService" ref="dingMiniAppDepartmentService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.DingMiniAppStaffService" ref="dingMiniAppStaffService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.DingAddressBookCallBackService" ref="dingAddressBookCallBackService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.marketingplugin.MarketingPluginService" ref="marketingPluginService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.marketingplugin.CouponTemplateService" ref="couponTemplateService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxcoupon.WxCouponPayService" ref="wxCouponPayService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QYWXMediaService" ref="qywxMediaService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutQuotaService" ref="outQuotaService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.QywxMomentService" ref="qywxMomentService" protocol="dubbo" version="1.0" timeout="30000"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QywxSopTaskService" ref="qywxSopTaskService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.OfficialWebsiteThirdPlatformService" ref="officialWebsiteThirdPlatformService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QywxWelcomeMsgService" ref="qywxWelcomeMsgService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.ClueDefaultSettingService" ref="clueDefaultSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.advertiser.TencentAdService" ref="tencentAdService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.pay.FsPayService" ref="fsPayService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.OutLinkService" ref="outLinkService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.open.material.MaterialOpenService" ref="materialOpenService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingEvenCustomTagSettingService" ref="marketingEvenCustomTagSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QywxGroupCodeService" ref="qywxGroupCodeService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.ApplyService" ref="applyOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.advertiser.ocpc.AdOCPCService" ref="adOCPCService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MktContentMgmtLogObjService" ref="mktContentMgmtLogObjService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.SyncStatisticsDataService" ref="syncStatisticsDataService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sms.SmsQuotaNoticeSettingService" ref="smsQuotaNoticeSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.MiniAppService" ref="miniAppService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.qywx.QywxObjectSendConfigService" ref="qywxObjectSendConfigService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.open.material.MaterialShowSettingService" ref="materialShowSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ParamsInvokeService" ref="paramsInvokeService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutPluginService" ref="outPluginService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.permission.DataPermissionService" ref="dataPermissionService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.AdService" ref="adOutService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.QywxDataSyncService" ref="qywxDataSyncService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.UserMarketingCustomizeActionService" ref="userMarketingCustomizeActionService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ai.AiChatService" ref="aiChatService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ai.ObjectQueryProxyService" ref="objectQueryProxyService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.advertiser.AdvertiserService" ref="advertiserService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.AuthenticationService" ref="authenticationService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.shanshan.ShanShanEditService" ref="shanShanEditService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.crowd.TargetCrowdOperationService" ref="targetCrowdOperationService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutWxCouponService" ref="outWxCouponService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ObjectSloganRelationService" ref="objectSloganRelationService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.MarketingModuleStatisticsService" ref="marketingModuleStatisticsService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.ExternalUserMarketingAccountAssociationService" ref="externalUserMarketingAccountAssociationService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.WebsiteSeoService" ref="websiteSeoService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.whatsapp.WhatsAppService" ref="whatsAppService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.outapi.service.OutObjectService" ref="outObjectService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.OutUserMarketingActionService" ref="outUserMarketingActionService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.wxcoupon.PublicCouponService" ref="publicCouponService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.baidu.BaiduFeedService" ref="baiduFeedService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.marketingAssistant.YxzsNoticeSendService" ref="yxzsNoticeSendService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.userrelation.UserRelationService" ref="userRelationService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.digitalHumans.DigitalHumansService" ref="digitalHumansService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.cta.CtaService" ref="ctaService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.kis.CtaService" ref="kisCtaService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.UserProfileRepositoryService" ref="userProfileRepositoryService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.appMenu.AppMenuTemplateService" ref="appMenuTemplateService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.ImageCaptchaService" ref="imageCaptchaService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.FunctionService" ref="functionService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.PartnerService" ref="partnerService" protocol="dubbo" version="1.0"/>

  <dubbo:service interface="com.facishare.marketing.api.service.connector.xiaohongshu.XiaoHongShuService" ref="xiaoHongShuService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.taskSchedule.TaskScheduleService" ref="taskScheduleService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.emailMaterial.EmailMaterialService" ref="emailMaterialService" protocol="dubbo" version="1.0"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sdr.SdrActionService" ref="sdrActionService" protocol="dubbo" version="1.0" timeout = "120000"/>
  <dubbo:service interface="com.facishare.marketing.api.service.sdr.SdrService" ref="sdrService" protocol="dubbo" version="1.0"/>
</beans>
