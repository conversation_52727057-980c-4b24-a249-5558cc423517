<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p" xsi:schemaLocation="http://www.springframework.org/schema/beans
			http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd
			http://www.springframework.org/schema/context
			http://www.springframework.org/schema/context/spring-context-3.0.xsd
			http://www.springframework.org/schema/tx
			http://www.springframework.org/schema/tx/spring-tx.xsd">

  <context:annotation-config/>
  <context:component-scan base-package="com.facishare.marketing"/>

  <context:annotation-config/>
  <bean class="com.github.mybatis.spring.DynamicDataSource" id="dataSource">
    <property name="configName" value="fs-mankeep-db"/>
  </bean>

  <bean id="mongoDatastore" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="fs-marketing-mongo-db"/>

  <!-- 配置事务管理器 -->
  <bean class="org.springframework.jdbc.datasource.DataSourceTransactionManager" id="transactionManager">
    <property name="dataSource" ref="dataSource"/>
  </bean>

  <tx:annotation-driven proxy-target-class="true" transaction-manager="transactionManager"/>

  <bean class="org.mybatis.spring.SqlSessionFactoryBean" id="sqlSessionFactory">
    <property name="dataSource" ref="dataSource"/>

    <property name="typeAliasesPackage" value="com.facishare.marketing.provider.entity,com.facishare.marketing.provider.crowd.entity"/>

    <property name="configLocation" value="classpath:spring/spring-mybatis.xml"/>
  </bean>

  <bean class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.facishare.marketing.provider.dao,com.facishare.marketing.script.dao,com.facishare.marketing.provider.crowd.dao"/>
    <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
  </bean>

</beans>
