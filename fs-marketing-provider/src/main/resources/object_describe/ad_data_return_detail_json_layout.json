{"buttons": [], "components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartner_button_default", "label": "更换合作伙伴"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "DeletePartner_button_default", "label": "移除合作伙伴"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple"}, {"field_section": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info"}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "is_tiled": true, "render_type": "select_one", "field_name": "convent_type"}, {"is_readonly": false, "is_required": true, "is_tiled": true, "render_type": "select_one", "field_name": "ad_platform"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "ad_account_id"}, {"is_readonly": false, "is_required": true, "is_tiled": true, "render_type": "select_one", "field_name": "status"}, {"is_readonly": false, "is_required": true, "render_type": "date_time", "field_name": "send_back_time"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "send_back_reason"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "trigger_employee"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "marketing_event_id"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "lead_id"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "landing_page"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_HYo29__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "_id": "form_component", "type": "form"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record"}, {"components": [["form_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"], ["payment_recordrelated_list_generate_by_UDObjectServer__c"]], "buttons": [], "api_name": "tabs_y2v3k__c", "tabs": [{"api_name": "form_component_us8EY__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_W1jCW__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "BPM_related_list_bO0ue__c", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_WMJ54__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_payment_recordrelated_list_generate_by_UDObjectServer__c", "header": "收款记录", "nameI18nKey": "paas.udobj.receipt"}], "header": "页签容器", "type": "tabs"}, {"relationType": 2, "buttons": [], "api_name": "payment_recordrelated_list_generate_by_UDObjectServer__c", "related_list_name": "payment_record_LIST", "ref_object_api_name": "payment_record", "header": "收款记录", "nameI18nKey": "paas.udobj.receipt", "type": "relatedlist"}, {"field_section": [], "buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "type": "relatedlist"}, {"field_section": [], "buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "type": "relatedlist"}], "is_deleted": false, "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "AdDataReturnDetailObj_detail_layout", "what_api_name": null, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_y2v3k__c"], ["relevant_team_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "display_name": "广告回传明细默认布局", "is_default": true, "layout_type": "detail", "package": "CRM", "ref_object_api_name": "AdDataReturnDetailObj", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": ["component_BZTZR__c", "sale_log"], "namespace": null, "enable_mobile_layout": false}