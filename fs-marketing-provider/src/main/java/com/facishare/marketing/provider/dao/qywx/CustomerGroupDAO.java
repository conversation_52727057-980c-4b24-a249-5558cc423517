package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.CustomerGroupEntity;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.pagination.Page;
import com.google.gson.annotations.SerializedName;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CustomerGroupDAO extends ICrudMapper<CustomerGroupEntity> {
    @Select("<script>"
          + "SELECT * FROM qywx_customer_group WHERE corp_id =#{corpId}\n"
          + "<if test=\"groupName != null\">AND group_name LIKE CONCAT('%', #{groupName}, '%') </if>"
          + "AND group_owner LIKE CONCAT('%', #{groupOwner}, '%')\n"
          + "</script>")
    List<CustomerGroupEntity> pageCustomerGroup(@Param("corpId") Integer corpId, @Param("groupName")String groupName,
                                                @Param("groupOwner")String groupOwner, @Param("page") Page page);
}
