package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created by ranluch on 2019/11/26.
 */
public interface BaiduCampaignDAO {
    @Insert("insert into baidu_campaign (\"id\", \"ea\", \"ad_account_id\", \"campaign_id\", \"campaign_name\", \"budget\", \"campaign_type\", \"source\", \"status\", \"marketing_event_id\", \"device\", \"create_time\", \"update_time\", \"refresh_time\") values("
        + " #{obj.id},\n"
        + " #{obj.ea},\n"
        + " #{obj.adAccountId},\n"
        + " #{obj.campaignId},\n"
        + " #{obj.campaignName},\n"
        + " #{obj.budget},\n"
        + " #{obj.campaignType},\n"
        + " #{obj.source},\n"
        + " #{obj.status},\n"
        + " #{obj.marketingEventId},\n"
        + " #{obj.device},\n"
        + " now(),\n"
        + " now(),\n"
        + " #{obj.refreshTime}\n"
        + ") ON CONFLICT DO NOTHING;")
    void addCampaign(@Param("obj") BaiduCampaignEntity campaignEntity);

    @Insert("<script>"
           + "INSERT INTO baidu_campaign(\"id\", \"ea\", \"ad_account_id\", \"campaign_id\", \"campaign_name\", \"budget\", \"campaign_type\", \"source\", \"status\", \"marketing_event_id\", \"device\", \"create_time\", \"update_time\", \"refresh_time\") VALUES"
           +  "  <foreach collection='campaignEntityList' item='item' separator=','>"
           +  "   (#{item.id}, #{item.ea}, #{item.adAccountId}, #{item.campaignId}, #{item.campaignName}, #{item.budget}, #{item.campaignType}, #{item.source}, #{item.status}, #{item.marketingEventId}, #{item.device}, now(), now(), #{item.refreshTime})"
           +  "  </foreach>"
           +  "ON CONFLICT DO NOTHING;"
           + "</script>")
    int batchAddCampaign(@Param("campaignEntityList") List<BaiduCampaignEntity> campaignEntityList);

    @Update("<script>"
        +"update baidu_campaign set "
        + "\"campaign_name\" = #{campaignName} , "
        + "\"budget\" = #{budget}, "
        + "\"campaign_type\" = #{campaignType}, "
        + "\"status\" = #{status}, "
        + "\"device\" = #{device}, "
        + "\"refresh_time\" = #{refreshTime}, "
        + "    <if test=\"adAccountId != null\">\n"
        + "         \"ad_account_id\" = #{adAccountId},\n"
        + "    </if>\n"
        + "\"update_time\" = now()"
        + "  where \"id\" = #{id}"
        + "</script>")
    @FilterLog
    void updateCampaignForRefresh(BaiduCampaignEntity campaignEntity);

    @Select("<script>"
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND marketing_event_id =ANY(ARRAY\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> getCampaignByMarketingEventIds(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("marketingEventIds")List<String> marketingEventIds);

    @Update("<script>UPDATE baidu_campaign as c SET campaign_name=tmp.campaignName, budget=tmp.budget, campaign_type=tmp.campaignType,\n"
            + "status=tmp.status, device=tmp.device, update_time=now() FROM (values"
            + "<foreach separator=',' collection='campaignEntityList' item='item'>"
            +   "(#{item.id}, #{item.campaignId}, #{item.campaignName}, #{item.budget}, #{item.campaignType}, #{item.status}, #{item.device}, #{item.refreshTime})"
            + "</foreach>"
            + ") as tmp(id, campaignId, campaignName, budget, campaignType, status, device, refreshTime) WHERE c.ea = #{ea} AND c.ad_account_id=#{adAccountId} AND c.id=tmp.id"
            + "</script>")
    void batchUpdateCampaign(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("campaignEntityList") List<BaiduCampaignEntity> campaignEntityList);

    @Select("<script>  " +
        "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND source=#{source}"
        + "</script>")
    List<BaiduCampaignEntity> queryCampaignByEa(@Param("ea") String ea, @Param("source")String source);

    @Select("SELECT * FROM baidu_campaign WHERE id=#{id}")
    BaiduCampaignEntity queryCampaignById(@Param("id") String id);

    @Update("update baidu_campaign set "
        + "\"marketing_event_id\" = #{marketingEventId}, "
        + "\"update_time\" = now(), "
        + "\"status\" = #{status} "
        + "  where \"id\" = #{id}")
    void relateCampaignMarketingEvent(@Param("id") String id, @Param("marketingEventId") String marketingEventId, @Param("status") Integer status);

    @Select("SELECT * FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND marketing_event_id=#{marketingEventId} limit 1")
    BaiduCampaignEntity queryByMarketingEventId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM baidu_campaign WHERE ea=#{ea} and ad_account_id=#{adAccountId} and campaign_id=#{campaignId}")
    @FilterLog
    BaiduCampaignEntity queryCampaignByCampaignId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("campaignId")  Long campaignId);

    @Select("SELECT * FROM baidu_campaign WHERE ea=#{ea} and campaign_id=#{campaignId} limit 1")
    BaiduCampaignEntity queryByCampaignId(@Param("ea") String ea, @Param("campaignId")  Long campaignId);


    @Select("<script>  "
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND campaign_id =ANY(ARRAY "
            +   "<foreach collection = 'campaignIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> queryCampaignByCampaignByIds(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("campaignIds") List<Long> campaignIds);

    @Select("<script>  "
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND marketing_event_id is not null AND campaign_id = ANY(ARRAY "
            +   "<foreach collection = 'campaignIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> queryMarketingEventIdNotEmptyCampaignByCampaignByIds(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("campaignIds") List<Long> campaignIds);


    @Select("SELECT * FROM baidu_campaign WHERE  ea=#{ea} and marketing_event_id is not null")
    List<BaiduCampaignEntity> listRelateMarketingEventCampaigns(@Param("ea") String ea);


    @Select("SELECT * FROM baidu_campaign WHERE  ea=#{ea} and marketing_event_id is not null")
    @FilterLog
    List<BaiduCampaignEntity> listRelateMarketingEventCampaign(@Param("ea") String ea, @Param("adAccountId") String adAccountId);

    @Select("<script>  "
        + "SELECT * \n"
        + "from baidu_campaign bc\n"
        + "WHERE bc.ea = #{ea} AND ad_account_id=#{adAccountId} \n"
        +	"<if test=\"status != null\">"
        +	    "AND bc.status = #{status}\n"
        +   "</if>"
        +	"<if test=\"nameKey != null\">"
        +	    "AND bc.campaign_name like concat(concat('%',#{nameKey}),'%')\n"
        +   "</if>"
        + "ORDER BY bc.marketing_event_id, bc.campaign_name"
        + "</script>")
    List<BaiduCampaignEntity> pageCampaign(@Param("ea") String ea,@Param("adAccountId") String adAccountId, @Param("status") Integer status, @Param("nameKey") String nameKey, @Param("page") Page page);

    @Select("SELECT COUNT(*) FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}")
    int queryRefereshCampaignTotalCount(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source")String source);

    @Select("SELECT COUNT(*) FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND marketing_event_id is not null")
    int queryMarketingEventNotNullTotalCount(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Select("<script>"
           + "SELECT campaign_id FROM baidu_campaign WHERE ea=#{ea} AND source=#{source}"
           + "</script>")
    List<Long> pageCampaignIds(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}"
            + "</script>")
    @FilterLog
    List<BaiduCampaignEntity> pageCampaignEntityData(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source} AND marketing_event_id is not null"
            + "</script>")
    List<BaiduCampaignEntity> pageMarketingEventNotNullData(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("page") Page page);


    @Update("<script>UPDATE baidu_campaign as c SET marketing_event_id=tmp.marketingEventId, update_time=now() FROM (values"
            + "<foreach separator=',' collection='campaignEntityList' item='item'>"
            +   "(#{item.id}, #{item.marketingEventId})"
            + "</foreach>"
            + ") as tmp(id, marketingEventId) WHERE c.ea = #{ea} AND c.ad_account_id=#{adAccountId} AND c.id=tmp.id"
            + "</script>")
    @FilterLog
    int batchUpdateMarketingEventById(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("campaignEntityList") List<AdCampaignEntity> campaignEntityList);


    @Select("SELECT COUNT(*) FROM baidu_campaign WHERE ea=#{ea} AND source=#{source}")
    int queryCampaignTotalCount(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source")String source);

    @Select("SELECT marketing_event_id FROM baidu_campaign WHERE ea=#{ea} AND source=#{source}")
    List<String> getCampaignLinkMarketingEventIds(@Param("ea") String ea, @Param("source")String source);

    @Select("SELECT marketing_event_id FROM baidu_campaign WHERE ea=#{ea}")
    List<String> getAllMarketingEventId(@Param("ea") String ea);

    @Select("<script>" +
            "SELECT marketing_event_id FROM baidu_campaign WHERE ea=#{ea} " +
            "    <if test=\"adAccountId != null\">\n" +
            "         AND ad_account_id=#{adAccountId}\n" +
            "    </if>\n"
            + " <if test=\"keyword != null and keyword != ''\">\n"
            + " AND campaign_name like concat(concat('%',#{keyword}),'%')\n"
            + " </if>\n"
            + " AND source=#{source}" +
            "</script>")
    List<String> getMarketingEventIdsByEaAndAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("keyword") String keyword);


    @Select("<script>"
            + "SELECT id FROM baidu_campaign WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND campaign_id != ANY(ARRAY\n"
            +   "<foreach collection = 'campaignIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<String> queryNotQueryCampaignData(@Param("ea")String ea, @Param("campaignIds")List<Long> campaignIds, @Param("adAccountId") String adAccountId);

    @Update("<script>"
            + "UPDATE baidu_campaign SET status=#{status} WHERE ea=#{ea} AND id =ANY(ARRAY \n"
            +   "<foreach collection = 'ids' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
          + "</script>")
    void updateCampaignStatusByIds(@Param("ea")String ea, @Param("ids")List<String> ids, @Param("status")Integer status);

    @Select("SELECT * FROM baidu_campaign WHERE ea = #{ea} and marketing_event_id = #{marketingEventId} limit 1")
    BaiduCampaignEntity queryCampaignByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);


    @Select("<script>"
            + "select * from baidu_campaign WHERE ea=#{ea} AND marketing_event_id = ANY(ARRAY \n"
            +   "<foreach collection = 'marketingEventIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> queryByMarketingEventIdList(@Param("ea")String ea, @Param("marketingEventIdList") List<String> marketingEventIdList);

    @Select("<script>"
            + "select * from baidu_campaign WHERE ea=#{ea} AND campaign_id = ANY(ARRAY \n"
            +   "<foreach collection = 'campaignIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> queryByCampaignIdList(@Param("ea")String ea, @Param("campaignIdList") List<Long> campaignIdList);


    @Select("<script>"
            + "select ea, id,marketing_event_id as marketingEventId,campaign_id as campaignId, ad_account_id as adAccountId  from baidu_campaign WHERE ea=#{ea} AND campaign_id =ANY(ARRAY\n"
            +   "<foreach collection = 'campaignIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> queryMarketingEventIdByCampaignIdList(@Param("ea")String ea, @Param("campaignIdList") List<Long> campaignIdList);

    @Select(" select count(*) from baidu_campaign where ea = #{ea} and ad_account_id = #{adAccountId} ")
    int countByAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId);

    @Select("<script>"
            + "select * from baidu_campaign  where  ea = #{ea} and ad_account_id = #{adAccountId} "
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "     and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<BaiduCampaignEntity> scanByAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("lastId") String lastId, @Param("limit") int limit);

    @Select("select campaign_name from baidu_campaign  where  ea = #{ea} and ad_account_id = #{adAccountId}")
    List<String> getAllNameByAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId);

    @Select("<script>"
            + "select * from baidu_campaign  where  ea = #{ea} and ad_account_id  =ANY(ARRAY\n"
            +   "<foreach collection = 'adAccountIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>"
    )
    List<BaiduCampaignEntity> queryAllByAdAccountIdList(@Param("ea") String ea, @Param("adAccountIdList") List<String> adAccountIdList);

    @Update("update baidu_campaign set marketing_event_id = #{marketingEventId} where ea = #{ea} and id = #{id}")
    int updateMarketingEventIdById(@Param("ea") String ea, @Param("id") String id, @Param("marketingEventId") String marketingEventId);

    @Select("<script>  "
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} AND campaign_name = ANY(ARRAY "
            +   "<foreach collection = 'nameList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    @FilterLog
    List<BaiduCampaignEntity> getByNameList(@Param("ea") String ea, @Param("nameList") List<String> nameList);

    @Select("<script>  "
            + "SELECT * FROM baidu_campaign WHERE ea=#{ea} and ad_account_id = #{adAccountId} AND campaign_name = ANY(ARRAY "
            +   "<foreach collection = 'nameList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<BaiduCampaignEntity> getByNameAndAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("nameList") List<String> nameList);

    @Select("<script>"
            + "select marketing_event_id from baidu_campaign WHERE ea=#{ea} AND ad_account_id = ANY(ARRAY \n"
            +   "<foreach collection = 'accountIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<String> queryMarketingEventIdByEaAndAccountIdList(@Param("ea")String ea, @Param("accountIdList") List<String> accountIdList);

}
