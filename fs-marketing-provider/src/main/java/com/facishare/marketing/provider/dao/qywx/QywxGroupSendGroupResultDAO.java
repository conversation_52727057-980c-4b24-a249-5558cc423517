package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.api.result.QywxGroupSendEmployeeRankingDataResult;
import com.facishare.marketing.api.result.qywx.ListEmployeeQywxGroupSendDetailResult;
import com.facishare.marketing.provider.dto.ListEmployeeQywxGroupSendDetailDto;
import com.facishare.marketing.provider.dto.dbroute.QywxGroupResultDTO;
import com.facishare.marketing.provider.dto.qywx.SendGroupResultDTO;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupFlatResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendResultEntity;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.pagination.Page;
import dev.langchain4j.agent.tool.P;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by zhengh on 2020/4/21.
 */
public interface QywxGroupSendGroupResultDAO extends ICrudMapper<QywxGroupSendGroupResultEntity> {
    @Insert("INSERT INTO qywx_group_send_group_result(id, ea, sendid, msgid, sender, errcode, errmsg, total_group_count, create_time, update_time)\n"
            + "VALUES(#{entity.id}, #{entity.ea}, #{entity.sendid}, #{entity.msgid}, #{entity.sender},#{entity.errcode},#{entity.errmsg}, #{entity.totalGroupCount}, now(), now())")
    int insert(@Param("entity")QywxGroupSendGroupResultEntity entity);

    //获取最近7天的发送任务
    @Select("SELECT t.id AS taskId, g.id AS groupResultId, t.ea AS ea, g.msgid AS msgid FROM qywx_group_send_group_result g INNER JOIN qywx_group_send_task t ON t.id = g.sendid\n"
            + "WHERE g.msgid is not null AND (extract(epoch FROM t.create_time))  >= (extract(epoch FROM now()) - 7 * 24 * 3600)")
    List<SendGroupResultDTO> getNeedSendGroupResultTask();

    @Select("SELECT t.id AS taskId, g.id AS groupResultId, t.ea AS ea, g.msgid AS msgid FROM qywx_group_send_group_result g INNER JOIN qywx_group_send_task t ON t.id = g.sendid\n"
            + "WHERE g.msgid is not null AND (extract(epoch FROM t.create_time))  >= (extract(epoch FROM now()) - 1 * 24 * 3600)")
    List<SendGroupResultDTO> getNeedSendGroupResultTaskThisDay();

    @Update("UPDATE qywx_group_send_group_result SET success_count=#{successCount}, failed_count=#{failedCount}, unsend_count=#{unsendCount}, send_group_ids=#{groupIds}, update_time=now() WHERE id=#{id}")
    int updateResultById(@Param("id")String id, @Param("successCount") int successCount, @Param("failedCount")int failedCount,
                         @Param("unsendCount")int unsendCount, @Param("groupIds")String groupIds);

    @Select("<script>"
            + "SELECT * FROM qywx_group_send_group_result WHERE sendid IN\n"
            +   "<foreach collection = 'sendIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{item}"
            +   "</foreach>"
            + "</script>")
    List<QywxGroupSendGroupResultEntity> queryGroupResult(@Param("sendIds")List<String> sendIds);

    @Select("<script>"
            + "SELECT * FROM qywx_group_send_group_result WHERE sendid IN\n"
            +   "<foreach collection = 'sendIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{item}"
            +   "</foreach>"
            + "<if test =\"status != null and status == 0\">\n"
            + "and send_group_ids is null "
            + "</if>\n"
            + "<if test =\"status != null and status == 1\">\n"
            + "and send_group_ids is not null  "
            + "</if>\n"
            + "<if test =\"senderIdList != null and senderIdList.size != 0\">\n"
            + "and sender in "
            +   "<foreach collection = 'senderIdList' item = 'item' index='idx' open = '(' separator = ',' close = ')'>"
            +        "#{senderIdList[${idx}]}"
            +   "</foreach>"
            + "</if>\n"
            + "</script>")
    List<QywxGroupSendGroupResultEntity> queryGroupResultBySender(@Param("sendIds") List<String> sendIds, @Param("senderIdList") List<String> senderIdList, @Param("status") Integer status);


    QywxGroupSendGroupResultEntity getById(@Param("id")String id);

    @Delete("DELETE FROM qywx_group_send_group_result WHERE sendid=#{sendId}")
    int deleteBySendId(@Param("sendId")String sendId);

    @Select("<script>"
            + "SELECT sender employeeId, min(create_time) groupSendTime,sum(success_count) successGroupCount FROM qywx_group_send_group_result WHERE sendid IN\n"
            +   "<foreach collection = 'sendIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{sendIds[${num}]}"
            +   "</foreach>"
            + "<if test =\"senderIdList != null and senderIdList.size != 0\">\n"
            + "and sender in "
            +   "<foreach collection = 'senderIdList' index='idx' open = '(' separator = ',' close = ')'>"
            +        "#{senderIdList[${idx}]}"
            +   "</foreach>"
            + "</if>\n"
            + " group by sender order by employeeId"
            + "</script>")
    List<ListEmployeeQywxGroupSendDetailDto> listEmployeeQywxGroupSendDetail(@Param("sendIds") List<String> sendIds, @Param("senderIdList") List<String> senderIdList,
                                                                             @Param("page") Page<ListEmployeeQywxGroupSendDetailResult> page);


    @Select("<script>"
            + "SELECT COUNT(*) FROM (SELECT sender employeeId, min(create_time) groupSendTime,sum(success_count) successGroupCount FROM qywx_group_send_group_result WHERE sendid IN\n"
            +   "<foreach collection = 'sendIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{sendIds[${num}]}"
            +   "</foreach>"
            + "<if test =\"senderIdList != null and senderIdList.size != 0\">\n"
            + "and sender in "
            +   "<foreach collection = 'senderIdList' index='idx' open = '(' separator = ',' close = ')'>"
            +        "#{senderIdList[${idx}]}"
            +   "</foreach>"
            + "</if>\n"
            + " group by sender ) t"
            + "</script>")
    int countEmployeeQywxGroupSendDetail(@Param("sendIds") List<String> sendIds, @Param("senderIdList") List<String> senderIdList);

    @Select("<script>" +
            "SELECT t.userid qywxUserId, count(distinct t.msgid) confirmCount FROM (" +
            "select distinct a.userid, a.msgid\n" +
            "from qywx_group_send_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "union\n" +
            "select distinct a.sender userid, a.msgid\n" +
            "from qywx_group_send_group_flat_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            ") t" +
            " group by t.userid" +
            "</script>")
    List<QywxGroupSendEmployeeRankingDataResult> calculateEmployeeConfirmCountBySendIds(@Param("sendIds") List<String> sendIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "SELECT t.userid qywxUserId, count(distinct t.cus) deliveryCount FROM (" +
            "select distinct a.userid, a.external_userid cus\n" +
            "from qywx_group_send_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "union\n" +
            "select distinct a.sender userid, a.send_group_id cus\n" +
            "from qywx_group_send_group_flat_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            ") t" +
            " group by t.userid" +
            "</script>")
    List<QywxGroupSendEmployeeRankingDataResult> calculateEmployeeDeliveryCountBySendIds(@Param("sendIds") List<String> sendIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("select\n" +
            "\ta.*\n" +
            "from\n" +
            "\tqywx_group_send_group_result a \n" +
            "left join qywx_group_send_task b on a.sendid = b.id\n" +
            "where b.ea = #{ea}")
    List<QywxGroupSendGroupResultEntity> queryPageByEa(@Param("ea") String ea, Page page);

    @Insert("<script>INSERT INTO " +
            "qywx_group_send_group_flat_result(id, ea, sendid, msgid, sender, status, send_group_id, create_time, update_time)\n" +
            "VALUES" +
            "<foreach collection=\"entities\" item=\"entity\" separator=\",\">" +
            "(#{entity.id}, #{entity.ea}, #{entity.sendid}, #{entity.msgid}, #{entity.sender}, #{entity.status}, #{entity.sendGroupId}, #{entity.createTime}, #{entity.updateTime})" +
            "</foreach> ON CONFLICT DO NOTHING" +
            "</script>")
    int batchInsertFlatResult(@Param("entities") List<QywxGroupSendGroupFlatResultEntity> entities);

    @Update("<script>" +
            "DELETE FROM qywx_group_send_group_flat_result\n" +
            "WHERE sendid=#{sendid} and sender = #{sender}" +
            "</script>")
    int deleteFlatResult(@Param("sendid") String sendid, @Param("sender") String sender);

    @Update("UPDATE qywx_group_send_group_result SET success_count=#{successCount}, failed_count=#{failedCount}, unsend_count=#{unsendCount}, " +
            "send_group_ids=#{groupIds}, update_time=now() WHERE msgid=#{msgid}")
    void updateResultByMsgid(@Param("msgid")String msgid, @Param("successCount") int successCount, @Param("failedCount")int failedCount,
                             @Param("unsendCount")int unsendCount, @Param("groupIds")String groupIds);

    @Select("select qywx_group_send_group_result.* FROM qywx_group_send_task  join  qywx_group_send_group_result on qywx_group_send_group_result.sendid = qywx_group_send_task.id  WHERE qywx_group_send_task.ea = #{ea} and qywx_group_send_group_result.sender = #{sender}")
    List<QywxGroupSendGroupResultEntity> getBySender(@Param("ea") String ea, @Param("sender") String sender);

    @Update("update qywx_group_send_group_result set sender = #{sender} where id = #{id}")
    int updateSender(@Param("id") String id, @Param("sender") String sender);

    @Select("select qywx_group_send_group_flat_result.* FROM qywx_group_send_task  join  qywx_group_send_group_flat_result on qywx_group_send_group_flat_result.sendid = qywx_group_send_task.id  WHERE qywx_group_send_task.ea = #{ea} and qywx_group_send_group_flat_result.sender = #{sender}")
    List<QywxGroupSendGroupFlatResultEntity> getFlatResultBySender(@Param("ea") String ea, @Param("sender") String sender);

    @Update("update qywx_group_send_group_flat_result set sender = #{sender} where id = #{id}")
    int updateFlatResultSender(@Param("id") String id, @Param("sender") String sender);

    @Select("select id, ea, sendid, sender from qywx_group_send_group_result where id = sendid")
    List<QywxGroupResultDTO> queryAllIdsByTrigger();

    @Select("select id, ea, sendid, sender from qywx_group_send_group_result where id != sendid")
    List<QywxGroupResultDTO> queryAllIdsByTask();


    @Update("<script>" +
            "UPDATE qywx_group_send_group_result as a " +
            "SET ea = tmp.ea " +
            "FROM (VALUES " +
            "<foreach collection='dtos' item='item' separator=','>" +
            "(#{item.id}, #{item.ea})" +
            "</foreach>" +
            ") AS tmp(id, ea) " +
            "WHERE a.id = tmp.id" +
            "</script>")
    int batchUpdateEaByDTO(@Param("dtos") List<QywxGroupResultDTO> dtos);

    @Select("select distinct(sendid) from qywx_group_send_group_flat_result")
    List<String> queryAllSendIds();

    @Update("update qywx_group_send_group_flat_result set ea=#{ea} where sendid=#{sendId}")
    int updateEaBySendId(@Param("ea")String ea, @Param("sendId")String sendId);
}
