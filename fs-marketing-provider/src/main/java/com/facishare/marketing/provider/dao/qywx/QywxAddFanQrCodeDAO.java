package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.dao.param.qywx.QywxAddFanQueryParam;
import com.facishare.marketing.provider.dto.qywx.QywxAddFanQrCodeDTO;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2020/4/22.
 */
public interface QywxAddFanQrCodeDAO extends ICrudMapper<QywxAddFanQrCodeEntity> {
    @Insert("INSERT INTO qywx_add_fan_qr_code(id, ea, qr_code_name, remark, channel_desc, tag, user_id, type, qr_code_url,"
            + " skip_verify, config_id, status, state, is_bind_website, customer_count, welcome_content, welcome_image_path, welcome_image_title, welcome_link_title,"
            + " welcome_link_url, welcome_miniprogram_title, welcome_miniprogram_media_path, "
            + " welcome_miniprogram_page, create_time, update_time, channel_value, parent_id, do_bind_fs_user_id, do_bind_fs_user_name, update_bind_status_time,department_id,marketing_event_id,create_by,tag_id,orgin_user_id,spread_user_id,poster_id, source, "
            + " bind_api_name, bind_object_id, user_base_remark, user_base_remark_status)\n"
            + " VALUES(#{entity.id}, #{entity.ea}, #{entity.qrCodeName}, #{entity.remark}, #{entity.channelDesc}, #{entity.tag},"
            + " #{entity.userId}, #{entity.type},#{entity.qrCodeUrl},#{entity.skipVerify}, #{entity.configId},  #{entity.status},"
            + "#{entity.state}, #{entity.isBindWebsite}, #{entity.customerCount}, #{entity.welcomeContent},  #{entity.welcomeImagePath}, #{entity.welcomeImageTitle}, #{entity.welcomeLinkTitle},"
            + " #{entity.welcomeLinkUrl}, #{entity.welcomeMiniprogramTitle},  "
            + " #{entity.welcomeMiniprogramMediaPath}, #{entity.welcomeMiniprogramPage},  now(), now(), #{entity.channelValue}, #{entity.parentId}, #{entity.doBindFsUserId}, #{entity.doBindFsUserName}, #{entity.updateBindStatusTime}, #{entity.departmentId}, #{entity.marketingEventId},#{entity.createBy},#{entity.tagId},#{entity.orginUserId},"
            + " #{entity.spreadUserId}, #{entity.posterId}, #{entity.source}, #{entity.bindApiName}, #{entity.bindObjectId}, #{entity.userBaseRemark}, #{entity.userBaseRemarkStatus})"
            + " ON CONFLICT (id) DO UPDATE SET "
            + " qr_code_name=#{entity.qrCodeName}, remark=#{entity.remark}, channel_desc=#{entity.channelDesc},"
            + " tag=#{entity.tag}, user_id=#{entity.userId}, type=#{entity.type}, state = #{entity.state}, qr_code_url=#{entity.qrCodeUrl}, skip_verify=#{entity.skipVerify},"
            + " config_id=#{entity.configId}, welcome_content=#{entity.welcomeContent}, welcome_image_path=#{entity.welcomeImagePath}, welcome_image_title=#{entity.welcomeImageTitle},"
            + " welcome_link_title=#{entity.welcomeLinkTitle},  welcome_link_url=#{entity.welcomeLinkUrl}, welcome_miniprogram_title=#{entity.welcomeMiniprogramTitle},"
            + " welcome_miniprogram_media_path=#{entity.welcomeMiniprogramMediaPath}, welcome_miniprogram_page=#{entity.welcomeMiniprogramPage}, channel_value=#{entity.channelValue},"
            + " department_id =#{entity.departmentId}, marketing_event_id =#{entity.marketingEventId},tag_id =#{entity.tagId},orgin_user_id =#{entity.orginUserId}, user_base_remark=#{entity.userBaseRemark}, user_base_remark_status=#{entity.userBaseRemarkStatus}, update_time=now()"
    )
    int insert(@Param("entity") QywxAddFanQrCodeEntity entity);

    @Select("<script>"
            + "SELECT * FROM qywx_add_fan_qr_code WHERE ea=#{ea} AND status = 0 AND is_bind_website=0"
            + "<if test=\"name != null\">AND qr_code_name LIKE CONCAT ('%', #{name}, '%')</if>"
            + " order by update_time desc"
            + "</script>")
    List<QywxAddFanQrCodeEntity> getPageByEaAndName(@Param("ea") String ea, @Param("name") String name, @Param("page") Page page);

    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE id=#{id}")
    QywxAddFanQrCodeEntity getById(@Param("id") String id);

    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE id=#{id} and is_bind_website = 1")
    QywxAddFanQrCodeEntity getBindWebsiteFanQrCodeById(@Param("id") String id);

    @Update("UPDATE qywx_add_fan_qr_code SET customer_count=#{customerCount} WHERE id=#{id}")
    void syncCustomerCountById(@Param("id") String id, @Param("customerCount") int customerCount);

    @Update("UPDATE qywx_add_fan_qr_code SET qr_code_name=#{entity.qrCodeName}, remark=#{entity.remark}, channel_desc=#{entity.channelDesc},\n"
            + "tag=#{entity.tag}, user_id=#{entity.userId}, type=#{entity.type}, qr_code_url=#{entity.qrCodeUrl}, skip_verify=#{entity.skipVerify},\n"
            + "config_id=#{entity.configId}, welcome_content=#{entity.welcomeContent}, welcome_image_path=#{entity.welcomeImagePath}, welcome_image_title=#{entity.welcomeImageTitle},\n"
            + " welcome_link_title=#{entity.welcomeLinkTitle},  welcome_link_url=#{entity.welcomeLinkUrl}, welcome_miniprogram_title=#{entity.welcomeMiniprogramTitle},\n"
            + " welcome_miniprogram_media_path=#{entity.welcomeMiniprogramMediaPath}, welcome_miniprogram_page=#{entity.welcomeMiniprogramPage}, channel_value=#{entity.channelValue}, department_id =#{entity.departmentId}, marketing_event_id =#{entity.marketingEventId},tag_id =#{entity.tagId},orgin_user_id =#{entity.orginUserId},user_base_remark=#{entity.userBaseRemark}, user_base_remark_status=#{entity.userBaseRemarkStatus}, update_time=now() WHERE id=#{entity.id}")
    int updateById(@Param("entity") QywxAddFanQrCodeEntity entity);

    @Update("UPDATE qywx_add_fan_qr_code SET status=#{status} WHERE id=#{id}")
    int updateStatusById(@Param("id") String id, @Param("status") Integer status);


    @Update("<script>"
            + "update qywx_add_fan_qr_code "
            + "set is_bind_website=#{bindStatus}, "
            + "do_bind_fs_user_id=#{fsUserId}, "
            + "<if test=\"fsUserName != null\"> do_bind_fs_user_name=#{fsUserName}, </if>"
            + "update_bind_status_time=now() "
            + "where id=#{id}"
            + "</script>")
    int updateFanQrCodeWebsiteBindStatus(@Param("id") String id, @Param("bindStatus") Integer bindStatus, @Param("fsUserId") Integer fsUserId, @Param("fsUserName") String fsUserName);

    @Select("select * from  qywx_add_fan_qr_code where is_bind_website=1 and ea=#{ea} order by create_time DESC ")
    List<QywxAddFanQrCodeEntity> queryBindWebsiteByEa(@Param("ea") String ea);

    @Update("UPDATE qywx_add_fan_qr_code SET customer_count=customer_count + 1 WHERE id=#{id}")
    int updateCustomerCountById(@Param("id") String id);

    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea=#{ea} and state=#{state}")
    QywxAddFanQrCodeEntity getByEaAndState(@Param("ea") String ea, @Param("state") String state);

    @Delete("DELETE FROM qywx_add_fan_qr_code WHERE id=#{id} AND ea=#{ea}")
    int deleteByIdAndEa(@Param("id") String id, @Param("ea") String ea);

    @Update("UPDATE qywx_add_fan_qr_code SET customer_count=customer_count+1 WHERE id=#{id} AND ea=#{ea}")
    int increaseScanCount(@Param("id") String id, @Param("ea") String ea);

    @Select("select * from  qywx_add_fan_qr_code where marketing_event_id=#{marketingEventId} and ea=#{ea} order by create_time DESC ")
    List<QywxAddFanQrCodeEntity> getByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("select\n" +
            "\t*\n" +
            "from\n" +
            "\tqywx_add_fan_qr_code qafqc\n" +
            "where\n" +
            "\tea = #{fsEa}\n" +
            "\tand user_id = concat('[\"', #{qyUserId}, '\"]')\n" +
            "\tand type = 1\n" +
            "\tand status = 0\n" +
            "\tand channel_value = 'qywx'\n" +
            "order by\n" +
            "\tupdate_time desc\n" +
            "limit 1")
    QywxAddFanQrCodeEntity queryByUserId(@Param("fsEa") String fsEa, @Param("qyUserId") String qyUserId);

    @Select("<script> " +
            "SELECT * FROM qywx_add_fan_qr_code WHERE status = 0 AND id IN  " +
            "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'> " +
            "#{item} " +
            "</foreach> " +
            "</script>")
    List<QywxAddFanQrCodeEntity> getByIds(@Param("ids") List<String> objectIdList);

    @Select("<script> " +
            "SELECT * FROM qywx_add_fan_qr_code WHERE  id IN  " +
            "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'> " +
            "#{item} " +
            "</foreach> " +
            "</script>")
    List<QywxAddFanQrCodeEntity> getAddFanQrCodeByIds(@Param("ids") List<String> objectIdList);

    @Update("<script> "
            + "UPDATE qywx_add_fan_qr_code SET status = 1 where id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    void deleteByIdList(@Param("idList") List<String> idList);

    @Select("<script>"
            + "select count(*) from ("
            + "SELECT qywx_add_fan_qr_code.id FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND status = 0 AND is_bind_website=0  AND qywx_add_fan_qr_code.poster_id is null\n"
            //   + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
            + " and qywx_add_fan_qr_code.source=1\n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 36)"
            + "UNION "
            + "SELECT qywx_add_fan_qr_code.id FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND create_by = #{userId}  AND status = 0 AND is_bind_website=0 AND qywx_add_fan_qr_code.poster_id is null\n"
            + " ) ct"
            + "</script>")
    int queryUnGroupAndCreateByMeCount(@Param("ea") String ea, @Param("userId") int userId);

    @Select("SELECT COUNT(*) FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND create_by = #{userId}  AND status = 0 AND is_bind_website=0 AND poster_id is null")
    int queryCountCreateByMe(@Param("ea") String ea, @Param("userId") Integer userId);

    @Select("<script>"
            + "SELECT count(*) FROM qywx_add_fan_qr_code WHERE ea= #{ea} AND status = 0 AND is_bind_website=0 AND poster_id is null\n"
            //  + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
            + " and qywx_add_fan_qr_code.source=1\n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 36)"
            + "</script>")
    int queryCountByUnGrouped(@Param("ea") String ea);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT qywx_add_fan_qr_code.id FROM qywx_add_fan_qr_code JOIN object_group_relation on  qywx_add_fan_qr_code.ea = object_group_relation.ea AND qywx_add_fan_qr_code.id = object_group_relation.object_id"
            + " WHERE qywx_add_fan_qr_code.ea = #{ea} AND qywx_add_fan_qr_code.status = 0  AND qywx_add_fan_qr_code.is_bind_website = 0   AND qywx_add_fan_qr_code.poster_id is null\n"
            //     + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
            + " and qywx_add_fan_qr_code.source=1\n"
            + " AND object_group_relation.group_id IN\n"
            + "<foreach collection = 'groupIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "UNION"
            + " select qywx_add_fan_qr_code.id from qywx_add_fan_qr_code  where ea = #{ea} and status = 0 and is_bind_website = 0  AND qywx_add_fan_qr_code.poster_id is null"
            //    + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
            + " and qywx_add_fan_qr_code.source=1\n"
            + " and id not in (select object_id from object_group_relation where ea = #{ea} and object_type = 36)"
            + "UNION "
            + "SELECT qywx_add_fan_qr_code.id FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND create_by = #{userId} AND status = 0 and is_bind_website = 0  AND qywx_add_fan_qr_code.poster_id is null"
            + " ) hex"
            + "</script>")
    int queryAccessibleCount(@Param("ea") String ea, @Param("groupIdList") List<String> groupIdList, @Param("userId") int userId);

    @Select(
            "<script>"
                    + "select qywx_add_fan_qr_code.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_add_fan_qr_code\n"
                    + "left join object_group_relation on qywx_add_fan_qr_code.id = object_group_relation.object_id and qywx_add_fan_qr_code.ea = object_group_relation.ea\n"
                    + "left join object_top on qywx_add_fan_qr_code.id = object_top.object_id and object_top.ea = qywx_add_fan_qr_code.ea\n"
                    + "where qywx_add_fan_qr_code.ea = #{queryParam.ea} and qywx_add_fan_qr_code.status = 0 and  qywx_add_fan_qr_code.is_bind_website=0  \n"
                    + " and qywx_add_fan_qr_code.source=1\n"
                    //        + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
                    + " <if test=\"queryParam.keyword != null and queryParam.keyword !='' \">\n"
                    + "  AND qywx_add_fan_qr_code.qr_code_name LIKE CONCAT('%',#{queryParam.keyword},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.isBandPoster == null or queryParam.isBandPoster == 0 \">\n"
                    + "  and qywx_add_fan_qr_code.poster_id is null \n"
                    + " </if>\n"
                    + "<if test=\"queryParam.groupIdList != null and queryParam.groupIdList.size != 0\">"
                    + " and object_group_relation.group_id in "
                    + "<foreach collection = 'queryParam.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    + "#{queryParam.groupIdList[${index}]}"
                    + "</foreach>"
                    + "</if>"
                    + "<if test=\"queryParam.marketingEventId != null\">"
                    + " and qywx_add_fan_qr_code.marketing_event_id = #{queryParam.marketingEventId} \n"
                    + "</if>"
                    + " and ( "
                    + "qywx_add_fan_qr_code.create_by = #{queryParam.userId} \n"
                    + "or object_group_relation.group_id is null"
                    + "<if test=\"queryParam.permissionGroupIdList != null and queryParam.permissionGroupIdList.size != 0\">"
                    + "or object_group_relation.group_id in "
                    + "<foreach collection = 'queryParam.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    + "#{queryParam.permissionGroupIdList[${index}]}"
                    + "</foreach>"
                    + "</if>"
                    + ")"
                    + "order by object_top.create_time desc nulls last, qywx_add_fan_qr_code.update_time desc"
                    + "</script>"
    )
    List<QywxAddFanQrCodeDTO> getAccessiblePage(@Param("queryParam") QywxAddFanQueryParam param, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qywx_add_fan_qr_code.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_add_fan_qr_code\n"
                    + "left join object_top on qywx_add_fan_qr_code.id = object_top.object_id and object_top.ea = qywx_add_fan_qr_code.ea\n"
                    + "where qywx_add_fan_qr_code.ea = #{queryParam.ea} and qywx_add_fan_qr_code.status = 0 and  qywx_add_fan_qr_code.is_bind_website=0 \n"
                    //      + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
                    + " and qywx_add_fan_qr_code.source=1\n"
                    + " <if test=\"queryParam.keyword != null and queryParam.keyword !='' \">\n"
                    + "  AND qywx_add_fan_qr_code.qr_code_name LIKE CONCAT('%',#{queryParam.keyword},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.isBandPoster == null or queryParam.isBandPoster == 0 \">\n"
                    + "  and qywx_add_fan_qr_code.poster_id is null \n"
                    + " </if>\n"
                    + "<if test=\"queryParam.marketingEventId != null\">"
                    + " and qywx_add_fan_qr_code.marketing_event_id = #{queryParam.marketingEventId} \n"
                    + "</if>"
                    + " AND qywx_add_fan_qr_code.create_by = #{queryParam.userId}"
                    + "order by object_top.create_time desc nulls last, qywx_add_fan_qr_code.update_time desc"
                    + "</script>"
    )
    List<QywxAddFanQrCodeDTO> getCreateByMePage(@Param("queryParam") QywxAddFanQueryParam queryParam, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qywx_add_fan_qr_code.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_add_fan_qr_code\n"
                    + "left join object_group_relation on qywx_add_fan_qr_code.id = object_group_relation.object_id and qywx_add_fan_qr_code.ea = object_group_relation.ea\n"
                    + "left join object_top on qywx_add_fan_qr_code.id = object_top.object_id and object_top.ea = qywx_add_fan_qr_code.ea\n"
                    + "where qywx_add_fan_qr_code.ea = #{queryParam.ea} and qywx_add_fan_qr_code.status = 0 and  qywx_add_fan_qr_code.is_bind_website=0  \n"
                    //    + " and qywx_add_fan_qr_code.qr_code_name not like '%企微二维码' and qywx_add_fan_qr_code.welcome_content != '你好你好' "
                    + " and qywx_add_fan_qr_code.source=1\n"
                    + " <if test=\"queryParam.keyword != null and queryParam.keyword !='' \">\n"
                    + "  AND qywx_add_fan_qr_code.qr_code_name LIKE CONCAT('%',#{queryParam.keyword},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.isBandPoster == null or queryParam.isBandPoster == 0 \">\n"
                    + "  and qywx_add_fan_qr_code.poster_id is null \n"
                    + " </if>\n"
                    + "<if test=\"queryParam.marketingEventId != null\">"
                    + " and qywx_add_fan_qr_code.marketing_event_id = #{queryParam.marketingEventId} \n"
                    + "</if>"
                    + " AND ( object_group_relation.group_id is null )"
                    + "order by object_top.create_time desc nulls last, qywx_add_fan_qr_code.update_time desc"
                    + "</script>"
    )
    List<QywxAddFanQrCodeDTO> noGroupPage(@Param("queryParam") QywxAddFanQueryParam queryParam, @Param("page") Page page);

    @Select("<script>"
            + "select * from qywx_add_fan_qr_code\n"
            + "where ea = #{fsEa} and type = 1"
            //  + " and qywx_add_fan_qr_code.qr_code_name like '%企微二维码' and qywx_add_fan_qr_code.welcome_content = '你好你好' "
            + " and qywx_add_fan_qr_code.source=2\n"
            + " and user_id IN "
            + " <foreach open='(' close=')' separator=',' collection='qyUserIdList' index='idx'>"
            + " concat('[\"', #{qyUserIdList[${idx}]}, '\"]')\n"
            + "</foreach>"
            + "</script>")
    List<QywxAddFanQrCodeEntity> queryCardFanQrCodeByUserIdList(@Param("fsEa") String fsEa, @Param("qyUserIdList") List<String> qyUserIdList);

    //根据parentId和ea查询二维码
    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND parent_id=#{parentId}")
    List<QywxAddFanQrCodeEntity> queryQrCodeByParentQrCodeId(@Param("ea") String ea, @Param("parentId") String parentId);

//    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND parent_id=#{parentId} and marketing_event_id=#{marketingEventId} and is_bind_poster = 1 and type=1 and  user_id = concat('[\"', #{userId}, '\"]')")
//    QywxAddFanQrCodeEntity queryQrCodeByParentAndQyUser(@Param("ea")String ea, @Param("parentId")String parentId, @Param("marketingEventId")String marketingEventId, @Param("userId")String userId);

    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND parent_id=#{parentId} and marketing_event_id=#{marketingEventId} and poster_id = #{posterId} and  spread_user_id = #{userId} order by create_time desc")
    List<QywxAddFanQrCodeEntity> queryQrCodeByParentAndSpreadUserId(@Param("ea") String ea, @Param("parentId") String parentId, @Param("marketingEventId") String marketingEventId, @Param("userId") Integer userId, @Param("posterId") String posterId);

    @Update("UPDATE qywx_add_fan_qr_code SET config_id=#{configId},state=#{state},qr_code_url=#{qrCodeUrl},status = 0,update_time=now() WHERE id=#{id}")
    int updateConfigById(@Param("id") String id, @Param("configId") String configId, @Param("state") String state, @Param("qrCodeUrl") String qrCodeUrl);

    @Select("SELECT ea FROM qywx_add_fan_qr_code WHERE status=0 and poster_id is not null group by ea having count(*)>300000")
    List<String> queryDeleteConfigEa();

    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND  poster_id is not null and status=0 ")
    List<QywxAddFanQrCodeEntity> queryDeleteConfig(@Param("ea") String ea);

    @Update("UPDATE qywx_add_fan_qr_code SET config_id=null,status = 1,update_time=now() WHERE id=#{id} and ea = #{ea}")
    int deletePosterConfigById(@Param("id") String id, @Param("ea") String ea);


    //    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea = #{ea} AND parent_id=#{parentId} and marketing_event_id=#{marketingEventId} and type=2 and is_bind_poster = 1")
//    List<QywxAddFanQrCodeEntity> queryMultipleQrCodeByParent(@Param("ea")String ea,  @Param("parentId")String parentId, @Param("marketingEventId")String marketingEventId);
    @Select("SELECT id,user_id FROM qywx_add_fan_qr_code WHERE ea = #{ea} and user_id like  CONCAT('%', #{userId}, '%')")
    List<QywxAddFanQrCodeEntity> getUserIdListByUserId(@Param("ea") String ea, @Param("userId") String userId);

    @Update("update qywx_add_fan_qr_code set user_id = #{userId},orgin_user_id = #{originUserId}, update_time = now() WHERE ea = #{ea} and id = #{id}")
    int updateUserId(@Param("ea") String ea, @Param("id") String id, @Param("userId") String userId, @Param("originUserId") String originUserId);

    @Select("<script>"
            + "SELECT DISTINCT(ea) as ea FROM qywx_add_fan_qr_code WHERE source = 1 AND create_time between #{startTime} AND #{endTime} AND ea IN\n"
            + "<foreach collection = 'eaList' item = 'item' open = '(' separator = ',' close = ')'>"
            +    "#{item}\n"
            + "</foreach>\n"
            + "</script>")
    List<String> queryEmployeeCreateFanQrCodeEaByCreateTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("eaList") List<String> eaList);

    @Select("SELECT * FROM qywx_add_fan_qr_code WHERE ea = #{ea} and status=0 and bind_api_name = #{bindApiName} and bind_object_id = #{bindObjectId} limit 1")
    QywxAddFanQrCodeEntity getByBindInfo(@Param("ea") String ea, @Param("bindApiName") String bindApiName, @Param("bindObjectId") String bindObjectId);
}
