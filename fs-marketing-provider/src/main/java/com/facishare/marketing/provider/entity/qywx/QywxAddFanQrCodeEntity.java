package com.facishare.marketing.provider.entity.qywx;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2020/4/22.
 */
@Data
public class QywxAddFanQrCodeEntity implements Serializable {
    private String id;           //id
    private String ea;           //企业帐号
    private String qrCodeName;     //吸粉码名称
    private String remark;       //备注
    private String channelDesc;  //渠道说明
    private String tag;          //标签json
    private String userId;       //企业微信userid json
    private String departmentId; //企业微信部门id json
    private String tagId;        //企业微信员工标签id json
    private int type;            //1：单人  2：多人
    private String qrCodeUrl;       //二维码地址
    private Integer skipVerify;  //跳过验证 0：跳过验证 1：需要验证
    private String configId;     //联系我的id
    private Integer status;      //0：正常 1：删除
    private String state;       //渠道id
    private Integer customerCount; //扫描客户数量
    private String welcomeContent;
    private String welcomeImagePath;
    private String welcomeImageTitle;
    private String welcomeLinkTitle;
    private String welcomeLinkUrl;
    private String welcomeMiniprogramTitle;
    private String welcomeMiniprogramMediaPath;
    private String welcomeMiniprogramPage;
    private Date createTime;     //创建时间
    private Date updateTime;     //更新时间
    private String channelValue; //推广渠道
    private Integer isBindWebsite;//是否关联官网--QrCodeBindWebsiteTypeEnum
    private Date updateBindStatusTime;//关联官网时间
    private Integer doBindFsUserId;//将该二维码关联官网的员工fsUserId
    private String doBindFsUserName;//将该二维码关联官网的员工姓名
    private String parentId;     //绑定的二维码id
    private String marketingEventId; //关联市场活动
    private Integer createBy; //创建人
    private String orginUserId; //原始的userId;
    private String websiteId; //官网id
    private Integer source;  //活码的来源 1：员工主动创建 2：名片自动创建
    private Integer spreadUserId; //推广员工参数
    private String posterId; //海报id0
    private String bindApiName;
    private String bindObjectId;
    private String userBaseRemark;
    private Integer userBaseRemarkStatus;
}
