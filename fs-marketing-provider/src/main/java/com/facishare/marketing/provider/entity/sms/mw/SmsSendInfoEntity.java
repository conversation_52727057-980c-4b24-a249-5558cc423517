package com.facishare.marketing.provider.entity.sms.mw;

import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import com.facishare.marketing.provider.entity.ExternalConfig;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Entity
public class SmsSendInfoEntity implements Serializable {
    private String id;
    // 公司账号
    private String ea;
    // 发送短信人的姓名
    private String creatorName;
    // 短信签名id
    private String signatureId;
    // 模板id
    private String templateId;
    // 发送状态
    private Integer status;
    //实际发送人数
    private Integer actualSenderCount;
    //需要发送人数
    private Integer toSenderCount;

    //消费短信数量
    private Integer totalFee;

    // 发送类型 立即发送和定时发送
    private Integer type;
    // 短信模板
    private String content;

    private Date createTime;
    private Date updateTime;

    // 错误码，为了展示前端信息
    private Integer resultCode;

    // 模板名称
    private String name;

    // 模板审核状态 状态：0确认有效，1审核中，2确认无效，3隐藏，4禁用，5模板不存在，6已过期，7删除，8审核失败
    private Integer templateStatus;

    private List<String> userGroups;

    private Date scheduleTime;

    private String marketingEventId;

    private FieldValueList paramDetail;
    
    private Integer sceneType;

    private ExternalConfig externalConfig;
}
