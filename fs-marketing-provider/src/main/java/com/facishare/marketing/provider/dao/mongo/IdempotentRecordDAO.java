package com.facishare.marketing.provider.dao.mongo;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.IdempotentRecordEntity;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.DuplicateKeyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.mongodb.morphia.query.UpdateResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Date;
import java.util.List;

@Repository
@Slf4j
public class IdempotentRecordDAO {

    @Autowired
    private DatastoreExt datastoreExt;

    @FilterLog
    public boolean insertRecord(String businessId) {
        IdempotentRecordEntity entity = new IdempotentRecordEntity();
        entity.set_id(businessId);
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        try {
            datastoreExt.insert(entity);
            return true;
        } catch (DuplicateKeyException e) {
            log.info("幂等id冲突, businessId: {}", businessId, e);
        } catch (UndeclaredThrowableException e) {
            if (e.getUndeclaredThrowable() != null && e.getUndeclaredThrowable() instanceof InvocationTargetException) {
                Throwable throwable = ((InvocationTargetException) e.getUndeclaredThrowable()).getTargetException();
                if (throwable instanceof DuplicateKeyException) {
                    log.info("幂等id冲突, businessId: {}", businessId, throwable);
                }
            }
        } catch (Exception e) {
            log.error("保存幂等记录失败,businessId: {}", businessId, e);
        }
        return false;
    }

    @FilterLog
    public boolean existRecord(String businessId) {
        Query<IdempotentRecordEntity> qy = datastoreExt.createQuery(IdempotentRecordEntity.class);
        qy.criteria("_id").equal(businessId);
        qy.limit(1);
        qy.offset(0);
        List<IdempotentRecordEntity> entityList = qy.asList();
        return CollectionUtils.isNotEmpty(entityList);
    }

    public IdempotentRecordEntity getByBusinessId(String businessId) {
        Query<IdempotentRecordEntity> qy = datastoreExt.createQuery(IdempotentRecordEntity.class);
        qy.criteria("_id").equal(businessId);
        qy.limit(1);
        qy.offset(0);
        List<IdempotentRecordEntity> entityList = qy.asList();
        return CollectionUtils.isNotEmpty(entityList) ? entityList.get(0) : null;
    }

    public void clearData(Date date) {
        Query<IdempotentRecordEntity> qy = datastoreExt.createQuery(IdempotentRecordEntity.class);
        qy.criteria("createTime").lessThan(date);
        datastoreExt.delete(qy);
    }

    public void deleteByBusinessId(String businessId) {
        Query<IdempotentRecordEntity> qy = datastoreExt.createQuery(IdempotentRecordEntity.class);
        qy.criteria("_id").equal(businessId);
        datastoreExt.delete(qy);
    }

    public boolean updateTime(String businessId, Date oldDate, Date date) {
        Query<IdempotentRecordEntity> query = datastoreExt.createQuery(IdempotentRecordEntity.class);
        query.criteria("_id").equal(businessId).criteria("updateTime").equal(oldDate);
        UpdateOperations<IdempotentRecordEntity> updateOperations = datastoreExt.createUpdateOperations(IdempotentRecordEntity.class)
                .set("updateTime", date);
        UpdateResults updateResults = datastoreExt.update(query, updateOperations);
        return updateResults != null && updateResults.getUpdatedCount() > 0;
    }
}
