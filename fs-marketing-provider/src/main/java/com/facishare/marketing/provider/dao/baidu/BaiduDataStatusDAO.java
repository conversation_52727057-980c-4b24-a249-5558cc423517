package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created by ranluch on 2019/11/29.
 */
public interface BaiduDataStatusDAO {
    @Insert("insert into ad_data_status (\"id\", \"ea\", \"ad_account_id\", \"refresh_time\", \"refresh_status\", \"source\", \"create_time\", \"update_time\") values("
        + " #{obj.id},\n"
        + " #{obj.ea},\n"
        + " #{obj.adAccountId},\n"
        + " #{obj.refreshTime},\n"
        + " #{obj.refreshStatus},\n"
        + " #{obj.source},\n"
        + " now(),\n"
        + " now()\n"
        + ") ON CONFLICT DO NOTHING;")
    boolean addAdDataStatus(@Param("obj") BaiduDataStatusEntity statusEntity);

    @Update("update ad_data_status set \n"
        + "\"refresh_time\" = now(), \n"
        + "\"refresh_status\" = #{refreshStatus}, \n"
        + "\"update_time\" = now()\n"
        + "  where \"id\" = #{id} and (refresh_time + '1 min') < now()")
    boolean updateDataStatus(@Param("id") String id, @Param("refreshStatus") Integer refreshStatus);

    @Select("SELECT * FROM ad_data_status WHERE ea=#{ea}")
    List<BaiduDataStatusEntity> queryRefreshStatusByEa(@Param("ea") String ea);

    @Select("SELECT * FROM ad_data_status WHERE ea=#{ea} AND source=#{source}")
    BaiduDataStatusEntity queryRefreshStatusByEaAndSource(@Param("ea") String ea, @Param("source") String source);

    @Select("SELECT * FROM ad_data_status WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}")
    BaiduDataStatusEntity queryRefreshStatus(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Update("update ad_data_status set \n"
        + "\"refresh_time\" = now(), \n"
        + "\"refresh_status\" = #{refreshStatus}, \n"
        + "\"update_time\" = now()\n"
        + "  where \"id\" = #{id} and (refresh_status = 2  OR refresh_status is null)")
    boolean updateDataStatusForFail(@Param("id") String id, @Param("refreshStatus") Integer refreshStatus);

    @Update("<script>"
        + "update ad_data_status set "
        + "<if test=\"refreshStatus == 1\"> "
        + "     refresh_success_time = now(), \n"
        + " </if> "
        + "\"refresh_status\" = #{refreshStatus}, "
        + "<if test=\"remark != null\"> "
        + "     remark = #{remark}, \n"
        + " </if> "
        + "\"update_time\" = now()"
        + "  where \"id\" = #{id} AND \"source\" = #{source}"
        + "</script>")
    boolean updateRefreshResult(@Param("id") String id, @Param("refreshStatus") Integer refreshStatus, @Param("remark") String remark, @Param("source") String source);

    @Select("SELECT * FROM ad_data_status WHERE refresh_status in (0, 1, 2)")
    List<BaiduDataStatusEntity> listForRefresh();

    @Select("SELECT * FROM ad_data_status WHERE id = #{id}")
    BaiduDataStatusEntity queryById(@Param("id") String id);
}
