package com.facishare.marketing.provider.manager.feed;

import com.facishare.marketing.api.data.material.ArticleBriefData;
import com.facishare.marketing.api.data.material.ProductBriefData;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
@Component
public class ProductMaterailDataManager extends MaterailDataManager<ProductBriefData> {
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Override
    public List<ProductBriefData> get(String ea,String... objectIds) {
        List<ProductBriefData> datas = Lists.newArrayList();
        List<ProductEntity> entities = productDAO.getByIds(Lists.newArrayList(objectIds));
        if (CollectionUtils.isNotEmpty(entities)) {
            List<String> imageUrls = Lists.newArrayList();
            for (ProductEntity entity : entities) {
                ProductBriefData data = new ProductBriefData();
                data.setId(entity.getId());
                data.setSummary(entity.getSummary());
                data.setName(entity.getName());
                data.setTitle(entity.getName());
                List<PhotoEntity> photoEntities = this.getPhotoEntities(entity.getId());
                String headPicThumbAPath = CollectionUtils.isNotEmpty(photoEntities) ? photoEntities.get(0).getPath() : null;
                data.setHeadPicThumbAPath(headPicThumbAPath);
                if (headPicThumbAPath != null) {
                    imageUrls.add(headPicThumbAPath);
                }
                data.setObjectType(ObjectTypeEnum.PRODUCT.getType());
                List<String> headPicsThumbs = getHeadImgs(ea,photoEntities);
                data.setHeadPicsThumbs(headPicsThumbs);
                data.setCreateTime(entity.getCreateTime().getTime());
                data.setUpdateTime(entity.getLastModifyTime().getTime());
                data.setCreator(entity.getFsUserId());
                datas.add(data);
            }
            Map<String, Map<Integer, PhotoEntity>> map = photoManager.batchQueryPhotoByTypesAndIds(Lists.newArrayList(46, 47, 48), Lists.newArrayList(objectIds));
            for (ProductBriefData data : datas) {
                Map<Integer, PhotoEntity> photoEntityMap = map.get(data.getId());
                if(photoEntityMap !=null){
                    if (photoEntityMap.get(46) != null) {
                        data.setSharePicMiniAppCutUrl(photoEntityMap.get(46).getThumbnailUrl());
                    }
                    if (photoEntityMap.get(47)  != null) {
                        data.setSharePicH5CutUrl(photoEntityMap.get(47).getThumbnailUrl());
                    }
                    if (photoEntityMap.get(48)  != null) {
                        data.setSharePicOrdinaryCutUrl(photoEntityMap.get(48).getThumbnailUrl());
                        //返回原图
                        data.setSharePicOrdinaryUrl(photoEntityMap.get(48).getUrl());
                    }
                }
            }
            //多线程处理图片
            Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
            datas.forEach(data -> {
                if (data.getHeadPicThumbAPath() != null && coverMap.containsKey(data.getHeadPicThumbAPath())) {
                    data.setCoverSize(coverMap.get(data.getHeadPicThumbAPath()));
                }
            });
        }
        return datas;
    }

    private List<String> getHeadImgs(String ea,List<PhotoEntity> photoEntityList) {
        if (CollectionUtils.isEmpty(photoEntityList)) {
            return null;
        }
        List<String> headImgs = Lists.newArrayList();
        photoEntityList.forEach(val -> {
            headImgs.add(fileV2Manager.getUrlByPath(val.getPath(), ea, false));
        });
        return headImgs;
    }

    private List<PhotoEntity> getPhotoEntities(String productId) {
        if (StringUtils.isEmpty(productId)) {
            return null;
        }
        return photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), productId);
    }

    @Override
    public Integer getType() {
        return ObjectTypeEnum.PRODUCT.getType();
    }
}
