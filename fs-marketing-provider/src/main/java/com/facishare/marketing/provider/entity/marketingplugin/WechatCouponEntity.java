package com.facishare.marketing.provider.entity.marketingplugin;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/15 18:24
 */
@Entity
@Data
public class WechatCouponEntity implements Serializable {
    private String id;
    private String ea;
    private String wechatCouponId;
    private Integer exchangePrice;
    private Integer expiredTip;
    private String availableDayTime;
    private String useMethod;
    private String couponCodeMode;
    private Integer discountAmount;
    private String merchantLogoUrl;
    private String couponImageUrl;
    private String channel;
    private Integer hideLink;
    private String description;
    private Integer expiredDays;
    private String outRequestNo;
    private Integer maxCoupons;
    private String goodsName;
    private Integer waitDaysAfterReceive;
    private String stockName;
    private Integer isMember;
    private String availableEndTime;
    private Integer transactionMinimum;
    private String merchantName;
    private String belongMerchant;
    private String stockId;
    private String tags;
    private String stockType;
    private String marketingEventId;
    private String backgroundColor;
    private Integer availableDayAfterReceive;
    private Integer maxCouponsPerUser;
    private String weekDay;
    private String templateId;
    private String comment;
    private Integer discountPercent;
    private String availableBeginTime;
    private Integer status;
    private String createTime;

    private Integer applicableChannel;

    private String sendScope; //发送范围

    private String couponId;

    private Integer operator;

    private String updateTime;

    private Integer dealerCount;

    private Integer isParticipate;

    private Integer scene;

    private Integer createCouponType;

    private Integer sendDownStatus;

    private String couponNo; //优惠券编号

    private String accountScope; // 前端传入的发送客户范围

    private String receiveScope; // 前端传入的领取客户范围
    private String storeScope; //领取门店范围

    private String sendScopeImportResult; //发送范围导入结果
    private String storeScopeImportResult; //领取门店范围导入结果
}
