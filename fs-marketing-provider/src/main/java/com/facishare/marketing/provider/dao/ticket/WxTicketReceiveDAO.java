package com.facishare.marketing.provider.dao.ticket;

import com.facishare.marketing.provider.entity.ticket.WxTicketReceiveEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2019/10/28
 **/
public interface WxTicketReceiveDAO {

    @Select("SELECT * FROM wx_ticket_receive WHERE form_data_user_id = #{formDataUserId}")
    WxTicketReceiveEntity getWxTicketReceiveByFormDataUserId(@Param("formDataUserId") String formDataUserId);


    @Insert("INSERT INTO wx_ticket_receive (\n"
        + "        \"id\",\n"
        + "        \"ea\",\n"
        + "        \"ticket_type\",\n"
        + "        \"association_id\",\n"
        + "        \"form_data_user_id\",\n"
        + "        \"card_id\",\n"
        + "        \"wx_app_id\",\n"
        + "        \"open_id\",\n"
        + "        \"ticket_status\",\n"
        + "        \"create_time\"\n"
        + "        )\n"
        + "        VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.ea},\n"
        + "        #{obj.ticketType},\n"
        + "        #{obj.associationId},\n"
        + "        #{obj.formDataUserId},\n"
        + "        #{obj.cardId},\n"
        + "        #{obj.wxAppId},\n"
        + "        #{obj.openId},\n"
        + "        #{obj.ticketStatus},\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;")
    int insertWxTicketReceive(@Param("obj") WxTicketReceiveEntity wxTicketReceiveEntity);

    @Select("SELECT * FROM wx_ticket_receive WHERE card_id = #{cardId} AND wx_app_id = #{wxAppId} AND open_id = #{openId}")
    WxTicketReceiveEntity getWxTicketReceiveByCardWxAppOpenId(@Param("cardId") String cardId, @Param("wxAppId") String wxAppId, @Param("openId") String openId);

    @Update("UPDATE wx_ticket_receive SET ticket_status = #{status}, receive_time = now() WHERE id = #{id}")
    int updateWxTicketReceiveStatus(@Param("status") Integer status, @Param("id") String id);

    @Delete("DELETE FROM wx_ticket_receive WHERE id = #{id}")
    int deleteById(@Param("id") String id);
}
