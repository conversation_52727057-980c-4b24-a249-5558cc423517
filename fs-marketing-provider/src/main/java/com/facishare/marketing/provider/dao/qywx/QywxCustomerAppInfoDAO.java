package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerTemplateInfoEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;


/**
 * Created  By zhoux 2020/05/15
 **/
public interface QywxCustomerAppInfoDAO {

    @Select(" SELECT * from qywx_customer_app_info where suit_id =#{suitId} and corp_id =#{corpId}")
    QywxCustomerAppInfoEntity selectOne(@Param("suitId") String suitId, @Param("corpId") String corpId);

    @Select(" SELECT * from qywx_customer_app_info where agent_id =#{agentId} and corp_id =#{corpId}")
    QywxCustomerAppInfoEntity getByCorpIdAndAgentId(@Param("agentId") String agentId, @Param("corpId") String corpId);

    @Insert("insert into qywx_customer_app_info (id,ea,corp_id,suit_id,auth_code,agent_id,create_time,update_time, suite_type) " +
            "values (#{entity.id}, #{entity.ea}, #{entity.corpId}, #{entity.suitId},#{entity.authCode},#{entity.agentId},now(),now(), #{entity.suiteType})")
    int insert(@Param("entity") QywxCustomerAppInfoEntity entity);

    @Update(" UPDATE qywx_customer_app_info SET auth_code = #{authCode},update_time =now(),agent_id=#{agentId},ea=#{ea} WHERE suit_id = #{suitId} and corp_id =#{corpId}")
    int update(@Param("authCode") String authCode, @Param("agentId") String agentId, @Param("corpId") String corpId, @Param("suitId") String suitId, @Param("ea") String ea);

    @Delete(" delete  from qywx_customer_app_info where suit_id =#{suitId} and corp_id =#{corpId}")
    int delete(@Param("suitId") String suitId, @Param("corpId") String corpId);

    @Select(" SELECT * from qywx_customer_app_info where corp_id =#{corpId} and ea = #{ea} and suite_type = 0")
    List<QywxCustomerAppInfoEntity> selectByCorpIdAndEa(@Param("corpId") String corpId, @Param("ea") String ea);

    @Select(" SELECT * from qywx_customer_app_info where ea = #{ea} and suite_type = #{suiteType} limit 1")
    QywxCustomerAppInfoEntity selectByEa(@Param("ea") String ea, @Param("suiteType") Integer suiteType);

    @Select("<script>"
            + "SELECT ea, corp_id FROM qywx_customer_app_info where 1 = 1 "
            + " <if test =\"eaList != null and eaList.size != 0\">\n"
            + "     and ea in "
            + "     <foreach open='(' close=')' separator=',' collection='eaList' index='idx'>"
            + "         #{eaList[${idx}]}"
            + "     </foreach>"
            + "</if>"
            + "</script>")
    List<QywxCustomerAppInfoEntity> getCorpIdAndEa(@Param("eaList") List<String> eaList);

    @Select(" SELECT * from qywx_customer_app_info where corp_id =#{corpId} and ea = #{ea}")
    List<QywxCustomerAppInfoEntity> selectAllByCorpIdAndEa(@Param("corpId") String corpId, @Param("ea") String ea);

    @Update(" UPDATE qywx_customer_app_info SET corp_id = #{newCorpId},update_time =now() WHERE id = #{id}")
    int updateCorpIdById(@Param("id") String id, @Param("newCorpId") String newCorpId);

    @Select("SELECT * FROM qywx_customer_app_info")
    List<QywxCustomerAppInfoEntity> selectAll();

}
