package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface QywxMiniappConfigDAO {

    @Select("SELECT * FROM qywx_miniapp_config WHERE appid = #{appid}")
    List<QywxMiniappConfigEntity> getByAppid(@Param("appid") String appid);

    @Select("SELECT * FROM qywx_miniapp_config WHERE ea = #{ea} AND appid=#{wxAppId} order by create_time desc limit 1")
    QywxMiniappConfigEntity getByEaAndAppId(@Param("ea") String ea, @Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM qywx_miniapp_config WHERE ea = #{ea} AND appid=#{wxAppId}")
    QywxMiniappConfigEntity getByEa(@Param("ea") String ea, @Param("wxAppId") String wxAppId);

    @Delete("DELETE FROM qywx_miniapp_config WHERE ea = #{ea} AND appid=#{wxAppId}")
    Integer delteByEaAndWxAppId(@Param("ea") String ea, @Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM qywx_miniapp_config WHERE appid=#{wxAppId} ORDER BY update_time ASC limit 1")
    QywxMiniappConfigEntity getAnyOne(@Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM qywx_miniapp_config WHERE corpid = #{corpId} AND appid = #{appId}")
    List<QywxMiniappConfigEntity> getByCorpidAndAppId(@Param("corpId") String corpId, @Param("appId") String appId);

    @Update("UPDATE qywx_miniapp_config SET corpid=#{corpId}, agentid=#{agentId}, secret=#{secret}, update_time=now() WHERE ea=#{ea} AND appid=#{wxAppId}")
    int updateMiniappConfigByEa(@Param("ea")String ea, @Param("wxAppId") String wxAppId, @Param("corpId") String corpId, @Param("agentId")String agentId, @Param("secret")String secret);

    @Insert("INSERT INTO qywx_miniapp_config(id, ea, corpid, agentid, secret, appid, create_time, update_time ) VALUES(#{entity.id}, " +
            "#{entity.ea}, #{entity.corpid}, #{entity.agentid}, #{entity.secret}, #{entity.appid},#{entity.createTime}, #{entity.updateTime})")
    void insert(@Param("entity")QywxMiniappConfigEntity entity);

    @Select("SELECT * FROM qywx_miniapp_config")
    List<QywxMiniappConfigEntity> listAll();

    @Select("SELECT * FROM qywx_miniapp_config WHERE corpid = #{corpId} and ea =#{ea} ORDER BY update_time ASC ")
    List<QywxMiniappConfigEntity> getByCorpIdAndEa(@Param("corpId") String corpId,@Param("ea") String ea);

    @Update("UPDATE qywx_miniapp_config SET corpid = #{corpId}, update_time=now() WHERE ea = #{ea}")
    void updateCorpIdByEa(@Param("ea") String ea, @Param("corpId") String corpId);

    @Update("UPDATE qywx_miniapp_config SET agentid = #{agentId}, update_time=now() WHERE ea = #{ea}")
    void updateAgentIdByEa(@Param("ea") String ea, @Param("agentId") String agentId);

    @Update("UPDATE qywx_miniapp_config SET appid = #{appId}, update_time=now() WHERE ea = #{ea}")
    void updateAppIdCorpIdByEa(@Param("appId") String appId, @Param("ea") String ea);

    @Update("UPDATE qywx_miniapp_config SET corpid = #{corpId}, update_time=now() WHERE ea = #{ea} and id = #{id}")
    int updateCorpIdById(@Param("ea") String ea, @Param("id") String id, @Param("corpId") String corpId);
}
