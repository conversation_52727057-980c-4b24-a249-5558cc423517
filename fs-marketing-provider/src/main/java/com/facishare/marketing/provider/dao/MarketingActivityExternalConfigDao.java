package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.provider.dto.MarketingEventActivityDTO;
import com.facishare.marketing.provider.entity.ExternalConfig;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.github.mybatis.mapper.IPaginationPostgresqlMapper;
import com.github.mybatis.pagination.Page;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/20.
 */
public interface MarketingActivityExternalConfigDao extends IPaginationPostgresqlMapper<MarketingActivityExternalConfigEntity> {
    @Insert("INSERT INTO marketing_activity_external_config(id, marketing_activity_id, external_config, associate_id_type, associate_id, ea,marketing_activity_type, marketing_event_id, is_need_audit) VALUES (#{entity.id}, #{entity.marketingActivityId}, #{entity.externalConfig}, #{entity.associateIdType}, #{entity.associateId},#{entity.ea},#{entity.marketingActivityType} ,#{entity.marketingEventId}, #{entity.isNeedAudit}) ON CONFLICT DO NOTHING")
    boolean insert(@Param("entity") MarketingActivityExternalConfigEntity entity);

    @Update("UPDATE marketing_activity_external_config SET  marketing_activity_id = #{entity.marketingActivityId}, external_config = #{entity.externalConfig}, associate_id_type = #{entity.associateIdType}, associate_id = #{entity.associateId} WHERE id = #{entity.id} ")
    void update(@Param("entity") MarketingActivityExternalConfigEntity entity);

    @Update("UPDATE marketing_activity_external_config SET associate_id = #{associateId} WHERE marketing_activity_id = #{marketingActivityId} AND ea = #{ea}")
    boolean updateAssociateIdByMarketingActivityId(@Param("associateId") String associateId, @Param("marketingActivityId") String marketingActivityId, @Param("ea") String ea);

    @Update("UPDATE marketing_activity_external_config SET  marketing_event_id = #{entity.marketingEventId}, external_config = #{entity.externalConfig} WHERE marketing_activity_id = #{entity.marketingActivityId} and ea=#{entity.ea}")
    boolean updateMarketingEvenIdByMarketingActivityId(@Param("entity") MarketingActivityExternalConfigEntity entity);

    @Update("UPDATE marketing_activity_external_config SET create_time = #{createTime}  WHERE id = #{id} ")
    void updateCreateTime(@Param("id") String id,@Param("createTime") Date createTime);

    @Update("UPDATE marketing_activity_external_config SET associate_id = #{associateId}, create_time = #{createTime}  WHERE id = #{id} ")
    void updateAssociateIdAndCreateTime(@Param("id") String id,@Param("associateId") String associateId, @Param("createTime") Date createTime);

    @Update("UPDATE marketing_activity_external_config SET external_config = #{externalConfig}  WHERE id = #{id} ")
    boolean updateExternalConfigById(@Param("id") String id, @Param("externalConfig") ExternalConfig externalConfig);

    @Select("SELECT * FROM marketing_activity_external_config WHERE marketing_activity_id = #{marketingActivityId}")
    MarketingActivityExternalConfigEntity getByMarketingActivityId(@Param("marketingActivityId") String marketingActivityId);
    
    @Select("SELECT * FROM marketing_activity_external_config WHERE ea=#{ea} AND marketing_activity_id = #{marketingActivityId}")
    MarketingActivityExternalConfigEntity getByEaAndMarketingActivityId(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId);

    @Select("SELECT * FROM marketing_activity_external_config WHERE marketing_activity_id = #{marketingActivityId} AND associate_id_type = #{associateIdType}")
    MarketingActivityExternalConfigEntity getByMarketingActivityIdAndAssociateIdType(@Param("marketingActivityId") String marketingActivityId, @Param("associateIdType") Integer associateIdType);

    @Select("SELECT id, marketing_activity_id, external_config, associate_id_type, associate_id,ea ,marketing_activity_type FROM marketing_activity_external_config WHERE id = #{id}")
    MarketingActivityExternalConfigEntity getById(@Param("id") String id);

    @FilterLog
    @Select("SELECT * FROM marketing_activity_external_config WHERE ea=#{ea} AND associate_id_type=#{associateIdType} AND associate_id = #{associateId}")
    MarketingActivityExternalConfigEntity getByEaAndAssociateMsg(@Param("ea")String ea, @Param("associateIdType") Integer associateIdType, @Param("associateId") String associateId);

    @Select("SELECT * FROM marketing_activity_external_config WHERE ea=#{ea} AND associate_id_type=#{associateIdType} AND marketing_event_id = #{marketingEventId}")
    MarketingActivityExternalConfigEntity getByEaAndAssociateMsgAndEventId(@Param("ea")String ea, @Param("associateIdType") Integer associateIdType, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM marketing_activity_external_config WHERE ea=#{ea} AND associate_id_type=#{associateIdType} AND marketing_event_id is null")
    MarketingActivityExternalConfigEntity getByEaAndAssociateType(@Param("ea")String ea, @Param("associateIdType") Integer associateIdType);


    @Select("SELECT * FROM marketing_activity_external_config WHERE associate_id = #{associateId}")
    List<MarketingActivityExternalConfigEntity> getByAssociateId(@Param("associateId") String associateId);

    @Select("SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId} LIMIT #{limit} OFFSET #{offset}")
    List<String> listMarketingActivityIdsByEaAndMarketingEventId(@Param("ea") String ea,@Param("marketingEventId") String marketingEventId,@Param("offset")Integer offset ,@Param("limit")Integer limit);

    @Select("<script>"
            + "SELECT marketing_activity_id, marketing_event_id FROM marketing_activity_external_config WHERE ea = #{ea} AND marketing_event_id in"
            + "<foreach collection = 'marketingEventIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +      "#{item}"
            + "</foreach>"
            + " LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<MarketingEventActivityDTO> batchListMarketingActivityIdsByEaAndMarketingEventId(@Param("ea") String ea, @Param("marketingEventIds") List<String> marketingEventIds, @Param("offset")Integer offset , @Param("limit")Integer limit);

    @Select("SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId}")
    List<String> listMarketingActivityIdsByEaAndMarketingEvent(@Param("ea") String ea,@Param("marketingEventId") String marketingEventId);

    @Select("SELECT COALESCE(count(*), 0) FROM marketing_activity_external_config WHERE ea=#{ea} AND marketing_event_id = #{marketingEventId} AND marketing_activity_type = 1")
    Integer countMarketingEventEnterpriseSpreadCount(@Param("ea") String ea,@Param("marketingEventId") String marketingEventId);

    @Select("<script>"
        + "SELECT id, marketing_activity_id, external_config, associate_id_type, associate_id ,marketing_activity_type\n"
        + "FROM marketing_activity_external_config\n"
        + "WHERE ea=#{ea} and associate_id = any(array<foreach collection = 'associateIds' item = 'id' open = '[' separator = ',' close = ']'> #{id} </foreach> ) "
        + "</script>")
    List<MarketingActivityExternalConfigEntity> getByAssociateIds(@Param("ea")String ea, @Param("associateIds") List<String> associateIds);

    @Update("DELETE FROM marketing_activity_external_config WHERE id = #{id}")
    void deleteById(@Param("id") String id);

    @Select("<script>" + "SELECT * FROM marketing_activity_external_config WHERE marketing_activity_id IN " + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}" + "</foreach>" + "</script>")
    List<MarketingActivityExternalConfigEntity> getByMarketingActivityIds(@Param("ids") List<String> ids);

    @Select("<script>" + "select count(n.*) from marketing_activity_external_config m join notice n on m.associate_id = n.id WHERE m.marketing_activity_id IN " + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}" + "</foreach>" + " and n.status!=11" + "</script>")
    Integer countByMarketingActivityIdsExcuCancle(@Param("ids") List<String> ids);

    @Select("SELECT * FROM marketing_activity_external_config WHERE (external_config::json->'weChatServiceMarketingActivityVO'->>'appId' = #{appId} OR external_config::json->'weChatTemplateMessageVO'->>'appId' = #{appId}) ORDER BY create_time desc LIMIT #{limit} OFFSET #{offset}")
    List<MarketingActivityExternalConfigEntity> listByAppId(@Param("appId") String appId, @Param("limit") Integer limit, @Param("offset") Integer offset);

    @Select("SELECT count(1) FROM marketing_activity_external_config WHERE external_config::json->'weChatServiceMarketingActivityVO'->>'appId' = #{wxAppId} OR external_config::json->'weChatTemplateMessageVO'->>'appId' = #{wxAppId}")
    Integer countByWxAppId(@Param("wxAppId") String wxAppId);

    @Select("SELECT count(1) FROM marketing_activity_external_config WHERE ea = #{ea} AND ((external_config->'marketingActivityGroupSenderVO'->>'marketingUserGroupIds')::jsonb ??| array[#{userGroupId}] OR (external_config->'weChatTemplateMessageVO'->>'marketingUserGroupIds')::jsonb ??| array[#{userGroupId}])")
    Integer countByUserGroupId(@Param("ea") String ea, @Param("userGroupId") String userGroupId);

    // 根据进来的list，返回同样位置的list
    @Select("<script>" + "SELECT * FROM marketing_activity_external_config WHERE marketing_activity_id = "
        + "any(array<foreach collection = 'ids' item = 'item' open = '[' separator = ',' close = ']'> #{item} </foreach> ) " + "order by array_positions(ARRAY"
        + "<foreach collection = 'ids' item = 'item' open = '[' separator = ',' close = ']'> #{item} </foreach>" + " , marketing_activity_id )" + "</script>")
    List<MarketingActivityExternalConfigEntity> getOriginByMarketingActivityIds(@Param("ids") List<String> ids);

    @Select("SELECT * FROM marketing_activity_external_config  WHERE ea=#{ea} and   associate_id = #{associateId} and associate_id_type=#{associateIdType} AND marketing_event_id IS NULL")
    MarketingActivityExternalConfigEntity getMarketingActivityExternalConfigEntityWithOutMarketingEventId(@Param("ea") String ea, @Param("associateId") String associateId, @Param("associateIdType") Integer associateIdType);

    @Select("SELECT * FROM marketing_activity_external_config  WHERE ea=#{ea} and   associate_id = #{associateId} and associate_id_type=#{associateIdType}")
    MarketingActivityExternalConfigEntity getMarketingActivityExternalConfigEntity(@Param("ea") String ea, @Param("associateId") String associateId, @Param("associateIdType") Integer associateIdType);

    @Select("SELECT * FROM marketing_activity_external_config  WHERE ea=#{ea} and   associate_id = #{associateId} and associate_id_type=#{associateIdType} AND marketing_event_id = #{marketingEventId}")
    MarketingActivityExternalConfigEntity getMarketingActivityExternalConfigEntityWithMarketingEventId(@Param("ea") String ea, @Param("associateId") String associateId, @Param("associateIdType") Integer associateIdType, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM marketing_activity_external_config " + "where ea = #{ea} and marketing_activity_type = #{type}  " + "and associate_id != '' " + " order by create_time desc")
    List<MarketingActivityExternalConfigEntity> listSuccessFulActivityExternalByEaAndType(@Param("ea") String ea, @Param("type") Integer type, @Param("page") Page page);

    @Select("<script>" + "SELECT * FROM marketing_activity_external_config WHERE associate_id_type=#{associateIdType} AND associate_id IN "
        + "<foreach collection = 'associateIds' item = 'item' open = '(' separator = ',' close = ')'>" + "#{item}" + "</foreach>" + "</script>")
    List<MarketingActivityExternalConfigEntity> listByAssociateIdTypeAndIds(@Param("associateIdType") int associateIdType, @Param("associateIds") List<String> associateIds);

    @Select("<script> SELECT COALESCE(count(1),0)  FROM   marketing_activity_external_config  where  marketing_activity_type =1  and   create_time  between #{startDate} and  #{endDate}   </script>")
    Integer getEnterpriseEmployeeLeadsWeeklyStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("SELECT marketing_event_id FROM marketing_activity_external_config WHERE marketing_activity_id = #{marketingActivityId}")
    String getMarketingEventIdByMarketingActivityId(@Param("marketingActivityId") String marketingActivityId);

    @Select("<script>"
            + "SELECT marketing_activity_id "
            + "FROM marketing_activity_external_config "
            + "WHERE ea=#{ea} AND marketing_activity_id IN "
            + "<foreach collection = 'marketingActivityIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<String> listMarketingActivityIdByMarketingActivityIds(@Param("ea")String ea, @Param("marketingActivityIds")Collection<String> marketingActivityIds);


    @Select(" SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND associate_id_type = #{associateIdType}")
    List<String> getActivityIdByEaAndAssociateIdType(@Param("ea")String ea, @Param("associateIdType") Integer associateIdType);

    @Select("<script>" +
            "SELECT maec.marketing_activity_id as id, mss.to_sender_count as count FROM marketing_activity_external_config maec join mw_sms_send mss on maec.associate_id = mss.id WHERE maec.ea = #{ea} AND maec.marketing_activity_id IN " +
            "<foreach collection='marketingActivityIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</script>")
    List<DataCount> groupCountSmsTotalSend(@Param("ea") String ea, @Param("marketingActivityIds")Collection<String> marketingActivityIds);

    @Select("<script>" +
            "SELECT maec.marketing_activity_id as id, mst.total_send_count as count FROM marketing_activity_external_config maec join mail_send_task mst on maec.associate_id = mst.id WHERE maec.ea = #{ea} AND maec.marketing_activity_id IN " +
            "<foreach collection='marketingActivityIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</script>")
    List<DataCount> groupCountEmailTotalSend(@Param("ea") String ea, @Param("marketingActivityIds")Collection<String> marketingActivityIds);

    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime} AND associate_id_type = 15" +
            "<if test='marketingEventId != null'> AND marketing_event_id=#{marketingEventId}</if>" +
            "<if test='objectId != null'> AND external_config -> 'marketingActivityNoticeSendVO' ->> 'content'=#{objectId}</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsInDateRange(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId, @Param("objectId") String objectId);

    @Select("SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND marketing_event_id=#{marketingEventId}")
    List<String> listAllEmployeeActivityIdsByMarketingEventId(@Param("ea")String ea, @Param("marketingEventId")String marketingEventId);


    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime} AND associate_id_type = 15" +
            "<if test='marketingEventId != null'> AND marketing_event_id=#{marketingEventId}</if>" +
            "<if test='objectIds != null'> AND external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN" +
                 "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsInDateRangeWithObjectIds(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);

    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime} AND associate_id_type = #{associateIdType}" +
            "<if test='marketingEventId != null'> AND marketing_event_id=#{marketingEventId}</if>" +
            "<if test='objectIds != null'> AND external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listEmployeeActivityIdsInDateRangeWithObjectIdsAndType(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds, @Param("associateIdType") int associateIdType);


    @Select("<script>" +
            "select marketing_activity_id from marketing_activity_external_config m join notice n on m.associate_id = n.id WHERE m.ea = #{ea} AND n.start_time &gt;= #{startTime} AND n.start_time &lt;= #{endTime} AND m.associate_id_type = #{associateIdType}" +
            "<if test='marketingEventId != null'> AND m.marketing_event_id=#{marketingEventId}</if>" +
            "<if test='objectIds != null'> AND m.external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTime(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds, @Param("associateIdType") int associateIdType);

    @Select("<script>" +
            "select marketing_activity_id from marketing_activity_external_config m join notice n on m.associate_id = n.id WHERE m.ea = #{ea} AND n.start_time &gt;= #{startTime} AND n.start_time &lt;= #{endTime} AND m.associate_id_type = #{associateIdType}" +
            "<if test='marketingEventIds != null and marketingEventIds.size() > 0' > AND m.marketing_event_id IN" +
            "<foreach collection='marketingEventIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "<if test='objectIds != null'> AND m.external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTimeV2(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime,  @Param("marketingEventIds") List<String> marketingEventIds, @Param("objectIds") List<String> objectIds, @Param("associateIdType") int associateIdType);


    @Select("<script>" +
            "select marketing_activity_id from marketing_activity_external_config m join notice n on m.associate_id = n.id WHERE m.ea = #{ea} AND m.associate_id_type = #{associateIdType}" +
            "<if test='marketingEventId != null'> AND m.marketing_event_id=#{marketingEventId}</if>" +
            "<if test='objectIds != null'> AND m.external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsInDateRangeWithObjectIdsWithouTime(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds, @Param("associateIdType") int associateIdType);

    @Select("<script>" +
            "select marketing_activity_id from marketing_activity_external_config m join notice n on m.associate_id = n.id WHERE m.ea = #{ea} AND m.associate_id_type = #{associateIdType}" +
            "<if test='marketingEventIds != null and marketingEventIds.size() > 0' > AND m.marketing_event_id IN" +
            "<foreach collection='marketingEventIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsInDateRangeWithObjectIdsWithouTimeV2(@Param("ea") String ea, @Param("marketingEventIds") List<String> marketingEventIds, @Param("associateIdType") int associateIdType);


    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime} AND associate_id_type = 16" +
            "<if test='marketingEventId != null'> AND marketing_event_id=#{marketingEventId}</if>" +
            "<if test='objectIds != null'> AND external_config -> 'marketingActivityPartnerNoticeSendData' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listPartnerEmployeeActivityIdsInDateRangeWithObjectIds(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId, @Param("objectIds") List<String> objectIds);

    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime} AND associate_id_type = 16" +
            "<if test='marketingEventIds != null and marketingEventIds.size() > 0'> AND marketing_event_id IN" +
            "<foreach collection='marketingEventIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "<if test='objectIds != null'> AND external_config -> 'marketingActivityPartnerNoticeSendData' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listPartnerEmployeeActivityIdsInDateRangeWithObjectIdsV2(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime,  @Param("marketingEventIds") List<String> marketingEventIds, @Param("objectIds") List<String> objectIds);

    @Select("<script>" +
            "select marketing_activity_id from marketing_activity_external_config m join notice n on m.associate_id = n.id WHERE m.ea = #{ea} AND n.start_time &gt;= #{startTime} AND n.start_time &lt;= #{endTime} AND m.associate_id_type = 16" +
            "<if test='marketingEventIds != null and marketingEventIds.size() > 0'> AND marketing_event_id IN" +
            "<foreach collection='marketingEventIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "<if test='objectIds != null'> AND m.external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</if>" +
            "</script>")
    List<String> listPartnerEmployeeActivityIdsInDateRangeWithObjectIdsBySendStartTime(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventIds") List<String> marketingEventIds, @Param("objectIds") List<String> objectIds);


    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND associate_id_type = 15" +
            "<if test='objectId != null'> AND external_config -> 'marketingActivityNoticeSendVO' ->> 'content'=#{objectId}</if>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsByObjectId(@Param("ea") String ea, @Param("objectId") String objectId);

    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea} AND associate_id_type = 15\n" +
            "AND external_config -> 'marketingActivityNoticeSendVO' ->> 'content' IN\n" +
            "<foreach collection='objectIds' item='item' open='(' close=')' separator=','>#{item}</foreach>" +
            "</script>")
    List<String> listAllEmployeeActivityIdsByObjectIds(@Param("ea") String ea, @Param("objectIds") List<String> objectIds);

    @Select("<script>" +
            "SELECT marketing_activity_id FROM marketing_activity_external_config WHERE ea = #{ea}" +
            "<if test='objectId != null'> AND external_config -> 'marketingActivityNoticeSendVO' ->> 'content'=#{objectId}</if>" +
            "</script>")
    List<String> listAllMarketingActivityIdsByObjectId(@Param("ea")String ea, @Param("objectId") String objectId);

    @Update("UPDATE marketing_activity_external_config SET external_config=#{obj.externalConfig} WHERE ea=#{ea} AND id=#{obj.id}")
    int updateExternalConfig(@Param("obj")MarketingActivityExternalConfigEntity obj, @Param("ea")String ea);
    
    @Select("select marketing_activity_id from marketing_activity_external_config where ea=#{ea} and (\n" +
        "array(select jsonb_array_elements_text(external_config -> 'weChatServiceMarketingActivityVO' -> 'marketingUserGroupIds')) @> array[#{marketingUserGroupId}::text] or \n" +
        "array(select jsonb_array_elements_text(external_config -> 'marketingActivityGroupSenderVO' -> 'marketingUserGroupIds')) @> array[#{marketingUserGroupId}::text] or \n" +
        "array(select jsonb_array_elements_text(external_config -> 'weChatTemplateMessageVO' -> 'marketingUserGroupIds')) @> array[#{marketingUserGroupId}::text] or \n" +
        "array(select jsonb_array_elements_text(external_config -> 'qywxGroupSendMessageVO' -> 'marketingUserGroupIds')) @> array[#{marketingUserGroupId}::text] or \n" +
        "array(select jsonb_array_elements_text(external_config -> 'mailServiceMarketingActivityVO' -> 'marketingUserGroupIds')) @> array[#{marketingUserGroupId}::text]" +
        ") order by create_time desc")
    List<String> listMarketingActivityIdsByMarketingUserGroupId(@Param("ea") String ea, @Param("marketingUserGroupId") String marketingUserGroupId, Page page);

    @Select("<script>"
            + "SELECT marketing_event_id FROM marketing_activity_external_config WHERE ea=#{ea} AND associate_id_type=15 AND marketing_event_id IN\n"
            + "<foreach collection='marketingEventIds' item='item' open='(' close=')' separator=','>#{item}</foreach>"
            + "</script>")
    List<String> listALLSpreadMarketingEventIds(@Param("ea")String ea, @Param("marketingEventIds")List<String> marketingEventIds);

    @Select(  " SELECT * FROM marketing_activity_external_config WHERE associate_id_type in ( 1001,1002) and ea = #{ea} " )
    List<MarketingActivityExternalConfigEntity> getWeChatSpreadActivityIdConfig( @Param("ea") String ea );

    @Select("SELECT * FROM marketing_activity_external_config WHERE ea = #{ea} and external_config::json->'weChatTemplateMessageVO'->>'weChatOfficialTemplateId' = #{weChatOfficialTemplateId} ORDER BY create_time DESC LIMIT 1")
    MarketingActivityExternalConfigEntity getNearlyExternalConfigByEaAndWeChatOfficialTemplateId(@Param("ea") String ea, @Param("weChatOfficialTemplateId") String weChatOfficialTemplateId);

    @Select("<script>"
            + "SELECT DISTINCT(ea) FROM marketing_activity_external_config WHERE associate_id_type IN\n"
            + "<foreach collection='associateIdType' item='item' open='(' close=')' separator=','>#{item}</foreach>\n"
            + "<if test='startDate != null and endDate != null'>AND create_time between #{startDate} and #{endDate}</if>\n"
            + "</script>")
    List<String> getMarketingActivitySpreadEnterpriseEaByAssociateIdType(@Param("associateIdType") List<Integer> associateIdType, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
    @Select("SELECT * FROM marketing_activity_external_config WHERE associate_id_type in (1001, 1002) and is_need_audit = 'true' AND create_time > #{startTime} AND create_time <= #{endTime}")
    List<MarketingActivityExternalConfigEntity> getWechatServiceNoticeTask(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>" +
            "SELECT count(*) FROM marketing_activity_external_config WHERE ea = #{ea}  AND associate_id_type in" +
            "<foreach collection = 'associateIdTypes' item = 'item' open = '(' separator = ',' close = ')'>" +
            "#{item}"+
            "</foreach>"+
            "<if test=\"startTime != null\">"+
            "AND create_time &gt;= #{startTime}\n"+
            "</if>"+
            "<if test=\"endTime != null\">"+
            "AND create_time &lt;= #{endTime}\n"+
            "</if>"+
            "</script>")
    int countMarketingActivityCountByTime(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("associateIdTypes") List<Integer> associateIdTypes);

    @Select("<script>"
            + "SELECT * FROM marketing_activity_external_config WHERE ea = #{ea} and marketing_activity_id = #{marketingActivityId}  and  associate_id_type IN\n"
            + "<foreach collection='associateIdTypeList' item='item' open='(' close=')' separator=','>#{item}</foreach>\n"
            + "</script>")
    MarketingActivityExternalConfigEntity getByMarketingActivityIdAndAssociateIdTypeList(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId, @Param("associateIdTypeList") List<Integer> associateIdTypeList);

    @Select("<script>"
            + "SELECT * FROM marketing_activity_external_config "
            + "WHERE ea = #{ea} "
            + "AND associate_id = ANY ("
            + "<foreach collection='objectIds' item='id' open='ARRAY[' separator=',' close=']'>"
            + "#{id}"
            + "</foreach>) "
            + "AND associate_id_type = ANY ("
            + "<foreach collection='objectTypes' item='objectType' open='ARRAY[' separator=',' close=']'>"
            + "#{objectType}"
            + "</foreach>)"
            + "</script>")
    List<MarketingActivityExternalConfigEntity> listByAssociateIdAndType( @Param("ea") String ea,
                                                                               @Param("objectIds") List<String> objectIds,
                                                                               @Param("objectTypes") List<Integer> objectTypes);

}
