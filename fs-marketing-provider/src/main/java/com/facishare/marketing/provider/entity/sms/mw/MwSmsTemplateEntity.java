package com.facishare.marketing.provider.entity.sms.mw;

import com.facishare.marketing.common.enums.sms.mw.SmsTemplateSourceEnum;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import com.facishare.marketing.common.typehandlers.value.SmsContentParamList;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/2/18.
 */
@Data
@Entity
public class MwSmsTemplateEntity implements Serializable {
    private String id;
    private String ea;
    private Integer type;
    private String name;
    private String content;
    private Integer seqNum;
    private String remark;
    private String reply;
    private Integer status;
    private Integer creatorUserId;
    private String creatorName;
    private Date createTime;
    private Date updateTime;
    private Integer sceneType;
    private FieldValueList paramDetail;
    private Integer channelType;
    private String tplid;    //模板审核时，梦网返回的模板id
    private String sendAccountId; // 发送账号id
    private String parentId;   // 父模板id
    private Integer tplType; // 模板类型 1业务通知短信 2营销短信
    private String relationApiName; // 关联对象
    private SmsContentParamList smsContentParam; // 短信内容参数
    private Integer tplVersion = 0; // 模板版本 0是历史旧模板 1是营销通新模板
    private Integer source = SmsTemplateSourceEnum.AUTO.getType(); // 模板来源
    private String templateObjId;

}
