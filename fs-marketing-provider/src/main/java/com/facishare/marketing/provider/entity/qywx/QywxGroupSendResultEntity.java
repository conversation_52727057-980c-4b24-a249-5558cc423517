package com.facishare.marketing.provider.entity.qywx;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 15:59 2020/2/17
 * @ModifyBy
 */
@Data
public class QywxGroupSendResultEntity implements Serializable {
    private String id;
    private String ea;
    private String msgid; //群发消息id
    private String externalUserid; //外部联系人userid
    private String userid;//企业服务人员的userid
    private Integer status;//发送状态 0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败
    private Integer sendTime;//发送时间，发送状态为1时返回
    private Date createTime;
    private Date updateTime;
    private String sendid;
}
