package com.facishare.marketing.provider.entity.sms;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import lombok.Data;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/25 14:40
 * @描述
 */
@Data
@Entity
public class SMSSendDetailEntity implements Serializable {

    private String id;
    //群发短信的表id
    private String smsSendId;
    //被发送人的手机号码
    private String phone;
    // 响应状态   -1 未发送
    private Integer status;
    // 腾讯返回的sid
    private String sid;
    private Integer fee; // 自己计算的单条消费
    private Integer feeTx; // 腾讯计算的单条消费
    //错误信息
    private List<String> params;
    private String errorMsg;
    private Date createTime;
    private Date updateTime;

    private String reportStatus; //腾讯回调过来的短信下发状态

    private String reportErrMsg; //腾讯回调过来的短信错误信息码

    private String reportDescription; //腾讯回调过来的短信发送结果描述

    private String resultCode; // 腾讯返回的错误码，用于前端展示错误信息
}
