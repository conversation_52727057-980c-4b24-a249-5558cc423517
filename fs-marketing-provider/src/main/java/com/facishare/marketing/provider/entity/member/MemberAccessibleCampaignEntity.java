package com.facishare.marketing.provider.entity.member;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/12/29
 **/
@Data
public class MemberAccessibleCampaignEntity implements Serializable {

    private String id;

    private String ea;

    private String uid;

    private String openId;

    private String wxAppId;

    private String fingerPrint;

    private String marketingEventId;

    private String marketingActivityId;

    private String objectId;

    private Integer objectType;

    private Integer bindCrmObjectType;

    private String bindCrmObjectId;

    private String campaignId;

    private String memberId;

    private Integer spreadFsUid;

    private Integer saveCrmStatus;

    private String saveCrmErrorMessage;

    private String channelValue;

    private Date createTime;

    private Date updateTime;

    public boolean openIdNotNull() {
        return StringUtils.isNotEmpty(openId);
    }

}
