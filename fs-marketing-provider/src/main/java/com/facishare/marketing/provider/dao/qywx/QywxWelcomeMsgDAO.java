package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.dao.param.qywx.QywxWelcomeMsgQueryParam;
import com.facishare.marketing.provider.dto.qywx.QywxWelcomeMsgDTO;
import com.facishare.marketing.provider.entity.qywx.QywxWelcomeMsgEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface QywxWelcomeMsgDAO {

    @Insert("INSERT INTO qywx_welcome_msg ("
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"welcome_msg_name\",\n"
            + "        \"type\",\n"
            + "        \"user_id_list\",\n"
            + "        \"department_id\",\n"
            + "        \"tag_id\",\n"
            + "        \"user_id_merge\",\n"
            + "        \"welcome_msg_content\",\n"
            + "        \"content_type\",\n"
            + "        \"link_type\",\n"
            + "        \"link_content_url\",\n"
            + "        \"link_title\",\n"
            + "        \"link_cover\",\n"
            + "        \"link_cover_path\",\n"
            + "        \"image_url\",\n"
            + "        \"image_title\",\n"
            + "        \"video_url\",\n"
            + "        \"video_title\",\n"
            + "        \"card_url\",\n"
            + "        \"card_pic_path\",\n"
            + "        \"card_page\",\n"
            + "        \"card_title\",\n"
            + "        \"mini_program_type\",\n"
            + "        \"mini_title\",\n"
            + "        \"mini_page\",\n"
            + "        \"mini_pic_path\",\n"
            + "        \"mini_pic_url\",\n"
            + "        \"mini_app_id\",\n"
            + "        \"status\",\n"
            + "        \"create_time\",\n"
            + "        \"update_time\",\n"
            + "        \"operator\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.ea},\n"
            + "        #{obj.welcomeMsgName},\n"
            + "        #{obj.type},\n"
            + "        #{obj.userIdList},\n"
            + "        #{obj.departmentId},\n"
            + "        #{obj.tagId},\n"
            + "        #{obj.userIdMerge},\n"
            + "        #{obj.welcomeMsgContent},\n"
            + "        #{obj.contentType},\n"
            + "        #{obj.linkType},\n"
            + "        #{obj.linkContentUrl},\n"
            + "        #{obj.linkTitle},\n"
            + "        #{obj.linkCover},\n"
            + "        #{obj.linkCoverPath},\n"
            + "        #{obj.imageUrl},\n"
            + "        #{obj.imageTitle},\n"
            + "        #{obj.videoUrl},\n"
            + "        #{obj.videoTitle},\n"
            + "        #{obj.cardUrl},\n"
            + "        #{obj.cardPicPath},\n"
            + "        #{obj.cardPage},\n"
            + "        #{obj.cardTitle},\n"
            + "        #{obj.miniProgramType},\n"
            + "        #{obj.miniTitle},\n"
            + "        #{obj.miniPage},\n"
            + "        #{obj.miniPicPath},\n"
            + "        #{obj.miniPicUrl},\n"
            + "        #{obj.miniAppId},\n"
            + "        #{obj.status},\n"
            + "        now(),\n"
            + "        now(),\n"
            + "        #{obj.operator}\n"
            + "        ) ON CONFLICT DO NOTHING;")
    int saveWelcomeMsg(@Param("obj") QywxWelcomeMsgEntity qywxWelcomeMsgEntity);

    @Select("select * from qywx_welcome_msg where ea = #{ea} and status = 1 and (user_id_list like '%-999999%' or user_id_list like CONCAT('%', #{userId}, '%')) order by update_time desc")
    List<QywxWelcomeMsgEntity> queryMsgList(@Param("ea") String ea,@Param("userId") String userId);

    @Update("<script>" +
            " UPDATE qywx_welcome_msg\n" +
            "        <set>\n" +
            "            <if test=\"welcomeMsgName != null\">\n" +
            "                \"welcome_msg_name\" = #{welcomeMsgName},\n" +
            "            </if>\n" +
            "            <if test=\"welcomeMsgContent != null\">\n" +
            "                \"welcome_msg_content\" = #{welcomeMsgContent},\n" +
            "            </if>\n" +
            "            <if test=\"contentType != null\">\n" +
            "                \"content_type\" = #{contentType},\n" +
            "            </if>\n" +
            "            <if test='linkType != null'>\n" +
            "                \"link_type\" = #{linkType},\n" +
            "            </if>\n" +
            "            <if test=\"linkContentUrl != null\">\n" +
            "                \"link_content_url\" = #{linkContentUrl},\n" +
            "            </if>\n" +
            "            <if test=\"linkTitle != null\">\n" +
            "                \"link_title\" = #{linkTitle},\n" +
            "            </if>\n" +
            "            <if test=\"linkCover != null\">\n" +
            "                \"link_cover\" = #{linkCover},\n" +
            "            </if>\n" +
            "            <if test=\"linkCoverPath != null\">\n" +
            "                \"link_cover_path\" = #{linkCoverPath},\n" +
            "            </if>\n" +
            "            <if test=\"imageUrl != null\">\n" +
            "                \"image_url\" = #{imageUrl},\n" +
            "            </if>\n" +
            "            <if test=\"imageTitle != null\">\n" +
            "                \"image_title\" = #{imageTitle},\n" +
            "            </if>\n" +
            "            <if test=\"videoUrl != null\">\n" +
            "                \"video_url\" = #{videoUrl},\n" +
            "            </if>\n" +
            "            <if test=\"videoTitle != null\">\n" +
            "                \"video_title\" = #{videoTitle},\n" +
            "            </if>\n" +
            "            <if test=\"cardUrl != null\">\n" +
            "                \"card_url\" = #{cardUrl},\n" +
            "            </if>\n" +
            "            <if test=\"cardPicPath != null\">\n" +
            "                \"card_pic_path\" = #{cardPicPath},\n" +
            "            </if>\n" +
            "            <if test=\"cardPage != null\">\n" +
            "                \"card_page\" = #{cardPage},\n" +
            "            </if>\n" +
            "            <if test=\"cardTitle != null\">\n" +
            "                \"card_title\" = #{cardTitle},\n" +
            "            </if>\n" +
            "            <if test=\"miniProgramType != null\">\n" +
            "                \"mini_program_type\" = #{miniProgramType},\n" +
            "            </if>\n" +
            "            <if test=\"miniTitle != null\">\n" +
            "                \"mini_title\" = #{miniTitle},\n" +
            "            </if>\n" +
            "            <if test=\"miniPage != null\">\n" +
            "                \"mini_page\" = #{miniPage},\n" +
            "            </if>\n" +
            "            <if test=\"miniPicPath != null\">\n" +
            "                \"mini_pic_path\" = #{miniPicPath},\n" +
            "            </if>\n" +
            "            <if test=\"miniPicUrl != null\">\n" +
            "                \"mini_pic_url\" = #{miniPicUrl},\n" +
            "            </if>\n" +
            "            <if test=\"miniAppId != null\">\n" +
            "                \"mini_app_id\" = #{miniAppId},\n" +
            "            </if>\n" +
            "            <if test=\"status != null\">\n" +
            "                \"status\" = #{status},\n" +
            "            </if>\n" +
            "                \"tag_id\" = #{tagId},\n" +
            "                \"user_id_list\" = #{userIdList},\n" +
            "                \"department_id\" = #{departmentId},\n" +
            "                \"user_id_merge\" = #{userIdMerge},\n" +
            "                \"update_time\" = now() \n" +
            "        </set>\n" +
            "        WHERE id = #{id}"
            + "</script>")
    int updateQywxWelcomeMsgById(QywxWelcomeMsgEntity qywxWelcomeMsgEntity);

    @Update("update qywx_welcome_msg set status = 0 where id = #{id}")
    int deleteWelcomeMsg(@Param("id") String id);

    @Select("select * from qywx_welcome_msg where ea = #{ea} and status = 1 order by update_time desc")
    List<QywxWelcomeMsgEntity> queryMsgListByPage(@Param("ea") String ea,@Param("page") Page page);

    @Select("select * from qywx_welcome_msg where id = #{id}")
    QywxWelcomeMsgEntity queryWelcomeMsgDetail(@Param("id") String id);

    @Select("select * from qywx_welcome_msg where ea = #{ea} and status = 1  and (user_id_merge like '%-999999%' or user_id_merge like CONCAT('%', #{userId}, '%')) order by update_time desc")
    List<QywxWelcomeMsgEntity> queryMsgListDepartment(@Param("ea") String ea, @Param("userId") String userId);

    @Select("select * from qywx_welcome_msg where ea = #{ea} and status = 1 order by update_time desc")
    List<QywxWelcomeMsgEntity> queryListByEa(@Param("ea") String ea);

    @Select(
            "<script>"
                    + "select qywx_welcome_msg.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_welcome_msg\n"
                    + "left join object_group_relation on qywx_welcome_msg.id = object_group_relation.object_id and qywx_welcome_msg.ea = object_group_relation.ea\n"
                    + "left join object_top on qywx_welcome_msg.id = object_top.object_id and object_top.ea = qywx_welcome_msg.ea\n"
                    + "where qywx_welcome_msg.ea = #{queryParam.ea} and qywx_welcome_msg.status = 1 \n"
                    + "<if test=\"queryParam.groupIdList != null and queryParam.groupIdList.size != 0\">"
                    + " and object_group_relation.group_id in "
                    + "<foreach collection = 'queryParam.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    + "#{queryParam.groupIdList[${index}]}"
                    + "</foreach>"
                    + "</if>"
                    + " and ( "
                    + "qywx_welcome_msg.operator = #{queryParam.userId} \n"
                    + "or object_group_relation.group_id is null"
                    + "<if test=\"queryParam.permissionGroupIdList != null and queryParam.permissionGroupIdList.size != 0\">"
                    + "or object_group_relation.group_id in "
                    +     "<foreach collection = 'queryParam.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    +     "#{queryParam.permissionGroupIdList[${index}]}"
                    +     "</foreach>"
                    + "</if>"
                    + ")"
                    + "order by object_top.create_time desc nulls last, qywx_welcome_msg.update_time desc"
                    + "</script>"
    )
    List<QywxWelcomeMsgDTO> getAccessiblePage(@Param("queryParam") QywxWelcomeMsgQueryParam queryParam, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qywx_welcome_msg.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_welcome_msg\n"
                    + "left join object_top on qywx_welcome_msg.id = object_top.object_id and object_top.ea = qywx_welcome_msg.ea\n"
                    + "where qywx_welcome_msg.ea = #{queryParam.ea} and qywx_welcome_msg.status = 1 \n"
                    + " AND qywx_welcome_msg.operator = #{queryParam.userId}"
                    + "order by object_top.create_time desc nulls last, qywx_welcome_msg.update_time desc"
                    + "</script>"
    )
    List<QywxWelcomeMsgDTO> getCreateByMePage(@Param("queryParam") QywxWelcomeMsgQueryParam queryParam, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qywx_welcome_msg.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_welcome_msg\n"
                    + "left join object_group_relation on qywx_welcome_msg.id = object_group_relation.object_id and qywx_welcome_msg.ea = object_group_relation.ea\n"
                    + "left join object_top on qywx_welcome_msg.id = object_top.object_id and object_top.ea = qywx_welcome_msg.ea\n"
                    + "where qywx_welcome_msg.ea = #{queryParam.ea} and qywx_welcome_msg.status = 1 \n"
                    + " AND ( object_group_relation.group_id is null )"
                    + "order by object_top.create_time desc nulls last, qywx_welcome_msg.update_time desc"
                    + "</script>"
    )
    List<QywxWelcomeMsgDTO> noGroupPage(@Param("queryParam") QywxWelcomeMsgQueryParam queryParam, @Param("page") Page page);

    @Select("<script>"
            + "select count(*) from ("
            + "SELECT qywx_welcome_msg.id FROM qywx_welcome_msg WHERE ea = #{ea} AND status = 1 \n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 38)"
            + "UNION "
            + "SELECT qywx_welcome_msg.id FROM qywx_welcome_msg WHERE ea = #{ea} AND operator = #{userId}  AND status = 1 "
            + " ) ct"
            + "</script>")
    int queryUnGroupAndCreateByMeCount(@Param("ea") String ea, @Param("userId") Integer fsUserId);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT qywx_welcome_msg.id FROM qywx_welcome_msg JOIN object_group_relation on  qywx_welcome_msg.ea = object_group_relation.ea AND qywx_welcome_msg.id = object_group_relation.object_id"
            + " WHERE qywx_welcome_msg.ea = #{ea} AND qywx_welcome_msg.status = 1 \n"
            + " AND object_group_relation.group_id IN\n"
            + "<foreach collection = 'groupIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "UNION"
            + " select qywx_welcome_msg.id from qywx_welcome_msg  where ea = #{ea} and status = 1 "
            + " and id not in (select object_id from object_group_relation where ea = #{ea} and object_type = 38)"
            + "UNION "
            + "SELECT qywx_welcome_msg.id FROM qywx_welcome_msg WHERE ea = #{ea} AND operator = #{userId} AND status = 1"
            + " ) hex"
            + "</script>")
    int queryAccessibleCount(@Param("ea") String ea, @Param("groupIdList") List<String> groupIdList, @Param("userId") Integer fsUserId);

    @Select("SELECT COUNT(*) FROM qywx_welcome_msg WHERE ea = #{ea} AND operator = #{userId}  AND status = 1")
    int queryCountCreateByMe(@Param("ea") String ea, @Param("userId") Integer fsUserId);

    @Select("<script>"
            + "SELECT count(*) FROM qywx_welcome_msg WHERE ea= #{ea} AND status = 1 \n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 38)"
            + "</script>")
    int queryCountByUnGrouped(@Param("ea") String ea);

    @Select("select * from qywx_welcome_msg where id = #{id}")
    QywxWelcomeMsgEntity getById(@Param("id") String id);

    @Select("<script> " +
            "SELECT * FROM qywx_welcome_msg WHERE id IN  " +
            "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'> " +
            "#{item} " +
            "</foreach> " +
            "</script>")
    List<QywxWelcomeMsgEntity> getByIds(@Param("ids") List<String> ids);

    @Select("select id,user_id_list,user_id_merge  from qywx_welcome_msg where ea = #{ea} and (user_id_list like  CONCAT('%', #{userId}, '%') or user_id_merge like  CONCAT('%', #{userId}, '%'))")
    List<QywxWelcomeMsgEntity> getUserIdListAndUserIdMergeById(@Param("ea") String ea, @Param("userId") String userId);

    @Update("<script>"
            + "update qywx_welcome_msg set "
            + "<if test=\"userIdList != null and userIdList != ''\">"
            + " user_id_list = #{userIdList}, "
            + "</if>"
            + "<if test=\"userIdMerge != null and userIdMerge != ''\">"
            + " user_id_merge = #{userIdMerge}, "
            + "</if>"
            + " update_time=now()\n"
            + " where id = #{id} and ea = #{ea}"
            + "</script>")
    int updateUseIdListAndUserIdMerge(@Param("ea") String ea, @Param("id") String id, @Param("userIdList") String userIdList, @Param("userIdMerge") String userIdMerge);
}
