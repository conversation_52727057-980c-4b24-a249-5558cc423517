package com.facishare.marketing.provider.entity.seo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OfficialWebsiteSeoDataEntity implements Serializable {

    private String id;
    private String ea;
    private String officialWebsiteId;
    private String timeStr;
    private String siteRecord;
    private String keywordRankInfo;
    private String seoHistoryData;
    private String siteInclusionTrend;
    private String siteAntiChain;
    private Date createTime;
    private Date updateTime;

    //分数计算
    private String rating;
    //tdk+页面数据
    private String htmlContent;
    //ai生成关键词
    private String aiKeyword;
}
