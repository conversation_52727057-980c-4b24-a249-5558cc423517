package com.facishare.marketing.provider.entity.qywx;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2020/1/7.
 */
@Data
public class QywxCorpAgentConfigEntity implements Serializable {
    private String id;
    private String ea;
    private String appName;
    private String corpid;
    private String agentid;
    private String secret;
    private String customerContactSecret;
    /** 自建应用Token */
    private String selfAppToken;
    /** 自建应用AESKey */
    private String selfAppEncodingAesKey;
    private Date createTime;
    private Date updateTime;

    /**
     * 带参授权链接
     */
    private String customerAuthUrl;

    /**
     * 是否加密
     */
    private Integer isEncrypt;

    /**
     * 明文corpId
     */
    private String originCorpId;

    /**
     * 确认完成状态 1已确认 0未确认
     */
    private Integer confirmStatus;
}
