package com.facishare.marketing.provider.dao.marketingflow;

import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.provider.entity.marketingflow.MarketingActivityNodeAdditionalConfigEntity;
import com.github.mybatis.mapper.IBatchMapper;
import com.github.mybatis.mapper.ICrudMapper;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 */
public interface MarketingActivityNodeAdditionalConfigDao extends ICrudMapper<MarketingActivityNodeAdditionalConfigEntity>, IBatchMapper<MarketingActivityNodeAdditionalConfigEntity> {
    @Select("SELECT * FROM marketing_activity_node_additional_config WHERE ei=#{ei} AND workflow_id=#{workflowId}")
    List<MarketingActivityNodeAdditionalConfigEntity> listByWorkflowId(@Param("ei") Integer ei, @Param("workflowId") String workflowId);

    @Select("SELECT * FROM marketing_activity_node_additional_config WHERE ei=#{ei} AND workflow_id=#{workflowId} AND workflow_activity_node_id = #{activityId}")
    MarketingActivityNodeAdditionalConfigEntity getByActivityId(@Param("ei") Integer ei, @Param("workflowId") String workflowId, @Param("activityId") String activityId);

    @Select("SELECT * FROM marketing_activity_node_additional_config WHERE ei=#{ei}")
    List<MarketingActivityNodeAdditionalConfigEntity> listByEi(@Param("ei") Integer ei);

    @Update("UPDATE marketing_activity_node_additional_config "
        + "SET tag_name_list = #{tagNameList} "
        + "WHERE workflow_id = #{workflowId} and workflow_activity_node_id = #{workflowActivityNodeId} and ei = #{ei}")
    void updateTagNameList(@Param("tagNameList") TagNameList tagNameList, @Param("ei") Integer ei, @Param("workflowId") String workflowId,
        @Param("workflowActivityNodeId") String workflowActivityNodeId);

    @Select("SELECT distinct ei FROM marketing_activity_node_additional_config ")
    List<Integer> listEi();
}
