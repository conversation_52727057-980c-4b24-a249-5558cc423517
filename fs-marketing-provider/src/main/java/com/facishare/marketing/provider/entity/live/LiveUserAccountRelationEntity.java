package com.facishare.marketing.provider.entity.live;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class LiveUserAccountRelationEntity implements Serializable{
    private String id;
    private String ea;
    private String phone;
    private String outerUserId;
    private int type;          //第三方平台类型 LivePlatformEnum
    private Date createTime;
    private Date updateTime;
}
