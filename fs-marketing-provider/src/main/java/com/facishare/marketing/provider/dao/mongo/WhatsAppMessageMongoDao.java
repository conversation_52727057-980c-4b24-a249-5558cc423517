package com.facishare.marketing.provider.dao.mongo;

import com.facishare.marketing.api.arg.whatsapp.GetWhatsAppChatMessageArg;
import com.facishare.marketing.api.vo.whatsapp.WhatsAppChatMessageResultVO;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.entity.whatsapp.WhatsAppMessageMongoEntity;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.mongodb.WriteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mongodb.morphia.query.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;


@Component
@Slf4j
public class WhatsAppMessageMongoDao {
    @Autowired
    private DatastoreExt datastoreExt;

    public void insert(WhatsAppMessageMongoEntity entity) {
        datastoreExt.insert(entity);
    }

    public void updateWhatsAppUserByIds(String ea, Collection<String> ids, String whatsAppUserId, String fsUserId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Query<WhatsAppMessageMongoEntity> qy = datastoreExt.createQuery(WhatsAppMessageMongoEntity.class);
        qy.criteria("ea").equal(ea).criteria("messageId").in(ids);
        UpdateOperations<WhatsAppMessageMongoEntity> updateOps = datastoreExt.createUpdateOperations(WhatsAppMessageMongoEntity.class);
        updateOps.set("whatsAppUserId", whatsAppUserId).set("fsUserId", fsUserId);
        datastoreExt.update(qy, updateOps);
    }

    public void updateContent(String ea, String id, String content) {
        Query<WhatsAppMessageMongoEntity> qy = datastoreExt.createQuery(WhatsAppMessageMongoEntity.class);
        qy.criteria("ea").equal(ea).criteria("messageId").equal(id);
        UpdateOperations<WhatsAppMessageMongoEntity> updateOps = datastoreExt.createUpdateOperations(WhatsAppMessageMongoEntity.class);
        updateOps.set("content", content);
        datastoreExt.update(qy, updateOps);
    }

    public List<WhatsAppMessageMongoEntity> getByMessageIds(String ea, Collection<String> ids) {
        Query<WhatsAppMessageMongoEntity> qy = datastoreExt.createQuery(WhatsAppMessageMongoEntity.class);
        qy.criteria("ea").equal(ea).criteria("messageId").in(ids);

        return qy.asList();
    }

    public int deleteByMessageIds(String ea, Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        Query<WhatsAppMessageMongoEntity> qy = datastoreExt.createQuery(WhatsAppMessageMongoEntity.class);
        qy.criteria("ea").equal(ea).criteria("messageId").in(ids);
        WriteResult writeResult = datastoreExt.delete(qy);
        return writeResult == null ? 0 : writeResult.getN();
    }

    public WhatsAppChatMessageResultVO getByChatId(String ea, String chatId, String whatsAppUserId, GetWhatsAppChatMessageArg arg) {
        Query<WhatsAppMessageMongoEntity> qy = datastoreExt.createQuery(WhatsAppMessageMongoEntity.class);
        qy.criteria("ea").equal(ea).criteria("chatId").equal(chatId);
        if(!CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName().equals(arg.getObjectApiName())) {
            qy.criteria("whatsAppUserId").equal(whatsAppUserId);
        }
        if(StringUtils.isNotBlank(arg.getMessageType())) {
            qy.criteria("messageType").equal(arg.getMessageType());
        } else {
//            "chat、text、document、video、audio、image"
            qy.criteria("messageType").in(Lists.newArrayList("chat", "text", "document", "image", "video", "audio"));
        }
        if(arg.getStartTime() != null) {
//            qy.criteria("messageTime").greaterThanOrEq(arg.getStartTime());
            Long startTime = arg.getStartTime() / 1000;
            qy.criteria("messageTime").greaterThanOrEq(startTime);
        }
        if(arg.getEndTime() != null) {
//            qy.criteria("messageTime").lessThanOrEq(arg.getEndTime());
            Long endTime = arg.getEndTime() / 1000 + 3600 * 24L;
            qy.criteria("messageTime").lessThan(endTime);
        }
        if(StringUtils.isNotBlank(arg.getKeyWord())) {
            qy.criteria("content").containsIgnoreCase(arg.getKeyWord());
        }
        int pageSize = 20;
        int pageNum = 1;
        if(arg.getPageSize() != null) {
            pageSize = arg.getPageSize();
        }
        if(arg.getPageNum() != null) {
            pageNum = arg.getPageNum();
        }
        int offset = (pageNum - 1) * pageSize;
        qy.limit(pageSize);
        qy.offset(offset);
        qy.order("-messageTime,-rowId");

        Long totalCount = qy.countAll();

        List<WhatsAppMessageMongoEntity> objectDataList = qy.asList();
        List<ObjectData> resultDataList = Lists.newArrayList();
        objectDataList.forEach(item -> {
            Map<String, Object> mapData = convertToMap(item);
            ObjectData data = new ObjectData();
            data.putAll(mapData);
            resultDataList.add(data);
        });
        WhatsAppChatMessageResultVO resultVO = new  WhatsAppChatMessageResultVO();
        resultVO.setObjectDataList(resultDataList);
        resultVO.setTotalCount(totalCount.intValue());
        return resultVO;
    }

    private static Map<String, Object> convertToMap(Object obj) {
        Map<String, Object> map = new LinkedHashMap<>();
        if (obj == null) return map;

        Class<?> clazz = obj.getClass();
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                int modifiers = field.getModifiers();
                if (Modifier.isStatic(modifiers) || Modifier.isTransient(modifiers)) {
                    continue;
                }
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(obj));
                } catch (IllegalAccessException ignored) {
                    // Handle exception if needed
                }
            }
            clazz = clazz.getSuperclass();
        }
        return map;
    }
}


