package com.facishare.marketing.provider.entity.qywx;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/4 15:37
 */
@Data
public class QywxWelcomeMsgEntity implements Serializable {

    private String id;

    private String ea;

    private String welcomeMsgName; //欢迎语名称

    private Integer type; //欢迎语类型 1:加好友欢迎语 2:入群欢迎语

    private String userIdList; //使用员工

    private String departmentId; //部门

    private String tagId; //员工标签

    private String welcomeMsgContent; // 发送内容

    private Integer contentType; //1:h5链接 2:图片 3:视频 4:员工名片

    private Integer linkType; //1:内容链接 2:外部链接

    private String linkContentUrl; //链接内容url

    private String linkTitle; //链接标题

    private String linkCover; //封面链接url

    private String linkCoverPath; //封面下载地址

    private String imageUrl; //contentType = 2 图片地址

    private String imageTitle; //图片标题

    private String videoUrl; //contentType = 3 视频地址

    private String videoTitle; // 视频名称

    private String cardUrl; //contentType = 4 名片封面url

    private String cardPicPath; // 封面下载地址

    private String cardPage; // card page

    private String cardTitle; // 名片标题

    private Integer miniProgramType; //小程序类型

    private String miniTitle;//小程序消息标题，最多64个字节

    private String miniPicPath;//小程序消息封面的mediaid，封面图建议尺寸为520*416

    private String miniPicUrl;//小程序消息封面的url

    private String miniPage;//小程序page路径

    private String miniAppId; //小程序appId

    private Date createTime;

    private Date updateTime;

    private Integer operator;

    private Integer status; //状态 0：删除 1：正常

    private String userIdMerge; //合并部门下的员工列表合集

}
