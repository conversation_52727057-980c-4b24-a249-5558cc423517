package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.dao.param.hexagon.HexagonTemplateSiteQueryParam;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface HexagonTemplateSiteDAO {

    @Select("select * from hexagon_template_site where id=#{id} and status=1")
    HexagonTemplateSiteEntity getById(@Param("id") String id);

    @Select("SELECT * FROM hexagon_template_site WHERE id=#{id}")
    @FilterLog
    HexagonTemplateSiteEntity queryByIdIgnoreStatus(@Param("id")String id);

    @Select("select * from hexagon_template_site where ea=#{ea} and status=1")
    List<HexagonTemplateSiteEntity> getByEa(@Param("ea") String ea, @Param("page") Page page);

    @Select("<script>"
            + "select * from hexagon_template_page where hexagon_template_site_id = #{hexagonTemplateSiteId} and status != 4"
            + " ORDER BY is_homepage ASC NULLS LAST, create_time ASC"
            + "</script>")
    List<HexagonTemplatePageEntity> getByHexagonTemplateSiteId(@Param("hexagonTemplateSiteId") String hexagonTemplateSiteId);

    @Insert("insert into hexagon_template_site"
            + "(id,name,type,ea,cover_apath,category,content,status,use_count,create_by,create_time,update_time)"
            + "values"
            + "(#{obj.id},#{obj.name},2,#{obj.ea},#{obj.coverApath},#{obj.category},#{obj.content},#{obj.status},0,#{obj.createBy},now(),now())")
    int insert(@Param("obj") HexagonTemplateSiteEntity obj);


    @Update("<script>"
            + "UPDATE hexagon_template_site"
            + "    <set>"
            + "         <if test=\"name != null\">\n"
            + "             name = #{name},\n"
            + "          </if>\n"
            + "         <if test=\"content != null\">\n"
            + "             content = #{content},\n"
            + "          </if>\n"
            + "         <if test=\"status != null\">\n"
            + "             status = #{status},"
            + "         </if>\n"
            + "         <if test=\"coverApath != null\">\n"
            + "             cover_apath = #{coverApath},"
            + "         </if>\n"
            + "         update_time = now()\n"
            + "    </set>"
            + "    WHERE id = #{id}"
            + "</script>")
    int update(HexagonTemplateSiteEntity hexagonTemplateSiteEntity);

    @Update("update hexagon_template_site set status=#{status}, update_time=now() where id=#{id}")
    int updateTemplateStatus(@Param("status") Integer status, @Param("id") String id);

    @Update("update hexagon_template_site set type=#{type}, is_default=#{isDefault} where id=#{id}")
    int updateTemplateTypeAndDefault(@Param("type") Integer type, @Param("id") String id, @Param("isDefault") Integer isDefault);

    //    deleteById
    @Update("update hexagon_template_site set status = 4 where id = #{id}")
    int deleteById(@Param("id") String id);


    @Select("<script>"
            + "select * from hexagon_template_site where status!=4 "
            + "<if test=\"ea != null and ea != ''\">"
            + "and ea=#{ea}"
            + "</if>"
            + "<if test=\"category!=null and category!=''\">"
            + "and category=#{category}"
            + "</if>"
            + "<if test=\"type!=null and type!=''\">"
            + "and type=#{type}"
            + "</if>"
            +"order by update_time desc"
            + "</script>")
    List<HexagonTemplateSiteEntity> getByCategory(@Param("category") Integer category, @Param("ea") String ea, @Param("page") Page page,@Param("type") Integer type);

    @Update("update  hexagon_template_site set use_count=use_count+1 where id=#{siteId}")
    int addTemplateSiteUseCount(@Param("siteId") String siteId);

    @Select("<script>"
             + "SELECT COUNT(*) FROM hexagon_template_site WHERE ea=#{ea} AND type=2 AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
             + "</script>")
    int queryHexagonTemplateCountByAll(@Param("ea")String ea, @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT COUNT(*) FROM("
            + "SELECT id FROM hexagon_template_site WHERE ea=#{ea} AND type=2 AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + " AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea=#{ea} AND object_type=#{objectType}))t"
            + "</script>")
    int queryHexagonTemplateCountByUnGrouped(@Param("ea")String ea, @Param("userId")Integer userId, @Param("objectType")Integer objectType,
                                             @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT COUNT(*) FROM hexagon_template_site WHERE ea is null AND status NOT IN(4,5)\n"
            + "<if test=\"langType != null\">AND default_lang=#{langType}\n</if>"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "</script>")
    int queryHexagonTemplateCountBySystem(@Param("status")Integer status, @Param("keyword")String keyword, @Param("langType")Integer langType);


    @Select("<script>"
            + "SELECT COUNT(*) FROM hexagon_template_site WHERE ea=#{ea} AND type=2 AND create_by=#{userId}  AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "</script>")
    int queryHexagonTemplateCountCreateByMe(@Param("ea")String ea, @Param("userId")Integer fsUserId, @Param("status")Integer status, @Param("keyword")String keyword);

    @Select("<script>"
            + "SELECT h.* FROM hexagon_template_site h INNER JOIN object_group_relation o ON h.id=o.object_id WHERE o.ea=#{ea} AND h.type=2\n"
            + "AND o.group_id=#{groupId} AND h.status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND h.name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND h.status=#{status}\n</if>"
            + "ORDER BY h.create_time DESC\n"
            + "</script>")
    List<HexagonTemplateSiteEntity> pageQueryHexagonTemplateByGroup(@Param("ea")String ea, @Param("groupId")String groupId, @Param("keyword")String keyword,
                                                                    @Param("status")Integer status,@Param("page") Page page);

    @Select(
        "<script>"
            + " select h.* from hexagon_template_site h\n"
            + " left join object_group_relation o ON h.id=o.object_id and h.ea = o.ea WHERE h.ea=#{queryParam.ea} AND h.type=2 AND h.status NOT IN(4,5)\n"
            + " <if test=\"queryParam.status!= null\">\n"
            + "     and h.status = #{queryParam.status}\n"
            + " </if>\n"
            + " <if test=\"queryParam.keyword != null\">\n"
            + "     AND h.name LIKE CONCAT('%', #{queryParam.keyword}, '%')\n"
            + " </if>\n"
            + "<if test=\"queryParam.groupIdList != null and queryParam.groupIdList.size != 0\">"
            + " and o.group_id in "
            + "<foreach collection = 'queryParam.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{queryParam.groupIdList[${index}]}"
            + "</foreach>"
            + "</if>"
            + " and ( "
            + " h.create_by = #{queryParam.userId} \n"
            + " or o.group_id is null"
            + " <if test=\"queryParam.permissionGroupIdList != null and queryParam.permissionGroupIdList.size != 0\">"
            + " or o.group_id in "
            + "<foreach collection = 'queryParam.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{queryParam.permissionGroupIdList[${index}]}"
            + "</foreach>"
            + " </if>"
            + " )"
            + " ORDER BY h.create_time DESC " +
        "</script>"
    )
    List<HexagonTemplateSiteEntity> getAccessiblePage(@Param("queryParam") HexagonTemplateSiteQueryParam queryParam, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM("
            + " select h.* from hexagon_template_site h\n"
            + " left join object_group_relation o ON h.id=o.object_id and h.ea = o.ea WHERE h.ea=#{queryParam.ea} AND h.type=2 AND h.status NOT IN(4,5)\n"
            + " <if test=\"queryParam.status!= null\">\n"
            + "     and h.status = #{queryParam.status}\n"
            + " </if>\n"
            + " <if test=\"queryParam.keyword != null\">\n"
            + "     AND h.name LIKE CONCAT('%', #{queryParam.keyword}, '%')\n"
            + " </if>\n"
            + "<if test=\"queryParam.groupIdList != null and queryParam.groupIdList.size != 0\">"
            + " and o.group_id in "
            + "<foreach collection = 'queryParam.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{queryParam.groupIdList[${index}]}"
            + "</foreach>"
            + "</if>"
            + " and ( "
            + " h.create_by = #{queryParam.userId} \n"
            + " or o.group_id is null"
            + " <if test=\"queryParam.permissionGroupIdList != null and queryParam.permissionGroupIdList.size != 0\">"
            + " or o.group_id in "
            + "<foreach collection = 'queryParam.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{queryParam.permissionGroupIdList[${index}]}"
            + "</foreach>"
            + " </if>"
            + " )"
            + " UNION"
            + " SELECT * FROM hexagon_template_site WHERE ea is null AND status NOT IN(4,5)\n"
            + "<if test=\"queryParam.langType != null\">AND default_lang=#{queryParam.langType}\n</if>"
            + "<if test=\"queryParam.keyword != null\">AND name LIKE CONCAT('%', #{queryParam.keyword}, '%')\n</if>"
            + "<if test=\"queryParam.status != null\">AND status=#{queryParam.status}\n</if>"
            + ")t ORDER BY t.create_time DESC"
            + "</script>")
    List<HexagonTemplateSiteEntity> pageQueryHexagonTemplateByAllGroup(@Param("queryParam") HexagonTemplateSiteQueryParam queryParam, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM hexagon_template_site WHERE ea=#{ea} AND type=2 AND create_by=#{userId} AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<HexagonTemplateSiteEntity> pageQueryHexagonTemplateCreateByMe(@Param("ea")String ea, @Param("userId")Integer userId,  @Param("keyword")String keyword, @Param("status")Integer status,  @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM hexagon_template_site WHERE ea=#{ea} AND type=2 AND status NOT IN(4,5)\n"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + " AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea=#{ea} AND object_type=#{objectType})\n"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<HexagonTemplateSiteEntity> pageQueryHexagonTemplateByUnGrouped(@Param("ea")String ea, @Param("keyword")String keyword, @Param("userId")Integer userId,
                                                                             @Param("status")Integer status, @Param("objectType")Integer objectType, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM hexagon_template_site WHERE ea is null AND status NOT IN(4,5)\n"
            + "<if test=\"langType != null\">AND default_lang=#{langType}\n</if>"
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%', #{keyword}, '%')\n</if>"
            + "<if test=\"status != null\">AND status=#{status}\n</if>"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<HexagonTemplateSiteEntity> pageQueryHexagonTemplateBySystem(@Param("keyword")String keyword, @Param("status")Integer status, @Param("langType")Integer langType, @Param("page") Page page);

    @Select("<script>"
            + "SELECT * FROM\n"
            + "(\n"
            + "SELECT C.hexagon_template_site_id AS hexagonSiteId,C.share_pic_h5_apath, row_number () over (PARTITION BY C.hexagon_template_site_id ORDER BY C.is_homepage ASC NULLS LAST, C.create_time DESC, C.share_pic_h5_apath DESC NULLS LAST) AS ROW FROM\n"
            + "(\n"
            + "select B.* from hexagon_template_site AS A JOIN hexagon_template_page AS B ON A.id = B.hexagon_template_site_id where A.id = #{templateId}\n"
            + "AND B.share_pic_h5_apath is not null\n"
            + ") AS C \n"
            + ") AS D WHERE D.row = 1"
            + "</script>")
    HexagonSiteListDTO getCoverByTemplateSiteId(@Param("templateId") String templateId);


    @Select("<script>"
            + "SELECT * FROM\n"
            + "(\n"
            + "SELECT C.hexagon_template_site_id AS hexagonSiteId,C.share_pic_h5_apath, row_number () over (PARTITION BY C.hexagon_template_site_id ORDER BY C.is_homepage ASC NULLS LAST, C.create_time DESC, C.share_pic_h5_apath DESC NULLS LAST) AS ROW FROM\n"
            + "(\n"
            + "select B.* from hexagon_template_site AS A JOIN hexagon_template_page AS B ON A.id = B.hexagon_template_site_id where A.id in\n"
            + " <foreach collection = 'templateIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "AND B.share_pic_h5_apath is not null\n"
            + ") AS C \n"
            + ") AS D WHERE D.row = 1"
            + "</script>")
    List<HexagonSiteListDTO> getCoverByTemplateSiteIds(@Param("templateIds") List<String> templateIds);

    @Select("<script>"
            + "SELECT * FROM\n"
            + "(\n"
            + "SELECT C.hexagon_template_site_id AS hexagonSiteId,C.form_id,C.id as hexagon_page_id, row_number () over (PARTITION BY C.hexagon_template_site_id ORDER BY C.is_homepage ASC NULLS LAST, C.create_time DESC, C.form_id DESC NULLS LAST) AS ROW FROM\n"
            + "(\n"
            + "select B.* from hexagon_template_site AS A JOIN hexagon_template_page AS B ON A.id = B.hexagon_template_site_id where A.id =#{templateId}\n"
            + "AND B.form_id is not null\n"
            + ") AS C \n"
            + ") AS D WHERE D.row = 1"
            + "</script>")
    HexagonSiteListDTO getFormByTemplateSiteId(@Param("templateId")String templateId);

    @Select("SELECT * FROM hexagon_template_site WHERE ea = #{ea} AND \"type\" = #{type} AND status = 1 and is_default = 1 ORDER BY create_time LIMIT 1")
    HexagonTemplateSiteEntity getMarketingDefaultTemplateByEa(@Param("ea") String ea, @Param("type") Integer type);

    @Select("SELECT * FROM hexagon_template_site WHERE type=#{type} ORDER BY create_time desc")
    List<HexagonTemplateSiteEntity> getByType(@Param("type")int type);

    @Select("<script>"
            + "SELECT * FROM hexagon_template_site WHERE id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "</script>")
    List<HexagonTemplateSiteEntity> getByIdList(@Param("idList") List<String> idList);

    @Update("update hexagon_template_site set is_default = #{isDefault} where id = #{id}")
    int updateDefaultById(@Param("id") String id, @Param("isDefault") Integer isDefault);

    @Select("SELECT * FROM hexagon_template_site WHERE ea = #{ea} AND \"type\" = #{type} AND status = 1 ORDER BY (CASE WHEN is_default=1 THEN 1 END ),create_time ASC")
    List<HexagonTemplateSiteEntity> getMarketingTemplatesByEa(@Param("ea") String ea, @Param("type") Integer type);

    @Update("update hexagon_template_site set  is_default=1 where id=(select id from hexagon_template_site where ea = #{ea} and \"type\" = #{type} and status = 1 order by create_time limit 1)")
    int setDefaultTemplate(@Param("ea") String ea, @Param("type") Integer type);

    @Update("update hexagon_template_site set  is_default=0 where id=(select id from hexagon_template_site where ea = #{ea} and \"type\" = #{type} and status = 1 and is_default = 1)")
    int unsetDefaultTemplate(@Param("ea") String ea, @Param("type") Integer type);

    @Select("select COUNT(*) from hexagon_template_site where ea=#{ea} AND name=#{name} AND status != 4 AND status != 5")
    int queryCountByName(@Param("ea")String ea, @Param("name")String name);

    @Select("SELECT * FROM hexagon_template_site WHERE ea = #{ea} AND status = 1 AND \"type\" in(3,4) ")
    List<HexagonTemplateSiteEntity> queryLiveAndConferenceTemplatesByEa(@Param("ea") String ea);
}