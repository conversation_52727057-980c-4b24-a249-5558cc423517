package com.facishare.marketing.provider.entity.hexagon;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class HexagonSiteEntity implements Serializable {
    private String id;

    private String ea;

    private String name;

    private String outDisplayName;

    private Integer status;

    private Integer createBy;

    private Integer updateBy;

    private Date createTime;

    private Date updateTime;
    
    private boolean isSystemSite;
}