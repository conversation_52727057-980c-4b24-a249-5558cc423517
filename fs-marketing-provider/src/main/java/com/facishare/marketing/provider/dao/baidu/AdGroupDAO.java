package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.api.arg.advertiser.QueryAdGroupListArg;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.bo.advertise.BaiduAdGroupBO;
import com.facishare.marketing.provider.entity.baidu.AdGroupEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

/**
 * Created by z<PERSON>gh on 2021/2/23.
 */
public interface AdGroupDAO {
    @Select("<script>"
            + "SELECT * FROM ad_group WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND campaign_id=#{campaignId} AND source=#{source} AND adgroup_id = ANY(ARRAY "
            +   "<foreach collection = 'adGroupIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<AdGroupEntity> queryAdgroupByAdgroupIds(@Param("ea") String ea, @Param("adAccountId") String adAccountId,  @Param("campaignId")Long campaignId, @Param("adGroupIds") List<Long> adGroupIds, @Param("source")String source);

    @Insert("<script> "
            + "  INSERT INTO ad_group(id, ea, ad_account_id, campaign_id, adgroup_id, adgroup_name, status, source, marketing_event_id, create_time, update_time) VALUES "
            + "  <foreach collection='adGroupEntities' item='item' separator=','>"
            + "    (#{item.id}, #{item.ea}, #{item.adAccountId}, #{item.campaignId}, #{item.adGroupId}, #{item.adGroupName}, #{item.status},#{item.source}, #{item.marketingEventId}, NOW(), NOW())"
            + "  </foreach>"
            + "</script>")
    int batchInsert(@Param("adGroupEntities") Collection<AdGroupEntity> adGroupEntities);

    @Update("<script>UPDATE ad_group as c SET adgroup_name=tmp.adGroupName, status=tmp.status, update_time=now() FROM (values"
            + "<foreach separator=',' collection='adGroupEntities' item='item'>"
            +   "(#{item.id}, #{item.adGroupName}, #{item.status})"
            + "</foreach>"
            + ") as tmp(id, adGroupName, status) WHERE c.ea = #{ea} AND ad_account_id=#{adAccountId} AND c.id=tmp.id"
            + "</script>")
    int batchUpdate(@Param("adGroupEntities") Collection<AdGroupEntity> adGroupEntities, @Param("ea")String ea, @Param("adAccountId") String adAccountId);

    @Select("SELECT COUNT(*) FROM ad_group WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND source=#{source}")
    int getRefreshAdgroupTotalCount(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("source")String source);

    @Select("<script>"
            + "SELECT adgroup_id FROM ad_group WHERE ea=#{ea} AND source=#{source} AND ad_account_id=#{adAccountId} order by id asc "
            + "</script>")
    List<Long> pageRefreshAdgroupIds(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("source")String source, @Param("page") Page page);

    @FilterLog
    @Select("<script>"
            + "select * from ad_group  where ea = #{ea} and ad_account_id = #{adAccountId} "
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<AdGroupEntity> scanByAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("lastId") String lastId, @Param("limit") int limit);

    @Update("<script>update ad_group as c SET marketing_event_id = tmp.marketingEventId, update_time = now() FROM (values"
            + "<foreach separator=',' collection='adGroupEntityList' item='item'>"
            +   "(#{item.id}, #{item.marketingEventId})"
            + "</foreach>"
            + ") as tmp(id, marketingEventId) WHERE c.ea = #{ea} AND c.ad_account_id = #{adAccountId} AND c.id = tmp.id"
            + "</script>")
    void batchUpdateMarketingEventById(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adGroupEntityList") List<AdGroupEntity> adGroupEntityList);

    @Select("SELECT * FROM ad_group WHERE ea=#{ea} AND adgroup_id=#{adGroupId} AND source=#{source}")
    AdGroupEntity queryAdgroupByAdgroupId(@Param("ea") String ea,  @Param("adGroupId") Long adGroupId, @Param("source")String source);

    @Select("SELECT count(*) FROM ad_group WHERE ea=#{ea} AND ad_account_id=#{adAccountId}")
    int count( @Param("ea") String ea, @Param("adAccountId") String adAccountId);

    @Select("<script>"
            + "SELECT * FROM ad_group WHERE ea=#{ea} AND ad_account_id=#{adAccountId} AND adgroup_id = ANY(ARRAY "
            +   "<foreach collection = 'adGroupIds' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    List<AdGroupEntity> queryByAdgroupIdList(@Param("ea") String ea, @Param("adAccountId") String adAccountId,  @Param("adGroupIds") List<Long> adGroupIds);

    @Select("<script>"
            + "select adGroup.id id, adGroup.adgroup_id adGroupId, adGroup.status status, adGroup.adgroup_name adGroupName, adGroup.marketing_event_id marketingEventId, campaign.campaign_id campaignId, campaign.campaign_name campaignName,campaign.marketing_event_id campaignMarketingEventId from ad_group adGroup "
            + " left join baidu_campaign campaign on adGroup.ea = campaign.ea and adGroup.ad_account_id = campaign.ad_account_id and adGroup.campaign_id = campaign.campaign_id"
            + " where adGroup.ea = #{arg.ea} and adGroup.ad_account_id = #{arg.adAccountId} "
            + " <if test=\"arg.status != null \">\n"
            + "    and adGroup.status = #{arg.status} \n"
            + " </if>\n"
            + " <if test=\"arg.keywordType != null and arg.keywordType != '' and arg.keyword != null and arg.keyword != ''\">\n"
            + " <if test=\"arg.keywordType == 'adGroup' \">\n"
            + "AND adGroup.adgroup_name like concat(concat('%',#{arg.keyword}),'%')\n"
            + " </if>\n"
            + " <if test=\"arg.keywordType == 'campaign' \">\n"
            + "AND campaign.campaign_name like concat(concat('%',#{arg.keyword}),'%')\n"
            + " </if>\n"
            + " </if>\n"
            + " order by adGroup.create_time desc"
            + "</script>"
    )
    List<BaiduAdGroupBO> pageAdGroup(@Param("arg") QueryAdGroupListArg arg, @Param("ea") Page<BaiduAdGroupBO> page);

    @Select( "<script>"
            + "select marketing_event_id from ad_group WHERE ea = #{ea} AND ad_account_id = #{adAccountId}"
            + " <if test=\"keyword != null and keyword != ''\">\n"
            + " AND adgroup_name like concat(concat('%',#{keyword}),'%')\n"
            + " </if>\n"
            + "</script>")
    List<String> getAllMarketingEventIdList(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("keyword") String keyword);

    @Select("select * from ad_group WHERE ea = #{ea} AND id = #{id}")
    AdGroupEntity getById(@Param("ea") String ea, @Param("id") String id);

    @Select("select * from ad_group WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId}")
    AdGroupEntity queryByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM ad_group WHERE ea=#{ea} AND adgroup_id=#{adGroupId} AND ad_account_id=#{adAccountId}")
    AdGroupEntity queryAdgroupByAdgroupIdAndAccountId(@Param("ea") String ea,  @Param("adAccountId") String adAccountId, @Param("adGroupId") Long adGroupId);

    @Select("SELECT * FROM ad_group WHERE ea=#{ea} AND adgroup_id=#{adGroupId} order by create_time desc")
    List<AdGroupEntity> queryByAdgroupId(@Param("ea") String ea, @Param("adGroupId") Long adGroupId);

    @Select("<script>  "
            + "SELECT * FROM ad_group WHERE ea=#{ea} AND adgroup_name = ANY(ARRAY "
            +   "<foreach collection = 'nameList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    @FilterLog
    List<AdGroupEntity> getByNameList(@Param("ea") String ea, @Param("nameList") List<String> nameList);
}
