package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.QywxMomentSendResultEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface QYWXMomentSendResultUpdateDAO {

    @Select("select id from qywx_moment_send_result WHERE ea = #{ea} and moment_id = #{momentId} and user_id =#{userId} and publish_status = 0 limit #{batchSize}")
    List<String> queryByMomentIdAndUserId(@Param("ea") String ea, @Param("momentId") String momentId, @Param("userId") String userId, @Param("batchSize") int batchSize);

    @Update("<script>" +
            "update qywx_moment_send_result set publish_status = #{publishStatus},update_time =now() " +
            "WHERE " +
            "id=ANY(ARRAY<foreach collection=\"ids\" open=\"[\" close=\"]\" item='item' separator=\",\"> #{item}</foreach>) " +
            "</script>")
    void batchUpdatePublishStatusByIds(@Param("publishStatus") int publishStatus, @Param("ids") List<String> ids);
}
