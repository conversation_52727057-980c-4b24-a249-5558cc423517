package com.facishare.marketing.provider.manager.qywx;

import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.manager.CampaignMergeDataResetManager;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2021/07/16
 **/
@Component
@Slf4j
public class ResetQywxAddressBookManager {

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private CampaignMergeDataResetManager campaignMergeDataResetManager;

    public void initQywxAddressBook(String ea) {
        log.info("+++++++++++++++++  开始刷库initQywxAddressBook +++++++++++++++++");
        if ("setAll".equals(ea)) {
            ea = null;
        }
        // 获取accessToken
        List<QywxCorpAgentConfigEntity> qywxCorpAgentConfigEntityList = qywxCorpAgentConfigDAO.queryQywxCorpAgentConfig(ea);
        Integer allDataNum = qywxCorpAgentConfigEntityList.size();
        Integer successNum = 0;
        Integer errorNum = 0;
        Integer nowNum = 0;
        log.info("+++++++++++++++++ 共：{}", allDataNum);
        for (QywxCorpAgentConfigEntity agentConfig : qywxCorpAgentConfigEntityList) {
            try {
                log.info("+++++++++++++++++ initQywxAddressBook 当前:{}/{}", nowNum + 1, qywxCorpAgentConfigEntityList.size());
                if (campaignMergeDataResetManager.enterpriseStop(agentConfig.getEa())) {
                    nowNum = nowNum + 1;
                    successNum = successNum + 1;
                    continue;
                }
                String accessToken = qywxManager.getAccessToken(agentConfig.getEa());
                if (StringUtils.isBlank(accessToken)) {
                    nowNum = nowNum + 1;
                    errorNum = errorNum + 1;
                    log.warn("error ===== ResetQywxAddressBookManager.initQywxAddressBook error accessToken is null agentConfig:{}", agentConfig);
                    continue;
                }
                qywxManager.queryAllStaff(agentConfig.getEa(), accessToken, false, true);
                nowNum = nowNum + 1;
                successNum = successNum + 1;
            } catch (Exception e) {
                log.warn("ResetQywxAddressBookManager.initQywxAddressBook error e:", e);
            }
        }
        log.info("----------------- 共:{}", allDataNum);
        log.info("----------------- 成功：{}", successNum);
        log.info("----------------- 失败：{}", errorNum);
        log.info("+++++++++++++++++  完成刷库initQywxAddressBook +++++++++++++++++");
    }

}
