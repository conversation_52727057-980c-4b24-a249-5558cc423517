package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.api.result.QywxGroupSendEmployeeRankingDataResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.dto.qywx.QueryMomentSendResultDTO;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxMomentSendResultEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created  By zhoux 2020/03/03
 **/
public interface QYWXMomentSendResultDaO {

    @Insert("INSERT INTO qywx_moment_send_result (id, ea, moment_id,  user_id, publish_status, external_user_id, create_time, update_time) "
            + " VALUES (#{obj.id}, #{obj.ea}, #{obj.momentId}, #{obj.userId}, #{obj.publishStatus}, #{obj.externalUserId}, #{obj.createTime}, #{obj.updateTime})")
    int insertQywxMomentTask(@Param("obj") QywxMomentSendResultEntity qywxMomentSendResultEntity);

//    @Select("SELECT * FROM qywx_moment_send_result WHERE moment_id = #{momentId} and user_id =#{userId} limit 1")
//    QywxMomentSendResultEntity getByMomentIdAndUserId(@Param("momentId") String momentId, @Param("userId") String userId);

    @Select("<script>" +
            "SELECT COALESCE(COUNT(*), 0) " +
            "FROM qywx_moment_send_result " +
            "WHERE " +
            "moment_id = #{momentId} " +
            "and user_id = #{userId} " +
            "<if test='status!=null'>" +
            "and publish_status = #{status}" +
            "</if>" +
            "</script>"
    )
    @FilterLog
    int countByMomentIdAndUserIdOrStatus(@Param("momentId") String momentId, @Param("userId") String userId, @Param("status") Integer status);


    @Select("SELECT * FROM qywx_moment_send_result WHERE moment_id = #{momentId} ")
    List<QywxMomentSendResultEntity> getByMomentId(@Param("momentId") String momentId);

    //收到和未收到客户数量
    @Select("<script>"
            + " SELECT moment_id AS momentId, publish_status as status, count(*) AS customerCount,count(distinct (user_id)) as employeeCount FROM qywx_moment_send_result WHERE ea =#{ea} and moment_id IN\n"
            + "<foreach collection = 'momentIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "GROUP BY moment_id, user_id, publish_status"
            + "</script>")
    List<QueryMomentSendResultDTO> queryMomentSendResultByMomentIds(@Param("momentIds") List<String> momentIds, @Param("ea") String ea);


    //朋友圈任务数
    @Select("SELECT count(distinct(moment_id)) FROM qywx_moment_send_result WHERE ea=#{ea} and execute_time between #{start} and #{end}")
    int momentCountByMonth(@Param("start") long start, @Param("end") long end, @Param("ea") String ea);


    //累计发表人数
    @Select("SELECT count(distinct(user_id))  as employeeCount, count(distinct(external_user_id))  as customerCount FROM qywx_moment_send_result WHERE ea=#{ea} and publish_status = 1 and execute_time between #{start} and #{end}")
    QueryMomentSendResultDTO emplyeeSendAndCusomerByMonth(@Param("ea") String ea, @Param("start") long start, @Param("end") long end);

    //累计发表次数
    @Select("SELECT COUNT(distinct(moment_id)) FROM qywx_moment_send_result WHERE ea=#{ea} and publish_status = 1 and execute_time between #{start} and #{end}")
    int getEmployeeSendTimes(@Param("ea") String ea, @Param("start") long start, @Param("end") long end);


    //已接受到四次朋友圈的客户数
    @Select("select count(1) from ( select 1 from qywx_moment_send_result where ea=#{ea} and publish_status = 1 and execute_time between #{start} and #{end} group by moment_id ,user_id having count(1) >= 4) m")
    int queryExceedFourMomentByMonth(@Param("ea") String ea, @Param("start") long start, @Param("end") long end);


    @Update("update qywx_moment_send_result set publish_status = #{publishStatus},update_time =now() WHERE moment_id = #{momentId} and user_id =#{userId}")
    void updatePublishStatusByMomentIdAndUserId(@Param("publishStatus") int publishStatus, @Param("momentId") String momentId, @Param("userId") String userId);

    @FilterLog
    @Insert("<script>" +
            "INSERT INTO qywx_moment_send_result (id, ea, moment_id,  user_id, publish_status, external_user_id,execute_time, create_time, update_time) " +
            "VALUES <foreach collection='entitys' item='item' separator=','>" +
            "(#{item.id}, #{item.ea}, #{item.momentId},  #{item.userId}, #{item.publishStatus}, #{item.externalUserId}, #{item.executeTime}, now(), now())" +
            "</foreach>" +
            " ON CONFLICT DO NOTHING" +
            "</script>")
    void batchInsert(@Param("entitys") List<QywxMomentSendResultEntity> qywxMomentSendResultEntity);

    @Select("SELECT count(distinct(user_id)) FROM qywx_moment_send_result WHERE moment_id = #{momentId} and publish_status = 1 and ea =#{ea} ")
    int countSendEmployeeByMomentId(@Param("momentId") String momentId,@Param("ea") String ea);

    @Select("SELECT count(distinct(user_id)) FROM qywx_moment_send_result WHERE moment_id = #{momentId} and publish_status = 0 and ea =#{ea} ")
    int countUnSendEmployeeByMomentId(@Param("momentId") String momentId,@Param("ea") String ea);

    @Select("SELECT count(distinct(external_user_id)) FROM qywx_moment_send_result WHERE moment_id = #{momentId} and publish_status = 1 and ea =#{ea} ")
    int receiveCountByMomentId(@Param("momentId") String momentId,@Param("ea") String ea);

    @Select("SELECT count(distinct(external_user_id)) FROM qywx_moment_send_result WHERE moment_id = #{momentId} and publish_status = 0 and ea =#{ea} ")
    int unReceiveCountByMomentId(@Param("momentId") String momentId,@Param("ea") String ea);

    @Select("select user_id as userId ,publish_status as status ,count( (external_user_id)) as customerCount from qywx_moment_send_result where moment_id =#{momentId} and ea =#{ea} group by publish_status ,user_id order by publish_status desc, customerCount desc")
    List<QueryMomentSendResultDTO> momentEmployeeRanking(@Param("momentId") String momentId,@Param("ea") String ea, @Param("Page") Page page);

    @Select("<script>" +
            "select user_id as userId ,publish_status as status ,count( (external_user_id)) as customerCount from qywx_moment_send_result where moment_id =#{momentId} and ea =#{ea} and user_id in " +
            "<foreach collection='userId' item='item' open = '(' separator = ',' close = ')' >" +
               "#{item}"+
            "</foreach> " +
            " <if test='status != null and status != \"\"'>" +
            " and publish_status =#{status} " +
            "</if>" +
            " group by publish_status ,user_id order by publish_status desc, customerCount desc "+
            "</script>")
    List<QueryMomentSendResultDTO> momentEmployeeRankingByUserId(@Param("momentId") String momentId,@Param("ea") String ea,
                                                                 @Param("userId") List<String> userId, @Param("status") Integer status);

    @Select("<script>" +
            "select user_id as userId from qywx_moment_send_result where moment_id =#{momentId} and ea =#{ea} and user_id in " +
            "<foreach collection='userId' item='item' open = '(' separator = ',' close = ')' >" +
            "#{item}"+
            "</foreach> " +
            " <if test='status != null and status != \"\"'>" +
            " and publish_status =#{status} " +
            "</if>" +
            " group by publish_status ,user_id "+
            "</script>")
    List<String> getSentUserIdList(@Param("momentId") String momentId,@Param("ea") String ea,
                                                                 @Param("userId") List<String> userId, @Param("status") Integer status);


    @Select("<script>" +
            "SELECT external_user_id as externalUserId, user_id as userId, min(create_time) as createTime, max(publish_status) as publishStatus FROM qywx_moment_send_result " +
            "WHERE ea =#{ea} and moment_id = #{momentId} " +
            "<if test='userId != null and userId != \"\"'>" +
            "and user_id =#{userId} " +
            "</if>" +
            "<if test='publishStatus != null'>" +
            "and publish_status = #{publishStatus}\n" +
            "</if>" +
            "<if test =\"userIdList != null and userIdList.size != 0\">\n" +
            "and user_id in " +
                "<foreach collection = 'userIdList' index='idx' open = '(' separator = ',' close = ')'>" +
                    "#{userIdList[${idx}]}" +
                "</foreach>" +
            "</if>\n" +
            "group by\n" +
            "\texternal_user_id ,user_id order by external_user_id" +
            "</script>")
    List<QywxMomentSendResultEntity> getByMomentIdAndUserIdAndStatus(@Param("ea") String ea, @Param("momentId") String momentId,
                                                                     @Param("userId") String userId,
                                                                     @Param("publishStatus") Integer publishStatus,
                                                                     @Param("userIdList") List<String> userIdList, @Param("page") Page page);

    @Select("<script>" +
            "SELECT count(distinct concat(external_user_id, user_id)) FROM qywx_moment_send_result " +
            "WHERE ea =#{ea} and moment_id = #{momentId} " +
            "<if test='userId != null and userId != \"\"'>" +
            "and user_id =#{userId} " +
            "</if>" +
            "<if test='publishStatus != null'>" +
            "and publish_status = #{publishStatus}\n" +
            "</if>" +
            "<if test =\"userIdList != null and userIdList.size != 0\">\n" +
            "and user_id in " +
            "<foreach collection = 'userIdList' index='idx' open = '(' separator = ',' close = ')'>" +
            "#{userIdList[${idx}]}" +
            "</foreach>" +
            "</if>\n" +
            "</script>")
    Integer countByMomentIdAndUserIdAndStatus(@Param("ea") String ea, @Param("momentId") String momentId,
                                              @Param("userId") String userId,
                                              @Param("publishStatus") Integer publishStatus,
                                              @Param("userIdList") List<String> userIdList);

    @Select("<script>" +
            "select\n" +
            "\texternal_user_id as externalUserId,\n" +
            "\tcount(*) as count\n" +
            "from\n" +
            "\tqywx_moment_send_result\n" +
            "where ea = #{ea} \n" +
            "\t and publish_status = 1\n" +
            "\tand external_user_id in \n" +
            "<foreach collection='externalUserIds' item='item' open = '(' separator = ',' close = ')' >" +
            "#{item}"+
            "</foreach> " +
            "<![CDATA[  and create_time >= date_trunc('month', current_date) and create_time < date_trunc('month', current_date) + interval '1 month'  ]]>\n"+
            "group by\n" +
            "\texternal_user_id" +
            "</script>")
    List<Map<String, Object>> getExternalUserIdAndcount(@Param("ea") String ea, @Param("externalUserIds") List<String> externalUserIds);

    @Select("select\n" +
            "\t*\n" +
            "from\n" +
            "\tqywx_moment_send_result as ta\n" +
            "where\n" +
            "\tta.ea = #{ea}\n" +
            "\tand\n" +
            "\tta.id <> (\n" +
            "\tselect\n" +
            "\t\tmax(tb.id)\n" +
            "\tfrom\n" +
            "\t\tqywx_moment_send_result as tb\n" +
            "\twhere\n" +
            "\t\tta.moment_id = tb.moment_id\n" +
            "\t\tand ta.user_id = tb.user_id\n" +
            "\t\tand ta.external_user_id = tb.external_user_id\n" +
            "\t)")
    List<QywxMomentSendResultEntity> pageQueryDuplicateData(@Param("ea") String ea, @Param("page") Page<QywxMomentSendResultEntity> page);

    @Delete("<script>" +
            "DELETE FROM qywx_moment_send_result WHERE id in" +
            "<foreach collection='ids' item='item' open = '(' separator = ',' close = ')' >" +
            "#{item}"+
            "</foreach> " +
            "</script>")
    void deleteByIds(@Param("ids") List<String> ids);

    @Select("<script>" +
            "SELECT count(distinct user_id) FROM qywx_moment_send_result WHERE ea=#{ea} and update_time between #{startDate} and #{endDate} and moment_id = " +
            "ANY(ARRAY<foreach collection = 'momentIds' index='idx' open = '[' separator = ',' close = ']'>#{momentIds[${idx}]}</foreach>)" +
            "</script>")
    int calculateReceiveCountByMomentIds(@Param("ea")String ea, @Param("momentIds") List<String> momentIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "SELECT count(distinct user_id) FROM qywx_moment_send_result WHERE publish_status = 1 and update_time between #{startDate} and #{endDate} and moment_id = " +
            "ANY(ARRAY<foreach collection = 'momentIds' index='idx' open = '[' separator = ',' close = ']'>#{momentIds[${idx}]}</foreach>)" +
            "</script>")
    int calculateConfirmCountByMomentIds(@Param("momentIds") List<String> momentIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "SELECT count(distinct external_user_id) FROM qywx_moment_send_result WHERE publish_status = 1 and update_time between #{startDate} and #{endDate} and moment_id = " +
            "ANY(ARRAY<foreach collection = 'momentIds' index='idx' open = '[' separator = ',' close = ']'>#{momentIds[${idx}]}</foreach>)" +
            "</script>")
    int calculateDeliveryCountByMomentIds(@Param("momentIds") List<String> momentIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "SELECT user_id qywxUserId, count(distinct moment_id) confirmCount FROM qywx_moment_send_result WHERE ea=#{ea} and publish_status = 1 and update_time between #{startDate} and #{endDate} and moment_id = " +
            "ANY(ARRAY<foreach collection = 'momentIds' index='idx' open = '[' separator = ',' close = ']'>#{momentIds[${idx}]}</foreach>) " +
            "group by user_id" +
            "</script>")
    List<QywxGroupSendEmployeeRankingDataResult> calculateEmployeeConfirmCountByMomentIds(@Param("ea")String ea, @Param("momentIds") List<String> momentIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "SELECT user_id qywxUserId, count(distinct external_user_id) deliveryCount FROM qywx_moment_send_result WHERE publish_status = 1 and update_time between #{startDate} and #{endDate} and moment_id = " +
            "ANY(ARRAY<foreach collection = 'momentIds' index='idx' open = '[' separator = ',' close = ']'>#{momentIds[${idx}]}</foreach>) " +
            "group by user_id" +
            "</script>")
    List<QywxGroupSendEmployeeRankingDataResult> calculateEmployeeDeliveryCountByMomentIds(@Param("momentIds") List<String> momentIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "select * from (" +
            "select a.user_id qywxUserId,count(distinct b.moment_id) receiveCount " +
            "from qywx_moment_send_result a " +
            "left join qywx_moment_task b on a.ea = b.ea and a.moment_id = b.moment_id " +
            "where a.ea = #{ea} " +
            "and b.marketing_activity_id = \n" +
            "ANY(ARRAY<foreach collection = 'marketingActivityIds' index='idx' open = '[' separator = ',' close = ']'>#{marketingActivityIds[${idx}]}</foreach>)" +
            "<if test =\"qywxStaticsticsVisitUserRange != null and qywxStaticsticsVisitUserRange.size > 0\">\n" +
            " and a.user_id = \n" +
            "ANY(ARRAY<foreach collection = 'qywxStaticsticsVisitUserRange' index='idx' open = '[' separator = ',' close = ']'>#{qywxStaticsticsVisitUserRange[${idx}]}</foreach>)" +
            "</if>" +
            "group by a.user_id " +
            ") tt " +
            "order by receiveCount" +
            "</script>")
    List<QywxGroupSendEmployeeRankingDataResult> queryPageByMarketingActivityIdsAndEmployeeds(@Param("ea")String ea, @Param("marketingActivityIds") List<String> marketingActivityIds, @Param("qywxStaticsticsVisitUserRange") List<String> qywxStaticsticsVisitUserRange, Page page);

    @Select("<script>"
            + "select * from qywx_moment_send_result where ea = #{ea} and external_user_id = #{externalUserId}"
            + " <if test='limit != null'>"
            + " limit #{limit}"
            + "</if>"
            + "</script>")
    List<QywxMomentSendResultEntity> getByExternalUserId(@Param("ea") String ea, @Param("externalUserId") String externalUserId, @Param("limit") Integer limit);

    @Update("update qywx_moment_send_result set external_user_id = #{externalUserId} where ea = #{ea} and external_user_id = #{oldExternalUserId}")
    int updateExternalUserIdByOldExternalUserId(@Param("ea") String ea,  @Param("oldExternalUserId") String oldExternalUserId, @Param("externalUserId") String externalUserId);

    @Update("update qywx_moment_send_result set external_user_id = #{externalUserId} where ea = #{ea} and id = #{id}")
    int updateExternalUserId(@Param("ea") String ea, @Param("id") String id, @Param("externalUserId") String externalUserId);

    @Select("select * from qywx_moment_send_result where ea = #{ea} and user_id = #{userId}")
    List<QywxMomentSendResultEntity> getByUserId(@Param("ea") String ea, @Param("userId") String userId);

    @Select("select count(*) from qywx_moment_send_result where ea = #{ea} and user_id = #{userId}")
    int countByUserId(@Param("ea") String ea, @Param("userId") String userId);

    @Select("<script>"
            + "select * from qywx_moment_send_result where ea = #{ea} and user_id = #{userId} "
            + " <if test='lastId != null and lastId != \"\"'>"
            + " and id <![CDATA[ > ]]> #{lastId}"
            + "</if>"
            + " order by id limit #{pageSize}"
            + "</script>"
    )
    List<QywxMomentSendResultEntity> scanByUserId(@Param("ea") String ea, @Param("userId") String userId, @Param("lastId") String lastId, @Param("pageSize") int pageSize);


    @Update("update qywx_moment_send_result set user_id = #{userId} where ea = #{ea} and id = #{id}")
    int updateUserId(@Param("ea") String ea, @Param("id") String id, @Param("userId") String userId);

    @Update("update qywx_moment_send_result set user_id = #{userId} where ea = #{ea} and user_id = #{oldUserId}")
    int updateUserIdByOldUserId(@Param("ea") String ea, @Param("oldUserId") String oldUserId, @Param("userId") String userId);


    @Select("<script>"
            + "SELECT * FROM qywx_moment_send_result WHERE ea = #{ea} AND publish_status = 0 AND id=ANY(ARRAY<foreach collection=\"ids\" open=\"[\" close=\"]\" item='item' separator=\",\"> #{item}</foreach>) "
            + "</script>")
    List<QywxMomentSendResultEntity> queryUnpublishMomentResultEntityByIds(@Param("ea") String ea, @Param("ids") List<String> ids);

}
