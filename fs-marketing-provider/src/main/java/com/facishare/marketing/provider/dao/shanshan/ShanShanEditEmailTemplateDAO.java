package com.facishare.marketing.provider.dao.shanshan;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.shanshan.ShanShanEmailTemplateEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface ShanShanEditEmailTemplateDAO {

    @Insert("INSERT INTO shanshan_edit_email_template(id, ea, email_id,name, subject, summary,content,snapshot,creator,create_time, update_time,status)" +
            " VALUES(#{obj.id}, #{obj.ea}, #{obj.emailId}, #{obj.name},#{obj.subject},#{obj.summary},#{obj.content},#{obj.snapshot},#{obj.creator},now(), now(),0) ON CONFLICT DO NOTHING")
    int insert(@Param("obj") ShanShanEmailTemplateEntity obj);


    @Select("<script>"
            + "SELECT * FROM shanshan_edit_email_template WHERE ea=#{ea} and status=0 "
            + "<if test=\"keyword != null\">AND name LIKE CONCAT('%',#{keyword},'%')</if>"
            + "ORDER BY update_time DESC"
            + "</script>")
    @FilterLog
    List<ShanShanEmailTemplateEntity> queryListByEa(@Param("ea")String ea, @Param("keyword")String keyword,@Param("page") Page page);

    @Select("SELECT * FROM shanshan_edit_email_template WHERE ea=#{ea} and email_id = #{emailId} and status=0 ")
    ShanShanEmailTemplateEntity queryByEaAndEmailId(@Param("ea")String ea, @Param("emailId")String emailId);

    @Update("UPDATE shanshan_edit_email_template SET name = #{obj.name}, subject = #{obj.subject}, summary = #{obj.summary},content = #{obj.content},snapshot = #{obj.snapshot}, update_time = now() " +
            " WHERE ea = #{obj.ea} AND email_id = #{obj.emailId}")
    @FilterLog
    int update(@Param("obj") ShanShanEmailTemplateEntity obj);

    @Update("UPDATE shanshan_edit_email_template SET status = 1,update_time = now() " +
            " WHERE ea = #{ea} AND email_id = #{emailId}")
    int deleteByEmailId(@Param("ea")String ea,@Param("emailId")String emailId);

    @Update("UPDATE shanshan_edit_email_template SET status = 1,update_time = now() " +
            " WHERE id = #{id}")
    int deleteById(@Param("id")String id);
}
