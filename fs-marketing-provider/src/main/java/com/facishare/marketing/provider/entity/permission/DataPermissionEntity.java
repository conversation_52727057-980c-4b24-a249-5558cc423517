package com.facishare.marketing.provider.entity.permission;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class DataPermissionEntity implements Serializable {
    private String id;

    private String ea;

    private Integer type;

    private Boolean status;

    private Date createTime;

    private Date updateTime;
}
