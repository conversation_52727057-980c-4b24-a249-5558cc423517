package com.facishare.marketing.provider.entity.qywx;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13 10:45
 */
@Data
public class QywxGroupCodeEntity implements Serializable {
    private String id;                      //id
    private String ea;                      //企业ea
    private String groupCodeName;           //企微群活码名称
    private Integer scene;                  //场景 1 - 群的小程序插件 2 - 群的二维码插件
    private String remark;                  //备注
    private Integer autoCreateRoom;         //是否自动新建群。0-否；1-是。 默认为1
    private String roomBaseName;            //自动建群的群名前缀
    private Integer roomBaseId;              //自动建群的群起始序号
    private String chatIdList;              //使用该配置的客户群ID列表
    private String state;                   //渠道id
    private String configId;                //联系我的id
    private String qrCodeUrl;               //二维码地址
    private Integer status;                 //企微群活码状态 0:正常 1:删除
    private Date createTime;                //创建时间
    private Date updateTime;                //更新时间
    private String marketingEventId;        //关联市场活动
    private Integer createBy;               //创建人
}
