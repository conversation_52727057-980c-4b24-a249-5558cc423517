package com.facishare.marketing.provider.dao.yxzs;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsUnionMessageExtendSettingEntity;
import org.apache.ibatis.annotations.*;

public interface YxzsUnionMessageExtendSettingDAO {

    @Insert("INSERT INTO yxzs_union_message_extend_setting(id, ea, send_union_message_arg, send_union_message_extend_arg,  open_wx_notice, wx_app_id, wx_template_msg, create_time, update_time) " +
            "VALUES(#{id}, #{ea}, #{sendUnionMessageArg}, #{sendUnionMessageExtendArg}, #{openWxNotice}, #{wxAppId}, #{wxTemplateMsg}, now(), now())")
    int insert(YxzsUnionMessageExtendSettingEntity entity);

    @Delete("DELETE FROM yxzs_union_message_extend_setting WHERE id=#{id}")
    void deleteById(@Param("id") String id);

    @Update("<script>" +
            "UPDATE yxzs_union_message_extend_setting" +
            "<set>" +
            "<if test='sendUnionMessageArg != null'>send_union_message_arg = #{sendUnionMessageArg},</if>" +
            "<if test='sendUnionMessageExtendArg != null'>send_union_message_extend_arg = #{sendUnionMessageExtendArg},</if>" +
            "<if test='openWxNotice != null'>open_wx_notice = #{openWxNotice},</if>" +
            "<if test='wxAppId != null'>wx_app_id = #{wxAppId},</if>" +
            "<if test='wxTemplateMsg != null'>wx_template_msg = #{wxTemplateMsg},</if>" +
            "update_time = now()" +
            "</set>" +
            "WHERE ea = #{ea}" +
            "</script>")
    void updateByEa(YxzsUnionMessageExtendSettingEntity entity);

    @Select("SELECT * FROM yxzs_union_message_extend_setting WHERE id=#{id}")
    YxzsUnionMessageExtendSettingEntity getById(@Param("id") String id);

    @FilterLog
    @Select("SELECT * FROM yxzs_union_message_extend_setting WHERE ea=#{ea}")
    YxzsUnionMessageExtendSettingEntity getByEa(@Param("ea") String ea);

    @Update("UPDATE yxzs_union_message_extend_setting set open_wx_notice = null , wx_app_id = null , wx_template_msg = null , update_time = now() WHERE ea = #{ea}")
    void clearWxTemplateMsg(@Param("ea") String ea);


}
