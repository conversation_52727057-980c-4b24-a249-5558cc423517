package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import java.util.List;

import org.apache.ibatis.annotations.*;

/**
 * Created  By zhoux 2020/04/07
 **/
public interface QywxVirtualFsUserDAO {

    @Insert("INSERT INTO qywx_virtual_fs_user(id, ea, user_id, corp_id, qy_user_id, create_time, crm_bind_time) VALUES(#{entity.id}, #{entity.ea}, " +
            "#{entity.userId}, #{entity.corpId}, #{entity.qyUserId}, now(), #{entity.crmBindTime}) ON CONFLICT(ea, user_id) DO UPDATE SET qy_user_id=#{entity.qyUserId}")
    int insert(@Param("entity") QywxVirtualFsUserEntity entity);

    @Update("UPDATE qywx_virtual_fs_user SET user_id=#{entity.userId}, qy_user_id=#{entity.qyUserId}, corp_id=#{entity.corpId} WHERE id=#{entity.id}")
    void updateVirtualFsUser(@Param("entity") QywxVirtualFsUserEntity entity);

    @Update("UPDATE qywx_virtual_fs_user SET qy_user_id=#{entity.qyUserId}, corp_id=#{entity.corpId}, crm_bind_time = #{entity.crmBindTime} WHERE id=#{entity.id}")
    void updateQywxUserIdAndCrmBindTimeById(@Param("entity") QywxVirtualFsUserEntity entity);

    @Select("SELECT MAX(user_id) FROM qywx_virtual_fs_user WHERE ea = #{ea} AND user_id >= 100000000 and user_id < 200000000")
    Integer getQywxMaxVirtualUserId(@Param("ea") String ea);

    @Select("SELECT MAX(user_id) FROM qywx_virtual_fs_user WHERE ea = #{ea} AND user_id >= 200000000 and user_id < 300000000")
    Integer getDingMaxVirtualUserId(@Param("ea") String ea);

    @Select("SELECT * FROM qywx_virtual_fs_user WHERE corp_id = #{corpId} AND qy_user_id = #{qyUserId} and ea = #{ea}")
    QywxVirtualFsUserEntity getVirtualUserByQywxInfo(@Param("corpId") String corpId, @Param("qyUserId") String qyUserId, @Param("ea") String ea);

    @FilterLog
    @Select("<script>"
        + " SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea} AND qy_user_id IN "
        + "<foreach collection = 'qyUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<QywxVirtualFsUserEntity> getVirtualUserByEaAndQyIds(@Param("ea") String ea, @Param("qyUserIds") List<String> qyUserId);

    @Select("<script>"
        + " SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea} AND qy_user_id = #{qyUserId} "
        + "</script>")
    QywxVirtualFsUserEntity getVirtualUserByEaAndQyId(@Param("ea") String ea, @Param("qyUserId") String qyUserId);

    @FilterLog
    @Select("<script>"
            + "select a.* from qywx_virtual_fs_user a right join qywx_corp_agent_config b on a.ea = b.ea and a.corp_id = b.corpid " +
            "where a.ea = #{ea} and a.qy_user_id = #{qyUserId}"
            + "</script>")
    QywxVirtualFsUserEntity getCurrentVirtualUserByEaAndQyId(@Param("ea") String ea, @Param("qyUserId") String qyUserId);

    @FilterLog
    @Select("<script>"
            + "select a.* from qywx_virtual_fs_user a right join qywx_corp_agent_config b on a.ea = b.ea and a.corp_id = b.corpid " +
            "where a.ea = #{ea} and a.qy_user_id in "
            + "<foreach collection = 'qyUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<QywxVirtualFsUserEntity> getCurrentVirtualUserByEaAndQyIds(@Param("ea") String ea, @Param("qyUserIds") List<String> qyUserIds);

    @Select("<script>"
        + " SELECT A.qy_user_id FROM qywx_virtual_fs_user AS A JOIN fs_bind AS B ON A.ea = B.fs_ea AND A.user_id = B.fs_user_id JOIN card AS C ON B.uid = C.uid "
        + " WHERE B.type = 1 AND A.ea = #{ea} AND B.app_id = #{appId} AND A.qy_user_id IN "
        + "<foreach collection = 'qywxUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<String> queryOpenCardQyuserId(@Param("ea") String ea, @Param("appId") String appId, @Param("qywxUserIds") List<String> qywxUserIds);


    @Select("<script>"
        + " SELECT A.qy_user_id AS qyUserId,B.fs_user_id AS userId FROM qywx_virtual_fs_user AS A JOIN fs_bind AS B ON A.ea = B.fs_ea AND A.user_id = B.fs_user_id JOIN card AS C ON B.uid = C.uid "
        + " WHERE B.type = 1 AND A.ea = #{ea} AND B.app_id = #{appId} AND A.qy_user_id IN "
        + "<foreach collection = 'qywxUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<QywxVirtualFsUserEntity> queryOpenCardQyAndFsUserId(@Param("ea") String ea, @Param("appId") String appId, @Param("qywxUserIds") List<String> qywxUserIds);

    @Select("<script>"
        + " SELECT qy_user_id FROM qywx_virtual_fs_user WHERE ea = #{ea} AND user_id IN "
        + "<foreach collection = 'userIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<String> queryQyUserIdByVirtualInfos(@Param("ea") String ea, @Param("userIds") List<Integer> userIds);

    @Select("<script>"
        + " SELECT qy_user_id FROM qywx_virtual_fs_user WHERE ea = #{ea} AND user_id = #{userId} "
        + "</script>")
    String queryQyUserIdByVirtualInfo(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update("UPDATE qywx_virtual_fs_user SET user_id=#{newUserId},crm_bind_time = now(), old_user_id = user_id WHERE ea = #{ea} AND user_id = #{oldUserId} and " +
            "not EXISTS (select * from qywx_virtual_fs_user where ea = #{ea} and user_id = #{newUserId})")
    int updateVirtualUserToFsUserId(@Param("ea") String ea, @Param("oldUserId") Integer oldUserId, @Param("newUserId")Integer newUserId);

    @Select("<script>"
        + " SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea} AND user_id IN "
        + "<foreach collection = 'userIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<QywxVirtualFsUserEntity> queryQyUserByVirtualInfos(@Param("ea") String ea, @Param("userIds") List<Integer> userIds);

    @Select("<script>"
        + " SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea} AND user_id = #{userId} "
        + "</script>")
    QywxVirtualFsUserEntity queryQyUserIdByUserIdAndEa(@Param("ea") String ea, @Param("userId") Integer userId);

    @Select(" SELECT user_id FROM qywx_virtual_fs_user WHERE ea = #{ea}")
    List<Integer> queryQyVirtualIdByEa(@Param("ea") String ea);

    @Delete("DELETE FROM qywx_virtual_fs_user WHERE id = #{id}")
    int deleteQywxVirtualFsUserById(@Param("id") String id);

    @Select(" SELECT qy_user_id FROM qywx_virtual_fs_user WHERE ea = #{ea} and user_id>=100000000")
    List<String> queryQywxVirtualUserIdByEa(@Param("ea") String ea);

    @Update("UPDATE qywx_virtual_fs_user SET user_id=#{newUserId}, old_user_id=#{oldUserId} WHERE corp_id = #{corpId} AND user_id = #{oldUserId}")
    int updateUserIdAndOldUserId(@Param("corpId") String corpId, @Param("oldUserId") Integer oldUserId, @Param("newUserId")Integer newUserId);

    @Select(" SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea} and old_user_id=#{oldUserId}")
    QywxVirtualFsUserEntity queryVirtualUserIdByEa(@Param("ea") String ea,@Param("oldUserId") Integer oldUserId);

    @Update("UPDATE qywx_virtual_fs_user SET old_user_id=#{oldUserId} WHERE ea = #{ea} AND id=#{id}")
    int updateOldUserId(@Param("ea")String ea, @Param("id")String id, @Param("oldUserId")Integer oldUserId);

    @Select("<script>"
            + " SELECT qy_user_id,crm_bind_time FROM qywx_virtual_fs_user WHERE ea = #{ea} and user_id <![CDATA[ < ]]> 100000000 and qy_user_id in "
            + " <foreach open='(' close=')' separator=',' collection='qyUserIdList' index='idx'>"
            + "     #{qyUserIdList[${idx}]}"
            + "</foreach>"
            + "</script>")
    List<QywxVirtualFsUserEntity> queryBindCrmQyUserId(@Param("ea") String ea, @Param("qyUserIdList") List<String> qyUserIdList);

    @Update("update qywx_virtual_fs_user set qy_user_id = #{qyUserId} where ea = #{ea} and id = #{id}")
    int updateQyUserId(@Param("ea") String ea, @Param("id") String id, @Param("qyUserId") String qyUserId);

    @Select(" SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea} and corp_id=#{corpId}")
    List<QywxVirtualFsUserEntity> queryByEaAndCorpId(@Param("ea") String ea,@Param("corpId") String corpId);

    @Update("update qywx_virtual_fs_user set corp_id = #{corpId} where ea = #{ea} and id = #{id}")
    int updateCorpIdById(@Param("ea") String ea, @Param("id") String id, @Param("corpId") String corpId);

    @Select("<script>"
            + " SELECT qy_user_id FROM qywx_virtual_fs_user WHERE ea = #{ea} AND qy_user_id IN "
            + "<foreach collection = 'qyUserIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " AND user_id IN "
            + "<foreach collection = 'userIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<String> queryQyUserIdByVirtualInfosAndQyUserIds(@Param("ea") String ea, @Param("userIds") List<Integer> userIds,@Param("qyUserIds") List<String> qyUserIds);

    @Select("SELECT * FROM qywx_virtual_fs_user WHERE ea = #{ea}")
    List<QywxVirtualFsUserEntity> getAllByEa(@Param("ea") String ea);
}
