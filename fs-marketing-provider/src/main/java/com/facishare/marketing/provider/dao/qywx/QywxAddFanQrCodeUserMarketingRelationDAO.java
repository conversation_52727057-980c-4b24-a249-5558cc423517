package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.dto.qywx.TemplateBindQrConfigIdDTO;
import com.facishare.marketing.provider.entity.qywx.QywxQrCodeUserMarketingRelationEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

public interface QywxAddFanQrCodeUserMarketingRelationDAO {

    @Insert("INSERT INTO qywx_qr_code_user_marketing_relation(id, ea, user_marketing_id, qywx_qr_code_id, config_id, qr_code, state, status, create_time, update_time) VALUES(#{obj.id}, #{obj.ea}, #{obj.userMarketingId}, #{obj.qywxQrCodeId}, #{obj.configId}, #{obj.qrCode}, #{obj.state}, #{obj.status}, now(), now())")
    int insert(@Param("obj") QywxQrCodeUserMarketingRelationEntity obj);

    @Select("SELECT * FROM qywx_qr_code_user_marketing_relation WHERE ea=#{ea} AND user_marketing_id=#{userMarketingId} AND qywx_qr_code_id=#{qywxQrCodeId} and status = 0 limit 1")
    QywxQrCodeUserMarketingRelationEntity queryByUserMarketingIdAndQrCodeId(@Param("ea")String ea, @Param("userMarketingId")String userMarketingId, @Param("qywxQrCodeId")String qywxQrCodeId);

    @Update("UPDATE qywx_qr_code_user_marketing_relation SET status=1, update_time=now() WHERE ea=#{ea} AND qywx_qr_code_id=#{qywxQrCodeId}")
    int deleteByQrCodeId(@Param("ea")String ea, @Param("qywxQrCodeId")String qywxQrCodeId);

    @Select("SELECT * FROM qywx_qr_code_user_marketing_relation WHERE ea=#{ea} AND state=#{state}")
    QywxQrCodeUserMarketingRelationEntity queryByState(@Param("ea")String ea, @Param("state")String state);

    @Select("SELECT ea, config_id AS configId, qywx_qr_code_id AS qrCodeId FROM qywx_qr_code_user_marketing_relation WHERE create_time<#{deletePointDate} AND status=0")
    List<TemplateBindQrConfigIdDTO> getHexagonBindQrConfigIdsByCheckTime(@Param("deletePointDate") Date deletePointDate);

    @Select("<script>" +
            "SELECT * FROM qywx_qr_code_user_marketing_relation WHERE  ea=#{ea} and user_marketing_id =ANY(ARRAY " +
            "<foreach collection='userMarketingIdList' item='userMarketingId' open='[' close=']' separator=','>#{userMarketingId}</foreach> )" +
            "</script>")
    List<QywxQrCodeUserMarketingRelationEntity> getByUserMarketingIdList(@Param("ea") String ea, @Param("userMarketingIdList") List<String> userMarketingIdList);
}
