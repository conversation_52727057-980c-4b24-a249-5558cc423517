package com.facishare.marketing.provider.manager;

import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.marketing.api.arg.conference.CheckSignInStatusArg;
import com.facishare.marketing.api.arg.conference.QueryActivityEnrollTimeArg;
import com.facishare.marketing.api.arg.conference.SignInArg;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.usermarketingaccount.BatchAddOrDeleteTagNamesToCrmDataArg;
import com.facishare.marketing.api.result.conference.CheckSignInStatusResult;
import com.facishare.marketing.api.result.conference.GetEnrollTimeResult;
import com.facishare.marketing.api.result.conference.SignInResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.service.kis.KisActionService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.contstant.CrmAuthConstants;
import com.facishare.marketing.common.contstant.DelayQueueTagConstants;
import com.facishare.marketing.common.contstant.RocketMqDelayLevelConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qr.QRCodeTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.service.MarketingFlowInstanceOuterService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceTagDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dto.ObjectIdWithMarketingUserIdAndPhoneDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2019/07/19
 **/
@Component
@Slf4j
public class ActivityManager {

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;

    @Autowired
    private QRPosterDAO qrPosterDAO;

    @Autowired
    private ConferenceInvitationDAO conferenceInvitationDAO;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private QRCodeManager qrCodeManager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private MarketingFlowInstanceOuterService marketingFlowInstanceOuterService;

    @Autowired
    private PhotoAssociationDAO photoAssociationDAO;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private CustomerCustomizeFormDataManager customerCustomizeFormDataManager;

    @Autowired
    private KisActionService kisActionService;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private ConferenceTagDAO conferenceTagDAO;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private MarketingSceneDao marketingSceneDao;
    @Autowired
    private SceneTriggerDao sceneTriggerDao;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private SceneTriggerTimedTaskDao sceneTriggerTimedTaskDao;
    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;
    @Autowired
    private TriggerInstanceDao triggerInstanceDao;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private ActivityEnrollTimeConfigDAO activityEnrollTimeConfigDAO;
    @ReloadableProperty("multiple_venues_hexagon_id")
    private String multipleVenuesHexagonSiteId;

    @ReloadableProperty("multiple_venues_hexagon_id_en")
    private String multipleVenuesHexagonSiteIdEn;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    private List<String> viewMarketingEventObjectAuth = ImmutableList.of("MarketingEventObj||View", "MarketingEventObj");
    @Autowired
    private RedisManager redisManager;

    @Data
    public static class ActivityQrCodeContainer implements Serializable{
        // h5签到二维码地址
        private String h5SignInUrl;

        // h5签到二维码path
        private String h5SignInPath;

        // 小程序签到path
        private String miniAppSignInPath;

        // 小程序签到地址
        private String miniAppSignInUrl;

        // h5会议主页二维码地址
        private String h5IndexUrl;

        // h5会议主页二维码path
        private String h5IndexPath;

        // 小程序主页二维码地址
        private String miniAppIndexUrl;

        // 小程序主页二维码path
        private String miniAppIndexPath;

        // h5表单二维码地址
        private String h5FormUrl;

        // h5表单二维码path
        private String h5FormPath;

        // 小程序表单二维码地址
        private String miniAppFormUrl;

        // 小程序表单二维码path
        private String miniAppFormPath;

        //签到地址的参数
        private Map<String, Object> urlParamMap;
    }

    public Optional<ActivityEntity> getActivityEntityByMarketingEventId(String ea, String marketingEventId) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(marketingEventId)) {
            log.warn("ActivityManager.getActivityEntityByMarketingEventId ea or marketingEventId is null", ea, marketingEventId);
            return Optional.empty();
        }
        return Optional.ofNullable(activityDAO.getActivityEntityByMarketingEventId(ea, marketingEventId));
    }

    /**
     * 设置会议报名来源
     * @param customizeFormDataUserEntity
     * @param qrSourceType
     * @return
     */
    public int buildActivityEnrollDataSourceType(CustomizeFormDataUserEntity customizeFormDataUserEntity, Integer qrSourceType, String qrSourceId) {
        if (qrSourceType != null && StringUtils.isNotBlank(qrSourceId)) {
            return CustomizeFormDataEnrollQrSourceEnum.conversionEnrollSourceTypeToConferenceSourceType(qrSourceType);
        } else if (StringUtils.isNotBlank(customizeFormDataUserEntity.getMarketingActivityId())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                .getByMarketingActivityId(customizeFormDataUserEntity.getMarketingActivityId());
            if (marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 全员营销
                return ConferenceEnrollSourceTypeEnum.FULL_MARKETING.getType();
            } else if (marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 短信营销
                return ConferenceEnrollSourceTypeEnum.SMS_MARKETING.getType();
            } else if (marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 公众号营销
                return ConferenceEnrollSourceTypeEnum.WX_OFFICIAL_ACCOUNTS_MARKETING.getType();
            }
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())) {
            return ConferenceEnrollSourceTypeEnum.MANKEEP.getType();
        }
        return ConferenceEnrollSourceTypeEnum.OTHER.getType();
    }

    /**
     * 根据身份签到活动
     * @return
     */
    public Result<SignInResult> activitySignIn(SignInArg signInArg) {

        String activityId = signInArg.getId();
        String openId = signInArg.getOpenId();
        String wxAppId = signInArg.getWxAppId();
        String fingerPrint = signInArg.getFingerPrint();
//        String ea = signInArg.getEnrollUserEa();
        Integer fsUserId = signInArg.getEnrollUserFsUid();
        String phone = signInArg.getPhone();
        String email = signInArg.getEmail();
        String tagId = signInArg.getTagId();
        Boolean delaySingIn = signInArg.getDelaySingIn();

        SignInResult signInResult = new SignInResult();
        if (StringUtils.isBlank(activityId)) {
            log.warn("ActivityManager.activitySignIn activity id is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        // 活动不存在
        if (activityEntity == null) {
            log.warn("ActivityManager.activitySignIn activityEntity is null activityId:{}", activityId);
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        String ea = activityEntity.getEa();

        // 活动未开始
       /* if (activityEntity.getStartTime().getTime() > new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_START_NOT_ALLOW_SIGN_IN);
        }*/
        // 活动已结束
        if (activityEntity.getEndTime().getTime() < new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_END);
        }

        // 活动停用
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
        }

        // 活动删除
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DELETED);
        }

       /* CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(activityEntity.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity == null) {
            log.warn("ActivityManager.activitySignIn customizeFormDataEntity is null activityEntity:{}", activityEntity);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }*/

        //因为报名后活动成员是异步生成的，如果直接签到会有概率找不到活动成员
        if (BooleanUtils.isTrue(delaySingIn)) {
            signInResult.setIsFirstSignIn(true);
            signInResult.setSignInTime(System.currentTimeMillis());
            signInArg.setFrom("h5");
            delayQueueSender.sendByObj(ea, signInArg, DelayQueueTagConstants.ACTIVITY_SIGN_IN, RocketMqDelayLevelConstants.THIRTY_SECOND);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        }
        //处理邮箱签到问题
        if (StringUtils.isNotBlank(email)) {
            //根据邮箱查询营销用户
            List<ObjectIdWithMarketingUserIdAndPhoneDTO> marketingUserIdAndPhoneDTOS = userMarketingAccountManager.queryMarketingUserDTOByEmail(email, Lists.newArrayList(CrmObjectApiNameEnum.CRM_LEAD.getName(),CrmObjectApiNameEnum.CUSTOMER.getName(),CrmObjectApiNameEnum.CONTACT.getName()), ea);
            String marketingPhone = null;
            //没有查到营销用户,或者查询到多个营销用户,则不进行埋点,营销用户,等依赖手机号的相关操作
            if (CollectionUtils.isNotEmpty(marketingUserIdAndPhoneDTOS) && marketingUserIdAndPhoneDTOS.size() == 1) {
                ObjectIdWithMarketingUserIdAndPhoneDTO marketingUserIdAndPhoneDTO = marketingUserIdAndPhoneDTOS.get(0);
                if (StringUtils.isNotBlank(marketingUserIdAndPhoneDTO.getPhone())) {
                    marketingPhone = marketingUserIdAndPhoneDTO.getPhone();
                }
            }
            //进行邮箱签到
            return signInWithEmail(signInResult,signInArg,activityEntity,tagId,marketingPhone);
        }
        String userPhone = phone;
        //判断手机号是不是只有输入了后六位
        boolean isOnlySix = StringUtils.isNotBlank(userPhone) && userPhone.length() == 6;
        List<String> needSetCampaignId = Lists.newArrayList();
        if (StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(wxAppId)) {
            // 公众号签到
            if (StringUtils.isNotBlank(phone)) {
                //如果只有输入了后六位,则使用手机号leftLike查询,否则使用手机号等于查询
                List<CampaignMergeDataEntity> campaignMergeDataEntityList;
                if (isOnlySix) {
                    campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLeftLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), phone, false);
                    if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
                        log.warn("ActivityManager.activitySignIn isOnlySix customizeFormDataUserEntityList is null openId:{}, wxAppId:{}", openId, wxAppId);
                        return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
                    }
                    //从中取出手机号
                    phone = campaignMergeDataEntityList.get(0).getPhone();
                } else {
                    campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), phone, false);
                }
                needSetCampaignId.addAll(campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList()));
                String finalEa = ea;
                String finalPhone = phone;
                ThreadPoolUtils.executeWithTraceContext(() -> {
                    AssociationArg associationArg = new AssociationArg();
                    associationArg.setEa(finalEa);
                    associationArg.setPhone(finalPhone);
                    associationArg.setWxAppId(wxAppId);
                    associationArg.setAssociationId(openId);
                    associationArg.setType(ChannelEnum.WX_SERVICE.getType());
                    associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
                    associationArg.setTriggerSource("activitySignIn");
                    try {
                        userMarketingAccountAssociationManager.associate(associationArg);
                    } catch (Exception e) {
                        log.warn("ActivityManager activitySignIn error arg:{}", JSON.toJSONString(associationArg), e);
                    }
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            } else {
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndEventId(openId, wxAppId, activityEntity.getMarketingEventId());
                userPhone = CollectionUtils.isNotEmpty(customizeFormDataUserEntityList) ? customizeFormDataUserEntityList.get(0).getSubmitContent().getPhone() : null;
                needSetCampaignId = mergeSamePhoneCampaignIdByEnrollData(activityEntity.getEa(), activityEntity.getMarketingEventId(), customizeFormDataUserEntityList);
            }
            if (CollectionUtils.isEmpty(needSetCampaignId)) {
                log.warn("ActivityManager.activitySignIn customizeFormDataUserEntityList is null openId:{}, wxAppId:{}", openId, wxAppId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityManager.activitySignIn activityEnrollDataEntityList is null needSetCampaignId:{}", needSetCampaignId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_FAIL);
                } else {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
                }
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //处理h5身份问题,导致会继续调签到接口,所以这里再次处理一次打标签
                handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                signInResult.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                signInResult.setIsFirstSignIn(false);
                return new Result<>(SHErrorCode.SUCCESS, signInResult);
            }
            campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            signInResult.setSignInTime(new Date().getTime());
            handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, userPhone, openId, wxAppId, fingerPrint, ea, fsUserId);
            //处理线索打标签
            handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        } else if (StringUtils.isNotBlank(fingerPrint)) {
            if (StringUtils.isNotBlank(phone)) {
                //如果只有输入了后六位,则使用手机号leftLike查询,否则使用手机号等于查询
                List<CampaignMergeDataEntity> campaignMergeDataEntityList;
                if (isOnlySix) {
                    campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLeftLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), phone, false);
                    if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
                        log.warn("ActivityManager.activitySignIn isOnlySix customizeFormDataUserEntityList is null fingerPrint:{}", fingerPrint);
                        return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
                    }
                    //从中取出手机号
                    phone = campaignMergeDataEntityList.get(0).getPhone();
                } else {
                    campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), phone, false);
                }
                needSetCampaignId.addAll(campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList()));
                //当前fingerPrint关联phone
                String finalEa = ea;
                String finalPhone1 = phone;
                ThreadPoolUtils.executeWithTraceContext(() -> {
                    AssociationArg associationArg = new AssociationArg();
                    associationArg.setEa(finalEa);
                    associationArg.setPhone(finalPhone1);
                    associationArg.setAssociationId(fingerPrint);
                    associationArg.setType(ChannelEnum.BROWSER_USER.getType());
                    associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
                    associationArg.setTriggerSource("activitySignIn");
                    try {
                        userMarketingAccountAssociationManager.associate(associationArg);
                    } catch (Exception e) {
                        log.warn("ActivityManager activitySignIn error arg:{}", JSON.toJSONString(associationArg), e);
                    }
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            } else {
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndEventId(fingerPrint, activityEntity.getMarketingEventId());
                userPhone = CollectionUtils.isNotEmpty(customizeFormDataUserEntityList) ? customizeFormDataUserEntityList.get(0).getSubmitContent().getPhone() : null;
                needSetCampaignId = mergeSamePhoneCampaignIdByEnrollData(activityEntity.getEa(), activityEntity.getMarketingEventId(), customizeFormDataUserEntityList);
            }
            if (CollectionUtils.isEmpty(needSetCampaignId)) {
                log.warn("ActivityManager.activitySignIn customizeFormDataUserEntityList is null fingerPrint:{}", fingerPrint);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityManager.activitySignIn activityEnrollDataEntityList is null fingerPrint:{}, needSetCampaignId:{}", fingerPrint, needSetCampaignId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_FAIL);
                } else {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
                }
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //处理h5身份问题,导致会继续调签到接口,所以这里再次处理一次打标签
                handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                signInResult.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                signInResult.setIsFirstSignIn(false);
                return new Result<>(SHErrorCode.SUCCESS, signInResult);
            }
            handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, userPhone, openId, wxAppId, fingerPrint, ea, fsUserId);
            campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            signInResult.setSignInTime(new Date().getTime());
            //处理线索打标签
            handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        } else if (StringUtils.isNotBlank(ea) && fsUserId != null) {
            if (StringUtils.isNotBlank(phone)) {                //如果只有输入了后六位,则使用手机号leftLike查询,否则使用手机号等于查询
                List<CampaignMergeDataEntity> campaignMergeDataEntityList;
                if (isOnlySix) {
                    campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLeftLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), phone, false);
                } else {
                    campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(activityEntity.getEa(), activityEntity.getMarketingEventId(), phone, false);
                }
                needSetCampaignId.addAll(campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList()));
            } else {
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByFsUserInfoAndEventId(ea, fsUserId, activityEntity.getMarketingEventId());
                userPhone = CollectionUtils.isNotEmpty(customizeFormDataUserEntityList) ? customizeFormDataUserEntityList.get(0).getSubmitContent().getPhone() : null;
                needSetCampaignId = mergeSamePhoneCampaignIdByEnrollData(activityEntity.getEa(), activityEntity.getMarketingEventId(), customizeFormDataUserEntityList);
            }
            if (CollectionUtils.isEmpty(needSetCampaignId)) {
                log.warn("ActivityManager.activitySignIn customizeFormDataUserEntityList is null ea:{}, fsUserId:{}", ea, fsUserId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityManager.activitySignIn activityEnrollDataEntityList is null ea:{}, fsUserId:{} needSetCampaignId:{}", ea, fsUserId, needSetCampaignId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_FAIL);
                } else {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
                }
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //处理h5身份问题,导致会继续调签到接口,所以这里再次处理一次打标签
                handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                signInResult.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                signInResult.setIsFirstSignIn(false);
                return new Result<>(SHErrorCode.SUCCESS, signInResult);
            }
            campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            signInResult.setSignInTime(new Date().getTime());
            handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, userPhone, openId, wxAppId, fingerPrint, ea, fsUserId);
            //处理线索打标签
            handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        }
        log.warn("ActivityManager.activitySignIn param error uid:{},openId:{},wxAppId:{},fingerPrint:{}", openId, wxAppId, fingerPrint);
        return new Result<>(SHErrorCode.PARAMS_ERROR);
    }

    public void handleConferenceTag(List<String> campaignIds, String ea, String tagId) {
        if (StringUtils.isBlank(tagId)) {
            return;
        }
        ConferenceTagEntity conferenceTagEntity = conferenceTagDAO.queryById(tagId);
        if (conferenceTagEntity == null) {
            return;
        }
        List<TagName> tagNameList = GsonUtil.getGson().fromJson(conferenceTagEntity.getTags(), new TypeToken<List<TagName>>(){}.getType());
        List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        Map<Integer, List<String>> bindCrmDataMap = campaignMergeDataEntities.stream().filter(d -> d.getBindCrmObjectType() != null).collect(Collectors.groupingBy(CampaignMergeDataEntity::getBindCrmObjectType, Collectors.mapping(CampaignMergeDataEntity::getBindCrmObjectId, Collectors.toList())));
        if (bindCrmDataMap != null) {
            Set<Map.Entry<Integer, List<String>>> entries = bindCrmDataMap.entrySet();
            for (Map.Entry<Integer, List<String>> entry : entries) {
                BatchAddOrDeleteTagNamesToCrmDataArg batchToCrmDataArg = new BatchAddOrDeleteTagNamesToCrmDataArg();
                batchToCrmDataArg.setCrmObjectDescribeApiName(CampaignMergeDataObjectTypeEnum.getApiNameByType(entry.getKey()));
                batchToCrmDataArg.setCrmObjectIds(entry.getValue());
                batchToCrmDataArg.setTagNames(tagNameList);
                Result<Void> batchAddTagNamesToCrmDataResult = userMarketingAccountService.batchAddTagNamesToCrmData(ea, -10000, batchToCrmDataArg);
                if (!batchAddTagNamesToCrmDataResult.isSuccess()) {
                    log.warn("handleConferenceTag batchAddTagNamesToCrmData fail errorCode:{} errorMsg:{} arg:{}", batchAddTagNamesToCrmDataResult.getErrCode(), batchAddTagNamesToCrmDataResult.getErrMsg(), batchToCrmDataArg);
                }
            }
        }
    }

    public Result<SignInResult> signInWithEmail(SignInResult signInResult,SignInArg arg,ActivityEntity activityEntity, String tagId,String phone){
        List<String> needSetCampaignId = Lists.newArrayList();
        String wxAppId = arg.getWxAppId();
        String openId = arg.getOpenId();
        String fingerPrint = arg.getFingerPrint();
        Integer fsUserId = arg.getEnrollUserFsUid();
        String ea = activityEntity.getEa();
        if (StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(wxAppId)) {
            // 公众号签到
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByEmail(ea,arg.getEmail(), activityEntity.getMarketingEventId());
            needSetCampaignId.addAll(customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            if (StringUtils.isNotBlank(phone)) {
                ThreadPoolUtils.executeWithTraceContext(() -> {
                    AssociationArg associationArg = new AssociationArg();
                    associationArg.setEa(ea);
                    associationArg.setPhone(phone);
                    associationArg.setWxAppId(wxAppId);
                    associationArg.setAssociationId(openId);
                    associationArg.setType(ChannelEnum.WX_SERVICE.getType());
                    associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
                    associationArg.setTriggerSource("activitySignInWithEmail");
                    try {
                        userMarketingAccountAssociationManager.associate(associationArg);
                    } catch (Exception e) {
                        log.warn("ActivityManager activitySignIn error arg:{}", JSON.toJSONString(associationArg), e);
                    }
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
            if (CollectionUtils.isEmpty(needSetCampaignId)) {
                log.warn("ActivityManager.signInWithEmail customizeFormDataUserEntityList is null openId:{}, wxAppId:{}", openId, wxAppId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityManager.signInWithEmail activityEnrollDataEntityList is null needSetCampaignId:{}", needSetCampaignId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_FAIL);
                } else {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
                }
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //处理h5身份问题,导致会继续调签到接口,所以这里再次处理一次打标签
                handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                signInResult.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                signInResult.setIsFirstSignIn(false);
                return new Result<>(SHErrorCode.SUCCESS, signInResult);
            }
            campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            signInResult.setSignInTime(new Date().getTime());
            //没有手机号,去掉处理
            if (StringUtils.isNotBlank(phone)) {
                handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, phone, openId, wxAppId, fingerPrint, ea, fsUserId);
            }
            //处理线索打标签
            handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        } else if (StringUtils.isNotBlank(fingerPrint)) {
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByEmail(ea,arg.getEmail(), activityEntity.getMarketingEventId());
            needSetCampaignId.addAll(customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            if (StringUtils.isNotBlank(phone)) {
                ThreadPoolUtils.executeWithTraceContext(() -> {
                    AssociationArg associationArg = new AssociationArg();
                    associationArg.setEa(ea);
                    associationArg.setPhone(phone);
                    associationArg.setAssociationId(fingerPrint);
                    associationArg.setType(ChannelEnum.BROWSER_USER.getType());
                    associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
                    associationArg.setTriggerSource("activitySignInWithEmail");
                    try {
                        userMarketingAccountAssociationManager.associate(associationArg);
                    } catch (Exception e) {
                        log.warn("ActivityManager activitySignIn error arg:{}", JSON.toJSONString(associationArg), e);
                    }
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
            if (CollectionUtils.isEmpty(needSetCampaignId)) {
                log.warn("ActivityManager.signInWithEmail customizeFormDataUserEntityList is null fingerPrint:{}", fingerPrint);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityManager.signInWithEmail activityEnrollDataEntityList is null fingerPrint:{}, needSetCampaignId:{}", fingerPrint, needSetCampaignId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_FAIL);
                } else {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
                }
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //处理h5身份问题,导致会继续调签到接口,所以这里再次处理一次打标签
                handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                signInResult.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                signInResult.setIsFirstSignIn(false);
                return new Result<>(SHErrorCode.SUCCESS, signInResult);
            }
            //没有手机号,去掉处理
            if (StringUtils.isNotBlank(phone)) {
                handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, phone, openId, wxAppId, fingerPrint, ea, fsUserId);
            }
            campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            signInResult.setSignInTime(new Date().getTime());
            //处理线索打标签
            handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        } else if (StringUtils.isNotBlank(ea) && fsUserId != null) {
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByEmail(ea,arg.getEmail(), activityEntity.getMarketingEventId());
            needSetCampaignId.addAll(customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(needSetCampaignId)) {
                log.warn("ActivityManager.signInWithEmail customizeFormDataUserEntityList is null ea:{}, fsUserId:{}", ea, fsUserId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
            if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityManager.signInWithEmail activityEnrollDataEntityList is null ea:{}, fsUserId:{} needSetCampaignId:{}", ea, fsUserId, needSetCampaignId);
                return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_FAIL);
                } else {
                    return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
                }
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //处理h5身份问题,导致会继续调签到接口,所以这里再次处理一次打标签
                handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                signInResult.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                signInResult.setIsFirstSignIn(false);
                return new Result<>(SHErrorCode.SUCCESS, signInResult);
            }
            campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            signInResult.setSignInTime(new Date().getTime());
            //没有手机号,去掉处理
            if (StringUtils.isNotBlank(phone)) {
                handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, phone, openId, wxAppId, fingerPrint, ea, fsUserId);
            }
            //处理线索打标签
            handleConferenceTag(needSetCampaignId,activityEntity.getEa(),tagId);
            return new Result<>(SHErrorCode.SUCCESS, signInResult);
        }
        log.warn("ActivityManager.signInWithEmail param error openId:{}, wxAppId:{}, fingerPrint:{}, fsUserId:{}", openId, wxAppId, fingerPrint, fsUserId);
        return new Result<>(SHErrorCode.PARAMS_ERROR);
    }

    public void handleActivitySignInOtherProcess(List<String> campaignIds, ActivityEntity activityEntity, String enrollPhone, String openId, String wxAppId,
        String fingerPrint, String ea, Integer fsUserId) {
        ThreadPoolUtils.execute(() -> {
            if (StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(wxAppId)) {
                marketingFlowInstanceOuterService.enrollOrCheckInConference(activityEntity.getEa(), wxAppId, openId, enrollPhone, activityEntity.getId(), ConferenceEnrollOrCheckInEnum.CHECK_IN);
            }
            if (StringUtils.isNotBlank(fingerPrint)) {
                marketingFlowInstanceOuterService.enrollOrCheckInConferenceByH5(activityEntity.getEa(), fingerPrint, enrollPhone, activityEntity.getId(), ConferenceEnrollOrCheckInEnum.CHECK_IN);
            }
            // 签到埋点
            sendActivitySignInRecord(activityEntity.getId(), openId, wxAppId, fingerPrint, ea, fsUserId, enrollPhone);
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(campaignIds);
            if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
                for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
                    if (StringUtils.isNotBlank(customizeFormDataUserEntity.getExtraDataId())) {
                        customerCustomizeFormDataManager
                            .updateSignInObject(activityEntity.getEa(), customizeFormDataUserEntityList.get(0).getExtraDataId(), ActivitySignOrEnrollEnum.SIGN_IN.getType());
                    }
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    public List<String> mergeSamePhoneCampaignIdByEnrollData(String ea, String marketingEventId, List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
            return result;
        }
        result.addAll(customizeFormDataUserEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignId())).map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toList()));
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntityList.get(0);
        if (StringUtils.isBlank(customizeFormDataUserEntity.getSubmitContent().getPhone())) {
            return result;
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO
            .getCampaignMergeDataByPhone(ea, marketingEventId, customizeFormDataUserEntity.getSubmitContent().getPhone(), false);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return result;
        }
        result.addAll(campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList()));
        return result;
    }

    /**
     * 根据一个物料获取对应会议绑定的所有物料(包含会议本身)
     * @param objectId
     * @param objectType
     * @param needAddType
     * @return
     */
    public List<String> getActivityLinkObject(String objectId, Integer objectType, List<ObjectTypeEnum> needAddType) {
        //获取会议id
        String activityId = objectId;
        if (ObjectTypeEnum.QR_POSTER.getType() == objectType) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(objectId);
            // 若二维码的跳转类型为邀请函则需获取对应会议
            if (qrPosterEntity.getForwardType().equals(QRPosterForwardTypeEnum.CONFERENCE_INVITATION.getType())) {
                //根据会议邀请函id得到邀请函实体，再得到会议id
                activityId = conferenceInvitationDAO.getInvitationById(qrPosterEntity.getTargetId()).getActivityId();
            } else {
                //跳转类型为会议详情
                activityId = qrPosterEntity.getTargetId();
            }
        } else if (ObjectTypeEnum.ACTIVITY_INVITATION.getType() == objectType) {
            ConferenceInvitationEntity conferenceInvitationEntity =  conferenceInvitationDAO.getInvitationById(objectId);
            activityId = conferenceInvitationEntity.getActivityId();
        }
        //会议详情
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        if (activityEntity == null) {
            log.warn("ActivityManager.getActivityLinkObject activityEntity is null activityId:{}", activityId);
            return Lists.newArrayList();
        }
        //根据会议id得到会议绑定的物料id，物料包含会议本身
        List<String> objectIds = Lists.newArrayList(activityId);
        //物料类型为二维码海报
        if (needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.QR_POSTER.getType())) {
            //根据会议所属企业，市场活动id，海报跳转类型得到所有的海报实体
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.queryByMarketingEventIdAndForwardTypes(activityEntity.getEa(),
                Lists.newArrayList(QRPosterForwardTypeEnum.CONFERENCE_DETAIL.getType(), QRPosterForwardTypeEnum.CONFERENCE_INVITATION.getType()), activityEntity.getMarketingEventId());
            //得到所有海报对应的物料id
            objectIds.addAll(qrPosterEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getId())).map(QRPosterEntity::getId).collect(Collectors.toList()));
        }
        //物料类型为活动邀请函
        if (needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.ACTIVITY_INVITATION.getType())) {
            List<String> invitationIds = conferenceInvitationDAO.getInvitationIdByActivityId(activityId);
            //得到所有活动邀请函对应的物料id
            objectIds.addAll(invitationIds);
        }

        //微页面报名
        if (needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.HEXAGON_SITE.getType())){
            List<ContentMarketingEventMaterialRelationEntity> relationEntities = contentMarketingEventMaterialRelationDAO.listByMarketingEventIdAndObjectType(activityEntity.getEa(), activityEntity.getMarketingEventId(), Lists.newArrayList(ObjectTypeEnum.HEXAGON_SITE.getType()), null);
            if (CollectionUtils.isNotEmpty(relationEntities)){
                Set<String> hexagonPageIdSet = new HashSet<>();
                for (ContentMarketingEventMaterialRelationEntity entity : relationEntities) {
                    List<String> hexagonPageIds = hexagonPageDAO.getPageIdsBySiteId(entity.getObjectId());
                    if (CollectionUtils.isNotEmpty(hexagonPageIds)){
                        hexagonPageIdSet.addAll(hexagonPageIds);
                    }
                }
                objectIds.addAll(hexagonPageIdSet);
            }
        }
        //获取文章, 产品,表单
        if (needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.CUSTOMIZE_FORM.getType())
                || needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.HEXAGON_PAGE.getType())
                || needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.ARTICLE.getType())
                || needAddType.stream().anyMatch(data -> data.getType() == ObjectTypeEnum.PRODUCT.getType())){
            if (CollectionUtils.isNotEmpty(needAddType)) {
                List<Integer> objectTypeList = needAddType.stream().map(objectTypeEnum -> objectTypeEnum.getType()).collect(Collectors.toList());
                List<ContentMarketingEventMaterialRelationEntity> list = contentMarketingEventMaterialRelationDAO.listByMarketingEventIdAndObjectType(activityEntity.getEa(), activityEntity.getMarketingEventId(), objectTypeList, null);
                if (CollectionUtils.isNotEmpty(list)) {
                    List<String> contentObjectIds = list.stream().map(contentMarketingEventMaterialRelationEntity -> contentMarketingEventMaterialRelationEntity.getObjectId()).collect(Collectors.toList());
                    objectIds.addAll(contentObjectIds);
                }
            }
        }

        return objectIds;
    }

    /**
     * 根据物料获取会议id
     * @param objectId
     * @param objectType
     * @return
     */
    public String getActivityIdByObject(String objectId, Integer objectType, String ea, String marketingEventId) {
        if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
            return objectId;
        } else if (ObjectTypeEnum.QR_POSTER.getType() == objectType) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(objectId);
            // 若二维码的跳转类型为邀请函则需获取对应会议
            if (qrPosterEntity.getForwardType().equals(QRPosterForwardTypeEnum.CONFERENCE_INVITATION.getType())) {
               return conferenceInvitationDAO.getInvitationById(qrPosterEntity.getTargetId()).getActivityId();
            } else {
                return qrPosterEntity.getTargetId();
            }
        } else if (ObjectTypeEnum.ACTIVITY_INVITATION.getType() == objectType) {
            ConferenceInvitationEntity conferenceInvitationEntity = conferenceInvitationDAO.getInvitationById(objectId);
            return conferenceInvitationEntity.getActivityId();
        } else if (StringUtils.isNotBlank(ea) && StringUtils.isNotBlank(marketingEventId)) {
            ActivityEntity conference = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
            if (conference != null) {
                return conference.getId();
            }
        }
        return objectId;
    }

    /**
     * 创建会议签到二维码
     * @param activityId
     */
    public ActivityQrCodeContainer createActivitySignInQrCode (String activityId, String ea, String urlPath) {
        ActivityQrCodeContainer activityQrCodeContainer = new ActivityQrCodeContainer();
        Map<String, Object> H5signInQrMap = Maps.newHashMap();
        H5signInQrMap.put("conferenceId", activityId);
        H5signInQrMap.put("byshare", 1); // 该值为前端识别使用
        H5signInQrMap.put("forSignin", true); // 该值为前端识别使用
        String H5signInQrJson = GsonUtil.getGson().toJson(H5signInQrMap);


        Map<String, Object> miniAppSignInMap = Maps.newHashMap();
        miniAppSignInMap.put("activityId", activityId);
        String miniAppSignInQrJson = GsonUtil.getGson().toJson(miniAppSignInMap);

        // 创建小程序签到二维码
        // 现小程序可实时更换二维码需重绘
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.ACTIVITY.getType());
            data.setValue(miniAppSignInQrJson);
            data.setEa(ea);
            data.setMiniappPath(urlPath);
            QRCodeManager.CreateQRCodeResult miniAppQrResult = qrCodeManager.createQRCode(data);
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.ACTIVITY_SIGN_QR_CODE, activityId, miniAppQrResult.getQrCodeApath(),  miniAppQrResult.getQrCodeApath());
            activityQrCodeContainer.setMiniAppSignInUrl(miniAppQrResult.getQrCodeUrl());
            activityQrCodeContainer.setMiniAppSignInPath(miniAppQrResult.getQrCodeApath());
        } catch (Exception e) {
            log.warn("ActivityManager.createActivitySignInQrCode error e:{}", e);
        }

        // 创建H5签到二维码
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.H5_ACTIVITY_SIGN_IN.getType());
            data.setValue(H5signInQrJson);
            data.setEa(ea);
            data.setH5Path(urlPath);
            QRCodeManager.CreateQRCodeResult h5QrResult = qrCodeManager.createQRCode(data);
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.H5_ACTIVITY_SIGN_QR_CODE, activityId, h5QrResult.getQrCodeApath(),  h5QrResult.getQrCodeApath());
            activityQrCodeContainer.setH5SignInUrl(h5QrResult.getQrCodeUrl());
            activityQrCodeContainer.setH5SignInPath(h5QrResult.getQrCodeApath());
        } catch (Exception e) {
            log.warn("ActivityManager.createActivitySignInQrCode error e:{}", e);
        }

        return activityQrCodeContainer;
    }

    /**
     * 创建会议签到二维码(添加标签)
     * @param activityId
     */
    public ActivityQrCodeContainer createActivitySignInQrCodeAddTag (String activityId, String ea, List<TagName> tagNameList, String h5Path, String mimiappPath) {
        ActivityQrCodeContainer activityQrCodeContainer = new ActivityQrCodeContainer();
        Map<String, Object> H5signInQrMap = Maps.newHashMap();
        String tagId = null;
        H5signInQrMap.put("conferenceId", activityId);
        H5signInQrMap.put("byshare", 1); // 该值为前端识别使用
        H5signInQrMap.put("forSignin", true); // 该值为前端识别使用
        if (CollectionUtils.isNotEmpty(tagNameList)) {
            ConferenceTagEntity entity = new ConferenceTagEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setConferenceId(activityId);
            entity.setTags(GsonUtil.getGson().toJson(tagNameList));
            entity.setEa(ea);
            conferenceTagDAO.addConferenceTag(entity);
            tagId = entity.getId();
        }
        if (StringUtils.isNotBlank(tagId)) {
            H5signInQrMap.put("tagId",tagId);
        }
        String H5signInQrJson = GsonUtil.getGson().toJson(H5signInQrMap);

        Map<String, Object> miniAppSignInMap = Maps.newHashMap();
        miniAppSignInMap.put("activityId", activityId);
        if (StringUtils.isNotBlank(tagId)) {
            miniAppSignInMap.put("tagId",tagId);
            if (StringUtils.isNotEmpty(h5Path)) {
                if (h5Path.contains("?")) {
                    h5Path += "&tagId=" + tagId;
                } else {
                    h5Path += "?tagId=" + tagId;
                }
            }
            if (StringUtils.isNotEmpty(mimiappPath)) {
                if (mimiappPath.contains("?")) {
                    mimiappPath += "&tagId=" + tagId;
                } else {
                    mimiappPath += "?tagId=" + tagId;
                }
            }
        }
        String miniAppSignInQrJson = GsonUtil.getGson().toJson(miniAppSignInMap);

        // 创建小程序签到二维码
        // 现小程序可实时更换二维码需重绘
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.ACTIVITY.getType());
            data.setValue(miniAppSignInQrJson);
            data.setEa(ea);
            data.setMiniappPath(mimiappPath);
            QRCodeManager.CreateQRCodeResult miniAppQrResult = qrCodeManager.createQRCode(data);
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.ACTIVITY_SIGN_QR_CODE, activityId, miniAppQrResult.getQrCodeApath(),  miniAppQrResult.getQrCodeApath());
            activityQrCodeContainer.setMiniAppSignInUrl(miniAppQrResult.getQrCodeUrl());
            activityQrCodeContainer.setMiniAppSignInPath(miniAppQrResult.getQrCodeApath());
        } catch (Exception e) {
            log.warn("ActivityManager.createActivitySignInQrCode error e:{}", e);
        }

        // 创建H5签到二维码
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.H5_ACTIVITY_SIGN_IN.getType());
            data.setValue(H5signInQrJson);
            data.setEa(ea);
            data.setH5Path(h5Path);
            QRCodeManager.CreateQRCodeResult h5QrResult = qrCodeManager.createQRCode(data);
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.H5_ACTIVITY_SIGN_QR_CODE, activityId, h5QrResult.getQrCodeApath(),  h5QrResult.getQrCodeApath());
            activityQrCodeContainer.setH5SignInUrl(h5QrResult.getQrCodeUrl());
            activityQrCodeContainer.setH5SignInPath(h5QrResult.getQrCodeApath());
        } catch (Exception e) {
            log.warn("ActivityManager.createActivitySignInQrCode error e:{}", e);
        }

        return activityQrCodeContainer;
    }

    /**
     * 创建会议二维码
     * @param activityId
     * @return
     */
    public ActivityQrCodeContainer createActivityQrCode(String activityId, String ea, String channelValue,  Map<String, String> extraParam) {
        ActivityQrCodeContainer activityQrCodeContainer = new ActivityQrCodeContainer();
        Map<String, Object> map = Maps.newHashMap();
        map.put("conferenceId", activityId);
        map.put("byshare", 1); // 该值为前端识别使用
        if (StringUtils.isNotBlank(channelValue)) {
            map.put("spreadChannel", channelValue);
        }
        if (MapUtils.isNotEmpty(extraParam)) {
            map.putAll(extraParam);
        }

        // H5 会议二维码
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.H5_ACTIVITY_DETAIL.getType());
            data.setValue(GsonUtil.getGson().toJson(map));
            data.setEa(ea);
            QRCodeManager.CreateQRCodeResult h5QrCodeResult = qrCodeManager.createQRCode(data);
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.H5_CONFERENCE_QR_CODE, activityId, h5QrCodeResult.getQrCodeApath(), h5QrCodeResult.getQrCodeApath());
            activityQrCodeContainer.setH5IndexUrl(h5QrCodeResult.getQrCodeUrl());
            activityQrCodeContainer.setH5IndexPath(h5QrCodeResult.getQrCodeApath());
        } catch (Exception e) {
            log.warn("ActivityManager.createActivityQrCode error e:{}", e);
        }

        // 小程序会议二维码
        // 现小程序可实时更换二维码需重绘
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.KIS_ACTIVITY.getType());
            data.setValue(GsonUtil.getGson().toJson(map));
            data.setEa(ea);
            QRCodeManager.CreateQRCodeResult qrCodeResult = qrCodeManager.createQRCode(data);
            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.MINI_APP_CONFERENCE_QR_CODE, activityId, qrCodeResult.getQrCodeApath(), qrCodeResult.getQrCodeApath());
            activityQrCodeContainer.setMiniAppIndexUrl(qrCodeResult.getQrCodeUrl());
            activityQrCodeContainer.setMiniAppIndexPath(qrCodeResult.getQrCodeApath());
        } catch (Exception e) {
            log.warn("ActivityManager.createActivityQrCode error e:{}", e);
        }
        return activityQrCodeContainer;
    }

    /**
     * 创建会议表单二维码
     * @param activityEntity
     * @param formId
     * @return
     */
    public ActivityQrCodeContainer createActivityFormQrCode(ActivityEntity activityEntity, String formId, String channelValue) {
        ActivityQrCodeContainer activityQrCodeContainer = new ActivityQrCodeContainer();
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("objectId", activityEntity.getId());
        jsonMap.put("objectType", ObjectTypeEnum.ACTIVITY.getType());
        jsonMap.put("formId", formId);
        jsonMap.put("marketingEventId", activityEntity.getMarketingEventId());
        jsonMap.put("byShare", 1);
        if (channelValue != null) {
            jsonMap.put("spreadChannel", channelValue);
        }
        String jsonValue = GsonUtil.getGson().toJson(jsonMap);

        String objectId = activityEntity.getId();
        Integer objectType = ObjectTypeEnum.ACTIVITY.getType();
        String marketingEventId = activityEntity.getMarketingEventId();
        // 查询H5二维码
        /*String h5LinkId = this.getQrCodeIdByType(objectId, formId, activityEntity.getMarketingEventId(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE.getType());
        if (StringUtils.isNotBlank(h5LinkId)) {
            PhotoEntity h5PhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE.getType(), h5LinkId);
            if (h5PhotoEntity != null) {
                activityQrCodeContainer.setH5FormPath(h5PhotoEntity.getPath());
                activityQrCodeContainer.setH5FormUrl(h5PhotoEntity.getUrl());
            } else {
                // 创建二维码
                QRCodeManager.CreateQRCodeResult h5qrCodeResult = qrCodeManager
                    .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.H5_CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE, objectId, objectType, formId,
                        marketingEventId, jsonValue, activityEntity.getEa());
                activityQrCodeContainer.setH5FormPath(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeApath() : null);
                activityQrCodeContainer.setH5FormUrl(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeUrl() : null);
            }
        } else {
            // 创建二维码
            QRCodeManager.CreateQRCodeResult h5qrCodeResult = qrCodeManager
                .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.H5_CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE, objectId, objectType, formId, marketingEventId,
                    jsonValue, activityEntity.getEa());
            activityQrCodeContainer.setH5FormPath(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeApath() : null);
            activityQrCodeContainer.setH5FormUrl(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeUrl() : null);
        }*/

        // 创建H5二维码
        try {
            QRCodeManager.CreateQRCodeResult h5qrCodeResult = qrCodeManager
                .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.H5_CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE, objectId, objectType, formId, marketingEventId,
                    jsonValue, activityEntity.getEa());
            activityQrCodeContainer.setH5FormPath(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeApath() : null);
            activityQrCodeContainer.setH5FormUrl(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeUrl() : null);
        } catch (Exception e) {
            log.warn("ActivityManager.createActivityFormQrCode error e:{}", e);
        }

        // 查询小程序二维码
        // 现小程序可实时更换二维码需重绘
        try {
            QRCodeManager.CreateQRCodeResult miniQrCodeResult = qrCodeManager
                .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE, objectId, objectType, formId, marketingEventId,
                    jsonValue, activityEntity.getEa());
            activityQrCodeContainer.setMiniAppFormPath(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeApath() : null);
            activityQrCodeContainer.setMiniAppFormUrl(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeUrl() : null);
        } catch (Exception e) {
            log.warn("ActivityManager.createActivityFormQrCode error e:{}", e);
        }
        /*String miniAppLinkId = this.getQrCodeIdByType(objectId, formId, marketingEventId, PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE.getType());
        if (StringUtils.isNotBlank(miniAppLinkId)) {
            PhotoEntity miniAppPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE.getType(), miniAppLinkId);
            if (miniAppPhotoEntity != null) {
                activityQrCodeContainer.setMiniAppFormPath(miniAppPhotoEntity.getPath());
                activityQrCodeContainer.setMiniAppFormUrl(miniAppPhotoEntity.getUrl());
            } else {
                // 创建二维码
                QRCodeManager.CreateQRCodeResult miniQrCodeResult = qrCodeManager
                    .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE, objectId, objectType, formId, marketingEventId,
                        jsonValue, activityEntity.getEa());
                activityQrCodeContainer.setMiniAppFormPath(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeApath() : null);
                activityQrCodeContainer.setMiniAppFormUrl(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeUrl() : null);
            }
        } else {
            // 创建二维码
            QRCodeManager.CreateQRCodeResult miniQrCodeResult = qrCodeManager
                .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE, objectId, objectType, formId, marketingEventId,
                    jsonValue, activityEntity.getEa());
            activityQrCodeContainer.setMiniAppFormPath(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeApath() : null);
            activityQrCodeContainer.setMiniAppFormUrl(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeUrl() : null);
        }*/
        return activityQrCodeContainer;
    }

    /**
     * 校验人员是否有查询市场活动权限
     */
    public boolean checkViewMarketingEvenObjectAuth(String ea, Integer fsUserId, String marketingEventId) {
        return StringUtils.isBlank(marketingEventId) || crmV2Manager
            .checkPrivilege(ea, fsUserId, marketingEventId, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), CrmAuthConstants.VIEW_AUTH);
    }

    /**
     * 发送签到埋点
     */
    public void sendActivitySignInRecord(String activityId, String openId, String wxAppId, String fingerPrint, String ea, Integer fsUserId, String userPhone) {
        try {
            // 为保证行为记录无身份直接获取至手机号关联
            try {
                if (StringUtils.isNotBlank(userPhone)) {
                    Optional<String> fingerPrintOptional = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, userPhone);
                    fingerPrint = fingerPrintOptional.get();
                }
            } catch (Exception e) {
                log.warn("ActivityManager.sendActivitySignInRecord error e:{}", e);
            }
            RecordActionArg recordActionArg = new RecordActionArg();
            recordActionArg.setActionType(ActionTypeEnum.CONFERENCE_CHECK_IN.getAction());
            recordActionArg.setObjectId(activityId);
            recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
            recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
            recordActionArg.setWxOpenId(openId);
            recordActionArg.setWxAppId(wxAppId);
            recordActionArg.setFingerPrint(fingerPrint);
            recordActionArg.setEa(ea);
            recordActionArg.setFsUserId(fsUserId);
            kisActionService.record(recordActionArg);
        } catch (Exception e) {
            log.warn("ActivityManager.sendActivitySignInRecord error e:{}", e);
        }

    }

    /**
     * 处理市场活动作废后,关联的推广海报状态,sop状态处理
     */
    public void asyncInvalidQrPosterAndSop(String ea,String marketingEventId){
        ThreadPoolUtils.execute(() -> {
            try {
                //更新海报的可用状态
                qrPosterDAO.updateQrPosterStatus(ea,marketingEventId);
                //更新sop状态
                MarketingSceneEntity marketingScene;
                ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
                MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
                if (activityEntity != null) {
                    marketingScene = marketingSceneDao.getMarketingSceneByTargetId(ea, "conference", activityEntity.getId());
                } else if (marketingLiveEntity != null) {
                    marketingScene = marketingSceneDao.getMarketingSceneByTargetId(ea, "live", marketingLiveEntity.getId());
                } else {
                    marketingScene = marketingSceneDao.getMarketingSceneByTargetId(ea, "marketing_event",marketingEventId);
                }
                log.info("asyncInvalidQrPosterAndSop marketingScene:{}",GsonUtil.getGson().toJson(marketingScene));
                if (marketingScene != null) {
                    List<SceneTriggerEntity> sceneTriggerEntities = sceneTriggerDao.listBySceneId(ea, marketingScene.getId());
                    //停止场景触发器
                    sceneTriggerDao.updateSceneTriggerBySceneId(ea,marketingScene.getId(),SceneTriggerLifeStatus.DISABLED.getLifeStatus());
                    //停止对应的定时任务(SceneTriggerTimedTaskCheckJob) 定时型
                    if (CollectionUtils.isNotEmpty(sceneTriggerEntities)) {
                        List<String> sceneTriggerIds = sceneTriggerEntities.stream().map(SceneTriggerEntity::getId).collect(Collectors.toList());
                        sceneTriggerTimedTaskDao.cancelTodoTaskBySceneTriggerId(ea,sceneTriggerIds);
                    }
                    //停止对应的定时任务(TriggerTaskInstanceExecuteJob) 触发型 延迟
                    if (CollectionUtils.isNotEmpty(sceneTriggerEntities)) {
                        List<String> triggerIds = sceneTriggerEntities.stream().map(SceneTriggerEntity::getTriggerId).collect(Collectors.toList());
                        List<String> triggerInstanceIds = triggerInstanceDao.listTriggerInstanceByTriggerId(ea, triggerIds);
                        if (CollectionUtils.isNotEmpty(triggerInstanceIds)) {
                            triggerTaskInstanceDao.cancelTodoTriggerTaskInstanceByTriggerInstanceId(ea,triggerInstanceIds);
                        }
                    }
                }
            }catch (Exception ex){
                log.warn("asyncInvalidQrPosterAndSop fail ,e : ",ex);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

    }

    /**
     * 处理多会场活动新增模板
     */
    public void asyncHexagonSite(String ea,String marketingEventId,List<String> owner){
        ThreadPoolUtils.execute(() -> {
            boolean lock = redisManager.lockMultiEventSystemHexagon(ea, marketingEventId);
            if (!lock) {
                log.warn("asyncHexagonSite lock fail ,e:{} marketingEventId: {}", ea, marketingEventId);
                return;
            }
            try {
                HexagonCopyArg arg =new HexagonCopyArg();
                String lang = marketingEventCommonSettingService.getLang(ea);
                int langType = StringUtils.equals(lang, I18nUtil.ZH_CN) ? 1 : 0;
                if(langType == 1){
                    arg.setId(multipleVenuesHexagonSiteId);
                    arg.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_ACTIVITYMANAGER_1023,I18nUtil.ZH_CN));
                }else {
                    arg.setId(multipleVenuesHexagonSiteIdEn);
                    arg.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_ACTIVITYMANAGER_1023,I18nUtil.EN));
                }
                String hexagonName = arg.getName();
                // 判断该多会场活动是否有预设的微页面模板
                List<ContentMarketingEventMaterialRelationEntity> entityList = contentMarketingEventMaterialRelationDAO.listByMarketingEventIdAndObjectType(ea, marketingEventId, Lists.newArrayList(ObjectTypeEnum.HEXAGON_SITE.getType()), null);
                if (CollectionUtils.isNotEmpty(entityList)) {
                    List<String> hexagonSiteIdList = entityList.stream().map(ContentMarketingEventMaterialRelationEntity::getObjectId).collect(Collectors.toList());
                    List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.listNameByIds(hexagonSiteIdList);
                    if (CollectionUtils.isNotEmpty(hexagonSiteEntityList) && hexagonSiteEntityList.stream().anyMatch(e -> hexagonName.equals(e.getName()))) {
                        log.info("asyncHexagonSite is already have pre hexagon ea: {} marketingEventId: {}", ea, marketingEventId);
                        return;
                    }
                }
                Result<CreateSiteResult> copySiteResult = hexagonService.hexagonCopySite(ea, SuperUserConstants.USER_ID, arg);
                //关联市场活动
                ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setObjectId(copySiteResult.getData().getId());
                entity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                entity.setMarketingEventId(marketingEventId);
                contentMarketingEventMaterialRelationDAO.save(entity);
                //设置为专属微页面
                hexagonSiteDAO.updateHexagonSiteSystemStatusAndCreator(ea, copySiteResult.getData().getId(), true, Integer.valueOf(owner.get(0)));
            } catch (Exception e){
                log.warn("asyncHexagonSite fail ,e : ",e);
            } finally {
                redisManager.unLockMultiEventSystemHexagon(ea, marketingEventId);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

    }

    public Result<CheckSignInStatusResult> checkSignInStatus(CheckSignInStatusArg arg) {
        CheckSignInStatusResult result = new CheckSignInStatusResult();
        ActivityEntity activityEntity = activityDAO.getById(arg.getId());
        result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
        result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
        if (activityEntity == null) {
            log.warn("ActivityServiceImpl.checkSignInStatus activityEntity is null activityId:{}", arg.getId());
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        if (StringUtils.isNotBlank(arg.getOpenId()) && StringUtils.isNotBlank(arg.getWxAppId())) {
            // 公众号身份
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndEventId(arg.getOpenId(), arg.getWxAppId(), activityEntity.getMarketingEventId());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                log.warn("ActivityServiceImpl.checkSignInStatus customizeFormDataUserEntityList is null openId:{}, wxAppId:{}, activityId:{}", arg.getOpenId(), arg.getWxAppId(), activityEntity.getId());
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toList()));
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityServiceImpl.checkSignInStatus activityEnrollDataEntityList is null customizeFormDataUserEntityList:{}", customizeFormDataUserEntityList);
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                //处理存在审核失败的情况
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_FAIL.getType());
                } else {
                    result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_AUDITING.getType());
                }
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_SUCCESS.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //重复添加已签到的标签
                final List<ActivityEnrollDataEntity> finalActivityEnrollDataEntityList = activityEnrollDataEntityList;
                ThreadPoolUtils.execute(() -> {
                    if (Objects.equals(ActivitySignOrEnrollEnum.SIGN_IN.getType(),result.getSignInStatus())) {
                        List<String> campaignIds = finalActivityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null && Objects.equals(data.getSignIn(), ActivitySignOrEnrollEnum.SIGN_IN.getType())).map(
                                ActivityEnrollDataEntity::getFormDataUserId).collect(Collectors.toList());
                        activityManager.handleConferenceTag(campaignIds,activityEntity.getEa(),arg.getTagId());
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                result.setSignInStatus(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                result.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
        } else if (StringUtils.isNotBlank(arg.getFingerPrint())) {
            // 无身份
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndEventId(arg.getFingerPrint(), activityEntity.getMarketingEventId());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                log.warn("ActivityServiceImpl.checkSignInStatus customizeFormDataUserEntityList is null fingerPrint:{}, activityId:{}", arg.getFingerPrint(), activityEntity.getId());
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toList()));
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityServiceImpl.checkSignInStatus activityEnrollDataEntityList is null fingerPrint:{}, customizeFormDataUserEntityList:{}", arg.getFingerPrint(), customizeFormDataUserEntityList);
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_FAIL.getType());
                } else {
                    result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_AUDITING.getType());
                }
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_SUCCESS.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //重复添加已签到的标签
                final List<ActivityEnrollDataEntity> finalActivityEnrollDataEntityList = activityEnrollDataEntityList;
                ThreadPoolUtils.execute(() -> {
                    if (Objects.equals(ActivitySignOrEnrollEnum.SIGN_IN.getType(),result.getSignInStatus())) {
                        List<String> campaignIds = finalActivityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null && Objects.equals(data.getSignIn(), ActivitySignOrEnrollEnum.SIGN_IN.getType())).map(
                                ActivityEnrollDataEntity::getFormDataUserId).collect(Collectors.toList());
                        activityManager.handleConferenceTag(campaignIds,activityEntity.getEa(),arg.getTagId());
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                result.setSignInStatus(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                result.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
        } else if (StringUtils.isNotBlank(arg.getEnrollUserEa()) && arg.getEnrollUserFsUid() != null) {
            // 纷享身份
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO
                    .queryCustomizeFormDataUsersByFsUserInfoAndEventId(arg.getEnrollUserEa(), arg.getEnrollUserFsUid(), activityEntity.getMarketingEventId());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                log.warn("ActivityServiceImpl.checkSignInStatus customizeFormDataUserEntityList is null enrollUserEa:{}, enrollUserFsUid:{}, activityId:{}", arg.getEnrollUserEa(),
                        arg.getEnrollUserFsUid(), activityEntity.getId());
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toList()));
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("ActivityServiceImpl.checkSignInStatus activityEnrollDataEntityList is null enrollUserEa:{}, enrollUserFsUid:{}, customizeFormDataUserEntityList:{}", arg.getEnrollUserEa(),
                        arg.getEnrollUserFsUid(), customizeFormDataUserEntityList);
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_ENROLL_IN.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            // 需所有数据审核通过才能签到
            boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
            if (!reviewResult) {
                List<Integer> reviewStatusList = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getReviewStatus).collect(Collectors.toList());
                if (reviewStatusList.contains(ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus())) {
                    result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_FAIL.getType());
                } else {
                    result.setSignNewStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_AUDITING.getType());
                }
                result.setSignInStatus(ActivitySignOrEnrollEnum.NOT_REVIEW_SUCCESS.getType());
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
            // 若全部状态为已签到则返回已签到
            boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
            if (allSignIn) {
                //重复添加已签到的标签
                final List<ActivityEnrollDataEntity> finalActivityEnrollDataEntityList = activityEnrollDataEntityList;
                ThreadPoolUtils.execute(() -> {
                    if (Objects.equals(ActivitySignOrEnrollEnum.SIGN_IN.getType(),result.getSignInStatus())) {
                        List<String> campaignIds = finalActivityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null && Objects.equals(data.getSignIn(), ActivitySignOrEnrollEnum.SIGN_IN.getType())).map(
                                ActivityEnrollDataEntity::getFormDataUserId).collect(Collectors.toList());
                        activityManager.handleConferenceTag(campaignIds,activityEntity.getEa(),arg.getTagId());
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                result.setSignInStatus(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                result.setSignNewStatus(ActivitySignOrEnrollEnum.SIGN_IN.getType());
                Optional<ActivityEnrollDataEntity> activityEnrollDataEntityOptional = activityEnrollDataEntityList.stream().filter(data -> data.getSignInTime() != null).findFirst();
                result.setSignInTime(activityEnrollDataEntityOptional.isPresent() ? activityEnrollDataEntityOptional.get().getSignInTime().getTime() : null);
                return new Result<>(SHErrorCode.SUCCESS, result);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    public Result<GetEnrollTimeResult> queryActivityEnrollEndTime(QueryActivityEnrollTimeArg arg) {
        GetEnrollTimeResult result = new GetEnrollTimeResult();
        result.setEnrollEnd(false);
        String marketingEventId = arg.getMarketingEventId();
        String objectId = arg.getObjectId();
        Integer objectType = arg.getObjectType();
        //微页面子页面转换成微页面站点
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == objectType) {
            HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getById(objectId);
            if (hexagonPageEntity != null){
                objectType = ObjectTypeEnum.HEXAGON_SITE.getType();
                objectId = hexagonPageEntity.getHexagonSiteId();
            }
        }
        String ea = objectManager.getObjectEa(objectId, objectType);
        ActivityEntity activityEntity = null;
        if (objectType == ObjectTypeEnum.ACTIVITY.getType() || objectType == ObjectTypeEnum.ACTIVITY_INVITATION.getType()) {
            String activityId = activityManager.getActivityIdByObject(objectId, objectType, null, null);
            activityEntity = activityDAO.getById(activityId);
        } else if (StringUtils.isNotBlank(ea) && StringUtils.isNotBlank(marketingEventId)) {
            activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        }
        if (activityEntity != null) {
            if (activityEntity.getEnrollEndTime() != null && isEnrollFromData(ea,marketingEventId,objectId,objectType) && activityEntity.getEnrollEndTime().getTime() < new Date().getTime()) {
                result.setEnrollEnd(true);
                result.setEnrollEndTime(DateUtil.format(activityEntity.getEnrollEndTime()));
                result.setEnrollTips(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_ACTIVITYMANAGER_1196));
                return Result.newSuccess(result);
            }
        }
        //添加直播营销,活动营销判断截止时间
        ActivityEnrollTimeConfigEntity configEntity = activityEnrollTimeConfigDAO.queryEnrollTime(ea, marketingEventId);
        if (configEntity != null) {
            if (configEntity.getStatus() != null && configEntity.getStatus() == ActivityEnrollTimeStatusEnum.ENABLED.getStatus() && isEnrollFromData(ea,marketingEventId,objectId,objectType) && configEntity.getEnrollTime().getTime() < new Date().getTime()) {
                result.setEnrollEnd(true);
                result.setEnrollEndTime(DateUtil.format(configEntity.getEnrollTime()));
                result.setEnrollTips(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_ACTIVITYMANAGER_1196));
                if (StringUtils.isNotBlank(configEntity.getEnrollTip())){
                    result.setEnrollTips(configEntity.getEnrollTip());
                }
                return Result.newSuccess(result);
            }
        }
        return Result.newSuccess(result);
    }

    private boolean isEnrollFromData(String ea, String marketingEventId, String objectId, Integer objectType) {
        //如果是会议活动主页,直接进行默认为报名表单
        if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
            return true;
        }
        //只要是活动营销下的推广内容,都默认为报名表单
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (objectData != null){
            String eventForm = objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName());
            if (StringUtils.isNotBlank(eventForm) && MarketingEventFormEnum.ONLINE_MARKETING.getValue().equals(eventForm)) {
                return true;
            }
        }
        ContentMarketingEventMaterialRelationEntity contentEntity = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(ea, marketingEventId, objectType, objectId);
        return contentEntity != null && contentEntity.getIsApplyObject();
    }

}