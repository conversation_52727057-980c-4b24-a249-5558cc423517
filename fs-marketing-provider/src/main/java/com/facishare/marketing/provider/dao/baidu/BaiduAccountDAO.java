package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created by ranluch on 2019/11/26.
 */
public interface BaiduAccountDAO {
    @Insert("insert into ad_account(\"id\", \"ea\", \"username\", \"password\",  \"status\", \"user_stat\", \"account_id\", \"balance\",\"cost\",\"budget\",\"budget_type\",\"create_time\",\"update_time\",\"token\",\"refresh_token\", \"pc_balance\", \"mobile_balance\", \"source\", \"auth_account_id\", \"qq_account_id\", \"auth_user_name\", \"type\") values("
        + " #{obj.id},\n"
        + " #{obj.ea},\n"
        + " #{obj.username},\n"
        + " #{obj.password},\n"
        + " #{obj.status},\n"
        + " #{obj.userStat},\n"
        + " #{obj.accountId},\n"
        + " #{obj.balance},\n"
        + " #{obj.cost},\n"
        + " #{obj.budget},\n"
        + " #{obj.budgetType},\n"
        + " now(),\n"
        + " now(),\n"
        + " #{obj.token},\n"
        + " #{obj.refreshToken},\n"
        + " #{obj.pcBalance},\n"
        + " #{obj.mobileBalance},\n"
        + " #{obj.source}, \n"
        + " #{obj.authAccountId}, \n"
        + " #{obj.qqAccountId}, \n"
        + " #{obj.authUserName}, \n"
        + " #{obj.type} \n"
        + ") ON CONFLICT DO NOTHING;")
    boolean addAccount(@Param("obj") AdAccountEntity adAccountEntity);

    @Update("<script>"
        + "update ad_account\n"
        + "<set>\n"
            + "<if test=\"obj.username != null\">\n"
                + "\"username\" = #{obj.username},\n"
            + "</if>\n"
            + "<if test=\"obj.password != null\">\n"
                + "\"password\" = #{obj.password},\n"
            + "</if>\n"
            + "<if test=\"obj.userStat != null\">\n"
                + "\"user_stat\" = #{obj.userStat},\n"
            + "</if>\n"
            + "<if test=\"obj.status != null\">\n"
                + "\"status\" = #{obj.status},\n"
            + "</if>\n"
            + "<if test=\"obj.accountId != null\">\n"
                + "\"account_id\" =  #{obj.accountId},\n"
            + "</if>\n"
            + "<if test=\"obj.balance != null\">\n"
                + "\"balance\" =  #{obj.balance},\n"
            + "</if>\n"
            + "<if test=\"obj.pcBalance != null\">\n"
                + "\"pc_balance\" =  #{obj.pcBalance},\n"
            + "</if>\n"
            + "<if test=\"obj.mobileBalance != null\">\n"
                + "\"mobile_balance\" =  #{obj.mobileBalance},\n"
            + "</if>\n"
            + "<if test=\"obj.cost != null\">\n"
                + "\"cost\" =  #{obj.cost},\n"
            + "</if>\n"
            + "<if test=\"obj.budget != null\">\n"
                + "\"budget\" =  #{obj.budget},\n"
            + "</if>\n"
            + "<if test=\"obj.budgetType != null\">\n"
                + "\"budget_type\" =  #{obj.budgetType},\n"
            + "</if>\n"
            + "<if test=\"obj.token != null\">\n"
            + "\"token\" =  #{obj.token},\n"
            + "</if>\n"
            + "<if test=\"obj.refreshToken != null\">\n"
            + "\"refresh_token\" =  #{obj.refreshToken},\n"
            + "</if>\n"
            + "<if test=\"obj.authAccountId != null\">\n"
            + "\"auth_account_id\" =  #{obj.authAccountId},\n"
            + "</if>\n"
            + "<if test=\"obj.authUserName != null\">\n"
            + "\"auth_user_name\" =  #{obj.authUserName},\n"
            + "</if>\n"
            + "<if test=\"obj.type != null\">\n"
            + "\"type\" =  #{obj.type},\n"
            + "</if>"
            + "\"update_time\" = now()"
        + " </set>"
        + "  where \"id\" = #{obj.id}"
        +"</script>")
    boolean updateAccount(@Param("obj") AdAccountEntity adAccountEntity);


    @Update("<script>"
            + "update ad_account\n"
            + "<set>\n"
            + "<if test=\"balance != null\">\n"
            + "\"balance\" =  #{balance},\n"
            + "</if>\n"
            + "\"update_time\" = now()"
            + " </set>"
            + "  where \"id\" = #{id}"
            +"</script>")
    boolean updateBalanceAndCostById(@Param("id") String id, @Param("balance") Double balance);



    @Update("<script>"
            + "update ad_account\n"
            + "<set>\n"
            + "<if test=\"refreshToken != null\">\n"
            + "\"refresh_token\" = #{refreshToken},\n"
            + "</if>"
            + "<if test=\"accessToken != null\">\n"
            + "\"token\" = #{accessToken}\n"
            + "</if>"
            + "</set>"
            + "where \"account_id\" = #{accountId}"
            + "</script>")
    boolean updateRefreshTokenByAccountId(@Param("refreshToken") String refreshToken, @Param("accessToken") String accessToken, @Param("accountId") Long accountId);

    @Update("<script>"
            + "update ad_account\n"
            + "<set>\n"
            + "<if test=\"refreshToken != null\">\n"
            + "\"refresh_token\" = #{refreshToken},\n"
            + "</if>"
            + "<if test=\"accessToken != null\">\n"
            + "\"token\" = #{accessToken}\n"
            + "</if>"
            + "</set>"
            + "where ea = #{ea} and auth_account_id = #{authAccountId}"
            + "</script>")
    boolean updateTokenByAuthAccountId(@Param("ea") String ea, @Param("authAccountId") Long authAccountId, @Param("refreshToken") String refreshToken, @Param("accessToken") String accessToken);


    @Update("<script>"
            + "update ad_account\n"
            + "<set>\n"
            + "<if test=\"refreshToken != null\">\n"
            + "\"refresh_token\" = #{refreshToken},\n"
            + "</if>"
            + "<if test=\"accessToken != null\">\n"
            + "\"token\" = #{accessToken}\n"
            + "</if>"
            + "</set>"
            + "where \"id\" = #{id}"
            + "</script>")
    boolean updateRefreshTokenById(@Param("refreshToken") String refreshToken, @Param("accessToken") String accessToken, @Param("id") String id);


    @Update("<script>"
        + "update ad_account\n"
        +"<set>"
            + "<if test=\"obj.userStat != null\">"
                + "\"user_stat\" = #{obj.userStat},\n"
            + "</if>"
            + "<if test=\"obj.accountId != null\">"
                + "\"account_id\" =  #{obj.accountId},\n"
            + "</if>"
            + "<if test=\"obj.balance != null\">"
                + "\"balance\" =  #{obj.balance},\n"
            + "</if>"
            + "<if test=\"obj.pcBalance != null\">\n"
                + "\"pc_balance\" =  #{obj.pcBalance},\n"
            + "</if>\n"
            + "<if test=\"obj.mobileBalance != null\">\n"
                + "\"mobile_balance\" =  #{obj.mobileBalance},\n"
            + "</if>\n"
            + "<if test=\"obj.cost != null\">"
                + "\"cost\" =  #{obj.cost},\n"
            + "</if>"
            + "<if test=\"obj.source != null\">"
            + "\"source\" =  #{obj.source},\n"
            + "</if>"
            + "<if test=\"obj.budget != null\">"
                + "\"budget\" =  #{obj.budget},\n"
            + "</if>"
            + "<if test=\"obj.budgetType != null\">"
                + "\"budget_type\" =  #{obj.budgetType},\n"
            + "</if>"
            + "\"update_time\" = now()"
         + "</set>"
         + "  where \"id\" = #{obj.id} AND \"source\" = #{obj.source}"
         +"</script>")
    boolean updateAccountRefreshData(@Param("obj") AdAccountEntity adAccountEntity);

    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and account_id=#{accountId} and source=#{source} and status != 99 and status != -1")
    AdAccountEntity queryAccountByEaAndAccountId(@Param("ea") String ea, @Param("accountId") Long accountId, @Param("source") String source);

    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and account_id=#{accountId} and source=#{source} ")
    AdAccountEntity queryAccountWithoutStatus(@Param("ea") String ea, @Param("accountId") Long accountId, @Param("source") String source);

    @Select("SELECT * FROM ad_account WHERE id=#{adAccountId} and status != 99 and status != -1 and status != 1")
    AdAccountEntity queryEnableAccountById(@Param("adAccountId") String adAccountId);

    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and status != 99 and status != -1 and status != 1")
    List<AdAccountEntity> queryEnableAccountByEa(@Param("ea") String ea);

    @Select("<script>" +
            "SELECT * FROM ad_account " +
            "WHERE ea=#{ea} " +
            "AND source=#{source} " +
            "<if test=\"adAccountId != null\">\n" +
            "   AND \"id\" = #{adAccountId} \n" +
            "</if>" +
            "AND status != 99 AND status != -1 " +
            "ORDER BY create_time ASC " +
            "</script>")
    List<AdAccountEntity> queryAccountByEaAndSource(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("source") String source);

    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and source=#{source} and status != 99 and status != -1")
    List<AdAccountEntity> queryAdAccount(@Param("ea") String ea, @Param("source") String source);

    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and status != 99 and status != -1")
    @FilterLog
    List<AdAccountEntity> queryAccountByEa(@Param("ea") String ea);

    @Select("SELECT * FROM ad_account WHERE id=#{id}")
    AdAccountEntity queryAccountById(@Param("id") String id);

    @Select("<script>" +
            "SELECT * FROM ad_account WHERE id = ANY(ARRAY "
            +   "<foreach collection = 'ids' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            +"</script>")
    List<AdAccountEntity> queryAccountByIds(@Param("ids") List<String> ids);

    @Select("SELECT * FROM ad_account WHERE status != 99 and status != -1")
    @FilterLog
    List<AdAccountEntity> getAllAdAccount();

    @Select("SELECT distinct(ea) FROM ad_account where source like '%百度%'")
    List<String> getBaiduAdAccountWithOutStatus();

    @Select("SELECT distinct(ea) FROM ad_account where source like '%巨量引擎%'")
    List<String> getHeadlinesAdAccountWithOutStatus();

    @Update("UPDATE ad_account SET status = #{obj.status} WHERE \"id\" = #{obj.id}")
    int updateAdAccountStatus(@Param("obj") AdAccountEntity adAccountEntity);

    @Update("UPDATE ad_account SET status = #{status} WHERE \"id\" = #{id}")
    int updateAdAccountStatusById(@Param("status") Integer status, @Param("id") String id);

    @Select("SELECT distinct(ea) FROM ad_account")
    List<String> findAllEa();

    @Select("SELECT * FROM ad_account WHERE ea = #{ea} and username = #{username} order by create_time desc")
    List<AdAccountEntity> queryAccountByUsername(@Param("ea") String ea, @Param("username") String username);

    @Select("SELECT * FROM ad_account where ea = #{ea}")
    List<AdAccountEntity> getAllByEa(@Param("ea") String ea);

    @Select("<script>" +
            "SELECT * FROM ad_account where ea = #{ea} and username = #{userName} "
            + "<if test=\"source != null\">"
            + " and \"source\" =  #{source}\n"
            + "</if>"
            +"</script>")
    List<AdAccountEntity> queryByName(@Param("ea") String ea, @Param("userName") String userName, @Param("source") String source);

    @Select("<script>" +
            "SELECT * FROM ad_account WHERE ea = #{ea} and account_id = ANY(ARRAY "
            +   "<foreach collection = 'accountIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            +"</script>")
    List<AdAccountEntity> queryAccountByAccountIdList(@Param("ea") String ea, @Param("accountIdList") List<Long> accountIdList);


    @Select("SELECT * FROM ad_account WHERE ea = #{ea} and auth_account_id = #{authAccountId}")
    List<AdAccountEntity> queryByAuthAccountId(@Param("ea") String ea, @Param("authAccountId") long authAccountId);

    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and account_id=#{accountId} and status != 99 and status != -1")
    AdAccountEntity queryAccountByAccountId(@Param("ea") String ea, @Param("accountId") Long accountId);

    @Select("SELECT * FROM ad_account where ea=#{ea} and account_id=#{accountId} and source=#{source} and status != 99 and status != -1")
    AdAccountEntity queryAccountBySourceAndAccountId(@Param("ea")String ea, @Param("accountId")Long accountId, @Param("source")String source);

    // 获取所有未解绑的本地推账户，status=1为解绑状态
    @Select("SELECT * FROM ad_account WHERE source=#{source} and type=#{type} and status != 99 and status != -1 and status != 1")
    List<AdAccountEntity> queryAllLocalAdAccount(@Param("source") String source, @Param("type")Integer type);

    // 根据EA获取所有本地推账户
    @Select("SELECT * FROM ad_account WHERE ea=#{ea} and source=#{source} and type=#{type} and status != 99 and status != -1 and status != 1")
    List<AdAccountEntity> queryAllLocalAdAccountByEa(@Param("source") String source, @Param("type")Integer type, @Param("ea")String ea);

    @Select("SELECT * FROM ad_account where ea=#{ea} and account_id=#{accountId}")
    AdAccountEntity queryAllStatusAccountByAccountId(@Param("ea")String ea, @Param("accountId")Long accountId);
}
