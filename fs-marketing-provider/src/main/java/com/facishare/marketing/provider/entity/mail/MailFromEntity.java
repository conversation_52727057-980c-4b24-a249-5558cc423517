package com.facishare.marketing.provider.entity.mail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2020/7/2.
 * 邮件发件人实体信息
 */
@Data
public class MailFromEntity implements Serializable{
    private String id;
    private String ea;        //企业帐号
    private String name;      //发件人名称
    private String address;   //发件人地址
    private Integer status;   //0：默认  1：其他
    private Date createTime;  //创建时间
    private Date updateTime;  //更新时间
}
