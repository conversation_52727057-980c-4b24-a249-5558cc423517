package com.facishare.marketing.provider.entity.mail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2020/6/4.
 */
@Data
public class MailSendTaskResultEntity implements Serializable{
    private String id;              //主键
    private String ea;              //企业账号
    private String taskId;          //发送任务id
    private Integer maillistTaskId; //地址列表发送，返回maillistTaskId
    private String mailId;          //xsmtpapi发送，返回每封邮件的mailId
    private String mail;            //邮件地址
    private Integer type;           //发送类型  MailSendTypeEnum
    private Date createTime;        //创建时间
    private Date updateTime;        //更新时间
}
