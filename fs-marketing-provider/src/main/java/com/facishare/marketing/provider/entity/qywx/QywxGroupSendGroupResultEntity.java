package com.facishare.marketing.provider.entity.qywx;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2020/4/21.
 * 保存企业微信客户群消息结果
 */
@Data
public class QywxGroupSendGroupResultEntity implements Serializable{
    private String id;       //主键
    private String ea;       //租户账号
    private String sendid;   //发送消息时设置的id
    private String msgid;    //发送消息后，企业微信返回的id
    private String sender;    //群主id, 指定群主为群消息的发送对象
    private Integer errcode; //发送错误码
    private String errmsg;   //发送错误描述
    private String sendGroupIds;  //发送的群id json
    private int successCount;    //发送成功数量
    private int failedCount;     //发送失败数量
    private int unsendCount;     //未发送数量
    private int totalGroupCount; //总群数量
    private Date createTime; //创建时间
    private Date updateTime; //更新时间
}
