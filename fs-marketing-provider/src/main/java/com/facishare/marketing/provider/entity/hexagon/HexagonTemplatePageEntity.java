package com.facishare.marketing.provider.entity.hexagon;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class HexagonTemplatePageEntity implements Serializable {
    private String id;

    private String ea;

    private String name;

    private String hexagonTemplateSiteId;

    private String formId;

    private String shareTitle;

    private String shareDesc;

    private String sharePicH5Apath;

    private String sharePicMpApath;

    private String content;

    private Integer isHomepage;

    private Integer status;

    private Integer createBy;

    // @JSONField(format = "MMM dd, yyyy h:mm:ss a")
    private Date createTime;

    // @JSONField(format = "MMM dd, yyyy h:mm:ss a")
    private Date updateTime;
}