package com.facishare.marketing.provider.dao.member;

import com.facishare.marketing.provider.dto.CustomizeFormDataUserObjectDTO;
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2020/12/29
 **/
public interface MemberAccessibleCampaignDAO {

    @Select(" SELECT * FROM member_accessible_campaign WHERE ea =  #{ea}  AND marketing_event_id = #{marketingEventId} AND member_id = #{memberId}")
    MemberAccessibleCampaignEntity getMemberAccessibleCampaignData(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("memberId") String memberId);

    @Select(" SELECT * FROM member_accessible_campaign WHERE id = #{id} ")
    MemberAccessibleCampaignEntity getMemberAccessibleCampaignDataById(@Param("id") String id);

    @Insert("INSERT INTO member_accessible_campaign (\n"
        + "        \"id\",\n"
        + "        \"ea\",\n"
        + "        \"uid\",\n"
        + "        \"open_id\",\n"
        + "        \"wx_app_id\",\n"
        + "        \"finger_print\",\n"
        + "        \"marketing_event_id\",\n"
        + "        \"marketing_activity_id\",\n"
        + "        \"object_id\",\n"
        + "        \"object_type\",\n"
        + "        \"bind_crm_object_type\",\n"
        + "        \"bind_crm_object_id\",\n"
        + "        \"campaign_id\",\n"
        + "        \"member_id\",\n"
        + "        \"spread_fs_uid\",\n"
        + "        \"save_crm_status\",\n"
        + "        \"save_crm_error_message\",\n"
        + "        \"channel_value\",\n"
        + "        \"create_time\",\n"
        + "        \"update_time\"\n"
        + "        )VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.ea},\n"
        + "        #{obj.uid},\n"
        + "        #{obj.openId},\n"
        + "        #{obj.wxAppId},\n"
        + "        #{obj.fingerPrint},\n"
        + "        #{obj.marketingEventId},\n"
        + "        #{obj.marketingActivityId},\n"
        + "        #{obj.objectId},\n"
        + "        #{obj.objectType},\n"
        + "        #{obj.bindCrmObjectType},\n"
        + "        #{obj.bindCrmObjectId},\n"
        + "        #{obj.campaignId},\n"
        + "        #{obj.memberId},\n"
        + "        #{obj.spreadFsUid},\n"
        + "        #{obj.saveCrmStatus},\n"
        + "        #{obj.saveCrmErrorMessage},\n"
        + "        #{obj.channelValue},\n"
        + "        now(),\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;")
    int addMemberAccessibleCampaignData(@Param("obj") MemberAccessibleCampaignEntity entity);


    @Update(" UPDATE member_accessible_campaign SET campaign_id = #{campaignId} WHERE id = #{id} ")
    void bindMemberAccessibleCampaignData(@Param("id") String id, @Param("campaignId") String campaignId);

    @Delete(" DELETE FROM member_accessible_campaign WHERE campaign_id = #{campaignId}")
    void deleteMemberAccessibleCampaignDataByCampaignId(@Param("campaignId") String campaignId);

    @Select("<script>"
        + " SELECT * FROM member_accessible_campaign WHERE save_crm_status != 0 AND campaign_id IN "
        + "<foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<MemberAccessibleCampaignEntity> queryMemberAccessibleCampaignSaveErrorData(@Param("campaignIds") List<String> campaignIds);

    @Select(" SELECT * FROM member_accessible_campaign WHERE campaign_id = #{campaignId} ")
    List<MemberAccessibleCampaignEntity> queryMemberAccessibleCampaignByCampaignId(@Param("campaignId") String campaignId);

    @Update(" UPDATE member_accessible_campaign SET save_crm_status = #{saveCrmStatus}, save_crm_error_message = #{saveCrmErrorMessage}, bind_crm_object_type = #{bindCrmObjectType}, bind_crm_object_id = #{bindCrmObjectId} WHERE id = #{id}")
    void updateMemberAccessibleCampaign(@Param("saveCrmStatus") Integer saveCrmStatus, @Param("saveCrmErrorMessage") String saveCrmErrorMessage, @Param("bindCrmObjectType") Integer bindCrmObjectType, @Param("bindCrmObjectId") String bindCrmObjectId, @Param("id") String id);

    @Select("<script>"
        + "SELECT * FROM "
        + "(SELECT *, row_number() over(PARTITION BY t1.campaign_id ORDER BY create_time DESC) AS ROW FROM\n"
        + "(\n"
        + "SELECT campaign_id,member_id,create_time FROM member_accessible_campaign WHERE campaign_id IN\n"
        + "<foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "ORDER BY create_time DESC\n"
        + ") AS t1 "
        + ") AS t2 WHERE t2.ROW = 1 "
        + "</script>")
    List<MemberAccessibleCampaignEntity> getLatestAccessibleMemberByCampaignIds(@Param("campaignIds") List<String> campaignIds);

    @Select("<script> " +
            "  SELECT * FROM member_accessible_campaign WHERE ea = #{ea} AND campaign_id IN " +
            "  <foreach collection = 'campaignIds' item = 'item' open = '(' separator = ',' close = ')'> #{item} </foreach> " +
            "  <if test = 'wxAppId != null'> AND wx_app_id = #{wxAppId} </if>" +
            "</script>")
    List<MemberAccessibleCampaignEntity> listMemberAccessibleCampaignEntitiesByEaAndCampaignIds(@Param("ea") String ea, @Param("campaignIds") Collection<String> campaignIds, @Param("wxAppId") String wxAppId);

    @Select("<script>"
            + "SELECT marketing_event_id AS marketingEventId, object_type AS objectType, object_id AS objectId, create_time AS submitTime FROM member_accessible_campaign WHERE member_id=#{memberId} AND  marketing_event_id IN\n"
            + "<foreach collection = 'marketingEventIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CustomizeFormDataUserObjectDTO> getByMarketingEventIds(@Param("marketingEventIds")List<String> marketingEventIds, @Param("memberId")String memberId);

    @Select("<script>" +
            "SELECT campaign_members_obj_id FROM campaign_merge_data WHERE ea=#{ea} AND  marketing_event_id = #{marketingEventId} and phone like CONCAT('%',#{phone},'%') limit 1 " +
            "</script>")
    String getCampaignIdByPhone(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("phone") String phone);
}
