package com.facishare.marketing.provider.entity.hexagon;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class HexagonTemplateSiteEntity implements Serializable {
    private String id;

    private String name;

    private Integer type;

    private String ea;

    private String coverApath;

    private Integer category;

    private String content;

    private Integer status;

    private Long useCount;

    private Integer index;

    private Integer createBy;

    private Date createTime;

    private Date updateTime;

    private Integer isDefault;

    private Integer defaultLang;  //预置模板语言类型 0：中文  1：英文
}