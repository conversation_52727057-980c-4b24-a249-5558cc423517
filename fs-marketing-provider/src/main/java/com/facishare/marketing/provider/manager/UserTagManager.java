/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.usermarketingaccounttag.FirstTag;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.TagModelSceneConstants;
import com.facishare.marketing.common.contstant.UserTagConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.entity.MarketingUserGroupCustomizeObjectMappingEntity;
import com.facishare.marketing.provider.entity.TagModelEntity;
import com.facishare.marketing.provider.entity.UserTagEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupDepartmentRelationEntity;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.MetadataTagData;
import com.fxiaoke.crmrestapi.common.data.MetadataTagGroupData;
import com.fxiaoke.crmrestapi.common.data.TagPage;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.service.MetadataTagService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-10-29
 */
@Service
@Slf4j
public class UserTagManager {
    @Autowired
    private UserTagDao userTagDao;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @Autowired
    private TagModelDao tagModelDao;
    @Autowired
    private TagModelUserTagRelationDao tagModelUserTagRelationDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao marketingUserGroupCustomizeObjectMappingDao;
    @Autowired
    private MetadataTagService metadataTagService;
    private final String CRM_SYNC_TAG_MODEL_NAME = "CRM历史标签";
    @ReloadableProperty("init_tag_model_list")
    private String initTagModelList;
    @ReloadableProperty("init_tag_model_list_en")
    private String initTagModelListEN;
    @Autowired
    private ObjectGroupDepartmentRelationDAO objectGroupDepartmentRelationDAO;
    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;
    /**
     * 根据标签名称来获取或者创建标签，如果只传入firstTagName,则表示处理一级标签
     * @param firstTagName: 一级标签名称
     * @return 标签id
     */
    public String getOrCreateFirstTag(String ea, Integer fsUserId, String firstTagName, Integer sourceType){
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(firstTagName));
        UserTagEntity firstTag = userTagDao.getByTagName(ea, UserTagConstants.NO_PARENT_TAG_ID, firstTagName, sourceType);
        if(firstTag == null){
            userTagDao.insertTagIgnore(UUIDUtil.getUUID(), ea, fsUserId, UserTagConstants.NO_PARENT_TAG_ID, UserTagConstants.FIRST_GRADE, firstTagName, sourceType);
            firstTag = userTagDao.getByTagName(ea, UserTagConstants.NO_PARENT_TAG_ID, firstTagName, sourceType);
        }
        return firstTag.getId();
    }

    public String getOrCreateSecondTag(String ea, Integer fsUserId, String parentTagId, String tagName, Integer sourceType) {
        UserTagEntity parentTag = userTagDao.getTagById(ea, parentTagId);
        Preconditions.checkArgument(parentTag.getGrade() == UserTagConstants.FIRST_GRADE);
        String tagId = UUIDUtil.getUUID();
        userTagDao.insertTagIgnore(tagId, ea, fsUserId, parentTagId, UserTagConstants.SECOND_GRADE, tagName, sourceType);
        UserTagEntity newUserTag = userTagDao.getByTagName(ea, parentTagId, tagName, sourceType);
        return newUserTag.getId();
    }

    public List<String> getOrCreateSecondTags(String ea, Integer fsUserId, String parentTagId, List<String> tagNames, Integer sourceType){
        return tagNames.stream().map(tagName -> getOrCreateSecondTag(ea, fsUserId, parentTagId, tagName, sourceType)).collect(Collectors.toList());
    }

    public List<TagName> listBySecondGradeTagName(String ea, Integer fsUserId, List<String> tagNames) {
        if (CollectionUtils.isEmpty(tagNames)) {
            return null;
        }
        List<UserTagEntity> secondGradeTagEntities = userTagDao.listByTagNames(ea, UserTagConstants.SECOND_GRADE, tagNames);
        if (CollectionUtils.isEmpty(secondGradeTagEntities)) {
            return null;
        }
        List<String> tagGroupIds = secondGradeTagEntities.stream().map(UserTagEntity::getParentTagId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagGroupIds)) {
            return null;
        }
        List<UserTagEntity> firstGradeTagEntities = userTagDao.batchGet(ea, tagGroupIds);
        Map<String, String> firstGradeTagIdAndNameMap = firstGradeTagEntities.stream().collect(Collectors.toMap(UserTagEntity::getId, UserTagEntity::getName, (v1, v2) -> v1));
        return secondGradeTagEntities.stream().filter(val -> StringUtils.isNotEmpty(firstGradeTagIdAndNameMap.get(val.getParentTagId())))
            .map(val -> new TagName(firstGradeTagIdAndNameMap.get(val.getParentTagId()), val.getName())).collect(Collectors.toList());
    }

    public void addTagByTagGroupNameAndTagName(String ea, Integer fsUserId, String tagGroupName, String tagName) {
        if (fsUserId == null) {
            fsUserId = SuperUserConstants.USER_ID;
        }
        String tagGroupId = this.getOrCreateFirstTag(ea, fsUserId, tagGroupName, TagModelSourceTypeEnum.USER.getValue());
        if (StringUtils.isNotEmpty(tagGroupId)) {
            UserTagEntity userTag = this.getByTagName(ea, tagGroupId, tagName);
            if (userTag == null) {
                this.getOrCreateSecondTag(ea, fsUserId, tagGroupId, tagName, TagModelSourceTypeEnum.USER.getValue());
            }
        }
    }

    public void initEnterpriseTagModel(String ea){
//        String finalTagModelList = I18nUtil.getSuitedLangText(initTagModelList, initTagModelListEN);
        String finalTagModelList = null;
        String lang = marketingEventCommonSettingService.getLang(ea);
        int langType = StringUtils.equals(lang, I18nUtil.ZH_CN) ? 1 : 0;
        if(langType == 1){
            finalTagModelList = initTagModelList;
        }else {
            finalTagModelList = initTagModelListEN;
        }
        List<InitEnterpriseTagMode> initEnterpriseTagModes = GsonUtil.fromJson(finalTagModelList, new TypeToken<List<InitEnterpriseTagMode>>(){}.getType());
        for (InitEnterpriseTagMode initEnterpriseTagMode : initEnterpriseTagModes) {
            try {
                createTagModelAndTags(ea, -10000, initEnterpriseTagMode.getTagModelName(), initEnterpriseTagMode.getTagModelType(), "_ALL_", initEnterpriseTagMode.getSceneId(),
                        initEnterpriseTagMode.getParentTagSelectable(), initEnterpriseTagMode.getMaxGrade(), initEnterpriseTagMode.getDescription(),
                        initEnterpriseTagMode.getTags(), null, null, "_ALL_", TagModelSourceTypeEnum.USER.getValue());
            }catch (Exception e){
                log.warn("Error at initEnterpriseTagModel, ea:{}, data:{}, msg:{}", ea, initEnterpriseTagMode, e.getMessage());
            }
        }
    }

    @Data
    private static final class InitEnterpriseTagMode{
        private String tagModelName;
        private String tagModelType;
        private String sceneId;
        private Integer maxGrade;
        private Boolean parentTagSelectable;
        private String description;
        private List<FirstTag> tags;
    }

    /**
     * 创建模型和标签
     *
     * @param ea
     * @param fsUserId
     * @param tagModelName
     * @param tagModelType
     * @param roles               模型适用角色 _ALL_表示所有角色适用，多个角色使用[,]隔开
     * @param sceneId
     * @param parentTagSelectable
     * @param maxGrade
     * @param description
     * @param firstTags
     * @param tagModelTemplateId
     * @param objects
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String createTagModelAndTags(String ea, Integer fsUserId, String tagModelName, String tagModelType, String roles, String sceneId, boolean parentTagSelectable, Integer maxGrade, String description,
                                        List<FirstTag> firstTags, String tagModelTemplateId, List<Integer> departmentIdList, String objects, Integer sourceType) {
        // 创建模型
        String tagModelId = createTagModel(ea, fsUserId, tagModelName, tagModelType, sceneId, maxGrade, parentTagSelectable, description, tagModelTemplateId, roles, objects, sourceType);

        if(firstTags != null){
            List<NestedId> nestedIds = new LinkedList<>();
            for (FirstTag firstTag : firstTags) {
                // 创建用户标签
                String firstTagId = getOrCreateFirstTag(ea, fsUserId, firstTag.getName(), TagModelSourceTypeEnum.USER.getValue());
                NestedId nestedId = new NestedId(firstTagId);
                nestedIds.add(nestedId);
                // 创建模型用户标签关联
                int parentInserted = tagModelUserTagRelationDao.insertIgnore(ea, tagModelId, UserTagConstants.NO_PARENT_TAG_ID, firstTagId, fsUserId, firstTag.getExclusive());
                if(parentInserted > 0 && firstTag.getSubTags() != null && !firstTag.getSubTags().isEmpty()){
                    // 创建子用户标签
                    List<String> secondTagIds = getOrCreateSecondTags(ea, fsUserId, firstTagId, firstTag.getSubTags(), TagModelSourceTypeEnum.USER.getValue());
                    // 创建子模型用户标签关联
                    tagModelUserTagRelationDao.batchInsertIgnore(ea, tagModelId, firstTagId, secondTagIds, fsUserId);
                    nestedId.pushSubIds(secondTagIds);
                }
            }
            displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getTagDisplayKey(tagModelId), nestedIds);
        }
        if (CollectionUtils.isNotEmpty(departmentIdList)) {
            List<ObjectGroupDepartmentRelationEntity> departmentRelationEntities = new ArrayList<>();
            for (Integer departmentId : departmentIdList) {
                ObjectGroupDepartmentRelationEntity entity = new ObjectGroupDepartmentRelationEntity();
                entity.setDepartmentId(departmentId);
                entity.setGroupId(tagModelId);
                entity.setEa(ea);
                entity.setId(UUIDUtil.getUUID());
                entity.setObjectType(ObjectTypeEnum.TAG_MODEL.getType());
                departmentRelationEntities.add(entity);
            }
            objectGroupDepartmentRelationDAO.batchInsert(departmentRelationEntities);
        }
        return tagModelId;
    }

    public String getOrCreateTagModelBySceneId(String ea, String sceneId, String defaultName, String description, String type, int maxGrade, boolean parentTagSelectable){
        for (int i = 1; i < 100; i++) {
            TagModelEntity tagModel = tagModelDao.getByEaAndSceneId(ea, sceneId);
            if(tagModel != null){
                return tagModel.getId();
            }
            String tagModelName = i == 1 ? defaultName : defaultName + i;
            try {
                return createTagModel(ea, -10000, tagModelName, type, sceneId, maxGrade, parentTagSelectable, description, null, "_ALL_", "_ALL_", TagModelSourceTypeEnum.USER.getValue());
            }catch (Exception e){
                log.warn("exception at create tagModel, ea:{}, sceneId:{}, defaultName:{}", ea, sceneId, tagModelName);
            }
        }
        throw new RuntimeException();
    }

    public String createTagModel(String ea, Integer fsUserId, String tagModelName, String tagModelType, String sceneId, Integer maxGrade, boolean parentTagSelectable, String description,
                                 String tagModelTemplateId, String roles, String objects, Integer sourceType) {
        String tagModelId = UUIDUtil.getUUID();
        TagModelEntity tagModelToInsert = new TagModelEntity();
        tagModelToInsert.setId(tagModelId);
        tagModelToInsert.setEa(ea);
        tagModelToInsert.setName(tagModelName);
        tagModelToInsert.setType(tagModelType);
        tagModelToInsert.setDescription(description);
        tagModelToInsert.setSceneId(sceneId);
        tagModelToInsert.setMaxGrade(maxGrade);
        tagModelToInsert.setState(StateEnum.ENABLED.getState());
        tagModelToInsert.setParentTagSelectable(parentTagSelectable);
        tagModelToInsert.setCreator(fsUserId);
        tagModelToInsert.setTemplateId(tagModelTemplateId);
        tagModelToInsert.setRoles(roles);
        tagModelToInsert.setObjects(objects);
        tagModelToInsert.setSourceType(sourceType);
        int insertCount = tagModelDao.insertIgnore(tagModelToInsert);
        Preconditions.checkArgument(insertCount == 1, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_USERTAGMANAGER_252));
        String displayKey = DisplayOrderConstants.TAG_MODEL_DISPLAY_KEY;
        if (Objects.equals(sourceType, TagModelSourceTypeEnum.MATERIAL.getValue())) {
            displayKey = DisplayOrderConstants.TAG_MODEL_MATERIAL_DISPLAY_KEY;
        }
        displayOrderManager.mergeNestedIdToLast(ea, displayKey, NestedIdList.buildFromIdList(ImmutableList.of(tagModelId)));
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getTagDisplayKey(tagModelId), new ArrayList<>(0));
        return tagModelId;
    }

    /**
     * 根据名称查询标签
     * @param ea
     * @param parentTagId
     * @param name
     * @return
     */
    public UserTagEntity getByTagName(String ea, String parentTagId, String name){
        return userTagDao.getByTagName(ea, parentTagId, name, TagModelSourceTypeEnum.USER.getValue());
    }
}