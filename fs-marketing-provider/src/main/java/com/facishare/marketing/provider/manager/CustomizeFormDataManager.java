package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.result.BaseUserInfoResult;
import com.facishare.marketing.api.arg.GetCustomizeFormDataByIdArg;
import com.facishare.marketing.api.arg.customizeFormData.FormDataUserArgContainer;
import com.facishare.marketing.api.data.WeChatAvatarData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.clueManagement.GetClueManagementSettingResult;
import com.facishare.marketing.api.result.CustomizeFormDataDetailResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.QueryFormUserDataResult;
import com.facishare.marketing.api.service.*;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.contstant.pay.PayOrderState;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMembersSaveStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.hexagon.SaveCrmObjectTypeEnum;
import com.facishare.marketing.common.enums.leads.*;
import com.facishare.marketing.common.enums.qr.QRCodeTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.arg.result.AssociateCrmLeadModel;
import com.facishare.marketing.outapi.arg.result.AssociateWxServiceModel.AssociateWxServiceArg;
import com.facishare.marketing.outapi.arg.result.AssociateWxServiceModel.AssociateWxServiceResult;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService;
import com.facishare.marketing.outapi.service.WxServiceMarketingAccountAssociationService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.manager.CustomizeFormDataDAOManager;
import com.facishare.marketing.provider.dao.pay.FsPayOrderDao;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendTaskDAO;
import com.facishare.marketing.provider.dto.CreateFormDataDTO;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.dto.FormBindObjectStatisticsDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.kis.BranchSearchFormBindObjectStatisticsDTO;
import com.facishare.marketing.provider.dto.kis.BranchSearchFormBindObjectStatisticsDTO.StatisticsMap;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.entity.landing.LandingObjCustomizeUserRelation;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.pay.FsPayOrder;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerData.FsCrmLeadSpecialField;
import com.facishare.marketing.provider.innerResult.UserRelationPartnerInfo;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.innerResult.crm.CreateObjResult;
import com.facishare.marketing.provider.manager.FileV2Manager.FileManagerPicResult;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.baidu.UtmDataManger;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.landing.LandingObjCustomizeUserRelationManager;
import com.facishare.marketing.provider.manager.pay.FsPayOrderManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.base.CrmDuplicateSearchVo;
import com.facishare.marketing.provider.service.marketingplugin.DataPermissionPluginService;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.wechat.union.core.api.model.result.WechatFanDetailResult;
import com.facishare.wechat.union.core.api.service.WechatFanService;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.result.*;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Created  By zhoux 2019/04/04
 * @IgnoreI18nFile
 **/
@Service
@Slf4j
public class CustomizeFormDataManager {

    @Autowired
    private FsMessageManager fsMessageManager;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private NoticeDAO noticeDAO;

    @Autowired
    private ProductDAO productDAO;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;

    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private CrmV2MappingManager crmV2MappingManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private KmUserManager kmUserManager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private WxServiceMarketingAccountAssociationService wxServiceMarketingAccountAssociationService;

    @Autowired
    private CrmLeadMarketingAccountAssociationService crmLeadMarketingAccountAssociationService;

    @Autowired
    private WechatFanService wechatFanService;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private QRCodeManager qrCodeManager;

    @Autowired
    private PhotoAssociationDAO photoAssociationDAO;

    @Autowired
    private ObjectTagManager objectTagManager;

    @Autowired
    private QywxGroupSendTaskDAO groupSendTaskDAO;

    private List<String> addLeadsObjectAuth = ImmutableList.of("2005", "LeadsObj||Add");

    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CustomizeFormDataDAOManager customizeFormDataDAOManager;

    @Autowired
    private FsPayOrderManager fsPayOrderManager;

    @Autowired
    private CustomerCustomizeFormDataManager customerCustomizeFormDataManager;

    @Autowired
    private AreaManager areaManager;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private CustomizeTicketManager customizeTicketManager;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private MemberManager memberManager;

    @Autowired
    private SafetyManagementManager safetyManagementManager;

    @Autowired
    private ClueManagementManager clueManagementManager;

    @Autowired
    private UtmDataManger utmDataManger;

    @Autowired
    private ExecuteTaskDetailManager executeTaskDetailManager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private CampaignMergeDataResetManager campaignMergeDataResetManager;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;

    @Autowired
    private SaveClueFailNoticeConfigDAO saveClueFailNoticeConfigDAO;

    @Autowired
    private NoticeService noticeService;

    @Autowired
    private FsBindService fsBindService;

    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;

    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;

    @Autowired
    private FsPayOrderDao fsPayOrderDao;

    @Autowired
    private LandingObjCustomizeUserRelationManager landingObjCustomizeUserRelationManager;

    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private DataPermissionPluginService dataPermissionPluginService;

    @Autowired
    private ClueManagementService clueManagementService;

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @ReloadableProperty("need_check_enroll_field_ea")
    private String needCheckEnrollFieldEa;

    @Autowired
    private UserRelationManager userRelationManager;

    private Gson gson = new Gson();
    @Data
    public class CustomizeFormQrCodeContainer implements Serializable {

        // h5表单二维码地址
        private String h5Url;

        // h5表单path
        private String h5Path;

        // 小程序url
        private String miniAppUrl;

        // 小程序path
        private String miniAppPath;

    }

    /**
     * 创建表单数据
     */
    public String createFormData(CreateFormDataDTO createFormDataDTO) {
        String formId = UUIDUtil.getUUID();
        CustomizeFormDataEntity customizeFormDataEntity = BeanUtil.copy(createFormDataDTO, CustomizeFormDataEntity.class);
        List<FieldMappings.FieldMapping> fieldMappings = BeanUtil.copy(createFormDataDTO.getCrmFormFieldMap(), FieldMappings.FieldMapping.class);
        FormFootSetting formFootSetting = BeanUtil.copy(createFormDataDTO.getFormFootSetting(), FormFootSetting.class);
        if (formFootSetting == null) {
            formFootSetting = new FormFootSetting();
            formFootSetting.setButtonName(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_1090));
            formFootSetting.setFontColor("#FFFFFF");
            formFootSetting.setButtonColor("#F8B05B");
            formFootSetting.setButtonBorderColor("#F8B05B");
        }
        customizeFormDataEntity.setFormFootSetting(formFootSetting);
        if (customizeFormDataEntity.getFormHeadSetting() != null && CollectionUtils.isNotEmpty(customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath())) {
            List<String> headPhotoPath = customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath();
            customizeFormDataEntity.getFormHeadSetting().setHeadPhotoPath(convertTaPathToApath(headPhotoPath, createFormDataDTO.getEa(), createFormDataDTO.getFsUserId()));
        }
        //不需要单独判断,前面调用的地方有处理crmApiName字段
//        if (customizeFormDataEntity.getFormMoreSetting().isSynchronousCRM()) {
//            customizeFormDataEntity.setCrmApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
//        }
        customizeFormDataEntity.setCrmFormFieldMapV2(FieldMappings.newInstance(fieldMappings));
        customizeFormDataEntity.setCreateBy(createFormDataDTO.getFsUserId());
        customizeFormDataEntity.setUpdateBy(createFormDataDTO.getFsUserId());
        customizeFormDataEntity.setType(createFormDataDTO.getType() != null ? createFormDataDTO.getType() : CustomizeFormDataTypeEnum.CUSTOMIZE.getValue());
        customizeFormDataEntity.setStatus(CustomizeFormDataStatusEnum.NORMAL.getValue());
        customizeFormDataEntity.setId(formId);
        customizeFormDataEntity.setContentStyle(createFormDataDTO.getContentStyle());
        int result = customizeFormDataDAOManager.insertCustomizeFormData(customizeFormDataEntity);
        return result == 1 ? formId : null;
    }

    public Map<String, Integer> batchGetFormUsageByFormIds(Collection<String> formIds){
        List<CustomizeFormDataEntity> customizeFormDataEntities =customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(new ArrayList<>(formIds));
        return customizeFormDataEntities.stream().collect(Collectors.toMap(CustomizeFormDataEntity::getId, customizeFormDataEntity -> Optional.ofNullable(customizeFormDataEntity.getFormUsage()).orElse(1)));
    }

    /**
     * 创建表单二维码
     */
    public CustomizeFormQrCodeContainer createCustomizeFormDataQRCode(String formId, String marketingEventId, String ea) {
        CustomizeFormQrCodeContainer customizeFormQrCodeContainer = new CustomizeFormQrCodeContainer();
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("objectId", formId);
        jsonMap.put("objectType", ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        jsonMap.put("formId", formId);
        jsonMap.put("marketingEventId", marketingEventId);
        jsonMap.put("byShare", 1);
        String jsonValue = GsonUtil.getGson().toJson(jsonMap);

        // 查询H5二维码
        String h5LinkId = this.getQrCodeIdByType(formId, marketingEventId, PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE.getType());
        if (StringUtils.isNotBlank(h5LinkId)) {
            PhotoEntity h5PhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE.getType(), h5LinkId,ea);
            if (h5PhotoEntity != null) {
                customizeFormQrCodeContainer.setH5Path(h5PhotoEntity.getPath());
                customizeFormQrCodeContainer.setH5Url(h5PhotoEntity.getUrl());
            } else {
                // 创建二维码
                QRCodeManager.CreateQRCodeResult h5qrCodeResult = qrCodeManager
                    .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.H5_CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE, formId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId, marketingEventId, jsonValue, ea);
                customizeFormQrCodeContainer.setH5Path(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeApath() : null);
                customizeFormQrCodeContainer.setH5Url(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeUrl() : null);
            }
        } else {
            // 创建二维码
            QRCodeManager.CreateQRCodeResult h5qrCodeResult = qrCodeManager
                .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.H5_CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_H5_QR_CODE, formId,  ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId, marketingEventId, jsonValue, ea);
            customizeFormQrCodeContainer.setH5Path(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeApath() : null);
            customizeFormQrCodeContainer.setH5Url(h5qrCodeResult != null ? h5qrCodeResult.getQrCodeUrl() : null);
        }

        // 创建二维码
        // 现小程序可实时更换二维码需重绘
        QRCodeManager.CreateQRCodeResult miniQrCodeResult = qrCodeManager
            .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE, formId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId, marketingEventId, jsonValue, ea);
        customizeFormQrCodeContainer.setMiniAppPath(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeApath() : null);
        customizeFormQrCodeContainer.setMiniAppUrl(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeUrl() : null);
        /*String miniAppLinkId = this.getQrCodeIdByType(formId, marketingEventId, PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE.getType());
        if (StringUtils.isNotBlank(miniAppLinkId)) {
            PhotoEntity miniAppPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE.getType(), miniAppLinkId);
            if (miniAppPhotoEntity != null) {
                customizeFormQrCodeContainer.setMiniAppPath(miniAppPhotoEntity.getPath());
                customizeFormQrCodeContainer.setMiniAppUrl(miniAppPhotoEntity.getUrl());
            } else {
                // 创建二维码
                QRCodeManager.CreateQRCodeResult miniQrCodeResult = qrCodeManager
                    .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE, formId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId, marketingEventId, jsonValue, ea);
                customizeFormQrCodeContainer.setMiniAppPath(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeApath() : null);
                customizeFormQrCodeContainer.setMiniAppUrl(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeUrl() : null);
            }
        } else {
            // 创建二维码
            QRCodeManager.CreateQRCodeResult miniQrCodeResult = qrCodeManager
                .saveQrCodeAndPhotoAssociationData(QRCodeTypeEnum.CUSTOMIZE_FORM_DATA.getType(), PhotoTargetTypeEnum.CUSTOMIZE_FORM_DATA_QR_CODE, formId,ObjectTypeEnum.CUSTOMIZE_FORM.getType(), formId,  marketingEventId,  jsonValue, ea);
            customizeFormQrCodeContainer.setMiniAppPath(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeApath() : null);
            customizeFormQrCodeContainer.setMiniAppUrl(miniQrCodeResult != null ? miniQrCodeResult.getQrCodeUrl() : null);
        }*/
        return customizeFormQrCodeContainer;
    }

    private String getQrCodeIdByType(String formId, String marketingEventId, Integer photoTargetType) {
        // 查询中间表
        List<String> ids = photoAssociationDAO.getDataByTypeFormIdAndMarketingEventId(photoTargetType, formId, ObjectTypeEnum.CUSTOMIZE_FORM.getType() + "", formId, marketingEventId);
        if (CollectionUtils.isNotEmpty(ids)) {
            return ids.get(0);
        } else {
            return null;
        }
    }

    /**
     * 设置头图
     */
    public CustomizeFormDataDetailResult setCustomizeFormDataHeadPhoto(CustomizeFormDataDetailResult customizeFormDataDetailResult) {
        if (customizeFormDataDetailResult.getFormHeadSetting() != null && CollectionUtils.isNotEmpty(customizeFormDataDetailResult.getFormHeadSetting().getHeadPhotoPath())) {
            List<String> headPhotoPath = customizeFormDataDetailResult.getFormHeadSetting().getHeadPhotoPath();
            customizeFormDataDetailResult.getFormHeadSetting().setHeadPhotoUrl(Lists.newArrayList());
            for (String path : headPhotoPath) {
                customizeFormDataDetailResult.getFormHeadSetting().getHeadPhotoUrl().add(fileV2Manager.getUrlByPath(path, null, false));
            }
        }
        return customizeFormDataDetailResult;
    }

    /**
     * 转换头图
     * @param photoPath
     * @return
     */
    public List<String> setCustomizeFormDataHeadPhoto(List<String> photoPath) {
        List<String> photoUrl = Lists.newArrayList();
        if(CollectionUtils.isEmpty(photoPath)) {
            return Lists.newArrayList();
        }
        for (String path : photoPath) {
            photoUrl.add(fileV2Manager.getUrlByPath(path, null, false));
        }
        return photoUrl;
    }


    /**
     * 设置文件信息
     */
    public CustomizeFormDataDetailResult setCustomizeFormDataFileInfo(CustomizeFormDataDetailResult customizeFormDataDetailResult) {
        if (customizeFormDataDetailResult == null) {
            return null;
        }
        if (customizeFormDataDetailResult.getFormSuccessSetting() != null && customizeFormDataDetailResult.getFormSuccessSetting().getFileInfo() != null
            && customizeFormDataDetailResult.getFormSuccessSetting().getFileInfo().getFilePath() != null) {
            FormSuccessSetting.FileInfo fileInfo = customizeFormDataDetailResult.getFormSuccessSetting().getFileInfo();
            customizeFormDataDetailResult.getFormSuccessSetting().getFileInfo().setFileUrl(fileV2Manager.getDiskFilePreviewUrl(customizeFormDataDetailResult.getEa(), fileInfo.getFilePath()));
        }
        return customizeFormDataDetailResult;
    }

    public List<String> convertTaPathToApath(List<String> paths, String ea, Integer fsUserId) {
        List<String> newPath = Lists.newArrayList();
        for (String path : paths) {
            if (path.startsWith("TA_")) {
                FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(path, ea, fsUserId);
                newPath.add(fileManagerPicResult != null ? fileManagerPicResult.getUrlAPath() : null);
            } else if (path.startsWith("A_")) {
                newPath.add(path);
            }
        }
        return newPath;
    }

    /**
     * 检查表单状态
     */
    public Result checkCustomizeFormDataStatus(CustomizeFormDataEntity customizeFormDataEntity, String formId, boolean needDisableData) {
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataManager.checkCustomizeFormDataStatus customizeFormDataEntity is null formId:{}", formId);
            return new Result(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        if (!needDisableData) {
            if (customizeFormDataEntity.getStatus().equals(CustomizeFormDataStatusEnum.DISABLE.getValue())) {
                //表单已停用
                log.warn("CustomizeFormDataManager.checkCustomizeFormDataStatus customizeFormDataEntity is disable, customizeFormDataEntity:{}", customizeFormDataEntity);
                return new Result(SHErrorCode.CUSTOMIZE_FORM_DATA_DISABLE);
            }
        }
        if (customizeFormDataEntity.getStatus().equals(CustomizeFormDataStatusEnum.DELETE.getValue())) {
            //表单已删除
            log.warn("CustomizeFormDataManager.checkCustomizeFormDataStatus customizeFormDataEntity is delete, customizeFormDataEntity:{}", customizeFormDataEntity);
            return new Result(SHErrorCode.CUSTOMIZE_FORM_DATA_DELETE);
        }
        return new Result(SHErrorCode.SUCCESS);
    }

    public void checkConferenceEnrollReview(String ea,String marketingEventId,CustomizeFormDataEnrollResult customizeFormDataEnrollResult){
        if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(ea)) {
            return;
        }
        //检查是否需要审核
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity != null && activityEntity.getEnrollReview() != null) {
            ObjectData marketingEventObj = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            if (marketingEventObj == null || marketingEventObj.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()) == null || !MarketingEventFormEnum.CONFERENCE_MARKETING.getValue().equals(marketingEventObj.getString(CrmV2MarketingEventFieldEnum.EVENT_FORM.getFieldName()))) {
                log.warn("checkConferenceEnrollReview marketing event form is not conference ea: {} marketingEventId:{}", ea, marketingEventId);
                return;
            }
            customizeFormDataEnrollResult.setEnrollReview(activityEntity.getEnrollReview());
            if (activityEntity.getEnrollReview()) {
                customizeFormDataEnrollResult.setEnrollStatus(ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus());
                customizeFormDataEnrollResult.setEnrollPendingReviewTip(StringUtils.isNotBlank(activityEntity.getEnrollPendingReviewTip()) ? activityEntity.getEnrollPendingReviewTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572));
                customizeFormDataEnrollResult.setEnrollReviewFailureTip(StringUtils.isNotBlank(activityEntity.getEnrollReviewFailureTip()) ? activityEntity.getEnrollReviewFailureTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573));
            }
        }
    }

    public void checkShowSettingConferenceEnrollReview(String ea, String marketingEventId, String objectId, Integer objectType, String formId, String wxAppId, String openId, String fingerPrint, String uid, CustomizeFormDataShowSettingResult result) {
        if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(ea)) {
            return;
        }
        //检查是否需要审核
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity != null && activityEntity.getEnrollReview() != null) {
            result.setEnrollReview(activityEntity.getEnrollReview());
            if (activityEntity.getEnrollReview()) {
                result.setEnrollPendingReviewTip(StringUtils.isNotBlank(activityEntity.getEnrollPendingReviewTip()) ? activityEntity.getEnrollPendingReviewTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572));
                result.setEnrollReviewFailureTip(StringUtils.isNotBlank(activityEntity.getEnrollReviewFailureTip()) ? activityEntity.getEnrollReviewFailureTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573));
                //只有开启了报名审核,才去查询审核状态,否则直接返回
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(ea, marketingEventId, fingerPrint, uid, wxAppId, openId, Lists.newArrayList(formId));
                if (CollectionUtils.isEmpty(customizeFormDataUserEntities)) {
                    return;
                }
                //按照创建时间倒序排序,并取第一条记录
                CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntities.stream().max(Comparator.comparing(CustomizeFormDataUserEntity::getCreateTime)).get();
                String campaignId = customizeFormDataUserEntity.getCampaignId();
                if (StringUtils.isEmpty(campaignId)) {
                    return;
                }
                ActivityEnrollDataEntity activityEnrollData = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(campaignId);
                if (activityEnrollData != null && activityEnrollData.getReviewStatus() != null) {
                    result.setEnrollStatus(activityEnrollData.getReviewStatus());
                }
            }
        }
    }

    /**
     * 更新表单关联设置
     */
    public void updateCustomizeFormDataObject(String formId, String objectId, Integer objectType, String ea, Integer fsUserId, Integer formStyleType, String formButtonName, ButtonStyle buttonStyle) {
        // 更新表单设置
        CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(ea, objectId, objectType);
        if (StringUtils.isNotBlank(formId)) {
            // 若之前有绑定需先解绑
            if (oldCustomizeFormDataObjectEntity != null) {
                customizeFormDataObjectDAO.deleteCustomizeFormDataObject(ea, oldCustomizeFormDataObjectEntity.getFormId(), objectId, objectType);
            }
            CustomizeFormDataObjectEntity newCustomizeFormDataObjectEntity = new CustomizeFormDataObjectEntity();
            newCustomizeFormDataObjectEntity.setId(UUIDUtil.getUUID());
            newCustomizeFormDataObjectEntity.setEa(ea);
            newCustomizeFormDataObjectEntity.setFormId(formId);
            newCustomizeFormDataObjectEntity.setObjectId(objectId);
            newCustomizeFormDataObjectEntity.setObjectType(objectType);
            newCustomizeFormDataObjectEntity.setCreateBy(fsUserId);
            newCustomizeFormDataObjectEntity.setUpdateBy(fsUserId);
            newCustomizeFormDataObjectEntity.setFormStyleType(formStyleType == null ? FormStyleTypeEnum.BOTTOM.getType() : formStyleType);
            newCustomizeFormDataObjectEntity.setFormButtonName(StringUtils.isEmpty(formButtonName) ? null : formButtonName);
            if (buttonStyle == null) {
                buttonStyle = new ButtonStyle();
                buttonStyle.setFontColor("#FFFFFF");
                buttonStyle.setButtonBorderColor("#FCB058");
                buttonStyle.setButtonColor("#FCB058");
            }
            newCustomizeFormDataObjectEntity.setButtonStyle(buttonStyle);
            customizeFormDataObjectDAO.insertCustomizeFormDataObject(newCustomizeFormDataObjectEntity);
        } else if (StringUtils.isBlank(formId) && oldCustomizeFormDataObjectEntity != null) { //若之前绑定目前解绑了
            customizeFormDataObjectDAO.deleteCustomizeFormDataObject(ea, oldCustomizeFormDataObjectEntity.getFormId(), objectId, objectType);
        }
    }

    /**
     * 物料绑定表单
     */
    public void bindCustomizeFormDataObject(String formId, String objectId, Integer objectType, String ea, Integer fsUserId, Integer formStyleType, String formButtonName, ButtonStyle buttonStyle) {
        // 可能出现错误数据先检测是否有绑定
        CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(ea, objectId, objectType);
        if (oldCustomizeFormDataObjectEntity != null) {
            customizeFormDataObjectDAO.deleteCustomizeFormDataObject(ea, oldCustomizeFormDataObjectEntity.getFormId(), objectId, objectType);
        }
        CustomizeFormDataObjectEntity customizeFormDataObjectEntity = new CustomizeFormDataObjectEntity();
        customizeFormDataObjectEntity.setId(UUIDUtil.getUUID());
        customizeFormDataObjectEntity.setEa(ea);
        customizeFormDataObjectEntity.setFormId(formId);
        customizeFormDataObjectEntity.setObjectId(objectId);
        customizeFormDataObjectEntity.setObjectType(objectType);
        customizeFormDataObjectEntity.setCreateBy(fsUserId);
        customizeFormDataObjectEntity.setUpdateBy(fsUserId);
        if (formStyleType == null) {
            formStyleType = FormStyleTypeEnum.BOTTOM.getType();
        }
        if (buttonStyle == null) {
            buttonStyle = new ButtonStyle();
            buttonStyle.setButtonColor("#FCB058");
            buttonStyle.setFontColor("#FFFFFF");
            buttonStyle.setButtonBorderColor("#FCB058");
        }
        customizeFormDataObjectEntity.setFormStyleType(formStyleType);
        customizeFormDataObjectEntity.setFormButtonName(StringUtils.isEmpty(formButtonName) ? null : formButtonName);
        customizeFormDataObjectEntity.setButtonStyle(buttonStyle);
        customizeFormDataObjectDAO.insertCustomizeFormDataObject(customizeFormDataObjectEntity);
    }

    /**
     * 解除物料表单关联
     */
    public void unBindCustomizeFormDataObject(String objectId, Integer objectType, String ea) {
        // 查询物料关联表单
        CustomizeFormDataObjectEntity customizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(ea, objectId, objectType);
        if (customizeFormDataObjectEntity == null) {
            return;
        }
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(ea, customizeFormDataObjectEntity.getFormId(), objectId, objectType);
    }

    /**
     * 解除物料表单关联
     */
    public void unBindCustomizeFormDataObjectBatch(List<String> objectIdList, Integer objectType, String ea) {
        // 查询物料关联表单
        List<CustomizeFormDataObjectEntity> customizeFormDataObjectEntityList = customizeFormDataObjectDAO.getObjectBindingFormByObjectIdList(ea, objectIdList, objectType);
        if (CollectionUtils.isEmpty(customizeFormDataObjectEntityList)) {
            return;
        }
        List<String> formIdList = customizeFormDataObjectEntityList.stream().map(CustomizeFormDataObjectEntity::getFormId).collect(Collectors.toList());
        customizeFormDataObjectDAO.deleteCustomizeFormDataObjectBatch(ea, formIdList, objectIdList, objectType);
    }

    /**
     * 解除表单绑定且删除表单
     */
    public void unBindCustomizeFormDataObjectAndDeleteForm(String objectId, Integer objectType, String ea, Integer fsUserId) {
        // 查询物料关联表单
        CustomizeFormDataObjectEntity customizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(ea, objectId, objectType);
        if (customizeFormDataObjectEntity == null) {
            return;
        }
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(ea, customizeFormDataObjectEntity.getFormId(), objectId, objectType);
        customizeFormDataDAOManager.updateCustomizeFormDataStatus(ea, customizeFormDataObjectEntity.getFormId(), fsUserId, CustomizeFormDataStatusEnum.DELETE.getValue());
    }

    /**
     * 解除表单绑定且删除表单
     *
     */
    public void unBindCustomizeFormDataObjectAndDeleteFormBatch(List<String> objectIdList, Integer objectType, String ea, Integer fsUserId) {
        // 查询物料关联表单
        List<CustomizeFormDataObjectEntity> customizeFormDataObjectEntityList = customizeFormDataObjectDAO.getObjectBindingFormByObjectIdList(ea, objectIdList, objectType);
        if (CollectionUtils.isEmpty(customizeFormDataObjectEntityList)) {
            return;
        }
        List<String> fromIdList = customizeFormDataObjectEntityList.stream().map(CustomizeFormDataObjectEntity::getFormId).collect(Collectors.toList());
        customizeFormDataObjectDAO.deleteCustomizeFormDataObjectBatch(ea, fromIdList, objectIdList, objectType);
        //FIXME 这里还是先用循环，因为updateCustomizeFormDataStatus还有一些逻辑，如果integralServiceManager.asyncRemoveMaterial支持幂等，改成批量
        for (CustomizeFormDataObjectEntity entity : customizeFormDataObjectEntityList) {
            customizeFormDataDAOManager.updateCustomizeFormDataStatus(ea, entity.getFormId(), fsUserId, CustomizeFormDataStatusEnum.DELETE.getValue());
        }
    }


    /**
     * 统计表单绑定物料数（单个查询）
     */
    public Map<Integer, Integer> bindObjectStatistics(String formId) {
        Map<Integer, Integer> resultMap = Maps.newHashMap();
        List<FormBindObjectStatisticsDTO> formBindObjectStatisticsDTOList = customizeFormDataObjectDAO.bindObjectStatistics(formId);
        if (CollectionUtils.isNotEmpty(formBindObjectStatisticsDTOList)) {
            resultMap = formBindObjectStatisticsDTOList.stream().collect(Collectors.toMap(FormBindObjectStatisticsDTO::getObjectType, FormBindObjectStatisticsDTO::getObjectCount));
        }
        return resultMap;
    }

    /**
     * 统计表单绑定物料数（批量查询）
     *
     * @return Key: 表单id  value: 物料类型,关联数量
     */
    public Map<String, Map<Integer, Integer>> bindObjectStatistics(List<String> formIds) {
        Map<String, Map<Integer, Integer>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(formIds)) {
            List<BranchSearchFormBindObjectStatisticsDTO> branchSearchFormBindObjectStatisticsDTOS = customizeFormDataObjectDAO.branchSearchBindObjectStatistics(formIds);
            if (CollectionUtils.isNotEmpty(branchSearchFormBindObjectStatisticsDTOS)) {
                branchSearchFormBindObjectStatisticsDTOS.forEach(branchSearchFormBindObject -> {
                    Type statisticsType = new TypeToken<List<BranchSearchFormBindObjectStatisticsDTO.StatisticsMap>>() {
                    }.getType();
                    List<BranchSearchFormBindObjectStatisticsDTO.StatisticsMap> statisticsMapList = GsonUtil.getGson().fromJson(branchSearchFormBindObject.getStatisticsResult(), statisticsType);
                    Map<Integer, Integer> statisticsMap = statisticsMapList.stream().collect(Collectors.toMap(StatisticsMap::getObjectType, StatisticsMap::getObjectCount));
                    resultMap.put(branchSearchFormBindObject.getFormId(), statisticsMap);
                });
            }
        }
        return resultMap;
    }

    /**
     * 确认人员是否有添加销售线索权限
     */
    public boolean checkAddLeadsObjectAuth(String ea, Integer fsUserId) {
        Map<String, Boolean> resultMap = crmV2Manager.funcPermissionCheck(ea, fsUserId, addLeadsObjectAuth);
        return MapUtils.isNotEmpty(resultMap) && ((resultMap.get("2005") != null && resultMap.get("2005")) || (resultMap.get("LeadsObj||Add") != null && resultMap.get("LeadsObj||Add")));
    }

    /**
     * 查询物料关联表单
     */
    public CustomizeFormDataEntity getBindFormDataByObject(String ea, String objectId, Integer objectType) {
        CustomizeFormDataEntity customizeFormDataEntity = null;
        if (StringUtils.isBlank(objectId) || objectType == null) {
            return null;
        }
        // 若物料为表单则直接查询
        if (ObjectTypeEnum.CUSTOMIZE_FORM.getType() == objectType) {
            customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(objectId);
            return customizeFormDataEntity;
        }
        // 若物料为会议则先查询该微页面表单
        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(objectId);
            if (activityEntity == null) {
                return null;
            }
            if (StringUtils.isNotBlank(activityEntity.getActivityDetailSiteId())) {
                List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(activityEntity.getActivityDetailSiteId()));
                if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList) && StringUtils.isNotBlank(hexagonSiteListDTOList.get(0).getFormId())) {
                    customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(hexagonSiteListDTOList.get(0).getFormId());
//                    customizeFormDataEntity.setConferenceSiteForm(true);
                    return customizeFormDataEntity;
                }
            }
        }

        // 若物料是产品或文章 查询产品或文章关联的 1.微页面-》表单 2.关联的表单
        if (ObjectTypeEnum.PRODUCT.getType() == objectType || ObjectTypeEnum.ARTICLE.getType() == objectType) {
            HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(ea, objectId, objectType);
            if (hexagonSiteObjectEntity != null) {
                List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(hexagonSiteObjectEntity.getSiteId()));
                if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList) && StringUtils.isNotBlank(hexagonSiteListDTOList.get(0).getFormId())) {
                    customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(hexagonSiteListDTOList.get(0).getFormId());
                    return customizeFormDataEntity;
                }
            }
        }

        if (ObjectTypeEnum.HEXAGON_SITE.getType() == objectType) {
            List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(objectId));
            if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList) && StringUtils.isNotBlank(hexagonSiteListDTOList.get(0).getFormId())) {
                customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(hexagonSiteListDTOList.get(0).getFormId());
                if (customizeFormDataEntity != null) {
                    return customizeFormDataEntity;
                }
            }
        }


        // 查询物料关联表单
        CustomizeFormDataObjectEntity customizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(ea, objectId, objectType);
        if (customizeFormDataObjectEntity == null) {
            log.warn("CustomizeFormDataManager.getBindFormDataByObject error customizeFormDataObjectEntity is null ea:{}, objectId:{}, objectType:{}", ea, objectId, objectType);
            return null;
        }
        // 查询对应表单数据
        customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataObjectEntity.getFormId());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataManager.getBindFormDataByObject error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}", ea, objectId, objectType);
            return null;
        }
        if (customizeFormDataEntity.getStatus().equals(CustomizeFormDataStatusEnum.DELETE.getValue())) {
            // 若表单删除则删除物料和表单的绑定关系
            customizeFormDataObjectDAO.deleteCustomizeFormDataObjectById(customizeFormDataObjectEntity.getId());
            return null;
        }
        return customizeFormDataEntity;
    }

    /**
     * 批量查询物料关联表单（非会议）
     */
    @FilterLog
    public Map<String, CustomizeFormDataEntity> getBindFormDataByObjects(String ea, List<String> objectIds) {
        Map<String, CustomizeFormDataEntity> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(objectIds)) {
            log.warn("CustomizeFormDataManager.getBindFormDataByObjects param error ea:{},objectIds:{}", ea, objectIds);
            return resultMap;
        }
        List<CustomizeFormDataObjectEntity> customizeFormDataObjectEntityList = customizeFormDataObjectDAO.getObjectBindingFormByObjectIds(ea, objectIds);
        if (CollectionUtils.isEmpty(customizeFormDataObjectEntityList)) {
            log.warn("CustomizeFormDataManager.getBindFormDataByObjects customizeFormDataObjectEntityList is null ea:{},objectIds:{}", ea, objectIds);
            return resultMap;
        }
        List<String> formIds = customizeFormDataObjectEntityList.stream().map(CustomizeFormDataObjectEntity::getFormId).collect(Collectors.toList());
        Map<String, String> objectFormIdMap = customizeFormDataObjectEntityList.stream()
            .collect(Collectors.toMap(CustomizeFormDataObjectEntity::getObjectId, CustomizeFormDataObjectEntity::getFormId));
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            log.warn("CustomizeFormDataManager.getBindFormDataByObjects customizeFormDataEntityList is null ea:{},objectIds:{}", ea, objectIds);
            return resultMap;
        }
        Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap =  customizeFormDataEntityList.stream().collect(Collectors.toMap(CustomizeFormDataEntity::getId, data -> data, (v1, v2)->v2));
        for (Map.Entry<String, String> entry : objectFormIdMap.entrySet()) {
            resultMap.put(entry.getKey(), customizeFormDataEntityMap.get(entry.getValue()));
        }
        return resultMap;
    }

    /*
    public Map<String, CustomizeFormDataEntity> getConferenceBindFormDataByObjects(String ea, List<String> objectIds) {
        Map<String, CustomizeFormDataEntity> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(objectIds)) {
            log.warn("CustomizeFormDataManager.getConferenceBindFormDataByObjects param error ea:{},objectIds:{}", ea, objectIds);
            return resultMap;
        }
        // 筛选出站点已有表单的数据
        List<ConferenceIdNameDTO> conferenceIdNameDTOList = conferenceDAO.queryConferenceNameDTO(objectIds);
        if (CollectionUtils.isEmpty(conferenceIdNameDTOList)) {
            return resultMap;
        }
        List<String> siteIds = conferenceIdNameDTOList.stream().map(ConferenceIdNameDTO::getActivityDetailSiteId).collect(Collectors.toList());
        List<HexagonSiteListDTO> conferenceSiteFormList = hexagonSiteDAO.getConferenceSiteFormBySiteIds(siteIds);
        if (CollectionUtils.isNotEmpty(conferenceSiteFormList)) {
            List<String> formIds = conferenceSiteFormList.stream().map(HexagonSiteListDTO::getFormId).collect(Collectors.toList());
            Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds).stream().collect(Collectors.toMap(
                CustomizeFormDataEntity::getId, data -> data));
            for (HexagonSiteListDTO hexagonSiteListDTO : conferenceSiteFormList) {
                objectIds.remove(hexagonSiteListDTO.getConferenceId());
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(hexagonSiteListDTO.getFormId());
                if (customizeFormDataEntity != null) {
                    customizeFormDataEntity.setConferenceSiteForm(true);
                    resultMap.put(hexagonSiteListDTO.getConferenceId(), customizeFormDataEntity);
                }
            }
        }
        // 剩余仍绑定旧表单的数据
        if (CollectionUtils.isNotEmpty(objectIds)) {
            resultMap.putAll(getBindFormDataByObjects(ea, objectIds));
        }
        return resultMap;
    }*/


    public Map<String, CustomizeFormClueNumDTO> getBindFormClueNum(List<String> formIds, String objectId, Integer objectType) {
        Map<String, CustomizeFormClueNumDTO> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(formIds)) {
            return result;
        }
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            return result;
        }
        List<CustomizeFormClueNumDTO> customizeFormClueNumDTOList = customizeFormDataUserDAO.getFormInfoAndClueNameByFormsAndObject(formIds, objectId, objectType);
        Map<String, CustomizeFormClueNumDTO> customizeFormClueNumDTOMap = customizeFormClueNumDTOList.stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getFormId, data -> data));
        for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormDataEntityList) {
            CustomizeFormClueNumDTO customizeFormClueNumDTO = customizeFormClueNumDTOMap.get(customizeFormDataEntity.getId());
            if (customizeFormClueNumDTO == null) {
                customizeFormClueNumDTO = new CustomizeFormClueNumDTO();
                customizeFormClueNumDTO.setFormId(customizeFormDataEntity.getId());
                customizeFormClueNumDTO.setFormName(customizeFormDataEntity.getFormHeadSetting().getName());
                customizeFormClueNumDTO.setCount(0);
            }
            result.put(customizeFormDataEntity.getId(), customizeFormClueNumDTO);
        }
        customizeFormDataEntityList = null;
        customizeFormClueNumDTOList = null;
        return result;
    }

    /**
     * 根据表单来源统计数据
     * @param formId
     * @param source
     * @return
     */
    public Map<String, CustomizeFormClueNumDTO> getFormClueNumBySource(List<String> formId, Integer source) {
        if (CollectionUtils.isEmpty(formId) || source == null) {
            return Maps.newHashMap();
        }

        CustomizeFormDataEntity entity = customizeFormDataDAO.getCustomizeFormDataById(formId.get(0));
        List<CustomizeFormClueNumDTO> customizeFormClueNumDTOList = customizeFormDataUserDAO.getFormClueCountBySourceType(entity.getEa() ,formId, source);
        if (CollectionUtils.isEmpty(customizeFormClueNumDTOList)) {
            return Maps.newHashMap();
        }
        return customizeFormClueNumDTOList.stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getFormId, data -> data));
    }


    /**
     * 获取报名数据最新的表单详情
     */
    public CustomizeFormDataEntity getLatestEnrollDataForm(Integer type, String objectId, Integer objectType, String marketingActivityId, String marketingEventId, String ea) {
        if (type.equals(QueryFormUserDataTypeEnum.OBJECT.getType())) {
            return customizeFormDataUserDAO.getLatestEnrollDataFormByObject(ea, objectId, objectType);
        } else if (type.equals(QueryFormUserDataTypeEnum.MARKETING_ACTIVITY.getType())) {
            return customizeFormDataUserDAO.getLatestEnrollDataFormByMarketingActivityId(ea, marketingActivityId);
        } else if (type.equals(QueryFormUserDataTypeEnum.MARKETING_EVENT.getType())) {
            return customizeFormDataUserDAO.getLatestEnrollDataFormByEventIdAndObject(ea, objectId, objectType, marketingEventId);
        }
        return null;
    }

    /**
     * 查询表单报名数据
     */
        public PageResult<QueryFormUserDataResult> getCustomizeFormDataUser(FormDataUserArgContainer formDataUserArgContainer, Page page) {
        PageResult<QueryFormUserDataResult> pageResult = new PageResult();
        pageResult.setPageNum(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        CustomizeFormDataEntity customizeFormDataEntity = null;
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = Lists.newArrayList();
        Integer type = formDataUserArgContainer.getType();
        String ea = formDataUserArgContainer.getEa();
        String objectId = formDataUserArgContainer.getObjectId();
        Integer objectType = formDataUserArgContainer.getObjectType();
        if (type.equals(QueryFormUserDataTypeEnum.OBJECT.getType())) {
            // 查询物料对应表单
            customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
            if (customizeFormDataEntity == null) {
                // 若物料解绑表单则查询报名数据最新表单
                customizeFormDataEntity = this.getLatestEnrollDataForm(type, objectId, objectType, null, null, ea);
                if (customizeFormDataEntity == null) {
                    log.warn("CustomizeFormDataManager.getCustomizeFormDataUser error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, type:{}", ea, objectId, objectType, type);
                    return pageResult;
                }
            }
            FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
            pageResult.setOtherData(fieldInfoList);
            // 查询对应表单报名数据
            if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
                List<String> objectIds = activityManager.getActivityLinkObject(objectId, objectType, Lists.newArrayList(ObjectTypeEnum.ACTIVITY_INVITATION));
                customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByObjects(objectIds, formDataUserArgContainer.getUsage(), page);
            } else {
                customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByObject(objectId, objectType, formDataUserArgContainer.getUsage(), page);
            }
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                return pageResult;
            }
        } else if (type.equals(QueryFormUserDataTypeEnum.MARKETING_ACTIVITY.getType()) ||type.equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())) {
            // 查询物料对应表单
            customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
            if (customizeFormDataEntity == null) {
                // 若物料解绑表单则查询报名数据最新表单
                customizeFormDataEntity = this.getLatestEnrollDataForm(type, null, null, formDataUserArgContainer.getMarketingActivityId(), null, ea);
                if (customizeFormDataEntity == null) {
                    log.warn("CustomizeFormDataManager.getCustomizeFormDataUser error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, marketingActivityId:{}, type:{}", ea, objectId,
                        objectType, formDataUserArgContainer.getMarketingActivityId(), type);
                    return pageResult;
                }
            }
            FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
            pageResult.setOtherData(fieldInfoList);
            // 查询对应表单报名数据
            customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityId(formDataUserArgContainer.getMarketingActivityId(), formDataUserArgContainer.getUsage(), page);
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                return pageResult;
            }
        } else if (type.equals(QueryFormUserDataTypeEnum.FORM_ID.getType()) || type.equals(QueryFormUserDataTypeEnum.ENROLL_SOURCE_TYPE.getType())) {
            // 根据表单id查询数据
            // 查询对应表单数据
            customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(objectId);
            if (customizeFormDataEntity == null) {
                log.warn("CustomizeFormDataManager.getCustomizeFormDataUser error customizeFormDataEntity is null formId:{}, type:{}", objectId, type);
                return pageResult;
            }
            FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
            pageResult.setOtherData(fieldInfoList);
            // 查询对应表单报名数据
            customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByFormId(objectId, formDataUserArgContainer.getUsage(), formDataUserArgContainer.getSourceType(), page);
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                return pageResult;
            }
        } else if (type.equals(QueryFormUserDataTypeEnum.MARKETING_EVENT.getType())) {
            // 查询物料对应表单
            customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
            if (customizeFormDataEntity == null) {
                // 若物料解绑表单则查询报名数据最新表单
                customizeFormDataEntity = this.getLatestEnrollDataForm(type, objectId, objectType, null, formDataUserArgContainer.getMarketingEventId(), ea);
                if (customizeFormDataEntity == null) {
                    log.warn(
                        "CustomizeFormDataManager.getCustomizeFormDataUser error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, marketingActivityId:{}, marketingEventId:{}, type:{}",
                        ea, objectId, objectType, formDataUserArgContainer.getMarketingActivityId(), formDataUserArgContainer.getMarketingEventId(), type);
                    return pageResult;
                }
            }
            FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
            pageResult.setOtherData(fieldInfoList);
            if (ObjectTypeEnum.CUSTOMIZE_FORM.getType() == objectType) {
                // 查询表单是否为微页面表单
                List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getPageByFormId(objectId);
                if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
                    // 若表单为微页面表单则查询市场活动下全部数据
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndFormId(objectId, formDataUserArgContainer.getMarketingEventId(), formDataUserArgContainer.getUsage(), page);
                } else {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndObject(objectId, objectType, formDataUserArgContainer.getMarketingEventId(), formDataUserArgContainer.getUsage(), page);
                }
            } else {
                customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndObject(objectId, objectType, formDataUserArgContainer.getMarketingEventId(), formDataUserArgContainer.getUsage(), page);
            }
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                return pageResult;
            }
        } else if (type.equals(QueryFormUserDataTypeEnum.CUSTOMER_CIRCLE.getType())) {
            customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
            customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByActivityd(ea, formDataUserArgContainer.getMarketingActivityId(), page);
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                return pageResult;
            }
        }else if (type.equals(QueryFormUserDataTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType())) {
            int count = groupSendTaskDAO.getGroupSendAttachments(formDataUserArgContainer.getMarketingActivityId());
            if(count>0){
                //只有一条,走旧逻辑
                List<MarketingActivityObjectRelationEntity> relationEntities = marketingActivityObjectRelationDAO.queryByMarketingActivityId(ea, formDataUserArgContainer.getMarketingActivityId());
                if(relationEntities.size()==1){
                    // 查询物料对应表单
                    customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                    if (customizeFormDataEntity == null) {
                        // 若物料解绑表单则查询报名数据最新表单
                        customizeFormDataEntity = this.getLatestEnrollDataForm(type, null, null, formDataUserArgContainer.getMarketingActivityId(), null, ea);
                        if (customizeFormDataEntity == null) {
                            log.warn("CustomizeFormDataManager.getCustomizeFormDataUser error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, marketingActivityId:{}, type:{}", ea, objectId,
                                    objectType, formDataUserArgContainer.getMarketingActivityId(), type);
                            return pageResult;
                        }
                    }
                    FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
                    pageResult.setOtherData(fieldInfoList);
                    // 查询对应表单报名数据
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityId(formDataUserArgContainer.getMarketingActivityId(), formDataUserArgContainer.getUsage(), page);
                }else {
                    //走新逻辑,直接取报名数据,表单内容写死,5个默认表单字段姓名,手机,公司,邮箱,职务
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByActivityd(ea, formDataUserArgContainer.getMarketingActivityId(), page);
                    String defaultFieldInfoStr = "[{\"apiName\":\"name\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入姓名\",\"isRequired\":true,\"isVerify\":false,\"label\":\"姓名\",\"type\":\"text\"},{\"apiName\":\"phone\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入手机号\",\"isRequired\":true,\"isVerify\":false,\"label\":\"手机号\",\"type\":\"phone_number\"},{\"apiName\":\"companyName\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入公司名称\",\"isRequired\":true,\"isVerify\":false,\"label\":\"公司名称\",\"type\":\"text\"},{\"apiName\":\"email\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入邮箱\",\"isRequired\":true,\"isVerify\":false,\"label\":\"邮箱\",\"type\":\"email\"},{\"apiName\":\"position\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入职务\",\"isRequired\":true,\"isVerify\":false,\"label\":\"职务\",\"type\":\"text\"}]";
                    FieldInfoList defaultFieldInfoList = JSON.parseObject(defaultFieldInfoStr,FieldInfoList.class);
                    pageResult.setOtherData(defaultFieldInfoList);
                }
                if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                    return pageResult;
                }
            }else {
                // 查询物料对应表单
                customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                if (customizeFormDataEntity == null) {
                    // 若物料解绑表单则查询报名数据最新表单
                    customizeFormDataEntity = this.getLatestEnrollDataForm(type, null, null, formDataUserArgContainer.getMarketingActivityId(), null, ea);
                    if (customizeFormDataEntity == null) {
                        log.warn("CustomizeFormDataManager.getCustomizeFormDataUser error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, marketingActivityId:{}, type:{}", ea, objectId,
                                objectType, formDataUserArgContainer.getMarketingActivityId(), type);
                        return pageResult;
                    }
                }
                FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
                pageResult.setOtherData(fieldInfoList);
                // 查询对应表单报名数据
                customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityId(formDataUserArgContainer.getMarketingActivityId(), formDataUserArgContainer.getUsage(), page);
                if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                    return pageResult;
                }
            }
        }
        // 转换地区信息
        buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
        // 转换sourceName与sourceType
        buildSourceNameAndType(customizeFormDataUserEntityList);
        // 转换utmMedium与source（目前只针对fs）
        buildUtmMediumAndSource(customizeFormDataUserEntityList, ea);
        List<QueryFormUserDataResult> queryFormUserDataResultList = BeanUtil.copy(customizeFormDataUserEntityList, QueryFormUserDataResult.class);
        doFillPayOrderMsg(queryFormUserDataResultList);
        // 推广人信息
        List<Integer> spreadUserIdList = customizeFormDataUserEntityList.stream().filter(data -> data.getSpreadFsUid() != null).map(CustomizeFormDataUserEntity::getSpreadFsUid)
            .collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, spreadUserIdList, true);
        // 报名者信息
        List<String> uidList = customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getUid).collect(Collectors.toList());
        Map<String, BaseUserInfoResult> basicInfoMap = kmUserManager.batchGetBaseUserInfo(uidList);
        // 报名者信息(微信身份)
        Map<String, WechatFanDetailResult> wechatFanDetailResultMap = batchesGetFanInfo(ea, queryFormUserDataResultList);
        Map<String, String> marketingActivityIdNameMap = Maps.newHashMap();
        Map<String, String> marketingEventIdNameMap = Maps.newHashMap();
        if (formDataUserArgContainer.getNeedMarketingEventAndActivityDetail() != null && formDataUserArgContainer.getNeedMarketingEventAndActivityDetail()) {
            Set<String> marketingActivityIds = queryFormUserDataResultList.stream().map(QueryFormUserDataResult::getMarketingActivityId).filter(Objects::nonNull).collect(Collectors.toSet());
            Filter filter = new Filter();
            filter.setFieldName("spread_type");
            filter.setFieldValues(Lists.newArrayList("4"));
            filter.setOperator(FilterOperatorEnum.N.getValue());
            marketingActivityIdNameMap = crmV2Manager.getObjectNameMapByIds(ea, -10000, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), marketingActivityIds, Lists.newArrayList(filter));
            Set<String> marketingEventIds = queryFormUserDataResultList.stream().map(QueryFormUserDataResult::getMarketingEventId).filter(Objects::nonNull).collect(Collectors.toSet());
            marketingEventIdNameMap = crmV2Manager.getObjectNameMapByIds(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, null);
        }
        // 若开启手机脱敏
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        // spreadUserIdList和伙伴信息的映射
        Map<Integer, UserRelationPartnerInfo> fsUserIdToPartnerInfoMap = userRelationManager.getPartnerInfoByFsUserIdList(ea, spreadUserIdList);

        List<String> campaignIdList =  queryFormUserDataResultList.stream().filter(e -> StringUtils.isNotBlank(e.getCampaignId()))
                .map(QueryFormUserDataResult::getCampaignId).collect(Collectors.toList());
        Map<String, CampaignMergeDataEntity> campaignMergeDataMap = Maps.newHashMap();
        Map<Integer, List<String>> crmObjectTypeToIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIdList);
            if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                campaignMergeDataEntityList.forEach(e -> {
                    campaignMergeDataMap.put(e.getId(), e);
                    if (e.getBindCrmObjectType() != null && StringUtils.isNotBlank(e.getBindCrmObjectId())) {
                        List<String> crmObjectIdList = crmObjectTypeToIdMap.computeIfAbsent(e.getBindCrmObjectType(), k -> Lists.newArrayList());
                        crmObjectIdList.add(e.getBindCrmObjectId());
                    }
                });
            }
        }

        for (QueryFormUserDataResult data : queryFormUserDataResultList) {
            if (StringUtils.isNotBlank(data.getLeadId())) {
                List<String> crmObjectIdList = crmObjectTypeToIdMap.computeIfAbsent(CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), k -> Lists.newArrayList());
                crmObjectIdList.add(data.getLeadId());
            }
            CustomizeFormBindOtherCrmObject otherCrmObjectBind = data.getOtherCrmObjectBind();
            if (otherCrmObjectBind != null && StringUtils.isNotBlank(otherCrmObjectBind.getObjectId())) {
                Integer objectBindType = CampaignMergeDataObjectTypeEnum.getTypeByName(otherCrmObjectBind.getApiName());
                if (objectBindType != null) {
                    List<String> crmObjectIdList = crmObjectTypeToIdMap.computeIfAbsent(objectBindType, k -> Lists.newArrayList());
                    crmObjectIdList.add(otherCrmObjectBind.getObjectId());
                }
            }
        }

        Map<String, String> crmObjectIdToNameMap = conferenceManager.getEnrollNameByObjectType(ea, crmObjectTypeToIdMap);
        Map<String,String> apiNameMap = crmV2Manager.getAllObjDescribeApiNameAndDisplayName(ea);
        String crmApiName = customizeFormDataEntity == null ? null: customizeFormDataEntity.getCrmApiName();
        List<String> extraDataIds = Lists.newArrayList();
        Map<String,String> extraDataNameMap = Maps.newHashMap();
        boolean saveObjData = customizeFormDataEntity != null && Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(),SaveCrmObjectTypeEnum.OBJ.getType());
        if (saveObjData) {
            extraDataIds = queryFormUserDataResultList.stream().map(QueryFormUserDataResult::getExtraDataId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            extraDataNameMap = conferenceManager.getExtraDataNameByObjectIds(ea,extraDataIds,crmApiName);
        }
        for (QueryFormUserDataResult data : queryFormUserDataResultList) {
            if (turnOnPhoneNumberSensitive) {
                safetyManagementManager.phoneNumberSensitive(data.getSubmitContent());
            }
            //根据业务数据来源来获取关联名称
            String relationDataName = data.getSubmitContent().getName();
            //处理任意对象关联名称
            if (saveObjData) {
                if (crmApiName != null) {
                    data.setApiName(crmApiName);
                    data.setDisplayName(apiNameMap.get(crmApiName));
                }
                relationDataName = extraDataNameMap.get(data.getExtraDataId());
            } else {
                if (StringUtils.isNotBlank(data.getCampaignId())) {
//                CampaignMergeDataEntity dataEntity = campaignMergeDataDAO.getCampaignMergeDataById(data.getCampaignId());
                    CampaignMergeDataEntity dataEntity = campaignMergeDataMap.get(data.getCampaignId());
                    if (dataEntity != null && StringUtils.isNotBlank(dataEntity.getBindCrmObjectId()) && dataEntity.getBindCrmObjectType() != null) {
//                    String objectName = conferenceManager.getEnrollNameByObjectType(ea,dataEntity.getBindCrmObjectType(),dataEntity.getBindCrmObjectId());
                        String objectName = crmObjectIdToNameMap.get(dataEntity.getBindCrmObjectId());
                        if (objectName != null) {
                            relationDataName = objectName;
                        }
                    }
                } else {
                    if (StringUtils.isNotBlank(data.getLeadId())) {
//                    String objectName = conferenceManager.getEnrollNameByObjectType(ea, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(),data.getLeadId());
                        String objectName = crmObjectIdToNameMap.get(data.getLeadId());
                        if (objectName != null) {
                            relationDataName = objectName;
                        }
                    } else {
                        if (data.getOtherCrmObjectBind() != null) {
                            CustomizeFormBindOtherCrmObject otherCrmObjectBind = data.getOtherCrmObjectBind();
//                        Integer objectBindType = CampaignMergeDataObjectTypeEnum.getTypeByName(otherCrmObjectBind.getApiName());
//                        String objectName = conferenceManager.getEnrollNameByObjectType(ea,objectBindType,otherCrmObjectBind.getObjectId());
                            String objectName = crmObjectIdToNameMap.get(otherCrmObjectBind.getObjectId());
                            if (objectName != null) {
                                relationDataName = objectName;
                            }
                        }
                    }
                }
            }
            data.setRelationDataName(relationDataName);
            data.setMarketingActivityName(marketingActivityIdNameMap.get(data.getMarketingActivityId()));
            data.setMarketingEventName(marketingEventIdNameMap.get(data.getMarketingEventId()));
            data.setMarketingActivityId(StringUtils.isNotBlank(marketingActivityIdNameMap.get(data.getMarketingActivityId())) ? data.getMarketingActivityId() : null);
            data.setMarketingEventId(StringUtils.isNotBlank(marketingEventIdNameMap.get(data.getMarketingEventId())) ? data.getMarketingEventId() : null);
            // 增加表单查询类型
            if (data.getSubmitContent() != null) {
                data.getSubmitContent().setCustomizeFormSearchType(1);
            }
            if (data.getSaveCrmStatus() != null && (data.getSaveCrmStatus().equals(SaveCrmStatusEnum.SUCCESS.getValue()) || data.getSaveCrmStatus().equals(SaveCrmStatusEnum.LINKED.getValue())) && StringUtils.isNotBlank(data.getLeadId())) {
                CustomizeFormBindOtherCrmObject customizeFormBindOtherCrmObject = new CustomizeFormBindOtherCrmObject();
                customizeFormBindOtherCrmObject.setObjectId(data.getLeadId());
                customizeFormBindOtherCrmObject.setApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                data.setOtherCrmObjectBind(customizeFormBindOtherCrmObject);
            }
            if (data.getSpreadFsUid() != null) {
                data.setSpreadUserName(fsEmployeeMsgMap.get(data.getSpreadFsUid()) != null ? fsEmployeeMsgMap.get(data.getSpreadFsUid()).getName() : null);
            }
            if (StringUtils.isNotBlank(data.getWxAppId()) && StringUtils.isNotBlank(data.getOpenId())) {
                data.setEnrollUserName(
                    wechatFanDetailResultMap.get(data.getWxAppId() + "#" + data.getOpenId()) != null ? wechatFanDetailResultMap.get(data.getWxAppId() + "#" + data.getOpenId()).getNickname() : null);
                data.setEnrollUserAvatar(
                    wechatFanDetailResultMap.get(data.getWxAppId() + "#" + data.getOpenId()) != null ? wechatFanDetailResultMap.get(data.getWxAppId() + "#" + data.getOpenId()).getHeadImgUrl() : null);
            } else {
                data.setEnrollUserName(basicInfoMap.get(data.getUid()) != null ? resetCardName(basicInfoMap.get(data.getUid())) : null);
                data.setEnrollUserAvatar(basicInfoMap.get(data.getUid()) != null ? basicInfoMap.get(data.getUid()).getAvatar() : null);
            }
            // 如果是伙伴推广，额外处理伙伴推广人和伙伴企业数据
            if (type.equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType()) || QywxUserConstants.isPartnerVirtualUserId(data.getSpreadFsUid())) {
                UserRelationPartnerInfo userRelationPartnerInfo = fsUserIdToPartnerInfoMap.get(data.getSpreadFsUid());
                String outTenantName = "";
                String spreadUserName = "";
                if (userRelationPartnerInfo != null) {
                    if (StringUtils.isNotBlank(userRelationPartnerInfo.getOuterTenantName())) {
                        outTenantName = userRelationPartnerInfo.getOuterTenantName();
                    }
                    if (StringUtils.isNotBlank(userRelationPartnerInfo.getOuterUserName())) {
                        spreadUserName = outTenantName + "-" +userRelationPartnerInfo.getOuterUserName();
                    }
                }
                data.setOutTenantName(outTenantName);
                data.setSpreadUserName(spreadUserName);
            }
        }
        // 获取表单体设置
        pageResult.setResult(queryFormUserDataResultList);
        pageResult.setTotalCount(page.getTotalNum());
        return pageResult;
    }


    public Optional<List<CustomizeFormDataEnroll>> getAllCustomizeFormDataEnrollByFormId(String formId) {
        if (StringUtils.isEmpty(formId)) {
            log.warn("CustomizeFormDataManager.getAllCustomizeFormDataEnrollByFormId formId is empty formId:{}", formId);
            return Optional.empty();
        }
        List<CustomizeFormDataUserEntity> customizeFormDataUserByFormId = customizeFormDataUserDAO.getCustomizeFormDataUserByFormId(formId, null, null, null);
        return Optional.of(customizeFormDataUserByFormId.stream().map(CustomizeFormDataUserEntity::getSubmitContent).collect(Collectors.toList()));
    }

    private void doFillPayOrderMsg(Collection<QueryFormUserDataResult> queryFormUserDataResults){
        Set<String> payOrderIdSet = queryFormUserDataResults.stream().filter(Objects::nonNull).map(QueryFormUserDataResult::getPayOrderId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<FsPayOrder> fsPayOrders = null;
        if (!payOrderIdSet.isEmpty()) {
            fsPayOrders = fsPayOrderDao.queryByIds(payOrderIdSet);
        }
        if (fsPayOrders != null) {
            Map<String, FsPayOrder> payOrderMap = fsPayOrders.stream().collect(Collectors.toMap(FsPayOrder::getId, Function.identity(), (o, n) -> o));
            queryFormUserDataResults.forEach(queryFormUserDataResult -> {
                FsPayOrder fsPayOrder = payOrderMap.get(queryFormUserDataResult.getPayOrderId());
                if (fsPayOrder == null) {
                    queryFormUserDataResult.setPayOrderState(PayOrderState.NOT_EXISTED.name());
                } else {
                    Integer status = fsPayOrder.getStatus();
                    if (status != 1) {
                        queryFormUserDataResult.setPayOrderState(PayOrderState.NOT_PAY.name());
                    } else {
                        queryFormUserDataResult.setPayOrderState(PayOrderState.SUCCESS.name());
                    }
                    Long amount = fsPayOrder.getAmount();
                    queryFormUserDataResult.setPayOrderTotalFee(new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                    queryFormUserDataResult.setPayDescription(fsPayOrder.getGoodsName());
                }
            });
        }
    }

    private String resetCardName(BaseUserInfoResult baseUserInfoResult) {
        if (baseUserInfoResult == null || StringUtils.isBlank(baseUserInfoResult.getName())) {
            return null;
        }
        return baseUserInfoResult.getName().replace("客脉访客", "访客");
    }

    public void buildAreaInfoByEnrollData(List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList) {
        List<String> valueList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
            return;
        }
        String ea = customizeFormDataUserEntityList.get(0).getEa();
        customizeFormDataUserEntityList.forEach(data -> {
            CustomizeFormDataEnroll customizeFormDataEnroll = data.getSubmitContent();
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getCountry())) {
                valueList.add(customizeFormDataEnroll.getCountry());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getProvince())) {
                valueList.add(customizeFormDataEnroll.getProvince());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getCity())) {
                valueList.add(customizeFormDataEnroll.getCity());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getDistrict())) {
                valueList.add(customizeFormDataEnroll.getDistrict());
            }
            if (MapUtils.isNotEmpty(customizeFormDataEnroll.getRegionMap())) {
                valueList.addAll(customizeFormDataEnroll.getRegionMap().values());
            }
        });
        if (CollectionUtils.isEmpty(valueList)) {
            return;
        }
        Map<String, String> areaMap = areaManager.getAreaNameByValue(ea, valueList, null);
        if (MapUtils.isEmpty(areaMap)) {
            return;
        }
        customizeFormDataUserEntityList.forEach(data -> {
            if (StringUtils.isNotBlank(data.getSubmitContent().getCountry())) {
                String country = areaMap.get(data.getSubmitContent().getCountry());
                data.getSubmitContent().setCountry(StringUtils.isNotBlank(country) ? country : null);
            }
            if (StringUtils.isNotBlank(data.getSubmitContent().getProvince())) {
                String province = areaMap.get(data.getSubmitContent().getProvince());
                data.getSubmitContent().setProvince(StringUtils.isNotBlank(province) ? province : null);
            }
            if (StringUtils.isNotBlank(data.getSubmitContent().getCity())) {
                String city = areaMap.get(data.getSubmitContent().getCity());
                data.getSubmitContent().setCity(StringUtils.isNotBlank(city) ? city : null);
            }
            if (StringUtils.isNotBlank(data.getSubmitContent().getDistrict())) {
                String district = areaMap.get(data.getSubmitContent().getDistrict());
                data.getSubmitContent().setDistrict(StringUtils.isNotBlank(district) ? district : null);
            }
            if (MapUtils.isNotEmpty(data.getSubmitContent().getRegionMap())) {
                data.getSubmitContent().getRegionMap().forEach((k, v) -> {
                    //如果是地址，则不处理
                    if (k.contains(CustomizeFormDataConstants.REGION_ADDRESS)) {
                        return;
                    }
                    String region = areaMap.get(v);
                    data.getSubmitContent().getRegionMap().put(k, StringUtils.isNotBlank(region) ? region : null);
                });
            }
        });
        areaMap.clear();
    }

    public void buildSourceNameAndType(List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList) {
        Map<String, String> sourceNameMap = LeadMarketingSourceNameEnum.getTypeMap();
        Map<String, String> sourceTypeMap = LeadMarketingSourceTypeEnum.getTypeMap();
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            if (customizeFormDataUserEntity.getSubmitContent() == null) {
                continue;
            }
            String name = sourceNameMap.get(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceName());
            String type = sourceTypeMap.get(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceType());
            if (StringUtils.isNotBlank(name)) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceName(name);
            }
            if (StringUtils.isNotBlank(name) && customizeFormDataUserEntity.getSubmitContent().getMarketingSourceName().contains(LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_OTHER.getName()) && StringUtils.isNotEmpty(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceSite())) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceName(LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_OTHER.getName() + ":" + customizeFormDataUserEntity.getSubmitContent().getMarketingSourceSite());
            }
            if (StringUtils.isNotBlank(type)) {
                customizeFormDataUserEntity.getSubmitContent().setMarketingSourceType(type);
            }
        }
    }

    public void buildUtmMediumAndSource(List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList, String ea) {
        if (!FsCrmLeadSpecialField.EA.equals(ea)) {
            return;
        }
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            // utmSource
            if (OfficialWebsiteConstants.BAIDU_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 百度
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.BAIDU_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.SOGOU_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 搜狗
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.SOGOU_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.MARKETING_SOURCE_NAME_360_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 360
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.MARKETING_SOURCE_NAME_360);
            } else if (OfficialWebsiteConstants.BING_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 必应
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.BING_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.SHENMA_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 神马
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.SHENMA_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.TOUTIAO_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 今日头条
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.TOUTIAO_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.DOUYIN_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 抖音
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.DOUYIN_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.TX_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 腾讯新闻
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.TX_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.SO_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 搜狐新闻
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.SO_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.FENG_HUANG_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 凤凰网
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.FENG_HUANG_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.WANG_YI_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 网易新闻
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.WANG_YI_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.WECHAT_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 微信
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.WECHAT_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.OFFICIAL_ACCOUNTS_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 公众号
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.OFFICIAL_ACCOUNTS_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.WEIBO_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 微博
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.WEIBO_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.UC_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // UC
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.UC_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.ZHI_HU_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 知乎
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.ZHI_HU_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.EDM_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // EDM
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.EDM_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.SMS_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 短信
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.SMS_MARKETING_SOURCE_NAME);
            } else if (OfficialWebsiteConstants.EMAIL_MARKETING_SOURCE_NAME_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 邮件
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.EMAIL_MARKETING_SOURCE_NAME);
            } else if (StringUtils.isBlank(customizeFormDataUserEntity.getSubmitContent().getUtmSource())) {
                // 其它
                customizeFormDataUserEntity.getSubmitContent().setUtmSource(OfficialWebsiteConstants.OTHER_MARKETING_SOURCE_NAME);
            }
            // utmMedium
            if (OfficialWebsiteConstants.UTM_MEDIUM_SEM_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium(OfficialWebsiteConstants.UTM_MEDIUM_SEM);
            } else if (OfficialWebsiteConstants.UTM_MEDIUM_SEO_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium(OfficialWebsiteConstants.UTM_MEDIUM_SEO);
            } else if (OfficialWebsiteConstants.UTM_MEDIUM_INFORMATION_FLOW_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium(OfficialWebsiteConstants.UTM_MEDIUM_INFORMATION_FLOW);
            } else if (OfficialWebsiteConstants.UTM_MEDIUM_MARKETING_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium(OfficialWebsiteConstants.UTM_MEDIUM_MARKETING);
            } else if (StringUtils.isBlank(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium(OfficialWebsiteConstants.UTM_MEDIUM_OTHER);
            }
        }
    }

    /**
     * 批量获取微信粉丝信息
     */
    public Map<String, WechatFanDetailResult> batchesGetFanInfo(String ea, List<QueryFormUserDataResult> queryFormUserDataResultList) {
        Map<String, Set<String>> wxInfoMap = Maps.newHashMap();
        Map<String, WechatFanDetailResult> result = Maps.newHashMap();
        try {
            for (QueryFormUserDataResult queryFormUserDataResult : queryFormUserDataResultList) {
                if (StringUtils.isNotBlank(queryFormUserDataResult.getOpenId()) && StringUtils.isNotBlank(queryFormUserDataResult.getWxAppId())) {
                    if (wxInfoMap.containsKey(queryFormUserDataResult.getWxAppId())) {
                        wxInfoMap.get(queryFormUserDataResult.getWxAppId()).add(queryFormUserDataResult.getOpenId());
                    } else {
                        wxInfoMap.put(queryFormUserDataResult.getWxAppId(), Sets.newHashSet(queryFormUserDataResult.getOpenId()));
                    }
                }
            }
            for (Map.Entry<String, Set<String>> entry : wxInfoMap.entrySet()) {
                FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, entry.getValue().size());
                paasQueryArg.addFilter("wx_app_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList(entry.getKey()));
                paasQueryArg.addFilter("wx_open_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(entry.getValue()));

                findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
                findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
                InnerPage<ObjectData> innerPage = crmMetadataManager.listV3(ea, -10000, findByQueryV3Arg);
                if (innerPage != null && CollectionUtils.isNotEmpty(innerPage.getDataList())) {
                    /** 微信头像昵称 */
                    List<WechatFanData> wechatFanDataList = innerPage.getDataList().stream().map(o -> WechatFanData.wrap(o)).collect(Collectors.toList());
                    List<String> nPathList = wechatFanDataList.stream().filter(o->o.getWxHeadImage()!=null).map(o -> (String)o.getWxHeadImage().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)).collect(Collectors.toList());
                    Map<String, String> wechatAvaterUrlMap = fileV2Manager.batchGetUrlByPath(nPathList, ea, false);
                    wechatFanDataList.forEach(data->{
                        WechatFanDetailResult wechatFanDetailResult = new WechatFanDetailResult();
                        wechatFanDetailResult.setWxAppId(data.getWxAppId());
                        wechatFanDetailResult.setWxOpenId(data.getWxOpenId());
                        wechatFanDetailResult.setNickname(String.valueOf(data.get("name")));
                        wechatFanDetailResult.setHeadImgUrl(data.getWxHeadImage()!=null ? wechatAvaterUrlMap.get(data.getWxHeadImage().get(0).get(WeChatAvatarData.WeChatAvatar.PATH)):null);
                        result.put(data.getWxAppId() + "#" + data.getWxOpenId(),wechatFanDetailResult);
                    });
                }
            }
        } catch (Exception e) {
            log.warn("CustomizeFormDataManager.batchesGetFanInfo error ea:{},message:{}",ea,e);
        }
        return result;
    }

    /**
     * 构建导出表单报名数据
     */
    public ExportEnrollsDataResult buildExportEnrollsData(String ea, String objectId, Integer objectType, String marketingActivityId, String marketingEventId, Integer type, Integer usage, Integer sourceType) {
        ExportEnrollsDataResult result = new ExportEnrollsDataResult();
        try {
            CustomizeFormDataEntity customizeFormDataEntity = new CustomizeFormDataEntity();
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = Lists.newArrayList();
            String fileName = null;
            if (type.equals(QueryFormUserDataTypeEnum.OBJECT.getType())) {
                // 查询物料对应表单
                customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                if (customizeFormDataEntity == null) {
                    // 若物料解绑表单则查询报名数据最新表单
                    customizeFormDataEntity = this.getLatestEnrollDataForm(type, objectId, objectType, null, null, ea);
                    if (customizeFormDataEntity == null) {
                        log.warn("CustomizeFormDataManager.buildExportEnrollsData error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, type:{}", ea, objectId, objectType, type);
                        return null;
                    }
                }
                // 查询对应表单报名数据
                if (ObjectTypeEnum.ACTIVITY.getType() == objectType) {
                    List<String> objectIds = activityManager.getActivityLinkObject(objectId, objectType, Lists.newArrayList(ObjectTypeEnum.ACTIVITY_INVITATION));
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByObjectsWithOutPage(objectIds, usage);
                } else {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByObjectWithOutPage(objectId, objectType, usage);
                }
                fileName = objectManager.getObjectName(objectId, objectType);
                /*buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
                result.setFileName(objectManager.getObjectName(objectId, objectType));
                result.setTitleList(this.generateExcelTitleList(customizeFormDataEntity.getFormBodySetting(), usage));
                result.setEnrollInfoList(this.generateExcelEnrollInfosList(customizeFormDataUserEntityList, customizeFormDataEntity.getFormBodySetting(), usage));*/
            } else if (type.equals(QueryFormUserDataTypeEnum.MARKETING_ACTIVITY.getType()) ||type.equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())) {
                // 查询物料对应表单
                customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                if (customizeFormDataEntity == null) {
                    // 若物料解绑表单则查询报名数据最新表单
                    customizeFormDataEntity = this.getLatestEnrollDataForm(type, null, null, marketingActivityId, null, ea);
                    if (customizeFormDataEntity == null) {
                        log.warn("CustomizeFormDataManager.buildExportEnrollsData error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, type:{}", ea, objectId, objectType, type);
                        return null;
                    }
                }
                // 查询对应表单报名数据
                customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityIdWithOutPage(marketingActivityId, usage);
                fileName = customizeFormDataEntity.getFormHeadSetting().getName();
                /*buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
                result.setFileName(customizeFormDataEntity.getFormHeadSetting().getName());
                result.setTitleList(this.generateExcelTitleList(customizeFormDataEntity.getFormBodySetting(), usage));
                result.setEnrollInfoList(this.generateExcelEnrollInfosList(customizeFormDataUserEntityList, customizeFormDataEntity.getFormBodySetting(), usage));*/
            } else if (type.equals(QueryFormUserDataTypeEnum.FORM_ID.getType()) || type.equals(QueryFormUserDataTypeEnum.ENROLL_SOURCE_TYPE.getType())) {
                // 根据表单id查询数据
                // 查询对应表单数据
                customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(objectId);
                if (customizeFormDataEntity == null) {
                    log.warn("CustomizeFormDataManager.buildExportEnrollsData error customizeFormDataEntity is null formId:{}, type:{}", objectId, type);
                    return null;
                }
                // 查询对应表单报名数据
                customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByFormIdWithOutPage(objectId, usage, sourceType);
                if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                    return null;
                }
                fileName = customizeFormDataEntity.getFormHeadSetting().getName();
                /*buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
                result.setFileName(customizeFormDataEntity.getFormHeadSetting().getName());
                result.setTitleList(this.generateExcelTitleList(customizeFormDataEntity.getFormBodySetting(), usage));
                result.setEnrollInfoList(this.generateExcelEnrollInfosList(customizeFormDataUserEntityList, customizeFormDataEntity.getFormBodySetting(), usage));*/
            } else if (type.equals(QueryFormUserDataTypeEnum.MARKETING_EVENT.getType())) {
                // 根据市场活动查询数据
                // 查询物料对应表单
                customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                if (customizeFormDataEntity == null) {
                    // 若物料解绑表单则查询报名数据最新表单
                    customizeFormDataEntity = this.getLatestEnrollDataForm(type, objectId, objectType, null, marketingEventId, ea);
                    if (customizeFormDataEntity == null) {
                        log.warn(
                            "CustomizeFormDataManager.buildExportEnrollsData error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, marketingActivityId:{}, marketingEventId:{}, type:{}",
                            ea, objectId, objectType, marketingActivityId, marketingEventId, type);
                        return null;
                    }
                }
                // 查询对应表单报名数据（若物料为表单则查询市场活动下全部数据）
                if (ObjectTypeEnum.CUSTOMIZE_FORM.getType() == objectType) {
                    // 查询表单是否为微页面表单
                    List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getPageByFormId(objectId);
                    if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
                        // 若表单为微页面表单则查询市场活动下全部数据
                        customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndFormIdWithOutPage(objectId, marketingEventId, usage);
                    } else {
                        customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndObjectWithOutPage(objectId, objectType, marketingEventId, usage);
                    }
                } else {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndObjectWithOutPage(objectId, objectType, marketingEventId, usage);
                }
                if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                    return null;
                }
                fileName = objectManager.getObjectName(objectId, objectType);
                /*buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
                result.setFileName(objectManager.getObjectName(objectId, objectType));
                result.setTitleList(this.generateExcelTitleList(customizeFormDataEntity.getFormBodySetting(), usage));
                result.setEnrollInfoList(this.generateExcelEnrollInfosList(customizeFormDataUserEntityList, customizeFormDataEntity.getFormBodySetting(), usage));*/
            }else if (type.equals(QueryFormUserDataTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType())) {
                int count = groupSendTaskDAO.getGroupSendAttachments(marketingActivityId);
                if(count>0){
                    //只有一条,走旧逻辑
                    List<MarketingActivityObjectRelationEntity> relationEntities = marketingActivityObjectRelationDAO.queryByMarketingActivityId(ea, marketingActivityId);
                    if(relationEntities.size()==1){
                        // 查询物料对应表单
                        customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                        if (customizeFormDataEntity == null) {
                            // 若物料解绑表单则查询报名数据最新表单
                            customizeFormDataEntity = this.getLatestEnrollDataForm(type, null, null, marketingActivityId, null, ea);
                            if (customizeFormDataEntity == null) {
                                log.warn("CustomizeFormDataManager.buildExportEnrollsData error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, type:{}", ea, objectId, objectType, type);
                                return null;
                            }
                        }
                        // 查询对应表单报名数据
                        customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityIdWithOutPage(marketingActivityId, usage);
                        fileName = customizeFormDataEntity.getFormHeadSetting().getName();
                        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                            return null;
                        }
                    }else {
                        //走新逻辑,直接取报名数据,表单内容写死,5个默认表单字段姓名,手机,公司,邮箱,职务
                        customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityIdWithOutPage(marketingActivityId, usage);
                        fileName = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1677);
                        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                            return null;
                        }
                        String defaultFieldInfoStr = "[{\"apiName\":\"name\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入姓名\",\"isRequired\":true,\"isVerify\":false,\"label\":\"姓名\",\"type\":\"text\"},{\"apiName\":\"phone\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入手机号\",\"isRequired\":true,\"isVerify\":false,\"label\":\"手机号\",\"type\":\"phone_number\"},{\"apiName\":\"companyName\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入公司名称\",\"isRequired\":true,\"isVerify\":false,\"label\":\"公司名称\",\"type\":\"text\"},{\"apiName\":\"email\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入邮箱\",\"isRequired\":true,\"isVerify\":false,\"label\":\"邮箱\",\"type\":\"email\"},{\"apiName\":\"position\",\"dateFormat\":\"yyyy-MM-dd HH:mm\",\"defineType\":\"custom\",\"helpText\":\"请输入职务\",\"isRequired\":true,\"isVerify\":false,\"label\":\"职务\",\"type\":\"text\"}]";
                        FieldInfoList defaultFieldInfoList = JSON.parseObject(defaultFieldInfoStr,FieldInfoList.class);
                        // 转换地区信息
                        buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
                        // 转换utmMedium与source（目前只针对fs）
                        buildUtmMediumAndSource(customizeFormDataUserEntityList, ea);
                        result.setFileName(fileName);
                        result.setTitleList(this.generateExcelTitleList(defaultFieldInfoList, usage, sourceType, type));
                        result.setEnrollInfoList(this.generateExcelEnrollInfosList(ea, customizeFormDataUserEntityList, defaultFieldInfoList, usage, sourceType, type));
                        return result;
                    }
                }else {
                    // 查询物料对应表单
                    customizeFormDataEntity = this.getBindFormDataByObject(ea, objectId, objectType);
                    if (customizeFormDataEntity == null) {
                        // 若物料解绑表单则查询报名数据最新表单
                        customizeFormDataEntity = this.getLatestEnrollDataForm(type, null, null, marketingActivityId, null, ea);
                        if (customizeFormDataEntity == null) {
                            log.warn("CustomizeFormDataManager.buildExportEnrollsData error customizeFormDataEntity is null ea:{}, objectId:{}, objectType:{}, type:{}", ea, objectId, objectType, type);
                            return null;
                        }
                    }
                    // 查询对应表单报名数据
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityIdWithOutPage(marketingActivityId, usage);
                    fileName = customizeFormDataEntity.getFormHeadSetting().getName();
                }
            }
            // 转换地区信息
            buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
            // 转换utmMedium与source（目前只针对fs）
            buildUtmMediumAndSource(customizeFormDataUserEntityList, ea);
            result.setFileName(fileName);
            result.setTitleList(this.generateExcelTitleList(customizeFormDataEntity.getFormBodySetting(), usage, sourceType, type));
            result.setEnrollInfoList(this.generateExcelEnrollInfosList(ea, customizeFormDataUserEntityList, customizeFormDataEntity.getFormBodySetting(), usage, sourceType, type));
            return result;
        } catch (Exception e) {
            log.warn("CustomizeFormDataManager.buildExportEnrollsData error e: {}", e);
            return null;
        }
    }

    private List<String> generateExcelTitleList(FieldInfoList fieldInfoList, Integer usage, Integer sourceType, Integer type) {
        List<String> titleList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fieldInfoList)) {
            return titleList;
        }
        for (FieldInfo fieldInfo : fieldInfoList) {
            titleList.add(fieldInfo.getLabel());
        }
        if (sourceType != null && sourceType.equals(CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType())) {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1731));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1732));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1733));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1734));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1735));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1736));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1076));
        }
        if (FormDataUsage.isForCollectOrder(usage)) {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1740));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1741));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1742));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1743));
        }
        if (type.equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())) {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1746));
        }
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1073));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1076));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1750));
        return titleList;
    }

    private List<List<Object>> generateExcelEnrollInfosList(String ea, List<CustomizeFormDataUserEntity> customizeFormDataEnrollList, FieldInfoList fieldInfoList, Integer usage, Integer sourceType, Integer type) {
        List<List<Object>> enrollInfoList = Lists.newArrayList();
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        if (turnOnPhoneNumberSensitive) {
            safetyManagementManager.phoneNumberSensitive(customizeFormDataEnrollList);
        }
        // 查询来源渠道
        Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(ea);
        // 推广人信息
        List<Integer> spreadUserIdList = customizeFormDataEnrollList.stream().map(CustomizeFormDataUserEntity::getSpreadFsUid).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, spreadUserIdList, true);
        Map<Integer, String> outUserNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(ea, spreadUserIdList);
        // spreadUid和伙伴的信息map
        Map<Integer, UserRelationPartnerInfo> fsUserIdToPartnerInfoMap = userRelationManager.getPartnerInfoByFsUserIdList(ea, spreadUserIdList);
        Set<String> payOrderIdSet = customizeFormDataEnrollList.stream().filter(Objects::nonNull).map(CustomizeFormDataUserEntity::getPayOrderId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, FsPayOrder> payOrderMap = new HashMap<>();
        if (payOrderIdSet != null && !payOrderIdSet.isEmpty()) {
            List<FsPayOrder> fsPayOrders = fsPayOrderDao.queryByIds(payOrderIdSet);
            if (fsPayOrders != null) {
                payOrderMap = fsPayOrders.stream().collect(Collectors.toMap(FsPayOrder::getId, Function.identity(), (o, n) -> o));
            }
        }
        Map<String, String> picMap = conversionEnrollDataPic(ea, customizeFormDataEnrollList);
        Map<String, String> fileAttachmentMap = conversionEnrollDataFileAttachment(ea, customizeFormDataEnrollList);
        for (CustomizeFormDataUserEntity customizeFormDataEnroll : customizeFormDataEnrollList) {
            List<Object> enrollInfos = Lists.newArrayList();
            for (FieldInfo fieldInfo : fieldInfoList) {
                Object result =  formatEnrollDataIncludeSpecialField(fieldInfo, customizeFormDataEnroll, picMap, false);
                if ((fieldInfo.getType().equals(FieldInfo.Type.FILE_ATTACHMENT.getValue()))){
                    if (result != null) {
                        List<FileAttachmentContainer> fileContainerList = (List<FileAttachmentContainer>) result;
                        List<String> fileLinks = fileContainerList.stream().map(file -> fileAttachmentMap.get(file.getPath())).collect(Collectors.toList());
                        enrollInfos.add(fileLinks);
                    }else{
                        enrollInfos.add("");
                    }
                }else {
                    enrollInfos.add(result);
                }
            }
            if (sourceType != null && sourceType.equals(CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType())) {
                enrollInfos.add(customizeFormDataEnroll.getSubmitContent().getUtmSource());
                enrollInfos.add(customizeFormDataEnroll.getSubmitContent().getUtmMedium());
                enrollInfos.add(customizeFormDataEnroll.getSubmitContent().getUtmCampaig());
                enrollInfos.add(customizeFormDataEnroll.getSubmitContent().getUtmContent());
                enrollInfos.add(customizeFormDataEnroll.getSubmitContent().getUtmTerm());
                enrollInfos.add(LeadMarketingSourceTypeEnum.getNameByType(customizeFormDataEnroll.getSubmitContent().getMarketingSourceType()));
                enrollInfos.add(LeadMarketingSourceNameEnum.getNameByType(customizeFormDataEnroll.getSubmitContent().getMarketingSourceName()));
            }
            //            titleList.add("产品名称");
            //            titleList.add("支付金额");
            //            titleList.add("支付状态");
            //            titleList.add("交易单号");
            if (FormDataUsage.isForCollectOrder(usage)) {
                FsPayOrder fsPayOrder = payOrderMap.get(customizeFormDataEnroll.getPayOrderId());
                if (fsPayOrder != null) {
                    enrollInfos.add(fsPayOrder.getGoodsName());
                    enrollInfos.add(new BigDecimal(fsPayOrder.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue());
                    Integer status = fsPayOrder.getStatus();
                    if (status != 1) {
                        enrollInfos.add(PayOrderState.NOT_PAY.getLabel());
                    } else {
                        enrollInfos.add(PayOrderState.SUCCESS.getLabel());
                    }
                    enrollInfos.add(customizeFormDataEnroll.getPayOrderId());
                } else {
                    enrollInfos.add(null);
                    enrollInfos.add(null);
                    enrollInfos.add(PayOrderState.NOT_EXISTED.getLabel());
                    enrollInfos.add(customizeFormDataEnroll.getPayOrderId());
                }
            }
            // 伙伴名称
            if (type.equals(QueryFormUserDataTypeEnum.PARTNER_ACTIVITY.getType())) {
                UserRelationPartnerInfo userRelationPartnerInfo = fsUserIdToPartnerInfoMap.get(customizeFormDataEnroll.getSpreadFsUid());
                String outTenantName = "";
                String spreadUserName = "";
                if (userRelationPartnerInfo != null) {
                    if (StringUtils.isNotBlank(userRelationPartnerInfo.getOuterTenantName())) {
                        outTenantName = userRelationPartnerInfo.getOuterTenantName();
                    }
                    if (StringUtils.isNotBlank(userRelationPartnerInfo.getOuterUserName())) {
                        spreadUserName = userRelationPartnerInfo.getOuterUserName();
                    }
                }
                enrollInfos.add(outTenantName);
                enrollInfos.add(spreadUserName);
            } else {
                FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(customizeFormDataEnroll.getSpreadFsUid());
                if (fsEmployeeMsg != null) {
                    enrollInfos.add(fsEmployeeMsg.getName());
                } else if (outUserNameMap.containsKey(customizeFormDataEnroll.getSpreadFsUid()) && StringUtils.isNotBlank(outUserNameMap.get(customizeFormDataEnroll.getSpreadFsUid()))) {
                    enrollInfos.add(outUserNameMap.get(customizeFormDataEnroll.getSpreadFsUid()));
                } else {
                    enrollInfos.add(null);
                }
            }
            enrollInfos.add(spreadChannelManager.getChannelLabelByChannelValue(ea, channelValueMap, customizeFormDataEnroll.getChannelValue()));
            enrollInfos.add(DateUtil.format(customizeFormDataEnroll.getCreateTime()));
            enrollInfoList.add(enrollInfos);
        }
        return enrollInfoList;
    }


    public Map<String, Object> generateEnrollData(CustomizeFormDataUserEntity customizeFormDataUserEntity, FieldInfoList fieldInfoList, Map<String, String> urlMap, boolean needCreateTime) {
        Map<String, Object> resultMap = Maps.newHashMap();
        for (FieldInfo fieldInfo : fieldInfoList) {
            if (StringUtils.isBlank(fieldInfo.getApiName())) {
                continue;
            }
            Object result = formatEnrollDataIncludeSpecialField(fieldInfo, customizeFormDataUserEntity, urlMap, true);
            if (fieldInfo.getApiName().contains(CustomizeFormDataConstants.TIME_KEY) && result != null) {
                result = DateUtil.format((Long) result);
            }
            resultMap.put(fieldInfo.getApiName(), result);
        }
        if (needCreateTime) {
            resultMap.put("user_enroll_time", DateUtil.format(customizeFormDataUserEntity.getCreateTime()));
        }
        return resultMap;
    }
    /**
     * 表单报名特殊数据处理
     */
    public void handlerSpecialTypeEnrollData(CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        Integer objectType = customizeFormDataUserEntity.getObjectType();
        String objectId = customizeFormDataUserEntity.getObjectId();
        String parentObjectId = customizeFormDataUserEntity.getParentObjectId();
        Integer parentObjectType = customizeFormDataUserEntity.getParentObjectType();
        if (Objects.equals(ObjectTypeEnum.PRODUCT.getType(), objectType)) {
            ProductEntity productEntity = productDAO.getById(objectId);
            if (productEntity != null) {
                // 报名数+1
                productDAO.updateSubmitCount(productEntity.getId(), (productEntity.getSubmitCount() == null ? 0 : productEntity.getSubmitCount()) + 1);
            }
        }
        if (Objects.equals(ObjectTypeEnum.PRODUCT.getType(), parentObjectType)) {
            ProductEntity productEntity = productDAO.getById(parentObjectId);
            if (productEntity != null) {
                // 报名数+1
                productDAO.updateSubmitCount(productEntity.getId(), (productEntity.getSubmitCount() == null ? 0 : productEntity.getSubmitCount()) + 1);
            }
        }
    }

    public void saveCrmLead(String ea, Integer operatorFsUserId, CustomizeFormDataEnroll customizeFormDataEnroll, CustomizeFormDataEntity customizeFormDataEntity, boolean updateData, String leadId, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        String marketingEventId = customizeFormDataUserEntity.getMarketingEventId();
        String marketingActivityId = customizeFormDataUserEntity.getMarketingActivityId();
        String customizeFormDataUserId = customizeFormDataUserEntity.getId();
        Integer spreadFsUserId = customizeFormDataUserEntity.getSpreadFsUid();
        String channelValue = customizeFormDataUserEntity.getChannelValue();
        String objectId = customizeFormDataUserEntity.getObjectId();
        Integer objectType = customizeFormDataUserEntity.getObjectType();
        if (updateData && StringUtils.isBlank(leadId)) {
            log.warn("saveCrmLead isUpdateData leadId is null");
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
            customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
            return;
        }
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        if (StringUtils.isBlank(ea)) {
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
            customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.EA_NOT_NULL));
            return;
        }
        try {
            if (customizeFormDataEntity.getCrmFormFieldMapV2() == null || customizeFormDataEntity.getCrmFormFieldMapV2().size() == 0) {
                //customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.UN_SAVE.getStatus());
                //customizeFormDataEnroll.setLeadMessage(CrmStatusMessageConstant.FIELD_MAPPING_NOT_NULL);
                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
                customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.DATA_SETTING_NOT_NULL));
                return;
            }
            Set<String> notNullCrmLeadFieldNames = crmV2Manager.getObjectFieldNameList(ea, CrmObjectApiNameEnum.CRM_LEAD);
            boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmLeadFieldNames);
            if (!verifyResult) {
                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
                customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.VERIFY_CRM_FIELDS_NOT_NULL));
                return;
            }
            //首先通过表单提交手机号判断是否有重复的活动成员数据(7.4 表单提交逻辑修改)  如果有数据,则直接合并到原有的活动成员上
            String phone = customizeFormDataUserEntity.getSubmitContent().getPhone();
            MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
            //判断是否开启不自动合并手机号设置
            boolean openMergePhone  = false;
            if (marketingEventCommonSettingEntity != null && marketingEventCommonSettingEntity.getOpenMergePhone() != null && marketingEventCommonSettingEntity.getOpenMergePhone()) {
                openMergePhone = true;
            }
            if (!updateData && !openMergePhone && StringUtils.isNotBlank(phone) && StringUtils.isNotBlank(marketingEventId)) {
                List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(ea, marketingEventId, phone);
                if (CollectionUtils.isNotEmpty(campaignMergeDataEntities)) {
                    CustomizeFormDataUserEntity formDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIdOrderByCreateTime(campaignMergeDataEntities.get(0).getId());
                    //只有相同手机号,并且已存入线索的才可以进行合并(需要考虑存入失败的场景)
                    if (formDataUserEntity != null && (StringUtils.isNotBlank(formDataUserEntity.getLeadId()) || formDataUserEntity.getOtherCrmObjectBind() != null)) {
                        customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_SUCCESS.getStatus());
                        customizeFormDataEnroll.setNewSave(false);
                        if (StringUtils.isNotBlank(formDataUserEntity.getLeadId())) {
                            customizeFormDataEnroll.setLeadId(formDataUserEntity.getLeadId());
                        } else if (formDataUserEntity.getOtherCrmObjectBind() != null){
                            //关联客户或者联系人
                            customizeFormDataEnroll.setLeadId(formDataUserEntity.getOtherCrmObjectBind().getObjectId());
                        }
                        if (StringUtils.isNotBlank(campaignMergeDataEntities.get(0).getCampaignMembersObjId())) {
                            //处理合并表单,表单映射活动成员自定义字段问题
                            String formId = formDataUserEntity.getFormId();
                            //根据表单id ,查询表单映射活动成员配置
                            CustomizeFormDataEntity formDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
                            FieldMappings campaignMemberMap = formDataEntity.getCampaignMemberMap();
                            if (CollectionUtils.isNotEmpty(campaignMemberMap)) {
                                //更新活动成员对象自定义字段
                                Map<String, Object> customizeFormDataToCampaignFieldDataMap = crmV2MappingManager.createCustomizeFormDataToCampaignFieldDataMap(formDataEntity, customizeFormDataUserEntity.getSubmitContent());
                                ThreadPoolUtils.execute(() ->{
                                    crmV2Manager.updateCampaign(campaignMergeDataEntities.get(0).getCampaignMembersObjId(),ea,-10000,customizeFormDataToCampaignFieldDataMap);
                                },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                            }
                        }
                        return;
                    }
                }
            }
            Map<String, Object> data = crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(customizeFormDataEntity, CrmObjectApiNameEnum.CRM_LEAD, customizeFormDataEnroll);
            if (StringUtils.isNotBlank(marketingEventId) && !updateData) {
                data.put(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), marketingEventId);
            } else if (StringUtils.isBlank(marketingEventId) && StringUtils.isNotBlank(marketingActivityId) && !updateData) {
                MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
                if (externalConfigEntity != null && StringUtils.isNotBlank(externalConfigEntity.getMarketingEventId())) {
                    data.put(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), externalConfigEntity.getMarketingEventId());
                }
            }
//            DuplicatesearchQueryResult duplicatesearchQueryResult = crmV2Manager.queryDuplicate(ea, operatorFsUserId, LeadsFieldContants.API_NAME, data);
//            if (null == duplicatesearchQueryResult) {
//                log.warn("CustomizeFormDataManager.saveCrmLead error duplicatesearchQueryResult is null customizeFormDataEnroll:{}", customizeFormDataEnroll);
//                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
//                customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
//                return;
//            } else {
//                log.warn("CustomizeFormDataManager.saveCrmLead customizeFormDataEnroll:{} duplicatesearchQueryResult {}", JSONObject.toJSONString(customizeFormDataEnroll), JSONObject.toJSONString(duplicatesearchQueryResult));
//            }

//            List<Map<String, Object>> duplicateDatas = new ArrayList<>();
//
//            if (duplicatesearchQueryResult.getData() != null) {
//                duplicateDatas.addAll(duplicatesearchQueryResult.getData());
//            }
//
//            if (!updateData && duplicatesearchQueryResult.getDuplicateMode() == CrmDuplicateSearchVo.DuplicateMode.SAME) {
//                List<Map<String, Object>> relatedDuplicateSearch = relatedDuplicateSearch(ea, customizeFormDataEnroll, data);
//                duplicateDatas.addAll(relatedDuplicateSearch);
//                if (CollectionUtils.isNotEmpty(duplicateDatas)) {
//                    handleDuplicatedData(ea, customizeFormDataEnroll, customizeFormDataUserEntity, duplicateDatas, marketingEventId, marketingActivityId);
//                    return;
//                } else {
//                    customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_SUCCESS.getStatus());
//                    boolean keepSave = handleRelatedDuplicateSearchData(ea, LeadsFieldContants.API_NAME, data, customizeFormDataUserId);
//                    if (!keepSave) {
//                        return;
//                    }
//                }
//            } else if(!updateData && duplicatesearchQueryResult.getDuplicateMode() == CrmDuplicateSearchVo.DuplicateMode.LIKE) {
//                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_SUCCESS.getStatus());
//                boolean keepSave = handleRelatedDuplicateSearchData(ea, LeadsFieldContants.API_NAME, data, customizeFormDataUserId);
//                if (!keepSave) {
//                    return;
//                }
//            }

            handleLeadsSpecialField(ea, customizeFormDataEnroll, data);
            // 增加营销活动
            if (StringUtils.isNotBlank(marketingActivityId)) {
                data.put(OfficialWebsiteConstants.MARKETING_ACTIVITY_ID, marketingActivityId);
            }
            // 增加推广人
            if (spreadFsUserId != null && spreadFsUserId != -10000 && !QywxUserConstants.isVirtualUserId(spreadFsUserId)) {
                data.put(OfficialWebsiteConstants.MARKETING_SPREAD_USER, Lists.newArrayList(spreadFsUserId.toString()));
            }
            // 增加来源渠道
            spreadChannelManager.buildCRMChannelData(data, channelValue);
            //处理开启多组织,线索归属组织字段存入
            data = clueManagementManager.addCustomizeFormOrganizationData(data, ea,marketingEventId,customizeFormDataEntity);
            // 表单统一设置
            data = clueManagementManager.addCustomizeFormCommonData(data, ea, objectId, objectType, customizeFormDataEnroll);
            data.put("from_marketing", true);

            // 伙伴推广数据设置
            if (customizeFormDataUserEntity.isPartner() || StringUtils.isNotEmpty(customizeFormDataUserEntity.getOutUid())) {
                // 伙伴推广人id
                data.put(OfficialWebsiteConstants.MARKETING_PARTNER_PROMOTER, Lists.newArrayList(customizeFormDataUserEntity.getOutUid()));
                // 伙伴企业id
                String mktPartner = this.getCustomerByEnterpriserelationId(ea, customizeFormDataUserEntity.getOutTenantId());
                if (StringUtils.isNotEmpty(mktPartner)) {
                    data.put(OfficialWebsiteConstants.MARKETING_PARTNER, mktPartner);
                }
            }
            if (StringUtils.isNotBlank(customizeFormDataUserEntity.getLandingObjId())) {
                data.put("landing_page_id", customizeFormDataUserEntity.getLandingObjId());
            }
            CreateLeadResult createLeadResult = null;
            if (updateData) {
                createLeadResult = crmV2Manager.updateLead(leadId, ea, operatorFsUserId, data);
            } else {
                if (StringUtils.isNotBlank(customizeFormDataEnroll.getMarketingPromotionSourceId()) ) {
                    data.put(CrmLeadFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getFieldName(), customizeFormDataEnroll.getMarketingPromotionSourceId());
                }
                // 虽然上面有查重，但是有可能会存在并发问题，导致查重结果查不到，但是实际上游重复，这里创建线索时，对于查重paas有做并发处理
                if (StringUtils.isBlank(customizeFormDataEntity.getCrmPoolId())) {
                    createLeadResult = crmV2Manager.createLead(ea, operatorFsUserId, data, true, true);
                } else {
                    createLeadResult = crmV2Manager.createLead(ea, operatorFsUserId, data, false, true);
                }
            }
            if (null == createLeadResult) {
                log.warn("CustomizeFormDataManager.saveCrmLead error createLeadResult is null customizeFormDataEnroll:{}", customizeFormDataEnroll);
                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
                customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
                return;
            }
            if (createLeadResult.getCode() != null && createLeadResult.getCode() == CrmConstants.REPEAT_CODE) {
                List<Map<String, Object>> duplicateDataList = crmV2Manager.queryDuplicateV2(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), data);
                if (CollectionUtils.isNotEmpty(duplicateDataList)) {
                    handleDuplicatedData(ea, customizeFormDataEnroll, customizeFormDataUserEntity, duplicateDataList, marketingEventId, marketingActivityId);
                    return;
                }
                log.warn("CustomizeFormDataManager.saveCrmLead lead is duplicated, but do not found target duplicated data, customizeFormDataEnroll: {} data: {}", customizeFormDataEnroll, data);
                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
                customizeFormDataEnroll.setLeadMessage(I18nUtil.getStaticByKey(I18nKeyStaticEnum.MARK_STATIC_CRMSTATUSMESSAGECONSTANT_LEAD_REPETITION.getKey()));
                return;
            }
            if (StringUtils.isNotBlank(createLeadResult.getLeadId())) {
                //如果是更新, 则保持原有的状态
                if (SaveCrmStatusEnum.LINKED.getValue().equals(customizeFormDataUserEntity.getSaveCrmStatus())) {
                    customizeFormDataEnroll.setNewSave(true);
                    customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.LINKED_SUCCESS.getStatus());
                    customizeFormDataEnroll.setLeadId(createLeadResult.getLeadId());
                    customizeFormDataEnroll.setLeadMessage(createLeadResult.getMessage());
                } else {
                    customizeFormDataEnroll.setNewSave(true);
                    customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_SUCCESS.getStatus());
                    customizeFormDataEnroll.setLeadId(createLeadResult.getLeadId());
                    customizeFormDataEnroll.setLeadMessage(createLeadResult.getMessage());
                }
            } else {
                customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
                customizeFormDataEnroll.setLeadMessage(this.conversionCrmErrorMessage(createLeadResult.getMessage()));
            }
        } catch (Exception e) {
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_FAIL.getStatus());
            customizeFormDataEnroll.setLeadMessage(this.conversionCrmErrorMessage(e.getMessage()));
            log.error("CustomizeFormDataManager.saveCrmLead customizeFormDataEnroll:{} error:{}", customizeFormDataEnroll, e);
        }
    }

    private List<Map<String, Object>> relatedDuplicateSearch(String ea, CustomizeFormDataEnroll customizeFormDataEnroll, Map<String, Object> data, String duplicateRuleApiName) {
        RelatedDuplicateSearchResult duplicateSearchResult = crmV2Manager.relatedDuplicateSearch(ea, LeadsFieldContants.API_NAME, data, 1, 10000, duplicateRuleApiName);
        if (duplicateSearchResult != null) {
            log.warn("CustomizeFormDataManager.saveCrmLead customizeFormDataEnroll:{} duplicateSearchResult {}", JSONObject.toJSONString(customizeFormDataEnroll), JSONObject.toJSONString(duplicateSearchResult));
            List<RelatedDuplicateSearchResult.DataList> results = duplicateSearchResult.getResults();
            if (results != null) {
                return results.stream().map(RelatedDuplicateSearchResult.DataList::getDataList).reduce(new ArrayList<>(), (all, item) -> {
                            all.addAll(item);
                            return all;
                        });
            }
        }
        return Lists.newArrayList();
    }

    private void handleDuplicatedData(String ea, CustomizeFormDataEnroll customizeFormDataEnroll, CustomizeFormDataUserEntity customizeFormDataUserEntity, List<Map<String, Object>> duplicateDatas, String marketingEventId, String marketingActivityId) {
        // 就算查重点击过滤掉已作废的，接口依旧会返回已作废数据，这里要做兼容，优先匹配没作废的数据
        Map<String, List<Map<String, Object>>> lifeStatusMap = duplicateDatas.stream().filter(e -> e.get(CrmV2LeadFieldEnum.LifeStatus.getNewFieldName()) != null).collect(Collectors.groupingBy(e -> e.get(CrmV2LeadFieldEnum.LifeStatus.getNewFieldName()).toString()));
        List<Map<String, Object>> finalDuplicatedDataList = lifeStatusMap.get("normal");
        if (CollectionUtils.isEmpty(finalDuplicatedDataList)) {
            finalDuplicatedDataList = lifeStatusMap.get("invalid");
        }
        if (CollectionUtils.isEmpty(finalDuplicatedDataList)) {
            log.error("handleDuplicatedData finalDuplicatedDataList is empty, ea: {} customizeFormDataEnroll: {} duplicateDatas: {}", ea, customizeFormDataEnroll, duplicateDatas);
            return;
        }
        Map<String, List<Map<String, Object>>> categoryObjectData = finalDuplicatedDataList.stream()
                .sorted(Comparator.comparing(CustomizeFormDataManager::getTime).reversed())
                .collect(Collectors.groupingBy(e -> String.valueOf(e.get("object_describe_api_name"))));
        Map<String, Object> objMap = null;
        if (categoryObjectData.get(CrmObjectApiNameEnum.CUSTOMER.getName()) != null) {
            objMap = categoryObjectData.get(CrmObjectApiNameEnum.CUSTOMER.getName()).get(0);
        } else if (categoryObjectData.get(CrmObjectApiNameEnum.CONTACT.getName()) != null) {
            objMap = categoryObjectData.get(CrmObjectApiNameEnum.CONTACT.getName()).get(0);
        } else if (categoryObjectData.get(CrmObjectApiNameEnum.CRM_LEAD.getName()) != null) {
            objMap = categoryObjectData.get(CrmObjectApiNameEnum.CRM_LEAD.getName()).get(0);
        }
        if (objMap != null) {
            log.info("CustomizeFormDataManager.saveCrmLead customizeFormDataEnroll:{} objMap:{}", JSONObject.toJSONString(customizeFormDataEnroll), JSONObject.toJSONString(objMap));
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.LINKED_SUCCESS.getStatus());
            customizeFormDataEnroll.setNewSave(false);
            customizeFormDataEnroll.setLeadId(String.valueOf(objMap.get(CrmV2LeadFieldEnum.ID.getNewFieldName())));
            if (!CrmObjectApiNameEnum.CRM_LEAD.getName().equals(objMap.get("object_describe_api_name"))) {
                CustomizeFormBindOtherCrmObject customizeFormBindOtherCrmObject = new CustomizeFormBindOtherCrmObject();
                customizeFormBindOtherCrmObject.setApiName(String.valueOf(objMap.get("object_describe_api_name")));
                customizeFormBindOtherCrmObject.setObjectId(String.valueOf(objMap.get(CrmV2LeadFieldEnum.ID.getNewFieldName())));
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmObjectBind(SaveCrmStatusEnum.LINKED.getValue(), customizeFormDataUserEntity.getId(), customizeFormBindOtherCrmObject);
            }
            this.syncHandleCampaignMembersDataSaveStatus(ea, marketingEventId, marketingActivityId, objMap);
        }
    }

    /**
     * 处理活动成员数据存入状态
     * @param ea
     * @param marketingEventId
     * @param marketingActivityId
     * @param objMap
     */
    public void syncHandleCampaignMembersDataSaveStatus(String ea, String marketingEventId, String marketingActivityId, Map<String, Object> objMap) {
        //处理活动成员数据存入状态 为已关联
        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        campaignMergeDataEntity.setBindCrmObjectType(CampaignMergeDataObjectTypeEnum.getTypeByName(String.valueOf(objMap.get("object_describe_api_name"))));
        campaignMergeDataEntity.setBindCrmObjectId(String.valueOf(objMap.get(CrmV2LeadFieldEnum.ID.getNewFieldName())));
        if (StringUtils.isNotBlank(marketingEventId)) {
            campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        } else if (StringUtils.isBlank(marketingEventId) && StringUtils.isNotBlank(marketingActivityId)) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
            if (externalConfigEntity != null && StringUtils.isNotBlank(externalConfigEntity.getMarketingEventId())) {
                campaignMergeDataEntity.setMarketingEventId(marketingEventId);
            }
        }
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getBindCrmObjectId())
                && campaignMergeDataEntity.getBindCrmObjectType() != null
                && StringUtils.isNotBlank(campaignMergeDataEntity.getMarketingEventId())) {
            String campaignMergeObjId = campaignMergeDataManager.getCampaignMergeObjIdByEntity(ea, campaignMergeDataEntity);
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(CampaignMembersConstants.MARKETING_SAVE_STATUS,CampaignMembersSaveStatusEnum.LINK.getType());
            campaignMergeDataManager.updateCampaignMembersObj(ea,campaignMergeObjId,dataMap);
        }
    }

    public static Long getTime(Map<String, Object> map){
        return ((Double) map.get("last_modified_time")).longValue();
    }

    public static Integer getPriority(Map<String, Object> map){
        return (Integer) map.get("priority");
    }

    /**
     * 根据外部ID获取客户对象详情
     *
     * @param ea
     * @param mktPartner
     * @return
     */
    public String getCustomerByEnterpriserelationId(String ea, String mktPartner) {
        String id = null;
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("enterpriserelation_id", Lists.newArrayList(mktPartner), FilterOperatorEnum.EQ);
        searchQuery.setOffset(0);
        searchQuery.setLimit(1);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> list = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), searchQuery);
        if (null != list && null != list.getDataList() && !list.getDataList().isEmpty()) {
            ObjectData objectData = list.getDataList().get(0);
            id = objectData.getId();
        }
        return id;
    }

    /**
     * 根据外部ID批量获取客户对象详情
     *
     * @param ea
     * @param mktPartners
     * @return
     */
    public List<ObjectData> getCustomerListByEnterpriserelationIds(String ea, List<String> mktPartners) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1000);
        searchQuery.addFilter("enterpriserelation_id", mktPartners, FilterOperatorEnum.IN);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> list = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), searchQuery);
        if (null != list && null != list.getDataList()) {
            return list.getDataList();
        }
        return new ArrayList<>();
    }


    /**
     * 根据外部ID批量获取客户对象详情
     *
     * @param ea
     * @param outerUids
     * @return
     */
    public List<ObjectData> getEmployeeListByOuterUidIds(String ea, List<String> outerUids) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1000);
        searchQuery.addFilter("outer_uid", outerUids, FilterOperatorEnum.IN);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> list = crmV2Manager.getList(ea, -10000, "PublicEmployeeObj", searchQuery);
        if (null != list && null != list.getDataList()) {
            return list.getDataList();
        }
        return new ArrayList<>();
    }



    public Boolean handleRelatedDuplicateSearchData(String ea, String apiName, Map<String, Object> data, String customizeFormDataUserId) {
        // 调用联合查重绑定数据
        Boolean keepSave = false;
        try {
            DuplicateSearchResult duplicateSearchResult = crmV2Manager.duplicateSearchResult(ea, apiName, data, 1, 1);
            if (duplicateSearchResult != null) {
                keepSave = (duplicateSearchResult.getKeepSave() != null) ? duplicateSearchResult.getKeepSave() : false;
            }
            log.info("handleRelatedDuplicateSearchData keepSave:{}", keepSave);
        } catch (Exception e) {
            log.warn("CustomizeFormDataManager.handleRelatedDuplicateSearchData error:{}", e);
        }
        if (!keepSave) {
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserId, SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue(), null, null);
        }
        return keepSave;
    }

    public void handleLeadsSpecialField(String ea, CustomizeFormDataEnroll customizeFormDataEnroll, Map<String, Object> data) {
        if (StringUtils.isBlank(ea)) {
            return;
        }
        if (ea.equals(FsCrmLeadSpecialField.EA)) {
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(crmV2Manager.createHeaderObj(ea, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CRM_LEAD.getName());
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmMedium())) {
                data.put(FsCrmLeadSpecialField.UTM_MEDIUM, getFsOptionsValue(describeResult, FsCrmLeadSpecialField.UTM_MEDIUM, customizeFormDataEnroll.getUtmMedium()));
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmSource())) {
                data.put(FsCrmLeadSpecialField.UTM_SOURCE, getFsOptionsValue(describeResult, FsCrmLeadSpecialField.UTM_SOURCE, customizeFormDataEnroll.getUtmSource()));
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmCampaig())) {
                data.put(FsCrmLeadSpecialField.UTM_CAMPAIG, customizeFormDataEnroll.getUtmCampaig());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmContent())) {
                data.put(FsCrmLeadSpecialField.UTM_CONTENT, customizeFormDataEnroll.getUtmContent());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmTerm())) {
                data.put(FsCrmLeadSpecialField.UTM_TERM, customizeFormDataEnroll.getUtmTerm());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getSearchKeyword())) {
                data.put(FsCrmLeadSpecialField.SEARCH_KEYWORD, customizeFormDataEnroll.getSearchKeyword());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUserStatus())) {
                data.put(FsCrmLeadSpecialField.USER_STATUS, customizeFormDataEnroll.getUserStatus());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getSensorsUserId())) {
                data.put(FsCrmLeadSpecialField.SENSORS_USER_ID, customizeFormDataEnroll.getSensorsUserId());
            }
        } else {
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmMedium())) {
                data.put(OfficialWebsiteConstants.UTM_MEDIUM, customizeFormDataEnroll.getUtmMedium());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmSource())) {
                data.put(OfficialWebsiteConstants.UTM_SOURCE, customizeFormDataEnroll.getUtmSource());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmCampaig())) {
                data.put(OfficialWebsiteConstants.UTM_CAMPAIGN, customizeFormDataEnroll.getUtmCampaig());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmContent())) {
                data.put(OfficialWebsiteConstants.UTM_CONTENT, customizeFormDataEnroll.getUtmContent());
            }
            if (StringUtils.isNotBlank(customizeFormDataEnroll.getUtmTerm())) {
                data.put(OfficialWebsiteConstants.UTM_TERM, customizeFormDataEnroll.getUtmTerm());
            }
        }
        /*if (StringUtils.isNotBlank(customizeFormDataEnroll.getMarketingSourceType())) {
            data.put(OfficialWebsiteConstants.MARKETING_SOURCE_TYPE, customizeFormDataEnroll.getMarketingSourceType());
        }*/
        if (StringUtils.isNotBlank(customizeFormDataEnroll.getMarketingSourceName())) {
            data.put(OfficialWebsiteConstants.MARKETING_SOURCE_NAME, customizeFormDataEnroll.getMarketingSourceName());
        }
    }

    // fs的特殊逻辑，UTM_MEDIUM UTM_SOURCE在某些场景下前端会转换异常，直接传中文了 这里做个兼容
    private String getFsOptionsValue(com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult, String field, String utmValue) {
        try {
            if (describeResult == null || describeResult.getData() == null || describeResult.getData().getDescribe() == null
                    || describeResult.getData().getDescribe().getFields() == null) {
                return utmValue;
            }
            HashMap<String, FieldDescribe> fieldDescribeHashMap = describeResult.getData().getDescribe().getFields();
            FieldDescribe fieldDescribe = fieldDescribeHashMap.get(field);
            if (fieldDescribe != null) {
                for (Map<String, Object> option : fieldDescribe.getOptions()) {
                    if (option.get("label").toString().equals(utmValue)) {
                        return option.get("value").toString();
                    }
                }
            }
        } catch (Exception e) {
            log.error("fs官网提交表单转换utm异常，field: {}, utmValue: {}", field, utmValue, e);
        }
        return utmValue;
    }
    /**
     * 转换crm错误信息
     */
    private String conversionCrmErrorMessage(String message) {
        if (StringUtils.isNotBlank(message)) {
            if (message.contains("http status") || message.contains("response code!=200")) {
                message = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_2328);
            }
        } else {
            message = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_2328);
        }
        return message;
    }

    /**
     * 创建公众号营销用户
     */
    public AssociateWxServiceResult associateWxUser(String wxAppId, String openId, String ea) {
        try {
            if (StringUtils.isBlank(wxAppId) || StringUtils.isBlank(openId) || StringUtils.isBlank(ea)) {
                log.warn("CustomizeFormDataManager.associateWxUser param error wxAppId:{}, openId:{}, ea:{}", wxAppId, openId, ea);
                return null;
            }
            AssociateWxServiceArg associateWxServiceArg = new AssociateWxServiceArg();
            associateWxServiceArg.setWxAppId(wxAppId);
            associateWxServiceArg.setWxOpenId(openId);
            associateWxServiceArg.setEa(ea);
            Result<AssociateWxServiceResult> associateWxServiceResult = wxServiceMarketingAccountAssociationService.associateWxService(associateWxServiceArg);
            if (!associateWxServiceResult.isSuccess() || associateWxServiceResult.getData() == null) {
                log.error("CustomizeFormDataManager.associateWxUser error wxAppId:{}, openId:{}, ea:{}, associateWxServiceResult:{}", wxAppId, openId, ea, associateWxServiceResult);
                return null;
            }
            return associateWxServiceResult.getData();
        } catch (Exception e) {
            log.error("CustomizeFormDataManager.associateWxUser exception wxAppId:{}, openId:{}, ea:{}, e:{}", wxAppId, openId, ea, e);
            return null;
        }
    }

    /**
     * 关联无身份与线索
     * @param fingerPrint
     * @param ea
     * @param crmLeadId
     * @param phone
     */
    private void bindBrowserUserAndCrmId(String fingerPrint, String ea, String crmLeadId, String phone) {
        try {
            if (StringUtils.isBlank(fingerPrint) || StringUtils.isBlank(ea) || StringUtils.isBlank(crmLeadId)) {
                log.warn("CustomizeFormDataManager.bindBrowserUserAndCrmId param error fingerPrint:{}, ea:{}, crmLeadId:{}", fingerPrint, ea, crmLeadId);
                return;
            }
            
            userMarketingAccountRelationManager.bindBrowserUserAndLead(ea, fingerPrint, crmLeadId, phone, "customizeFormDataEnroll");
        } catch (Exception e) {
            log.warn("CustomizeFormDataManager.bindBrowserUserAndCrmId error e:{}", e);
        }
    }

    /**
     * 设置报名来源
     */
    public int getEnrollType(CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (customizeFormDataUserEntity.getSourceType() != null) {
            return customizeFormDataUserEntity.getSourceType();
        }
        if ((customizeFormDataUserEntity.getObjectType() != null && customizeFormDataUserEntity.getObjectType() == ObjectTypeEnum.OFFICIAL_WEBSITE.getType()) ||
                (customizeFormDataUserEntity.getParentObjectType() != null && customizeFormDataUserEntity.getParentObjectType() == ObjectTypeEnum.OFFICIAL_WEBSITE.getType())) {
            return CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType();
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getMarketingActivityId())) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                .getByMarketingActivityId(customizeFormDataUserEntity.getMarketingActivityId());
            if (marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 全员营销
                return CustomizeFormDataUserSourceTypeEnum.FULL_MARKETING.getType();
            } else if (marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 短信营销
                return CustomizeFormDataUserSourceTypeEnum.SMS_MARKETING.getType();
            } else if (marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 公众号营销
                return CustomizeFormDataUserSourceTypeEnum.WX_OFFICIAL_ACCOUNTS_MARKETING.getType();
            } else if(marketingActivityExternalConfigEntity != null && AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType() == marketingActivityExternalConfigEntity.getAssociateIdType()) {
                // 邮件营销
                return CustomizeFormDataUserSourceTypeEnum.MAIL_SEND.getType();
            }
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())) {
            //小程序 (uid为小程序id)
            return CustomizeFormDataUserSourceTypeEnum.MANKEEP.getType();
        }
        //无身份报名
        return CustomizeFormDataUserSourceTypeEnum.NO_IDENTITY_ENROLL.getType();
    }

    public String getSystemPromotionChannelType(CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getChannelValue())) {
            return customizeFormDataUserEntity.getChannelValue();
        }
        if (CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType().equals(customizeFormDataUserEntity.getSourceType())) {
            // 若utmMedium为SEM 则为广告
            // 将utm的SEO 与 SEM 统一转换为大写
            if (StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getUtmMedium()) && customizeFormDataUserEntity.getSubmitContent().getUtmMedium().toUpperCase().equals("SEM")) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium("SEM");
            }
            if (StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getUtmMedium()) && customizeFormDataUserEntity.getSubmitContent().getUtmMedium().toUpperCase().equals("SEO")) {
                customizeFormDataUserEntity.getSubmitContent().setUtmMedium("SEO");
            }
            if (customizeFormDataUserEntity.getSubmitContent() != null && StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())
                && OfficialWebsiteConstants.UTM_MEDIUM_SEM_LIST.contains(customizeFormDataUserEntity.getSubmitContent().getUtmMedium())) {
                return SystemPromotionChannelEnum.AD.getValue();
            }
            return SystemPromotionChannelEnum.WEBSITE.getValue();
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getMarketingActivityId())) {
            String marketingActivityChannel = spreadChannelManager.getChannelByMarketingActivityId(customizeFormDataUserEntity.getMarketingActivityId(), SystemPromotionChannelEnum.WECHAT);
            if (StringUtils.isNotBlank(marketingActivityChannel)) {
                return marketingActivityChannel;
            }
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getUid()) || (StringUtils.isNotBlank(customizeFormDataUserEntity.getOpenId()) && StringUtils
            .isNotBlank(customizeFormDataUserEntity.getWxAppId()))) {
            return SystemPromotionChannelEnum.WECHAT.getValue();
        }
        return null;
    }

    public String getMarketingSourceType(CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (customizeFormDataUserEntity.getSubmitContent() == null || StringUtils.isBlank(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceType())) {
            return LeadMarketingSourceTypeEnum.MARKETING_SOURCE_OTHER.getType();
        }
        if (Arrays.stream(LeadMarketingSourceTypeEnum.values()).noneMatch(data -> data.getType().equals(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceType()))) {
            return LeadMarketingSourceTypeEnum.MARKETING_SOURCE_OTHER.getType();
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceType())) {
            return customizeFormDataUserEntity.getSubmitContent().getMarketingSourceType();
        }
        return LeadMarketingSourceTypeEnum.MARKETING_SOURCE_OTHER.getType();
    }


    public String getMarketingSourceName(CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (customizeFormDataUserEntity.getSourceType() == null || !CustomizeFormDataUserSourceTypeEnum.OFFICIAL_WEBSITE.getType().equals(customizeFormDataUserEntity.getSourceType())) {
            return null;
        }
        if (customizeFormDataUserEntity.getSubmitContent() == null) {
            return LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_OTHER.getType();
        }
        if (Arrays.stream(LeadMarketingSourceNameEnum.values()).noneMatch(data -> data.getType().equals(customizeFormDataUserEntity.getSubmitContent().getMarketingSourceName()))) {
            return LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_OTHER.getType();
        }
        return customizeFormDataUserEntity.getSubmitContent().getMarketingSourceName();
    }

    /**
     * 报名数据存入CRM
     */
    public boolean saveEnrollDataToCrm(CustomizeFormDataEntity customizeFormDataEntity, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (customizeFormDataUserEntity.getSaveCrmStatus() != null && SaveCrmStatusEnum.SUCCESS.getValue().equals(customizeFormDataUserEntity.getSaveCrmStatus())) {
            return true;
        }
        Boolean isActivityObject = false;
        ActivityEntity activityEntity = new ActivityEntity();
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getMarketingEventId())) {
            activityEntity = conferenceDAO.getConferenceByMarketingEventId(customizeFormDataUserEntity.getMarketingEventId(), customizeFormDataEntity.getEa());
            if (activityEntity != null) {
                isActivityObject = true;
            }
        }
        String triggerAction = "customizeFormDataEnroll";
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getWxAppId()) && StringUtils.isNotBlank(customizeFormDataUserEntity.getOpenId())) {
            // 公众号营销入口
            // 创建公众号营销用户
            Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
            // 获取有权限创建、更新线索的推广人
            spreadFsUid = getAccessibleSpreadFsUid(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataUserEntity.getMarketingEventId(), customizeFormDataUserEntity.getChannelValue());
            CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
            customizeFormDataEnroll.setIpAddr(customizeFormDataUserEntity.getIpAddr());
            customizeFormDataEnroll.setUserAgent(customizeFormDataUserEntity.getUserAgent());
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.UN_SAVE.getStatus());
            customizeFormDataEnroll.setIsDuplicateData(false);
            this.saveCrmLead(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataEnroll, customizeFormDataEntity, false, null, customizeFormDataUserEntity);
            if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() && customizeFormDataEnroll.getIsDuplicateData()) {
                // 重复数据
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm duplicate data  customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() || customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.UN_SAVE
                .getStatus()) {
                // 保存失败
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
                // 保存成功
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.SUCCESS.getValue(), null, customizeFormDataEnroll.getLeadId());
                userMarketingAccountRelationManager.bindWxUserAndLead(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getWxAppId(), customizeFormDataUserEntity.getOpenId(), customizeFormDataEnroll.getLeadId(), customizeFormDataEnroll.getPhone(), triggerAction);
                objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                    ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                if (isActivityObject) {
                    objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                }
                // 修改crmUtm字段
                Integer finalSpreadFsUid = spreadFsUid;
                ThreadPoolUtils.execute(
                    () -> utmDataManger.syncUtmFormDataToROI(customizeFormDataEntity.getEa(), finalSpreadFsUid, customizeFormDataEnroll.getLeadId(), customizeFormDataUserEntity.getSubmitContent()),
                    ThreadPoolTypeEnums.HEAVY_BUSINESS);
                return true;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus()) {
                return true;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.LINKED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
                //关联成功
                log.info("saveEnrollDataToCrm linked success  customizeFormDataEntity:{} customizeFormDataEnroll:{} ", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.LINKED.getValue(), null, customizeFormDataEnroll.getLeadId());
                //userMarketingAccountRelationManager.bindWxUserAndLead(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getWxAppId(), customizeFormDataUserEntity.getOpenId(), customizeFormDataEnroll.getLeadId(), customizeFormDataEnroll.getPhone());
                String objId = customizeFormDataEnroll.getLeadId();
                int type = bindWxUserRepeatDataToUserMarketing(customizeFormDataEntity, customizeFormDataUserEntity, customizeFormDataEnroll);
                objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        type, objId, null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                if (isActivityObject) {
                    objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                            type, objId, null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                }
//                ThreadPoolUtils.execute(() -> associateMarketingUserId(customizeFormDataEntity, customizeFormDataUserEntity), ThreadPoolTypeEnums.HEAVY_BUSINESS);
                return true;
            } else {
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            }
        } else if (StringUtils.isNotBlank(customizeFormDataUserEntity.getFingerPrint())
            || StringUtils.isNotBlank(customizeFormDataUserEntity.getUid())
            || (StringUtils.isNotBlank(customizeFormDataUserEntity.getEnrollUserEa()) && customizeFormDataUserEntity.getEnrollUserFsUid() != null)) {
            // 无身份报名/小程序报名/纷享身份报名
            // 获取有权限创建、更新线索的推广人
            Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
            spreadFsUid = getAccessibleSpreadFsUid(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataUserEntity.getMarketingEventId(), customizeFormDataUserEntity.getChannelValue());
            CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.UN_SAVE.getStatus());
            customizeFormDataEnroll.setIpAddr(customizeFormDataUserEntity.getIpAddr());
            customizeFormDataEnroll.setUserAgent(customizeFormDataUserEntity.getUserAgent());
            customizeFormDataEnroll.setIsDuplicateData(false);
            this.saveCrmLead(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataEnroll, customizeFormDataEntity, false, null, customizeFormDataUserEntity);
            if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() && customizeFormDataEnroll.getIsDuplicateData()) {
                // 重复数据
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm duplicate data  customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() || customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.UN_SAVE
                .getStatus()) {
                // 保存失败
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
                // 保存成功
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.SUCCESS.getValue(), null, customizeFormDataEnroll.getLeadId());
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                // 建立线索与报名者关联
                this.bindBrowserUserAndCrmId(customizeFormDataUserEntity.getFingerPrint(), customizeFormDataEntity.getEa(), customizeFormDataEnroll.getLeadId(), customizeFormDataEnroll.getPhone());
                objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                    ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                if (isActivityObject) {
                    objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                }
                // 修改crmUtm字段
                Integer finalSpreadFsUid = spreadFsUid;
                ThreadPoolUtils.execute(
                    () -> utmDataManger.syncUtmFormDataToROI(customizeFormDataEntity.getEa(), finalSpreadFsUid, customizeFormDataEnroll.getLeadId(), customizeFormDataUserEntity.getSubmitContent()), ThreadPoolTypeEnums.HEAVY_BUSINESS);
                return true;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus()) {
                return true;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.LINKED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
                //关联成功
                log.info("saveEnrollDataToCrm linked success  customizeFormDataEntity:{} customizeFormDataEnroll:{} ", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.LINKED.getValue(), null, customizeFormDataEnroll.getLeadId());
                String objId = customizeFormDataEnroll.getLeadId();
                // 重复的数据绑定到营销用户上
                int type = bindBrowserUserRepeatDataToUserMarketing(customizeFormDataUserEntity, customizeFormDataEntity.getEa(), objId, customizeFormDataEnroll.getPhone());
                objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        type, objId, null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                if (isActivityObject) {
                    objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                            type, objId, null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                }
//                ThreadPoolUtils.execute(() -> associateMarketingUserId(customizeFormDataEntity, customizeFormDataUserEntity), ThreadPoolTypeEnums.HEAVY_BUSINESS);
                return true;
            } else {
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            }
        } else if (customizeFormDataUserEntity.getSourceType().equals(CustomizeFormDataUserSourceTypeEnum.IMPORT_DATA.getType())) {
            CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
            customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.UN_SAVE.getStatus());
            customizeFormDataEnroll.setIpAddr(customizeFormDataUserEntity.getIpAddr());
            customizeFormDataEnroll.setUserAgent(customizeFormDataUserEntity.getUserAgent());
            customizeFormDataEnroll.setIsDuplicateData(false);
            // 获取有权限创建、更新线索的推广人
            Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
            spreadFsUid = getAccessibleSpreadFsUid(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataUserEntity.getMarketingEventId(), customizeFormDataUserEntity.getChannelValue());
            this.saveCrmLead(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataEnroll, customizeFormDataEntity,  false, null, customizeFormDataUserEntity);
            if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() && customizeFormDataEnroll.getIsDuplicateData()) {
                // 重复数据
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm duplicate data  customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() || customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.UN_SAVE
                .getStatus()) {
                // 保存失败
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
                // 保存成功
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.SUCCESS.getValue(), null, customizeFormDataEnroll.getLeadId());
                objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                    ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                if (isActivityObject) {
                    objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                }
                return true;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus()) {
                return true;
            } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.LINKED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
                //关联成功
                log.info("saveEnrollDataToCrm linked success  customizeFormDataEntity:{} customizeFormDataEnroll:{} ", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.LINKED.getValue(), null, customizeFormDataEnroll.getLeadId());
                objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                        ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                if (isActivityObject) {
                    objectTagManager.batchAddTagsToUserMarketings(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getSubmitContent().getPhone(),
                            ChannelEnum.CRM_LEAD.getType(), customizeFormDataEnroll.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, customizeFormDataUserEntity.getMarketingEventId(), triggerAction);
                }
                ThreadPoolUtils.execute(() -> associateMarketingUserId(customizeFormDataEntity, customizeFormDataUserEntity), ThreadPoolTypeEnums.HEAVY_BUSINESS);
                return true;
            } else {
                log.warn("CustomizeFormDataManager.saveEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
                customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
                return false;
            }
        }
        return false;
    }

    private Integer getAccessibleSpreadFsUid(String ea, Integer spreadFsUid, String marketingEventId, String channelValue) {
        if (spreadFsUid == null) {
            return clueDefaultSettingService.getClueCreator(marketingEventId, ea, clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(channelValue));
        }
        if (QywxUserConstants.isMemberVirtualUserId(spreadFsUid) ||QywxUserConstants.isPartnerVirtualUserId(spreadFsUid)) {
            return SuperUserConstants.USER_ID;
        }
        boolean addLeadsObjectAuth = checkAddLeadsObjectAuth(ea, spreadFsUid);
        if (!addLeadsObjectAuth) {
            return clueDefaultSettingService.getClueCreator(marketingEventId, ea, clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(channelValue));
        }
        return spreadFsUid;
    }

    public boolean saveObjectToCrm(CustomizeFormDataEntity customizeFormDataEntity, CustomizeFormDataUserEntity customizeFormDataUserEntity,boolean updateData) {
        String ea = customizeFormDataEntity.getEa();
        String objectId = customizeFormDataUserEntity.getObjectId();
        Integer objectType = customizeFormDataUserEntity.getObjectType();
        CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
        String crmApiName = customizeFormDataEntity.getCrmApiName();
        String marketingEventId = customizeFormDataUserEntity.getMarketingEventId();
        Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
        String extraDataId = customizeFormDataUserEntity.getExtraDataId();
        if (updateData && StringUtils.isEmpty(extraDataId)) {
            return false;
        }
        Integer fsUserId = null;
        if (spreadFsUid != null) {
            fsUserId = spreadFsUid;
        }
        // 线索创建人设为市场活动负责人
        if (fsUserId == null && StringUtils.isNotEmpty(marketingEventId)) {
            ObjectData objectMarketingData = crmV2Manager.getDetail(ea,-10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            log.info("saveObjectToCrm objectData:{}", GsonUtil.getGson().toJson(objectMarketingData));
            if (objectMarketingData != null && objectMarketingData.getOwner() != null) {
                fsUserId = objectMarketingData.getOwner();
            }
        }
        if (fsUserId == null) {
            fsUserId = -10000;
        }
        FieldMappings fieldMappings = customizeFormDataEntity.getCrmFormFieldMapV2();
        if (StringUtils.isBlank(ea)) {
            customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
            customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.EA_NOT_NULL));
            customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(customizeFormDataUserEntity.getId(),customizeFormDataUserEntity.getSaveCrmStatus(),customizeFormDataUserEntity.getSaveCrmErrorMessage());
            return false;
        }
        try {
            if (customizeFormDataEntity.getCrmFormFieldMapV2() == null || customizeFormDataEntity.getCrmFormFieldMapV2().size() == 0) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.DATA_SETTING_NOT_NULL));
                customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(customizeFormDataUserEntity.getId(),customizeFormDataUserEntity.getSaveCrmStatus(),customizeFormDataUserEntity.getSaveCrmErrorMessage());
                return false;
            }
            Set<String> notNullCrmLeadFieldNames = crmV2Manager.getObjectFieldNameListByApiName(ea, crmApiName);
            boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmLeadFieldNames);
            if (!verifyResult) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(I18nUtil.getStaticByKey(CrmStatusMessageConstant.VERIFY_CRM_FIELDS_NOT_NULL));
                customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(customizeFormDataUserEntity.getId(),customizeFormDataUserEntity.getSaveCrmStatus(),customizeFormDataUserEntity.getSaveCrmErrorMessage());
                return false;
            }
            Map<String, Object> fieldDataMap = crmV2MappingManager.createCustomizeFormDataToObjFieldDataMap(customizeFormDataEntity, customizeFormDataUserEntity.getSubmitContent(), crmApiName);
            List<String> userIds = Collections.singletonList(fsUserId.toString());
            fieldDataMap.put(ObjectDescribeContants.DESCRIBE_API_NAME,crmApiName);
            fieldDataMap.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID,crmApiName);
            fieldDataMap.put(ObjectDescribeContants.OWNER,userIds);
            int ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(HeaderObj.newInstance(ei, -10000), crmApiName);
            if (describeResult.isSuccess() && describeResult.getData() != null && describeResult.getData().getDescribe() != null) {
                fieldDataMap.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID,describeResult.getData().getDescribe().getId());
            }
            //处理开启多组织,线索归属组织字段存入
            fieldDataMap = clueManagementManager.addCustomizeFormOrganizationData(fieldDataMap, ea,marketingEventId,customizeFormDataEntity);
            // 表单统一设置
            fieldDataMap = clueManagementManager.addCustomizeFormCommonData(fieldDataMap, ea, objectId, objectType, customizeFormDataEnroll);
            CreateObjResult createObjResult = null;
            if (updateData) {
                createObjResult = crmV2Manager.updateCrmObj(ea, fsUserId,extraDataId, fieldDataMap,crmApiName);
            } else {
                createObjResult = crmV2Manager.createCrmObj(ea, fsUserId, fieldDataMap,crmApiName);
            }
            if (StringUtils.isNotBlank(createObjResult.getId())) {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(createObjResult.getMessage());
                customizeFormDataUserEntity.setExtraDataId(createObjResult.getId());
                customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrm(customizeFormDataUserEntity.getId(),customizeFormDataUserEntity.getSaveCrmStatus(),customizeFormDataUserEntity.getSaveCrmErrorMessage(),customizeFormDataUserEntity.getExtraDataId());
            } else {
                customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                customizeFormDataUserEntity.setSaveCrmErrorMessage(createObjResult.getMessage());
                customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(customizeFormDataUserEntity.getId(),customizeFormDataUserEntity.getSaveCrmStatus(),customizeFormDataUserEntity.getSaveCrmErrorMessage());
            }
            return true;
        } catch (Exception e) {
            customizeFormDataUserEntity.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
            customizeFormDataUserEntity.setSaveCrmErrorMessage(this.conversionCrmErrorMessage(e.getMessage()));
            customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(customizeFormDataUserEntity.getId(),customizeFormDataUserEntity.getSaveCrmStatus(),customizeFormDataUserEntity.getSaveCrmErrorMessage());
            log.warn("saveObjectToCrm customizeFormDataUserEntity:{} error:{}", customizeFormDataUserEntity, e);
        }
        return false;
    }

    // 返回的是绑定的什么类型的对象
    private int bindBrowserUserRepeatDataToUserMarketing(CustomizeFormDataUserEntity customizeFormDataUserEntity, String ea, String objId, String phone) {
        CustomizeFormDataUserEntity latestCustomizeFormDataUser = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserEntity.getId());
        CustomizeFormBindOtherCrmObject bindOtherCrmObject = latestCustomizeFormDataUser.getOtherCrmObjectBind();
        String fingerPrint = customizeFormDataUserEntity.getFingerPrint();
        // 注意： 这里不一定是线索ID,有重复的时候这里可能是客户、联系人ID
        int type = ChannelEnum.CRM_LEAD.getType();
        if (bindOtherCrmObject != null) {
            String apiName = bindOtherCrmObject.getApiName();
            if (CrmObjectApiNameEnum.CUSTOMER.getName().equals(apiName)) {
                type = ChannelEnum.CRM_ACCOUNT.getType();
                userMarketingAccountRelationManager.bindBrowserUserAndCustomer(ea, fingerPrint, objId, phone, "customizeFormDataEnroll");
            } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(apiName)) {
                type = ChannelEnum.CRM_CONTACT.getType();
                userMarketingAccountRelationManager.bindBrowserUserAndContact(ea, fingerPrint, objId, phone, "customizeFormDataEnroll");
            } else {
                this.bindBrowserUserAndCrmId(fingerPrint, ea, objId, phone);
            }
        } else {
            this.bindBrowserUserAndCrmId(fingerPrint, ea, objId, phone);
        }
        return type;
    }

    // 返回的是绑定的什么类型的对象
    private int bindWxUserRepeatDataToUserMarketing(CustomizeFormDataEntity customizeFormDataEntity, CustomizeFormDataUserEntity customizeFormDataUserEntity, CustomizeFormDataEnroll customizeFormDataEnroll) {
        CustomizeFormDataUserEntity latestCustomizeFormDataUser = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserEntity.getId());
        CustomizeFormBindOtherCrmObject bindOtherCrmObject = latestCustomizeFormDataUser.getOtherCrmObjectBind();
        String ea = customizeFormDataEntity.getEa();
        int type = ChannelEnum.CRM_LEAD.getType();
        String wxAppId = customizeFormDataUserEntity.getWxAppId();
        String openId = customizeFormDataUserEntity.getOpenId();
        String objId = customizeFormDataEnroll.getLeadId();
        String phone = customizeFormDataEnroll.getPhone();
        if (bindOtherCrmObject != null) {
            String apiName = bindOtherCrmObject.getApiName();
            if (CrmObjectApiNameEnum.CUSTOMER.getName().equals(apiName)) {
                type = ChannelEnum.CRM_ACCOUNT.getType();
                userMarketingAccountRelationManager.bindWxUserAndCustomer(ea, wxAppId, openId, objId, phone, "customizeFormDataEnroll");
            } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(apiName)) {
                type = ChannelEnum.CRM_CONTACT.getType();
                userMarketingAccountRelationManager.bindWxUserAndContact(ea, wxAppId, openId, objId, phone, "customizeFormDataEnroll");
            } else {
                userMarketingAccountRelationManager.bindWxUserAndLead(ea, wxAppId, openId, objId, phone, "customizeFormDataEnroll");
            }
        } else {
            userMarketingAccountRelationManager.bindWxUserAndLead(ea, wxAppId, openId, objId, phone, "customizeFormDataEnroll");
        }
        return type;
    }

    public void associateMarketingUserId(CustomizeFormDataEntity customizeFormDataEntity, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(customizeFormDataEntity.getEa()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(customizeFormDataUserEntity.getFingerPrint()));
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(customizeFormDataEntity.getEa());
        associationArg.setPhone(customizeFormDataUserEntity.getSubmitContent().getPhone());
        if (StringUtils.isNotEmpty(customizeFormDataUserEntity.getFingerPrint())) {
            associationArg.setAssociationId(customizeFormDataUserEntity.getFingerPrint());
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
            associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        } else if (StringUtils.isNotEmpty(customizeFormDataUserEntity.getWxAppId()) && StringUtils.isNotEmpty(customizeFormDataUserEntity.getOpenId())) {
            associationArg.setWxAppId(customizeFormDataUserEntity.getWxAppId());
            associationArg.setAssociationId(customizeFormDataUserEntity.getOpenId());
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        } else if (StringUtils.isNotEmpty(customizeFormDataUserEntity.getUid())) {
            associationArg.setAssociationId(customizeFormDataUserEntity.getUid());
            associationArg.setType(ChannelEnum.MINIAPP.getType());
            associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        }
        associationArg.setTriggerAction("customizeFormDataEnroll");

        try {
            userMarketingAccountAssociationManager.associate(associationArg);
        } catch (Exception e) {
            log.warn("CustomizeFormDataManager associateMarketingUserId error arg:{}", JSON.toJSONString(associationArg), e);
        }
    }

    /**
     * 保存线索失败给负责人发送通知
     *
     * @param ea
     * @param customizeFormDataUserEntity
     */
    public void sendNotice(String ea, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        this.sendSaveClueFailMessage(ea, customizeFormDataUserEntity.getFormId(), customizeFormDataUserEntity.getMarketingEventId(), false);
    }

    public void sendSaveClueFailMessage(String ea, String formId, String marketingEventId, Boolean isJob) {
        // 查询线索保存失败通知配置
        SaveClueFailNoticeConfigEntity saveClueFailNoticeConfig = saveClueFailNoticeConfigDAO.querySaveClueFailNoticeConfigByEaAndType(ea,
                StringUtils.isEmpty(marketingEventId) ? LeadsSourceTypeEnum.WEBSITE.getType() : LeadsSourceTypeEnum.EVENT.getType());
        if (saveClueFailNoticeConfig == null) {
            log.info("saveClueFailNoticeConfig args:{}", ea);
            return;
        }
        if(LeadsSendTypeEnum.DELAY.getType().equals(saveClueFailNoticeConfig.getSendType()) && !isJob){
            return;
        }
        Integer clueType = saveClueFailNoticeConfig.getClueType();
        Integer receiverType = saveClueFailNoticeConfig.getReceiverType();
        // 获取消息接收人
        List<Integer> toUserIds = new ArrayList<>();
        String sendScope = saveClueFailNoticeConfig.getSendScope();
        // 获取消息标题
        String title = Objects.equals(saveClueFailNoticeConfig.getClueType(), LeadsSourceTypeEnum.EVENT.getType()) ? "待处理活动报名数据提醒" : "待处理官网表单留咨数据提醒";
        // 获取消息描述
        // 查询存入失败的线索数
        List<Integer> saveCrmStatus = new ArrayList<>();
        saveCrmStatus.add(SaveCrmStatusEnum.ERROR.getValue());
        saveCrmStatus.add(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue());
        int countSaveCrmFailLeads = StringUtils.isNotEmpty(marketingEventId)
                ? customizeFormDataUserDAO.getSaveCrmFailLeadsCountByMarketingEventId(marketingEventId,saveCrmStatus)
                : customizeFormDataUserDAO.getSaveCrmFailLeadsCountByFormId(formId, saveCrmStatus);
        if (countSaveCrmFailLeads==0) {
            return;
        }
        String name = null;
        // 查询活动/表单,并给活动名、接收人设值
        if (StringUtils.isNotEmpty(marketingEventId) && Objects.equals(clueType, LeadsSourceTypeEnum.EVENT.getType())) {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            log.info("sendSaveClueFailMessage queryMarketingEventOwner objectData:{}", GsonUtil.getGson().toJson(objectData));
            if (objectData != null) {
                name = objectData.getName();
                if (Objects.equals(SaveLeadsFailNoticeRecevierEnum.APPOINT.getValue(), receiverType)) {
                    toUserIds.addAll(Arrays.stream(sendScope.split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                } else if (objectData.getOwner() != null) {
                    toUserIds.add(objectData.getOwner());
                }
            }
        } else {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
            if (customizeFormDataEntity == null || customizeFormDataEntity.getFormHeadSetting() == null) {
                log.warn("customizeFormDataEntity is not exist, args:{}", formId);
                return;
            }
            name = customizeFormDataEntity.getFormHeadSetting().getName();
            if (Objects.equals(SaveLeadsFailNoticeRecevierEnum.APPOINT.getValue(), receiverType)) {
                if (StringUtils.isNotBlank(sendScope)) {
                    toUserIds.addAll(Arrays.stream(sendScope.split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                }
            }
        }
        if (toUserIds.isEmpty()) {
            return;
        }
        Map<String, String> desMap = new HashMap<>();
        desMap.put("internationalTitleKey", Objects.equals(saveClueFailNoticeConfig.getClueType(), LeadsSourceTypeEnum.EVENT.getType()) ?
                "qx.ot.mark.todo_data_notice1" : "qx.ot.mark.todo_data_notice2");
        desMap.put("nameValue", name);//活动名称
        desMap.put("numValue", String.valueOf(countSaveCrmFailLeads));
        // 发送
        fsMessageManager.sendSaveLeadsFailFxMessage(toUserIds, ea, title, gson.toJson(desMap));
    }

    /**
     * 更新销售线索数据
     */
    public boolean updateEnrollDataToCrm(CustomizeFormDataEntity customizeFormDataEntity, CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
            log.warn("CustomizeFormDataManager.updateEnrollDataToCrm leadId is null");
            return false;
        }
        boolean addLeadsObjectAuth = false;
        Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
        if (spreadFsUid != null && !QywxUserConstants.isMemberVirtualUserId(spreadFsUid)  && !QywxUserConstants.isPartnerVirtualUserId(spreadFsUid)) {
            addLeadsObjectAuth = this.checkAddLeadsObjectAuth(customizeFormDataEntity.getEa(), spreadFsUid);
        }
        if (!addLeadsObjectAuth) {
            spreadFsUid = -10000;
        }
        CustomizeFormDataEnroll customizeFormDataEnroll = customizeFormDataUserEntity.getSubmitContent();
        customizeFormDataEnroll.setLeadSaveStatus(LeadSaveStatusEnum.SAVED_SUCCESS.getStatus());
        customizeFormDataEnroll.setIsDuplicateData(false);
        this.saveCrmLead(customizeFormDataEntity.getEa(), spreadFsUid, customizeFormDataEnroll, customizeFormDataEntity, true, customizeFormDataUserEntity.getLeadId(), customizeFormDataUserEntity);
        LandingObjCustomizeUserRelation landingObjCustomizeUserRelation = landingObjCustomizeUserRelationManager.getByEnrollId(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getId());
        if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() && customizeFormDataEnroll.getIsDuplicateData()) {
            // 重复数据
            log.warn("CustomizeFormDataManager.updateEnrollDataToCrm duplicate data  customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
            // 保存线索失败给负责人发送通知
            sendNotice(customizeFormDataEntity.getEa(), customizeFormDataUserEntity);
            return false;
        } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() || customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.UN_SAVE
            .getStatus()) {
            // 保存失败
            log.warn("CustomizeFormDataManager.updateEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
            // 保存线索失败给负责人发送通知
            sendNotice(customizeFormDataEntity.getEa(), customizeFormDataUserEntity);
            return false;
        } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
            // 保存成功
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.SUCCESS.getValue(), null, customizeFormDataEnroll.getLeadId());
            updateLandingCustomizeUserRelation(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getLeadId(), landingObjCustomizeUserRelation);
            return true;
        } else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_SUCCESS.getStatus()) {
            updateLandingCustomizeUserRelation(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getLeadId(), landingObjCustomizeUserRelation);
            return true;
        }  else if (customizeFormDataEnroll.getLeadSaveStatus() == LeadSaveStatusEnum.LINKED_SUCCESS.getStatus() && StringUtils.isNotBlank(customizeFormDataEnroll.getLeadId())) {
            //关联成功
            log.info("saveEnrollDataToCrm linked success  customizeFormDataEntity:{} customizeFormDataEnroll:{} ", customizeFormDataEntity, customizeFormDataEnroll);
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.LINKED.getValue(), null, customizeFormDataEnroll.getLeadId());
            updateLandingCustomizeUserRelation(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getLeadId(), landingObjCustomizeUserRelation);
            return true;
        } else {
            log.warn("CustomizeFormDataManager.updateEnrollDataToCrm save crm lead error customizeFormDataEntity:{} customizeFormDataEnroll:{}", customizeFormDataEntity, customizeFormDataEnroll);
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(customizeFormDataUserEntity.getId(), SaveCrmStatusEnum.ERROR.getValue(), customizeFormDataEnroll.getLeadMessage(), null);
            // 保存线索失败给负责人发送通知
            sendNotice(customizeFormDataEntity.getEa(), customizeFormDataUserEntity);
            return false;
        }
    }

    /**
     * 校验用户是否报名（不特殊处理会议）
     */
    public Result<CustomizeFormDataUserEntity> checkUserIsEnrolledWithNoHandleActivity(String marketingEventId, CustomizeFormDataEntity customizeFormDataEntity, String objectId, Integer objectType, String wxAppId,
        String openId, String fingerPrint, String ea, Integer fsUserId, String phone) {
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = null;
        //物料类型为活动或活动邀请函
        if (objectType == ObjectTypeEnum.ACTIVITY.getType() || objectType == ObjectTypeEnum.ACTIVITY_INVITATION.getType()) {
            //得到会议绑定的所有物料id，包含会议本身
            List<String> linkList = activityManager.getActivityLinkObject(objectId, objectType, Lists.newArrayList(ObjectTypeEnum.ACTIVITY_INVITATION));
            //根据用户的身份信息和物料id得到用户提交的表单信息
            if (StringUtils.isNotEmpty(phone)) {
                customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByObjectIds(marketingEventId, customizeFormDataEntity.getId(), linkList, phone);
            } else {
                if (StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(wxAppId)) {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndObjectIds(marketingEventId, openId, wxAppId, customizeFormDataEntity.getId(), linkList, null);
                } else if (StringUtils.isNotBlank(fingerPrint)) {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndObjectIds(marketingEventId, fingerPrint, customizeFormDataEntity.getId(), linkList, null);
                } else if (StringUtils.isNotBlank(ea) && fsUserId != null) {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByFsUserInfoAndObjectIds(marketingEventId, ea, fsUserId, customizeFormDataEntity.getId(), linkList, null);
                }
            }
        } else {
            //objectId就是物料id，物料就是会议本身
            if (StringUtils.isNotEmpty(phone)) {
                customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUsersByObjectIds(marketingEventId, customizeFormDataEntity.getId(), null, phone);
            } else {
                if (StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(wxAppId)) {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByOpenId(marketingEventId, openId, wxAppId, customizeFormDataEntity.getId(), objectId, objectType);
                } else if (StringUtils.isNotBlank(fingerPrint)) {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO
                            .queryCustomizeFormDataUsersByFingerPrintAndObjectIds(marketingEventId, fingerPrint, customizeFormDataEntity.getId(), Lists.newArrayList(objectId), null);
                } else if (StringUtils.isNotBlank(ea) && fsUserId != null) {
                    customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByEaAndFsUserId(marketingEventId, ea, fsUserId, customizeFormDataEntity.getId(), objectId, objectType);
                }
            }
        }
        return CollectionUtils.isNotEmpty(customizeFormDataUserEntityList) ? new Result<>(SHErrorCode.USERS_HAVE_REGISTERED, customizeFormDataUserEntityList.get(0))
            : new Result<>(SHErrorCode.SUCCESS);
    }

    public String checkWxAppUserIsEnrolledWithNoHandleActivity(String marketingEventId, CustomizeFormDataEntity customizeFormDataEntity, String objectId, Integer objectType, String uid,String phone) {
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = null;
        List<String> linkList = null;
        if (objectType == com.facishare.mankeep.common.enums.ObjectTypeEnum.ACTIVITY.getType() || objectType == com.facishare.mankeep.common.enums.ObjectTypeEnum.ACTIVITY_INVITATION.getType()) {
            linkList = activityManager.getActivityLinkObject(objectId, objectType, Lists.newArrayList(ObjectTypeEnum.ACTIVITY_INVITATION));
        }
        if (StringUtils.isNotEmpty(phone)) {
            customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByPhoneAndMarketingEventId(marketingEventId, phone, customizeFormDataEntity.getId(), linkList);
        } else {
            String userMarketingId = customizeFormDataUserDAO.getMarketingUserAccountByEaAndUid(customizeFormDataEntity.getEa(), uid);
            List<String> crmLeadIds = customizeFormDataUserDAO.listByUserMarketingIds(customizeFormDataEntity.getEa(), Lists.newArrayList(userMarketingId));
            if (!crmLeadIds.isEmpty()) {
                //根据用户的身份信息和物料id得到用户提交的表单信息
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.queryCustomizeFormDataUsersByCrmLeadIds(crmLeadIds, marketingEventId, customizeFormDataEntity.getId(), linkList);
                if (customizeFormDataUserEntities != null && !customizeFormDataUserEntities.isEmpty()) {
                    customizeFormDataUserEntityList = customizeFormDataUserEntities;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
            return customizeFormDataUserEntityList.get(0).getId();
        } else {
            return null;
        }
    }


    public List<String> saveEnrollDataToCrmByActivityEnroll(List<ActivityEnrollDataEntity> enrollDataEntityList, boolean needSetEnrollStatus, Integer reviewStatus, String reviewFailedMsg) {
        if (CollectionUtils.isEmpty(enrollDataEntityList)) {
            return Lists.newArrayList();
        }

        ActivityEntity activityEntity = conferenceDAO.getConferenceById(enrollDataEntityList.get(0).getActivityId());
        List<String> activityEnrollId = enrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(activityEnrollId)) {
            return Lists.newArrayList();
        }

        List<String> campaignIds = campaignMergeDataManager.activityEnrollIdToCampaignId(activityEnrollId);
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(campaignIds);
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
            List<String> formIds = customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getFormId).distinct().collect(Collectors.toList());
            List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
            Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = customizeFormDataEntityList.stream()
                .collect(Collectors.toMap(CustomizeFormDataEntity::getId, data -> data, (v1, v2) -> v1));
            for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
                boolean saveCrmRet = saveEnrollDataToCrm(customizeFormDataEntityMap.get(customizeFormDataUserEntity.getFormId()), customizeFormDataUserEntity);
                if (!saveCrmRet) {
                    log.info("save to crm lead failed customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                }
                String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(customizeFormDataUserEntity.getId(), false);
                customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), campaignId, activityEntity.getEa(), null);
                List<String> activityEnrollDataId = campaignMergeDataManager.campaignIdToActivityEnrollId(Lists.newArrayList(campaignId));
                if (CollectionUtils.isNotEmpty(activityEnrollDataId) && needSetEnrollStatus) {
                    activityEnrollId.addAll(activityEnrollDataId);
                    campaignMergeDataManager.updateConferenceReviewStatus(activityEnrollDataId, reviewStatus, reviewFailedMsg, true);
                }
            }
        }

        // 若为会议还需生成参会码
        for (String campaignId : campaignIds) {
            customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), campaignId, activityEntity.getEa(), null);
        }

        return activityEnrollId;
    }

    public boolean saveConferenceParticipantsToCrm(String ea, List<String> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return true;
        }

        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(campaignIds);
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
            return true;
        }

        // 过滤出存入失败的
        List<CustomizeFormDataUserEntity> needHandleData = Lists.newArrayList();

        List<String> leadIdList = customizeFormDataUserEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getLeadId())).map(CustomizeFormDataUserEntity::getLeadId).collect(Collectors.toList());
        Map<String, ObjectData> leadIdToDataMap = new HashMap<>();
        List<ObjectData> leadDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CRM_LEAD.getName(), null, leadIdList);
        if (CollectionUtils.isNotEmpty(leadDataList)) {
            leadDataList.forEach(e -> leadIdToDataMap.put(e.getId(), e));
        }

        List<String> campaignIdList = customizeFormDataUserEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getCampaignId())).map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toList());
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIdList);
        Map<String, CampaignMergeDataEntity> campaignIdToEntityMap = new HashMap<>();
        campaignMergeDataEntityList.forEach(e -> campaignIdToEntityMap.put(e.getId(), e));

        List<String> updateIdList = Lists.newArrayList();
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            if (customizeFormDataUserEntity.getSaveCrmStatus() == null){
                needHandleData.add(customizeFormDataUserEntity);
                continue;
            }
            if (customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())
                    || customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.ERROR.getValue())){
                needHandleData.add(customizeFormDataUserEntity);
                continue;
            }
            if (customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.LINKED.getValue()) && !StringUtils.isBlank(customizeFormDataUserEntity.getCampaignId())){
                //查询活动成员是否绑定成功
                CampaignMergeDataEntity campaignMergeDataEntity = campaignIdToEntityMap.get(customizeFormDataUserEntity.getCampaignId());
                if (campaignMergeDataEntity != null && StringUtils.isEmpty(campaignMergeDataEntity.getBindCrmObjectId())){
                    needHandleData.add(customizeFormDataUserEntity);
                    continue;
                }
            }
            if (customizeFormDataUserEntity.getSaveCrmStatus() != null && !customizeFormDataUserEntity.getSaveCrmStatus()
                    .equals(SaveCrmStatusEnum.SUCCESS.getValue()) && !customizeFormDataUserEntity.getSaveCrmStatus()
                    .equals(SaveCrmStatusEnum.LINKED.getValue())){
                needHandleData.add(customizeFormDataUserEntity);
                continue;
            }

            // 若保存成功需校验关联数据是否作废或未关联活动成员
            if (customizeFormDataUserEntity.getSaveCrmStatus() != null && customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.SUCCESS.getValue())) {
                if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
                    continue;
                }
                // 查询关联数据是否仍存在
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> leadResult = null;
                try {
                    ObjectData leadData = leadIdToDataMap.get(customizeFormDataUserEntity.getLeadId());
                    if (leadData == null) {
                        updateIdList.add(customizeFormDataUserEntity.getId());
                        needHandleData.add(customizeFormDataUserEntity);
                    } else if (StringUtils.isNotBlank(customizeFormDataUserEntity.getCampaignId())){
                        // 若数据存在查看是否保存活动成员且关联
                        CampaignMergeDataEntity campaignMergeDataEntity = campaignIdToEntityMap.get(customizeFormDataUserEntity.getCampaignId());
                        if (campaignMergeDataEntity != null && StringUtils.isBlank(campaignMergeDataEntity.getBindCrmObjectId())
                                && StringUtils.isBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
                            needHandleData.add(customizeFormDataUserEntity);
                        }
                    }
                } catch (Exception e) {
                    log.warn("CustomizeFormDataManager.saveConferenceParticipantsToCrm objectData error e:{}", e);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateIdList)) {
            customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessageWithIdList(updateIdList, SaveCrmStatusEnum.ERROR.getValue(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_3172), null);
        }
        if (CollectionUtils.isEmpty(needHandleData)) {
            //如果没有需要重新存入的,则进行更新操作
            List<String> formIds = customizeFormDataUserEntityList.stream().filter(data -> (StringUtils.isNotBlank(data.getLeadId()))).map(CustomizeFormDataUserEntity::getFormId).distinct().collect(Collectors.toList());
            List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
            Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = new HashMap<>();
            for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormDataEntityList) {
                customizeFormDataEntityMap.putIfAbsent(customizeFormDataEntity.getId(), customizeFormDataEntity);
            }
            for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
                if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
                    continue;
                }
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(customizeFormDataUserEntity.getFormId());
                boolean updateRet = updateEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
            }
            return true;
        }
        List<String> formIds = needHandleData.stream().map(CustomizeFormDataUserEntity::getFormId).distinct().collect(Collectors.toList());
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            log.info("saveConferenceParticipantsToCrm failed customizeFormDataEntityList null");
            return false;
        }
        // 筛选出没有做映射的数据
        List<String> noMappingFormIds = customizeFormDataEntityList.stream().filter(data -> (data.getCrmFormFieldMapV2() == null || CollectionUtils.isEmpty(data.getCrmFormFieldMapV2()))).map(
            CustomizeFormDataEntity::getId).collect(
            Collectors.toList());
        Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = new HashMap<>();
        for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormDataEntityList) {
            customizeFormDataEntityMap.putIfAbsent(customizeFormDataEntity.getId(), customizeFormDataEntity);
        }
        List<String> enrollIdList = needHandleData.stream().map(CustomizeFormDataUserEntity::getId).collect(Collectors.toList());
        // 根据报名查询落地页相关信息
        List<LandingObjCustomizeUserRelation> landingObjCustomizeUserRelationList = landingObjCustomizeUserRelationManager. getByEnrollIdList(ea, enrollIdList);
        Map<String, LandingObjCustomizeUserRelation> enrollIdToLandingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(landingObjCustomizeUserRelationList)) {
            landingObjCustomizeUserRelationList.forEach(e -> enrollIdToLandingMap.put(e.getEnrollId(), e));
        }
        //后续要优化成批量
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : needHandleData) {
            if (CollectionUtils.isNotEmpty(noMappingFormIds) && noMappingFormIds.contains(customizeFormDataUserEntity.getFormId())) {
                continue;
            }
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(customizeFormDataUserEntity.getFormId());
            if (customizeFormDataUserEntity.getSaveCrmStatus() != SaveCrmStatusEnum.LINKED.getValue()) {
                //非已关联，需要重新存入
                boolean saveRet = saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                if (saveRet){
                    String leadId = customizeFormDataUserEntity.getSubmitContent() == null ? null : customizeFormDataUserEntity.getSubmitContent().getLeadId();
                    //上报神策埋点
                    marketingStatLogPersistorManger.sendLeadData(ea, leadId, customizeFormDataUserEntity.getMarketingEventId(), MarketingStatLogPersistorManger.CHANNEL_H5);
                    LandingObjCustomizeUserRelation landingObjCustomizeUserRelation = enrollIdToLandingMap.get(customizeFormDataUserEntity.getId());
                    updateLandingCustomizeUserRelation(ea, leadId, landingObjCustomizeUserRelation);
                }
            }
            String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(customizeFormDataUserEntity.getId(), false);
            // 若为会议还需生成参会码
            customizeTicketManager.createConferenceCustomizeTicket(customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(), campaignId, customizeFormDataEntityList.get(0).getEa(), customizeFormDataUserEntity.getMarketingEventId());
            try {
                // 生成会员数据
                CustomizeFormDataUserEntity saveData = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserEntity.getId());
                if (StringUtils.isNotBlank(saveData.getLeadId()) && customizeFormDataEntity.getFormMoreSetting().isSyncToMember()) {
                    String marketingPromotionSourceId = null;
                    if (customizeFormDataUserEntity.getSubmitContent() != null) {
                        marketingPromotionSourceId = customizeFormDataUserEntity.getSubmitContent().getMarketingPromotionSourceId();
                    }
                    memberManager.saveLeadToMember(customizeFormDataEntity.getEa(), saveData.getLeadId(), marketingPromotionSourceId);
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataManager.saveConferenceParticipantsToCrm saveLeadToMember error e:", e);
            }
        }

        return true;
    }

    private void updateLandingCustomizeUserRelation(String ea, String leadId, LandingObjCustomizeUserRelation landingObjCustomizeUserRelation) {
        if (StringUtils.isNotBlank(leadId) && landingObjCustomizeUserRelation != null) {
            String userMarketingId = null;
            UserMarketingCrmLeadAccountRelationEntity userMarketingCrmLeadAccountRelation = userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadId(ea, leadId);
            if (userMarketingCrmLeadAccountRelation != null) {
                userMarketingId = userMarketingCrmLeadAccountRelation.getUserMarketingId();
            }
            log.info("更新落地页映射关系,ea:[{}], 更新前:[{}],更新后的leadId:[{}],userMarketingId:[{}]", ea, userMarketingCrmLeadAccountRelation, leadId, userMarketingId);
            landingObjCustomizeUserRelationManager.updateLeadIdAndUserMarketingId(ea, landingObjCustomizeUserRelation.getId(), leadId, userMarketingId);
            Map<String, Object> objectData = new HashMap<>();
            String landingId = landingObjCustomizeUserRelation.getLandingObjId();
            objectData.put("landing_page_id", landingId);
            CreateLeadResult createLeadResult = crmV2Manager.updateLead(leadId, ea, -10000, objectData);
            log.info("销售线索关联落地页, ea:[{}], leadId:[{}], landingId:[{}], result:[{}]", ea, leadId, landingId, createLeadResult);
        }
    }

    public void saveFromDataByEnrollids(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.querySaveFailedCrmEntityByIds(ids);
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
            //没有保存失败的表单, 直接进行表单更新
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.queryCustomizeFormDataUserEntityByIds(ids);
            if (CollectionUtils.isEmpty(customizeFormDataUserEntities)) {
                return;
            }
            List<String> formIds = customizeFormDataUserEntities.stream().filter(data -> (StringUtils.isNotBlank(data.getLeadId()))).map(CustomizeFormDataUserEntity::getFormId).distinct().collect(Collectors.toList());
            List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
            Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = new HashMap<>();
            for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormDataEntityList) {
                customizeFormDataEntityMap.putIfAbsent(customizeFormDataEntity.getId(), customizeFormDataEntity);
            }
            for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntities) {
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(customizeFormDataUserEntity.getFormId());
                //单独处理存入其他任意对象
                if (Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(), SaveCrmObjectTypeEnum.OBJ.getType())) {
                    CustomizeFormDataUserEntity saveCustomizeFormDataUserEntity = customizeFormDataUserEntity;
                    ThreadPoolUtils.executeWithTraceContext(()->{
                        this.saveObjectToCrm(customizeFormDataEntity, saveCustomizeFormDataUserEntity,true);
                    },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                } else {
                    if (StringUtils.isBlank(customizeFormDataUserEntity.getLeadId())) {
                        continue;
                    }
                    updateEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                }
            }
            return;
        }
        List<String> formIds = customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getFormId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formIds)) {
            return;
        }
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formIds);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            return;
        }
        // 筛选出没有做映射的数据
        List<String> noMappingFormIds = customizeFormDataEntityList.stream().filter(data -> (data.getCrmFormFieldMapV2() == null || CollectionUtils.isEmpty(data.getCrmFormFieldMapV2()))).map(
            CustomizeFormDataEntity::getId).collect(
            Collectors.toList());
        Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = new HashMap<>();
        for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormDataEntityList) {
            customizeFormDataEntityMap.putIfAbsent(customizeFormDataEntity.getId(), customizeFormDataEntity);
        }
        // 校验是否有任务在执行
        if (executeTaskDetailManager.hasSameTaskExecute(customizeFormDataEntityList.get(0).getEa(), ExecuteTaskDetailTypeEnum.SAVE_FORM_DATA_USER_TO_CRM, ids)) {
            return;
        }
        List<String> enrollIdList = customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getId).collect(Collectors.toList());
        // 根据报名查询落地页相关信息
        List<LandingObjCustomizeUserRelation> landingObjCustomizeUserRelationList =
                landingObjCustomizeUserRelationManager.getByEnrollIdList(customizeFormDataEntityList.get(0).getEa(), enrollIdList);
        Map<String, LandingObjCustomizeUserRelation> enrollIdToLandingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(landingObjCustomizeUserRelationList)) {
            landingObjCustomizeUserRelationList.forEach(e -> enrollIdToLandingMap.put(e.getEnrollId(), e));
        }
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            if (CollectionUtils.isNotEmpty(noMappingFormIds) && noMappingFormIds.contains(customizeFormDataUserEntity.getFormId())) {
                continue;
            }
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(customizeFormDataUserEntity.getFormId());
            if (customizeFormDataEntity == null) {
                continue;
            }
            if (executeTaskDetailManager
                .checkTaskAndAddIfNotExist(customizeFormDataEntity.getEa(), ExecuteTaskDetailTypeEnum.SAVE_FORM_DATA_USER_TO_CRM, customizeFormDataUserEntity.getId())) {
                continue;
            }
            //单独处理存入其他任意对象
            if (Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(), SaveCrmObjectTypeEnum.OBJ.getType())) {
                CustomizeFormDataUserEntity saveCustomizeFormDataUserEntity = customizeFormDataUserEntity;
                ThreadPoolUtils.executeWithTraceContext(()->{
                    this.saveObjectToCrm(customizeFormDataEntity, saveCustomizeFormDataUserEntity,false);
                },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                //释放掉任务表中的数据
                executeTaskDetailManager.taskComplete(customizeFormDataEntity.getEa(), ExecuteTaskDetailTypeEnum.SAVE_FORM_DATA_USER_TO_CRM, customizeFormDataUserEntity.getId());
                continue;
            }
            try {
                boolean saveResult = saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                if (saveResult) {
                    String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(customizeFormDataUserEntity.getId(), false);
                    // 若为会议还需生成参会码
                    customizeTicketManager.createConferenceCustomizeTicket(customizeFormDataUserEntity.getObjectType(), customizeFormDataUserEntity.getObjectId(), campaignId,
                        customizeFormDataEntityList.get(0).getEa(), customizeFormDataUserEntity.getMarketingEventId());
                    String leadId = customizeFormDataUserEntity.getSubmitContent() == null ? null : customizeFormDataUserEntity.getSubmitContent().getLeadId();
                    LandingObjCustomizeUserRelation landingObjCustomizeUserRelation = enrollIdToLandingMap.get(customizeFormDataUserEntity.getId());
                    String ea = customizeFormDataEntity.getEa();
                    updateLandingCustomizeUserRelation(ea, leadId, landingObjCustomizeUserRelation);
                }
                //重新存入线索，观看直播打标签
                CustomizeFormDataUserEntity saveData = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserEntity.getId());
                if (StringUtils.isNotBlank(saveData.getLeadId()) && StringUtils.isNotEmpty(saveData.getMarketingEventId())){
                    MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(customizeFormDataEntity.getEa()), saveData.getMarketingEventId());
                    List<String> marketingUserIds = Lists.newArrayList();
                    if (marketingLiveEntity != null && StringUtils.isNotEmpty(marketingLiveEntity.getTags())){
                        AssociateCrmLeadModel.AssociateCrmLeadArg associateCrmLeadArg = new AssociateCrmLeadModel.AssociateCrmLeadArg();
                        associateCrmLeadArg.setEa(customizeFormDataEntity.getEa());
                        associateCrmLeadArg.setCrmLeadId(saveData.getLeadId());
                        associateCrmLeadArg.setPhone(saveData.getSubmitContent().getPhone());
                        com.facishare.marketing.common.result.Result<AssociateCrmLeadModel.AssociateCrmLeadResult> associateCrmLeadResultResult = crmLeadMarketingAccountAssociationService.associateCrmLead(associateCrmLeadArg);
                        if (associateCrmLeadResultResult.isSuccess() && associateCrmLeadResultResult.getData() != null){
                            marketingUserIds.add(associateCrmLeadResultResult.getData().getUserMarketingId());
                        }
                        if (CollectionUtils.isNotEmpty(marketingUserIds)) {
                            TagNameList tagNames = GsonUtil.getGson().fromJson(marketingLiveEntity.getTags(), new TypeToken<TagNameList>(){}.getType());
                            if (tagNames != null) {
                                userMarketingAccountService.batchAddTagsToUserMarketings(eieaConverter.enterpriseIdToAccount(marketingLiveEntity.getCorpId()),
                                        null, null, marketingUserIds, tagNames);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataManager.saveFromDataByEnrollids error e:{}", e);
            } finally {
                executeTaskDetailManager.taskComplete(customizeFormDataEntity.getEa(), ExecuteTaskDetailTypeEnum.SAVE_FORM_DATA_USER_TO_CRM, customizeFormDataUserEntity.getId());
            }
            try {
                // 生成会员数据
                CustomizeFormDataUserEntity saveData = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserEntity.getId());
                if (StringUtils.isNotBlank(saveData.getLeadId()) && customizeFormDataEntity.getFormMoreSetting().isSyncToMember()) {
                    String marketingPromotionSourceId = null;
                    if (customizeFormDataUserEntity.getSubmitContent() != null) {
                        marketingPromotionSourceId = customizeFormDataUserEntity.getSubmitContent().getMarketingPromotionSourceId();
                    }
                    memberManager.saveLeadToMember(customizeFormDataEntity.getEa(), saveData.getLeadId(), marketingPromotionSourceId);
                }
            } catch (Exception e) {
                log.warn("CustomizeFormDataManager.saveFromDataByEnrollids saveLeadToMember error e:{}", e);
            }
        }
    }

    public Map<String, String> getFormEnrollDataBindObject(CustomizeFormDataUserEntity customizeFormDataUserEntity) {
        Map<String, String> bindObject = Maps.newHashMap();
        if (customizeFormDataUserEntity == null) {
            return bindObject;
        }
        if (customizeFormDataUserEntity.getSaveCrmStatus() == null || customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.ERROR.getValue()) || customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())) {
            return bindObject;
        }
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getLeadId())) {
            bindObject.put(CrmObjectApiNameEnum.CRM_LEAD.getName(), customizeFormDataUserEntity.getLeadId());
            return bindObject;
        }
        if (customizeFormDataUserEntity.getOtherCrmObjectBind() != null && StringUtils.isNotBlank(customizeFormDataUserEntity.getOtherCrmObjectBind().getApiName()) && StringUtils
            .isNotBlank(customizeFormDataUserEntity.getOtherCrmObjectBind().getObjectId())) {
            bindObject.put(customizeFormDataUserEntity.getOtherCrmObjectBind().getApiName(), customizeFormDataUserEntity.getOtherCrmObjectBind().getObjectId());
        }
        return bindObject;
    }

    public Result<CustomizeFormDataDetailResult> getCustomizeFormDataById(GetCustomizeFormDataByIdArg getCustomizeFormDataByIdArg) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(getCustomizeFormDataByIdArg.getId());
        if (customizeFormDataEntity == null) {
            log.warn("CustomizeFormDataServiceImpl.getCustomizeFormDataById customizeFormDataEntity is null getCustomizeFormDataByIdArg:{}", getCustomizeFormDataByIdArg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        Result result = checkCustomizeFormDataStatus(customizeFormDataEntity, getCustomizeFormDataByIdArg.getId(), getCustomizeFormDataByIdArg.isNeedDisableData());
        if (!result.isSuccess()) {
            return result;
        }
        CustomizeFormDataDetailResult customizeFormDataDetailResult = BeanUtil.copy(customizeFormDataEntity, CustomizeFormDataDetailResult.class);
        customizeFormDataDetailResult.setCrmFormFieldMap(customizeFormDataEntity.getCrmFormFieldMapV2());
        customizeFormDataDetailResult = setCustomizeFormDataFileInfo(customizeFormDataDetailResult);
        customizeFormDataDetailResult = setCustomizeFormDataHeadPhoto(customizeFormDataDetailResult);
        customizeFormDataDetailResult.setCreateTimeStamp(customizeFormDataDetailResult.getCreateTime().getTime());
        customizeFormDataDetailResult.setUpdateTimeStamp(customizeFormDataDetailResult.getUpdateTime().getTime());
        customizeFormDataDetailResult.setCampaignMemberMap(customizeFormDataEntity.getCampaignMemberMap());
        //处理新加的是否识别会员字段
        customizeFormDataDetailResult.getFormMoreSetting().setMemberCheckType(customizeFormDataEntity.getFormMoreSetting().getMemberCheckType());
        ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(customizeFormDataEntity.getEa(), customizeFormDataEntity.getId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        if (objectTagEntity != null) {
            customizeFormDataDetailResult.setTagNameList(objectTagEntity.getTagNameList());
        }
        if(customizeFormDataDetailResult != null && StringUtils.isNotBlank(customizeFormDataDetailResult.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_MINIAPP_COVER.getType(), customizeFormDataDetailResult.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                customizeFormDataDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_H5_COVER.getType(), customizeFormDataDetailResult.getId());
            if (coverCutH5PhotoEntity != null) {
                customizeFormDataDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.CUSTOMIZE_FORM_SHARE_ORDINARY_COVER.getType(), customizeFormDataDetailResult.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                customizeFormDataDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                customizeFormDataDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, customizeFormDataDetailResult);
    }


    public Map<String, String> conversionEnrollDataPic(String ea, Collection<CustomizeFormDataUserEntity> customizeFormDataUserEntityList) {
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList) || StringUtils.isBlank(ea)) {
            return Maps.newHashMap();
        }
        // 取出全部的npath
        List<String> allPath = Lists.newArrayList();
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            if (customizeFormDataUserEntity.getSubmitContent() == null || MapUtils.isEmpty(customizeFormDataUserEntity.getSubmitContent().getPicMap())) {
                continue;
            }
            Map<String, List<PicContainer>> picMap = customizeFormDataUserEntity.getSubmitContent().getPicMap();
            for (Map.Entry<String, List<PicContainer>> entry : picMap.entrySet()) {
                allPath.addAll(entry.getValue().stream().map(data -> (data.getPath() + "." + data.getExt())).collect(Collectors.toList()));
            }
        }
        Map<String, String> pathUrlMap = fileV2Manager.batchGetUrlByPath(allPath, ea, false);
        if (MapUtils.isEmpty(pathUrlMap)) {
            return Maps.newHashMap();
        }
        allPath.clear();
        return pathUrlMap;
    }

    public Map<String, String> conversionEnrollDataFileAttachment(String ea, Collection<CustomizeFormDataUserEntity> customizeFormDataUserEntityList){
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList) || StringUtils.isBlank(ea)) {
            return Maps.newHashMap();
        }
        // 取出全部的npath
        List<String> allPath = Lists.newArrayList();
        Map<String, String> filePathNameMap = new HashMap<>();
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            if (customizeFormDataUserEntity.getSubmitContent() == null || MapUtils.isEmpty(customizeFormDataUserEntity.getSubmitContent().getFileAttachmentMap())) {
                continue;
            }
            Map<String, List<FileAttachmentContainer>> fileAttachmentMap = customizeFormDataUserEntity.getSubmitContent().getFileAttachmentMap();
            for (Map.Entry<String, List<FileAttachmentContainer>> entry : fileAttachmentMap.entrySet()) {
                allPath.addAll(entry.getValue().stream().map(FileAttachmentContainer::getPath).collect(Collectors.toList()));
                for (FileAttachmentContainer fileAttachmentContainer : entry.getValue()) {
                    filePathNameMap.put(fileAttachmentContainer.getPath(), fileAttachmentContainer.getFilename());
                }
            }
        }
        Map<String, String> fileDownloadUrls = fileV2Manager.batchGetDownloadFileUrl(ea, 1000, allPath, filePathNameMap);
        allPath.clear();
        return fileDownloadUrls;
    }


    public Object formatEnrollDataIncludeSpecialField(FieldInfo fieldInfo, CustomizeFormDataUserEntity customizeFormDataEnroll, Map<String, String> urlMap, boolean returnPicList) {
        Object result = fieldInfo.formatObjectData(customizeFormDataEnroll.getSubmitContent());
        if (fieldInfo.getType().equals(FieldInfo.Type.IMAGE.getValue())) {
            if (result != null && MapUtils.isNotEmpty(urlMap)) {
                List<String> pathList = (List<String>) result;
                List<String> urlList = Lists.newArrayList();
                for (String path : pathList) {
                    String url = urlMap.get(path);
                    if (StringUtils.isNotBlank(url)) {
                        urlList.add(url);
                    }
                }
                if (CollectionUtils.isNotEmpty(urlList) && urlList.size() == 1 && !returnPicList) {
                    return urlList.get(0);
                }
                return urlList;
            } else {
                return null;
            }
        } else {
            return result;
        }
    }

    public Object formatEnrollDataPicObject(Object object, Map<String, String> picMap, boolean returnPicList) {
        if (object == null || MapUtils.isEmpty(picMap)) {
            return null;
        }
        List<String> picPathList = (List<String>) object;
        List<String> urlList = Lists.newArrayList();
        for (String path : picPathList) {
            if (StringUtils.isNotBlank(picMap.get(path))) {
                urlList.add(picMap.get(path));
            }
        }
        if (CollectionUtils.isNotEmpty(urlList) && urlList.size() == 1 && !returnPicList) {
            return urlList.get(0);
        }
        return urlList;
    }

    /**
     * 表单复制
     */
    public String copy(String ea, String oldFormId, int newCreator) {
        String newFormId = UUIDUtil.getUUID();
        CustomizeFormDataEntity form = customizeFormDataDAO.getCustomizeFormDataById(oldFormId);
        form.setId(newFormId);
        form.setCreateBy(newCreator);
        form.setUpdateBy(newCreator);
        // 复制表单的时候需要将表单模板的标签也复制
        ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(ea, oldFormId, ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        if (objectTagEntity != null) {
            objectTagManager.addOrUpdateObjectTag(ea, newCreator, newFormId, ObjectTypeEnum.CUSTOMIZE_FORM.getType(), objectTagEntity.getTagNameList());
        }
        return customizeFormDataDAOManager.insertCustomizeFormData(form) == 1 ? newFormId : newFormId;
    }

    public void setCustomizeFormToMarketingEvent(String ea) {
        log.info("+++++++++++++++++  开始刷库setCustomizeFormToMarketingEvent ea:{} +++++++++++++++++", ea);
        if (StringUtils.isBlank(ea)) {
            return;
        }
        if (ea.equals("setAll")) {
            ea = null;
        }
        List<ActivityEntity> activityEntityList = conferenceDAO.getAllDataByEa(ea);
        if(CollectionUtils.isEmpty(activityEntityList)) {
            return;
        }
        Integer allDataNum = activityEntityList.size();
        Integer successNum = 0;
        Integer errorNum = 0;
        Integer nowNum = 0;
        log.info("+++++++++++++++++ 共：{}", allDataNum);
        for (ActivityEntity activityEntity : activityEntityList) {
            try {
                log.info("+++++++++++++++++ setCustomizeFormToMarketingEvent 当前:{}/{}", nowNum + 1, allDataNum);
                boolean enterpriseStop = campaignMergeDataResetManager.enterpriseStop(activityEntity.getEa());
                if(enterpriseStop) {
                    successNum = successNum + 1;
                    nowNum = nowNum + 1;
                    continue;
                }
                CustomizeFormDataEntity customizeFormDataEntity = getBindFormDataByObject(activityEntity.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
                if (customizeFormDataEntity == null) {
                    successNum = successNum + 1;
                    nowNum = nowNum + 1;
                    continue;
                }
                if (StringUtils.isBlank(activityEntity.getMarketingEventId())) {
                    successNum = successNum + 1;
                    nowNum = nowNum + 1;
                    continue;
                }
                ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(activityEntity.getEa());
                entity.setObjectId(customizeFormDataEntity.getId());
                entity.setObjectType(ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                entity.setMarketingEventId(activityEntity.getMarketingEventId());
                contentMarketingEventMaterialRelationDAO.save(entity);
                successNum = successNum + 1;
                nowNum = nowNum + 1;
            } catch (Exception e) {
                log.warn("error ===== CustomizeFormDataManager.setCustomizeFormToMarketingEvent error e:{}", e);
                errorNum = errorNum + 1;
                nowNum = nowNum + 1;
            }
        }
        log.info("----------------- 共:{}", allDataNum);
        log.info("----------------- 成功：{}", successNum);
        log.info("----------------- 失败：{}", errorNum);
        log.info("+++++++++++++++++  完成刷库setCustomizeFormToMarketingEvent ea:{} +++++++++++++++++", ea);
    }

    public Result checkEnrollField(CustomizeFormDataEntity customizeFormDataEntity, CustomizeFormDataEnroll submitContent, Integer objectType, String objectId, String stepFormComponentId) {
        if (StringUtils.isEmpty(needCheckEnrollFieldEa)
                || !Arrays.asList(needCheckEnrollFieldEa.split(",")).contains(customizeFormDataEntity.getEa())) {
            return Result.newSuccess();
        }
        // 根据表单字段设置进行校验
        boolean isStepForm = false;
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == objectType || ObjectTypeEnum.OFFICIAL_WEBSITE.getType() == objectType) {
            HexagonPageEntity hexagonPage = ObjectTypeEnum.HEXAGON_PAGE.getType() == objectType ? hexagonPageDAO.getById(objectId) : hexagonPageDAO.getByFormId(customizeFormDataEntity.getId());
            String content = hexagonPage.getContent();
            JSONObject contentObj = JSON.parseObject(content);
            if (contentObj != null) {
                JSONArray pageComponents = contentObj.getJSONArray("components");
                if (CollectionUtils.isNotEmpty(pageComponents)) {
                    JSONObject stepForm = null;
                    for (int i = 0; i < pageComponents.size(); i++) {
                        JSONObject component = pageComponents.getJSONObject(i);
                        if (component != null && "step-form".equals(component.getString("typeValue"))) {
                            stepForm = component;
                            break;//分步表单组件一个页面只会有一个
                        }
                    }
                    JSONObject stepFormComponent = null;
                    if (stepForm != null) {
                        isStepForm = true;
                        /*
                        JSONArray stepFormComponents = stepForm.getJSONArray("components");
                        if (CollectionUtils.isNotEmpty(stepFormComponents)) {
                            if (StringUtils.isBlank(stepFormComponentId)) {
                                return Result.newError(SHErrorCode.PARAMS_ERROR);
                            }
                            for (int i = 0; i < stepFormComponents.size(); i++) {
                                JSONObject component = stepFormComponents.getJSONObject(i);
                                if (component != null && StringUtils.isNotBlank(component.getString("id")) && stepFormComponentId.equals(component.getString("id"))) {
                                    stepFormComponent = component;
                                    break;
                                }
                            }
                        }
                        */
                    }
                    /*
                    JSONObject phoneFieldInfo = null;
                    if (stepFormComponent != null) {
                        JSONArray fieldInfos = stepFormComponent.getJSONArray("components");
                        for (int i = 0; i < fieldInfos.size(); i++) {
                            JSONObject component = fieldInfos.getJSONObject(i);
                            if (component != null && "phone".equals(component.getString("fieldName"))) {
                                phoneFieldInfo = component;
                                break;
                            }
                        }
                    } else if (stepForm != null) {
                        log.warn("stepFormComponentId is error {}", stepFormComponentId);
                        return Result.newError(SHErrorCode.PARAMS_ERROR);
                    }
                    if (phoneFieldInfo != null) {
                        if (BooleanUtils.isTrue(phoneFieldInfo.getBoolean("verify"))) {
                            if (BooleanUtils.isTrue(phoneFieldInfo.getBoolean("required"))
                                    && (StringUtils.isBlank(submitContent.getPhone()) || StringUtils.isBlank(submitContent.getPhoneVerifyCode()))) {
                                return Result.newError(SHErrorCode.NOT_ENTER_PHONE_VERIFY_CODE);
                            } else if (BooleanUtils.isNotTrue(phoneFieldInfo.getBoolean("required"))
                                    && StringUtils.isNotBlank(submitContent.getPhone()) && StringUtils.isBlank(submitContent.getPhoneVerifyCode())) {
                                return Result.newError(SHErrorCode.NOT_ENTER_PHONE_VERIFY_CODE);
                            }
                        }
                    }
                     */
                }
            }
        }
        if (!isStepForm) {
            Optional<FieldInfo> phoneOptional = customizeFormDataEntity.getFormBodySetting().stream().filter(data -> data.getApiName().equals("phone")).findAny();
            if (phoneOptional.isPresent()) {
                FieldInfo phoneFieldInfo = phoneOptional.get();
                if (BooleanUtils.isTrue(phoneFieldInfo.getIsVerify())) {
                    if (BooleanUtils.isTrue(phoneFieldInfo.getIsRequired())
                            && (StringUtils.isBlank(submitContent.getPhone()) || StringUtils.isBlank(submitContent.getPhoneVerifyCode()))) {
                        return Result.newError(SHErrorCode.NOT_ENTER_PHONE_VERIFY_CODE);
                    } else if (BooleanUtils.isNotTrue(phoneFieldInfo.getIsRequired())
                            && StringUtils.isNotBlank(submitContent.getPhone()) && StringUtils.isBlank(submitContent.getPhoneVerifyCode())) {
                        return Result.newError(SHErrorCode.NOT_ENTER_PHONE_VERIFY_CODE);
                    }
                }
            }
        }
        return Result.newSuccess();
    }

    public void updateFormDataLeadCreator(String ea) {
        //查询企业下所有表单
        List<CustomizeFormDataEntity> formDataEntityList = customizeFormDataDAO.list(ea);
        if (CollectionUtils.isEmpty(formDataEntityList)) {
            return ;
        }
        List<String> formIds = formDataEntityList.stream().map(CustomizeFormDataEntity::getId).collect(Collectors.toList());
        //根据表单查询到所有提交表单信息
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.getUpdateLeadCreatorFormData(formIds);
        customizeFormDataUserEntities = customizeFormDataUserEntities.stream().filter(distinctByKey(CustomizeFormDataUserEntity::getLeadId)).collect(Collectors.toList());
        Integer allDataNum = customizeFormDataUserEntities.size();
        Integer successNum = 0;
        Integer errorNum = 0;
        Integer nowNum = 0;
        log.info("共执行：{}", allDataNum);
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntities) {
            try {
                log.info("执行 updateFormDataLeadCreator 当前:{}/{}", nowNum + 1, allDataNum);
                //查询线索详情
                ObjectData leadObject = crmV2Manager.getObjectData(ea,-10000,CrmObjectApiNameEnum.CRM_LEAD.getName(),customizeFormDataUserEntity.getLeadId());
                if (leadObject == null) {
                    log.warn("查询 lead detail error leadId:{}",customizeFormDataUserEntity.getLeadId());
                    errorNum = errorNum + 1;
                    nowNum = nowNum + 1;
                    continue;
                }
                //如果线索创建不为系统,则不处理
                Integer createBy = leadObject.getCreateBy();
                if (createBy != -10000){
                    successNum = successNum + 1;
                    nowNum = nowNum + 1;
                    continue;
                }
                //获取推广人
                boolean addLeadsObjectAuth = false;
                Integer spreadFsUid = customizeFormDataUserEntity.getSpreadFsUid();
                if (spreadFsUid != null) {
                    addLeadsObjectAuth = this.checkAddLeadsObjectAuth(ea, spreadFsUid);
                    if (!addLeadsObjectAuth) {
                        spreadFsUid = -10000;
                    }
                } else {
                    //如果没有推广人, 则取市场活动负责人
                    if (StringUtils.isBlank(customizeFormDataUserEntity.getMarketingEventId())) {
                        //兼容官网微页面没有市场活动id
                        spreadFsUid = -10000;
                    } else {
                        ObjectData objectData = crmV2Manager.getDetail(ea,-10000,CrmObjectApiNameEnum.MARKETING_EVENT.getName(),customizeFormDataUserEntity.getMarketingEventId());
                        if (objectData == null) {
                            spreadFsUid = -10000;
                        } else {
                            spreadFsUid = objectData.getOwner() == null ? -10000 : objectData.getOwner();
                        }
                    }
                }
                if (spreadFsUid == -10000) {
                    successNum = successNum + 1;
                    nowNum = nowNum + 1;
                    continue;
                }
                com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> actionEditResultResult = crmV2Manager.updateLeadObj(ea,customizeFormDataUserEntity.getLeadId(), spreadFsUid, null);
                if (actionEditResultResult.isSuccess()) {
                    successNum = successNum + 1;
                    nowNum = nowNum + 1;
                } else {
                    log.warn("edit crm lead error msg:{}",actionEditResultResult.getMessage());
                    errorNum = errorNum + 1;
                    nowNum = nowNum + 1;
                }
            } catch (Exception e) {
                log.warn("error ===== CustomizeFormDataManager.updateFormDataLeadCreator error e:{}", e);
                errorNum = errorNum + 1;
                nowNum = nowNum + 1;
            }
        }
        log.info("共执行:{}", allDataNum);
        log.info("执行成功：{}", successNum);
        log.info("执行失败：{}", errorNum);
        log.info("执行 updateFormDataLeadCreator ea:{} ", ea);
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 表单删除报名数据
     * @param enrollUserId
     * @param ea
     * @param fsUserId
     * @return
     */
    @Transactional
    public Result<Void> deleteEnrollData(String enrollUserId, String ea, Integer fsUserId){
        if (StringUtils.isBlank(enrollUserId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(enrollUserId);
        if (customizeFormDataUserEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        //检查有无产生活动成员
        String campaignId = customizeFormDataUserEntity.getCampaignId();
        Integer saveCrmStatus = customizeFormDataUserEntity.getSaveCrmStatus();
        String leadId = customizeFormDataUserEntity.getLeadId();
        String extraDataId = customizeFormDataUserEntity.getExtraDataId();//其他任意对象id
        String formId = customizeFormDataUserEntity.getFormId();
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
        if (StringUtils.isBlank(campaignId) || campaignMergeDataDAO.getCampaignMergeDataById(campaignId) == null) {
            // 没有活动成员，则没有挂市场活动，直接通过分享微页面表单收集的数据
            // 判断存入状态，若为已关联，则无需处理；若为已存入，则需要作废掉对象数据
            if (Objects.equals(saveCrmStatus, SaveCrmStatusEnum.SUCCESS.getValue())) {
                // 存入成功,存在两种情况：1存入线索 2存入其他对象
                if (StringUtils.isNotBlank(leadId)) {
                    crmV2Manager.bulkInvalidWithResult(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CRM_LEAD.getName(), Lists.newArrayList(leadId));
                } else {
                    if (StringUtils.isNotBlank(extraDataId)) {
                        crmV2Manager.bulkInvalidWithResult(ea, SuperUserConstants.USER_ID, customizeFormDataEntity.getCrmApiName(), Lists.newArrayList(extraDataId));
                    }
                }
            }
        } else {
            // 有活动成员
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
            if (campaignMergeDataEntity == null) {
                log.info("活动成员id有误，campaignId：{}", campaignId);
                return Result.newSuccess();
            }
            // 检查该活动成员有无关联其他提交记录，当表单重复提交时会出现一个活动成员关联多条提交记录的情况
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(campaignId);
            if (CollectionUtils.isNotEmpty(customizeFormDataUserEntities)) {
                List<String> collect = customizeFormDataUserEntities.stream().map(CustomizeFormDataUserEntity::getId).collect(Collectors.toList());
                boolean hasOtherElements = collect.stream().anyMatch(e -> !e.equals(enrollUserId));
                if (hasOtherElements) {
                    // 说明该活动成员还有其他提交记录关联，此时不应作废对象数据
                } else {
                    Integer bindCrmObjectType = campaignMergeDataEntity.getBindCrmObjectType();
                    String bindCrmObjectId = campaignMergeDataEntity.getBindCrmObjectId();
                    if (Objects.equals(saveCrmStatus, SaveCrmStatusEnum.SUCCESS.getValue())) {
                        // 存入成功，作废掉对象数据
                        if (Objects.nonNull(bindCrmObjectType) && StringUtils.isNotBlank(bindCrmObjectId)) {
                            crmV2Manager.bulkInvalidWithResult(ea, SuperUserConstants.USER_ID, CampaignMergeDataObjectTypeEnum.getApiNameByType(bindCrmObjectType), Lists.newArrayList(bindCrmObjectId));
                        }
                    }
                    // 删除本地表活动成员
                    campaignMergeDataDAO.deleteCampaignMergeData(campaignId);
                    // 作废活动成员对象数据
                    String campaignMembersObjId = campaignMergeDataEntity.getCampaignMembersObjId();
                    if (StringUtils.isNotBlank(campaignMembersObjId)) {
                        crmV2Manager.bulkInvalidWithResult(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), Lists.newArrayList(campaignMembersObjId));
                    }
                }
            }
        }
        // 删除表单提交记录
        customizeFormDataUserDAO.deleteById(enrollUserId);
        return Result.newSuccess();
    }
}