package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.dto.QYWXContactFollowUpDTO;
import com.facishare.marketing.provider.entity.QywxContactFollowUpEntity;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2020/03/03
 **/
public interface QYWXContactFollowUpDAO {

    @Insert("INSERT INTO qywx_contact_follow_up(id, ea, follower_id, target_id, follow_type, content, action_type, status, create_time, update_time) "
        + " VALUES (#{obj.id}, #{obj.ea}, #{obj.followerId}, #{obj.targetId}, #{obj.followType}, #{obj.content, typeHandler=ContactFollowUpTypeHandler}, #{obj.actionType}, #{obj.status},now(),now());")
    int insertQywxContactFollowUp(@Param("obj") QywxContactFollowUpEntity qywxContactFollowUpEntity);

    @Select("SELECT * FROM qywx_contact_follow_up WHERE follower_id = #{followerId} AND target_id = #{targetId} AND follow_type = #{followType} AND action_type = #{actionType}")
    List<QywxContactFollowUpEntity> getByQywxContactFollowUp(@Param("followerId") String followerId, @Param("targetId") String targetId, @Param("followType") Integer followType, @Param("actionType") Integer actionType);

    @Select("SELECT follower_id,target_id,MAX(cast(content ->> 'nextFollowUpTime' as bigint)) AS num FROM qywx_contact_follow_up WHERE follower_id = #{followerId} GROUP BY follower_id,target_id")
    List<QYWXContactFollowUpDTO> queryFollowerContactInfo(@Param("followerId") String followerId, @Param("followType") Integer followType, @Param("actionType") Integer actionType);

}
