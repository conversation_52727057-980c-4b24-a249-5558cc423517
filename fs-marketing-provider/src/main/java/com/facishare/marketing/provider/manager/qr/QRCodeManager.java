package com.facishare.marketing.provider.manager.qr;

import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.qrCode.CreateQRCodeArg;
import com.facishare.mankeep.api.outService.service.OutQRCodeService;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.result.qr.QueryQRCodeResult;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.qr.QRCodeTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.PhotoAssociationLinkIds;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.qr.OutQrCodeSettingDAO;
import com.facishare.marketing.provider.dao.qr.QRCodeDAO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.EnterpriseInfoEntity;
import com.facishare.marketing.provider.entity.PhotoAssociationEntity;
import com.facishare.marketing.provider.entity.qr.OutQrCodeSettingEntity;
import com.facishare.marketing.provider.entity.qr.QRCodeEntity;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.innerResult.SaveQrCodeAndPhotoDataResult;
import com.facishare.marketing.provider.innerResult.qywx.OutLinkQRCodeResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsParamManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.reflect.TypeToken;
import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.Serializable;
import java.util.*;

@Service
@Slf4j
public class QRCodeManager {

    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private QRCodeDAO qrCodeDAO;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private PhotoAssociationDAO photoAssociationDAO;

    @Autowired
    private EnterprseInfoDao enterprseInfoDao;

    @Autowired
    private SmsParamManager smsParamManager;

    @Autowired
    private OutQRCodeService outQRCodeService;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private OutQrCodeSettingDAO outQrCodeSettingDAO;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private FileManager fileManager;
    @Autowired
    private EnterpriseSelfDomainDAO enterpriseSelfDomainDAO;
    @Autowired
    private HttpManager httpManager;

    @ReloadableProperty("host")
    private String host;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EnterpriseDefaultManager enterpriseDefaultManager;
    @Value("${default.enterprise.icon.path}")
    private String defaultEnterpriseIconPath;
    @Value("${enterprise.environment}")
    private String enterpriseEnvironment;

    private static final int BLACK = 0xFF000000;
    private static final int WHITE = 0xFFFFFFFF;

    @Data
    public static class CreateQRCodeResult implements Serializable {
        // 二维码ID
        private Integer qrCodeId;
        // 二维码图片Apath
        private String qrCodeApath;
        // 二维码图片URL
        private String qrCodeUrl;
        // 二维码扫出来的URL
        private String qrUrl;
        // 二维码返回的参会列表（针对非跳转中间页的场景，如：优化后的签到二维码)
        private Map<String, Object> urlParamMap;

        private Integer errorCode;
        private String errorMessage;

        public boolean isSuccess() {
            if (errorCode == 0) {
                return true;
            }

            return false;
        }
    }


    //创建外部二维码
    private CreateQRCodeResult createOutLinkQRCode(CreateQRCodeInnerData data) {
        Map<String,String> paramsMap = GsonUtil.getGson().fromJson(data.getValue(), new TypeToken<Map<String, String>>() {}.getType());
        OutQrCodeSettingEntity qrCodeSettingEntity = outQrCodeSettingDAO.getByEa(data.getEa());
        if (qrCodeSettingEntity == null || StringUtils.isBlank(qrCodeSettingEntity.getDomainNameUrl())) {
            log.warn("QRCodeManager.createQRCode qrCodeSettingEntity is null,ea ={}, value={} qrCodeSettingEntity:{}", data.getEa(), data.getValue(), qrCodeSettingEntity);
            return null;
        }
        CreateQRCodeResult createQRCodeResult = new CreateQRCodeResult();
        if (!paramsMap.containsKey("outerQrcodeSpreadFsUid")) {
            log.warn("create ouQrCode paramsMap key outerQrcodeSpreadFsUid is null");
            createQRCodeResult.setErrorCode(SHErrorCode.OUT_QR_CODE_PARAM_NOT_FOUND.getErrorCode());
            createQRCodeResult.setErrorMessage(SHErrorCode.OUT_QR_CODE_PARAM_NOT_FOUND.getErrorMessage());
            return createQRCodeResult;
        }
        if (StringUtils.isBlank(paramsMap.get("outerQrcodeSpreadFsUid"))) {
            log.warn("create ouQrCode outerQrcodeSpreadFsUid is null");
            createQRCodeResult.setErrorCode(SHErrorCode.OUT_QR_CODE_PARAM_NOT_FOUND.getErrorCode());
            createQRCodeResult.setErrorMessage(SHErrorCode.OUT_QR_CODE_PARAM_NOT_FOUND.getErrorMessage());
            return createQRCodeResult;
        }
        //处理移动端传入fsUserId问题
        Integer fsUserId = null;
        if (paramsMap.containsKey("spreadFsUid") && paramsMap.get("spreadFsUid") != null) {
            fsUserId = Integer.valueOf(paramsMap.get("spreadFsUid"));
        }
        if (fsUserId == null) {
            fsUserId = Integer.valueOf(paramsMap.get("outerQrcodeSpreadFsUid"));
        }
        log.info("out qrcode fsUserId:{}",fsUserId);
        String uid = fsBindManager.queryUidByLimitOne(data.getEa(),fsUserId);
        if (StringUtils.isBlank(uid)) {
            log.warn("create ouQrCode uid is null");
            createQRCodeResult.setErrorCode(SHErrorCode.FSBIND_USER_NOFOUND.getErrorCode());
            createQRCodeResult.setErrorMessage(SHErrorCode.FSBIND_USER_NOFOUND.getErrorMessage());
            return createQRCodeResult;
        }
        CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
        if (cardEntity == null) {
            log.warn("create ouQrCode cardEntity is null");
            createQRCodeResult.setErrorCode(SHErrorCode.OUT_QR_CODE_CARD_NOT_FOUND.getErrorCode());
            createQRCodeResult.setErrorMessage(SHErrorCode.OUT_QR_CODE_CARD_NOT_FOUND.getErrorMessage());
            return createQRCodeResult;
        }
        String phone = cardEntity.getPhone();
        if (StringUtils.isBlank(phone)) {
            log.warn("create ouQrCode phone is null");
            createQRCodeResult.setErrorCode(SHErrorCode.OUT_QR_CODE_CREATE_FAILED.getErrorCode());
            createQRCodeResult.setErrorMessage(SHErrorCode.OUT_QR_CODE_CREATE_FAILED.getErrorMessage());
            return createQRCodeResult;
        }
        paramsMap.put("mobile",phone);
        String url = qrCodeSettingEntity.getDomainNameUrl();
        url = HttpUtil.getAppendUrl(url,paramsMap);
        OutLinkQRCodeResult qrCodeResult = httpManager.executeGetHttp(url, new TypeToken<OutLinkQRCodeResult>() {});
        if (qrCodeResult == null || !qrCodeResult.isSuccess() || qrCodeResult.getRetVal() == null) {
            log.warn("QRCodeManager.createQRCode OutLinkQRCodeResult is error,value={}",data.getValue());
            createQRCodeResult.setErrorCode(SHErrorCode.SYSTEM_ERROR.getErrorCode());
            createQRCodeResult.setErrorMessage(SHErrorCode.SYSTEM_ERROR.getErrorMessage());
            if (qrCodeResult != null) {
                createQRCodeResult.setErrorMessage(qrCodeResult.getMsg());
            }
            return createQRCodeResult;
        }
        QRCodeEntity qrCodeEntity = new QRCodeEntity();
        qrCodeEntity.setType(data.getType());
        qrCodeEntity.setValue(data.getValue());
        qrCodeEntity.setEa(data.getEa());
        int addQRCodeResult = qrCodeDAO.insert(qrCodeEntity);
        if (addQRCodeResult != 1) {
            log.error("insert failed, qrCodeEntity={}", qrCodeEntity);
            return null;
        }
        String path = qrCodeResult.getRetVal().getImageUrl();
        String qrCodeUrl = "";
        byte[] picBytes = fileV2Manager.downloadFileByUrl(path,data.getEa());
        if (picBytes != null) {
            //上传文件服务器
            String ext = "png";
            if (path.contains("wx_fmt=gif")) {
                ext = "gif";
            } else if (path.contains("wx_fmt=jpeg")) {
                ext = "jpeg";
            } else if (path.contains("jpg")) {
                ext = "jpg";
            }
            String fsPath = fileV2Manager.uploadToApath(picBytes,ext,data.getEa());
            if (StringUtils.isBlank(fsPath)) {
                log.warn("create ouQrCode fsPath isBlank");
                createQRCodeResult.setErrorCode(SHErrorCode.OUT_QR_CODE_URL_NOT_PARSE.getErrorCode());
                createQRCodeResult.setErrorMessage(SHErrorCode.OUT_QR_CODE_URL_NOT_PARSE.getErrorMessage());
                return createQRCodeResult;
            }
            qrCodeUrl = fileV2Manager.getUrlByPath(fsPath,data.getEa(),false);
        }

        createQRCodeResult.setQrCodeUrl(qrCodeUrl);
        createQRCodeResult.setQrCodeId(qrCodeEntity.getId());
        return createQRCodeResult;
    }

    public CreateQRCodeResult createMiniappQRCode(CreateQRCodeInnerData data){
        String appid = null;
        Integer id = null;
        String apath = null;
        String qrCodeUrl = null;
        Boolean isNeedCustomizeCode = false;
        CreateQRResult createQRResult = null;

        if (StringUtils.isNotBlank(data.getEa())) {
            String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(data.getEa());
            if(!WxAppInfoEnum.isMankeep(wxAppId)){
                isNeedCustomizeCode = true;
                appid = wxAppId;
            }
        }

        log.info("isNeedCustomizeCode is:{}", isNeedCustomizeCode);
        if (isNeedCustomizeCode) {
            try {
                createQRResult = createMiniappQRCodeResult(QRCodeTypeEnum.getByType(data.getType()), data.getValue(), appid, data.getEa(), data.getMiniappPath());
            } catch (Exception e) {
                log.error("QRCodeManager.createQRCode createMiniappQRCode exception:", e);
                return null;
            }

            if (null == createQRResult) {
                log.error("QRCodeManager.createQRCode createQRResult is null");
                return null;
            }
            id = createQRResult.getId();
            apath = createQRResult.getApath();
            qrCodeUrl = fileV2Manager.getUrlByPath(createQRResult.getApath(), data.getEa(), false);
        } else {
            CreateQRCodeArg createQRCodeArg = new CreateQRCodeArg();
            createQRCodeArg.setType(data.getType());
            createQRCodeArg.setValue(data.getValue());

            ModelResult<com.facishare.mankeep.api.outService.result.qrCode.CreateQRCodeResult> createQRCodeResultModelResult = outQRCodeService.createQRCode(createQRCodeArg);
            if (!createQRCodeResultModelResult.isSuccess()) {
                log.error("QRCodeManager.createQRCode outQRCodeService.creator failed, createQRCodeArg={}", createQRCodeArg);
                return null;
            }

            id = createQRCodeResultModelResult.getData().getId();
            apath = createQRCodeResultModelResult.getData().getApath();
            qrCodeUrl = createQRCodeResultModelResult.getData().getQrCodeUrl();
        }
        if (EmptyUtil.isNullForList(id, apath)) {
            log.error("QRCodeManager.createQRCode createMiniappQRCode apath is blank, result={}", createQRResult);
            return null;
        }

        if (StringUtils.isNotBlank(data.getEa())) {
            EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(data.getEa());
            if (null == enterpriseInfoEntity || StringUtils.isBlank(enterpriseInfoEntity.getIconPath())) {
                CreateQRCodeResult result = new CreateQRCodeResult();
                result.setErrorCode(SHErrorCode.QRPOSTER_ELOGO_NOT_FOUND.getErrorCode());
                result.setErrorMessage(SHErrorCode.QRPOSTER_ELOGO_NOT_FOUND.getErrorMessage());
                return result;
            }

            if (enterpriseInfoEntity.getDrawQrcodeElogo() != null && enterpriseInfoEntity.getDrawQrcodeElogo() == 1) {
                String eLogoApath = enterpriseInfoEntity.getIconPath();
                byte[] eLogoBytes = fileV2Manager.downloadAFile(eLogoApath, data.getEa());
                if (eLogoBytes == null || eLogoBytes.length <= 0) {
                    log.error("QRCodeManager.createMiniappQRCode eLogoBytes is blank, eLogoApath={}", eLogoApath);
                    return null;
                }

                byte[] bytes = fileV2Manager.downloadAFile(apath, data.getEa());
                if (bytes == null || bytes.length <= 0) {
                    log.error("QRCodeManager.createMiniappQRCode apath is blank, eLogoApath={}", apath);
                    return null;
                }

                BufferedImage bufferedImage = ImageUtil.bytes2Image(bytes);
                BufferedImage eLogoBufferedImage = ImageUtil.bytes2Image(eLogoBytes);
                if (null != bufferedImage && null != eLogoBufferedImage) {
                    eLogoBufferedImage = ImageUtil.cropSquare(eLogoBufferedImage);
                    eLogoBufferedImage = ImageUtil.scale(eLogoBufferedImage, 456, true);
                    eLogoBufferedImage = ImageUtil.cropCircular(eLogoBufferedImage, eLogoBufferedImage.getWidth(), eLogoBufferedImage.getWidth()/2);

                    Graphics2D bgG2d = bufferedImage.createGraphics();
                    bgG2d.drawImage(eLogoBufferedImage, (bufferedImage.getWidth()-eLogoBufferedImage.getWidth())/2,
                            (bufferedImage.getHeight()-eLogoBufferedImage.getHeight())/2,
                            eLogoBufferedImage.getWidth(), eLogoBufferedImage.getHeight(), null);
                    bgG2d.dispose();

                    byte[] resultBytes = ImageUtil.image2Bytes(bufferedImage, "jpg");
                   // String tempApath = fileV2Manager.uploadToApath(resultBytes, "jpg", data.getEa());
                    String tempApath = fileV2Manager.uploadToCpathOrNpath(data.getEa(), resultBytes, true, null);
                    if (StringUtils.isNotBlank(tempApath)) {
                        apath = tempApath;
                        qrCodeUrl = fileV2Manager.getUrlByPath(tempApath, data.getEa(), false);
                    }
                }
            }
        }

        CreateQRCodeResult result = new CreateQRCodeResult();
        result.setQrCodeId(id);
        result.setQrCodeApath(apath);
        result.setQrCodeUrl(qrCodeUrl);

        return result;
    }

    private CreateQRCodeResult createNormalQRCode(CreateQRCodeInnerData data) {
        QRCodeEntity qrCodeEntity = new QRCodeEntity();
        qrCodeEntity.setType(data.getType());
        qrCodeEntity.setValue(data.getValue());
        qrCodeEntity.setAuthCode(MD5Util.md5String(data.getType()+data.getValue()+new Date().getTime()));
        qrCodeEntity.setEa(data.getEa());
        if (qrCodeDAO.insert(qrCodeEntity) != 1) {
            log.error("QRCodeManager.createQRCode qrCodeDAO.insert failed, type={}, value={}", data.getType(), data.getValue());
            return null;
        }

        String domainUrl = fileV2Manager.getSpreadContentDomain(data.getEa());
        String qrUrl = domainUrl + "/proj/page/marketing-qrcode?id=" + qrCodeEntity.getId() + "&authCode=" + qrCodeEntity.getAuthCode() + "&ea=" + data.getEa();
        Map<String, Object> urlParamMap = null;
        if (StringUtils.isNotEmpty(data.getH5Path())){
            //前端自定义的二维码地址，不走中间页（优化后的签到逻辑）
            qrUrl = domainUrl + data.getH5Path();
            urlParamMap = GsonUtil.getGson().fromJson(data.getValue(), new TypeToken<Map<String, String>>(){}.getType());
        }

        qrUrl = smsParamManager.getShortUrl(qrUrl);
        String eLogoApath = null;
        if (StringUtils.isNotBlank(data.getEa())) {
            EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(data.getEa());
            if (null == enterpriseInfoEntity || StringUtils.isBlank(enterpriseInfoEntity.getIconPath())) {
                CreateQRCodeResult result = new CreateQRCodeResult();
                result.setErrorCode(SHErrorCode.QRPOSTER_ELOGO_NOT_FOUND.getErrorCode());
                return result;
            }

            if (enterpriseInfoEntity.getDrawQrcodeElogo() != null && enterpriseInfoEntity.getDrawQrcodeElogo() == 1) {
                eLogoApath = enterpriseInfoEntity.getIconPath();
            }
        }

        CreateQRCodeResult result = drawNormalQRCode(qrUrl, data.getLengthOfSide(), eLogoApath, data.getEa());
        if (StringUtils.isNotEmpty(data.getH5Path())){
            result.setUrlParamMap(urlParamMap);
        }
        result.setQrCodeId(qrCodeEntity.getId());
        return result;
    }

    /**
     * 同步创建二维码
     * @param data
     * @return
     */
    public CreateQRCodeResult createQRCode(CreateQRCodeInnerData data) {
        if (EmptyUtil.isNullForList(data.getType())) {
            log.warn("QRCodeManager.createQRCode params error");
            return null;
        }

        if (StringUtils.isNotBlank(data.getValue()) && !TextUtil.isJson(data.getValue())) {
            log.warn("QRCodeManager.createQRCode value is not json string, value={}", data.getValue());
            return null;
        }
        //处理建发对接外部系统二维码
        if (QRCodeTypeEnum.needOutLinkQRCode(data.getType())) {
            return createOutLinkQRCode(data);
        }

        //初始化企业的名称和icon
        if (!enterpriseDefaultManager.initEnterpriseInfo(data.getEa())){
            return null;
        }

        if (QRCodeTypeEnum.needCreateMiniappQRCode(data.getType())) {
            //创建小程序二维码
            return createMiniappQRCode(data);
        } else if (QRCodeTypeEnum.needCreateNormalQRCode(data.getType())) {
            //创建H5二维码
            return createNormalQRCode(data);
        }else {
            return null;
        }
    }

    @Data
    @ToString
    public static class CreateQRResult implements Serializable {
        private String apath;
        private Integer id;
        private Integer type;
    }

    public CreateQRResult createMiniappQRCodeResult(QRCodeTypeEnum type, String value, String appid, String ea, String miniappPath) throws Exception {
        if (type == null) {
            log.warn("QRCodeManager.createMiniappQRCode type is null");
            return null;
        }

        if (StringUtils.isBlank(value)) {
            log.warn("QRCodeManager.createMiniappQRCode value is null");
            return null;
        }

        QRCodeEntity qrCodeEntity = new QRCodeEntity();
        qrCodeEntity.setType(type.getType());
        qrCodeEntity.setValue(value);
        qrCodeEntity.setEa(ea);
        int addQRCodeResult = qrCodeDAO.insert(qrCodeEntity);
        if (addQRCodeResult != 1) {
            log.error("insert failed, qrCodeEntity={}", qrCodeEntity);
            return null;
        }

        String accessToken = wechatAccountManager.getAccessTokenByWxAppId(appid);
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }

        Map queries = new HashMap();
        queries.put("access_token", accessToken);

        Map<String, Object> params = new HashMap<>();

        //参数传递不支持{},无法使用json串:还有其他参数使用&进行分割
        params.put("scene", "id=" + qrCodeEntity.getId() + "&hostType=" + enterpriseEnvironment);
        //上线后需要增加的参数
        String prodPath = "pages/qr/qr";
        params.put("page", prodPath);
        params.put("width", 1024);
        params.put("auto_color", false);
        Map<String, Object> line_color = new HashMap<>();
        line_color.put("r", 51);
        line_color.put("g", 51);
        line_color.put("b", 51);
        params.put("line_color", line_color);

        String wxRequestUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit";
        String realUrl = HttpUtil.joinParamters(wxRequestUrl, queries);
        byte[] result = httpManager.postBytesByUrl(realUrl, params);
        if (result == null) {
            log.warn("QRCodeManager.createMiniappQRCode params:{}, wxRequestUrl");
            return null;
        }
//        FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.uploadPic(result, "jpg");
        String cpath = fileV2Manager.uploadToCpathOrNpath(ea, result, true, null);
        CreateQRResult qrCodeResult = new CreateQRResult();
        qrCodeResult.setApath(cpath);
        qrCodeResult.setId(qrCodeEntity.getId());
        qrCodeResult.setType(type.getType());

        return qrCodeResult;
    }

    public CreateQRCodeResult createCustomQRCode(Integer type, String qrUrl, Integer lengthOfSide, String ea) {
        if (QRCodeTypeEnum.canCreateCustomQRCode(type)) {

            String eLogoApath = null;
            if (StringUtils.isNotBlank(ea)) {
                EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(ea);
                if (null == enterpriseInfoEntity || StringUtils.isBlank(enterpriseInfoEntity.getIconPath())) {
                    CreateQRCodeResult result = new CreateQRCodeResult();
                    result.setErrorCode(SHErrorCode.QRPOSTER_ELOGO_NOT_FOUND.getErrorCode());
                    return result;
                }

                if (enterpriseInfoEntity.getDrawQrcodeElogo() != null && enterpriseInfoEntity.getDrawQrcodeElogo() == 1) {
                    eLogoApath = enterpriseInfoEntity.getIconPath();
                }
            }

            CreateQRCodeResult result = drawNormalQRCode(qrUrl, lengthOfSide, eLogoApath,ea);
            return result;
        }

        return null;
    }

    public CreateQRCodeResult drawNormalQRCode(String qrUrl, Integer lengthOfSide, String eLogoApath,String ea) {
        byte[] bytes = creator(qrUrl, lengthOfSide, eLogoApath,ea);
        if (null == bytes || bytes.length < 1) {
            log.warn("QRCodeManager.createQRCode image2Bytes failed");
            return null;
        }

        FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.uploadPic(bytes, "jpg");
        String tempApath = fileV2Manager.uploadToCpathOrNpath(ea, bytes, true, null);

        if (EmptyUtil.isNullForList(fileManagerPicResult, fileManagerPicResult.getUrlAPath(), fileManagerPicResult.getUrl())) {
            log.warn("QRCodeManager.createQRCode uploadPic failed");
            return null;
        }

        CreateQRCodeResult result = new CreateQRCodeResult();
        result.setQrCodeApath(tempApath);
        result.setQrCodeUrl(fileV2Manager.getUrlByPath(ea,tempApath));
        result.setQrUrl(qrUrl);
        return result;
    }

    public Result<QueryQRCodeResult> queryQRCode(Integer qrCodeId, String authCode) {
        QRCodeEntity qrCodeEntity = qrCodeDAO.queryById(qrCodeId);
        if (null == qrCodeEntity) {
            return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
        }

        if (StringUtils.isNotBlank(qrCodeEntity.getAuthCode())) {
            if (StringUtils.isBlank(authCode)) {
                return Result.newError(SHErrorCode.QRCODE_AUTH_CODE_NOT_FOUND);
            }

            if (!qrCodeEntity.getAuthCode().equals(authCode)) {
                return Result.newError(SHErrorCode.QRCODE_AUTH_FAILED);
            }
        }

        QueryQRCodeResult result = BeanUtil.copy(qrCodeEntity, QueryQRCodeResult.class);
        result.setQrCodeId(qrCodeEntity.getId());
        return Result.newSuccess(result);
    }

    public Result<QueryQRCodeResult> decodeQRCodeByURLOrApath(String url) {
        Long startTime = System.currentTimeMillis();
        byte[] bytes = fileV2Manager.downloadFileByUrl(url, null);
        if (null == bytes || bytes.length == 0) {
            return null;
        }

        Long endTime = System.currentTimeMillis();
        log.info("QRCodeManager.decodeQRCodeByURLOrApath download duration = {}", endTime-startTime);

        BufferedImage bufferedImage = ImageUtil.bytes2Image(bytes);
        if (null == bufferedImage) {
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        String dispatherUrl = decoder(bufferedImage);
        if (StringUtils.isBlank(dispatherUrl)) {
            log.warn("QRCodeManager.decodeQRCodeByApath dispatherUrl not found");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        Integer qrCodeId = Integer.valueOf(UrlUtils.getParam(dispatherUrl, "id"));
        if (null == qrCodeId) {
            log.warn("QRCodeManager.decodeQRCodeByApath qrCodeId not found, dispatherUrl={}", dispatherUrl);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        String authCode = UrlUtils.getParam(dispatherUrl, "authCode");
        if (StringUtils.isBlank(authCode)) {
            log.warn("QRCodeManager.decodeQRCodeByApath authCode not found, dispatherUrl={}", dispatherUrl);
            return new Result<>(SHErrorCode.QRCODE_AUTH_CODE_NOT_FOUND);
        }

        return queryQRCode(qrCodeId, authCode);

    }

    private String decoder(BufferedImage bufferedImage) {
        MultiFormatReader multiFormatReader = new MultiFormatReader();
        HashMap hints = new HashMap();
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");

        try {
            BinaryBitmap binaryImg = new BinaryBitmap(new HybridBinarizer(new BufferedImageLuminanceSource(bufferedImage)));
            com.google.zxing.Result result = multiFormatReader.decode(binaryImg,hints);
            return result.getText();
        }catch (Exception e){
            log.error("QRCodeManager.decoder failed");
        }
        return null;
    }

    private byte[] creator(String url, Integer lengthOfSide, String eLogoApath,String ea) {
        try {
            if (null == lengthOfSide || lengthOfSide <= 0) {
                lengthOfSide = 600;
            }

            Hashtable<EncodeHintType, Object> hints = new Hashtable<>();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.MARGIN, 0);
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q);
            BitMatrix matrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, lengthOfSide, lengthOfSide, hints);
            BufferedImage image = new BufferedImage(lengthOfSide, lengthOfSide, BufferedImage.TYPE_INT_RGB);
            for (int x = 0; x < lengthOfSide; x++) {
                for (int y = 0; y < lengthOfSide; y++) {
                    image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
                }
            }

            if (StringUtils.isNotBlank(eLogoApath)) {
                byte[] eLogoBytes = fileV2Manager.downloadAFile(eLogoApath, ea);
                if (null != eLogoBytes || eLogoBytes.length > 0) {
                    BufferedImage eLogoBufferedImage = ImageUtil.bytes2Image(eLogoBytes);
                    if (null != image && null != eLogoBufferedImage) {
                        eLogoBufferedImage = ImageUtil.cropSquare(eLogoBufferedImage);
                        eLogoBufferedImage = ImageUtil.scale(eLogoBufferedImage, lengthOfSide/3, true);

                        Graphics2D bgG2d = image.createGraphics();
                        bgG2d.drawImage(eLogoBufferedImage, (image.getWidth() - eLogoBufferedImage.getWidth()) / 2,
                                (image.getHeight() - eLogoBufferedImage.getHeight()) / 2,
                                eLogoBufferedImage.getWidth(), eLogoBufferedImage.getHeight(), null);
                        bgG2d.dispose();

                    }
                }
            }

            return ImageUtil.image2Bytes(image, "jpg");
        } catch (Exception e) {
            log.error("QRCodeManager.creator failed, url={}", url, e);
        }

        return null;
    }


    public QRCodeManager.CreateQRCodeResult saveQrCodeAndPhotoAssociationData(Integer qrCodeType, PhotoTargetTypeEnum photoType, String objectId, Integer objectType,
        String formId, String marketingEventId, String jsonValue, String ea) {
        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(qrCodeType);
        data.setValue(jsonValue);
        data.setEa(ea);
        QRCodeManager.CreateQRCodeResult createQRCodeResult = createQRCode(data);
        if (createQRCodeResult == null) {
            log.warn("QRCodeManager.saveQrCodeAndPhotoAssociationData error createQRCodeResult is null jsonValue:{}", jsonValue);
            return null;
        }

        // 插入中间表
        String photoAssociationId = UUIDUtil.getUUID();
        PhotoAssociationEntity photoAssociationEntity = new PhotoAssociationEntity();
        PhotoAssociationLinkIds photoAssociationLinkIds = new PhotoAssociationLinkIds();
        photoAssociationLinkIds.setObjectId(objectId);
        photoAssociationLinkIds.setObjectType(objectType);
        photoAssociationLinkIds.setFormId(formId);
        photoAssociationLinkIds.setMarketingEventId(marketingEventId);
        photoAssociationEntity.setPhotoAssociationLinkIds(photoAssociationLinkIds);
        photoAssociationEntity.setId(photoAssociationId);
        photoAssociationEntity.setPhotoTargetType(photoType.getType());
        photoAssociationEntity.setEa(ea);
        photoAssociationDAO.insertPhotoAssociation(photoAssociationEntity);

        // 保存二维码数据
        boolean result = photoManager.savePhotoByAapath(ea,photoType, photoAssociationId, createQRCodeResult.getQrCodeApath(), createQRCodeResult.getQrCodeApath());
        if (!result) {
            log.warn("QRCodeManager.saveQrCodeAndPhotoAssociationData savePhoto error jsonValue:{}", jsonValue);
            return null;
        }

        return createQRCodeResult;
    }

    public SaveQrCodeAndPhotoDataResult saveQrCodeAndPhotoData(String ea , Integer qrCodeType, String jsonValue,Integer lengthOfSide,PhotoTargetTypeEnum photoType, String photoTargetId ) {
        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(qrCodeType);
        data.setValue(jsonValue);
        data.setLengthOfSide(lengthOfSide);
        data.setEa(ea);
        SaveQrCodeAndPhotoDataResult createQRCodeResult = this.convert(createQRCode(data));
        if (createQRCodeResult == null) {
            log.warn("QRCodeManager.saveQrCodeAndPhotoAssociationData error createQRCodeResult is null jsonValue:{}", jsonValue);
            return null;
        }
        // 保存二维码数据
        String result = photoManager.savePhotoByAPath(ea,photoType, photoTargetId, createQRCodeResult.getQrCodeApath(), createQRCodeResult.getQrCodeApath());
        if (StringUtils.isEmpty(result)) {
            log.warn("QphotoManager.savePhotoByAPath result is null, jsonValue:{}", jsonValue);
        }

        createQRCodeResult.setPhotoId(result);
        return createQRCodeResult;
    }

    public SaveQrCodeAndPhotoDataResult convert(CreateQRCodeResult source){
        if(source==null){
            return null;
        }
        SaveQrCodeAndPhotoDataResult result = new SaveQrCodeAndPhotoDataResult();
        result.setQrCodeId(source.getQrCodeId());
        result.setQrCodeApath(source.getQrCodeApath());
        result.setQrCodeUrl(source.getQrCodeUrl());
        result.setQrUrl(source.getQrUrl());
        result.setErrorCode(source.getErrorCode());
        result.setErrorMessage(source.getErrorMessage());
        return result;

    }
}
