package com.facishare.marketing.provider.entity.baidu;

import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2021/2/2.
 */
@Data
public class AdObjectFieldMappingEntity implements Serializable{
    private String id;

    private String ea;

    private String adAccountId;

    private Integer createBy;

    private FieldMappings crmDataMapping;

    private String crmRecordType;

    private String crmApiName;

    private Integer enable;     //0：不同步  1：同步

    private Date createTime;

    private Date updateTime;
}
