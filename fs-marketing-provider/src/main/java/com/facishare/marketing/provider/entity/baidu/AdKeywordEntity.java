package com.facishare.marketing.provider.entity.baidu;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2021/2/23.
 */
@Data
public class AdKeywordEntity implements Serializable{
    private String id;
    private String ea;
    private String adAccountId;
    private Long campaignId;
    private Long adgroupId;
    private Long keywordId;
    private String keyword;
    private String source;
    private Integer channel;
    private Integer status;  //0：启用  1：停用
    private String marketingKeywordId;   //同步关键词对象id
    private Date createTime;
    private Date updateTime;
    private Long adId;
}
