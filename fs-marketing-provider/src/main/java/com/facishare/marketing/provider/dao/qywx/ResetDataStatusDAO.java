package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.ResetDataStatusEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2020/05/22
 **/
public interface ResetDataStatusDAO {

    @Select(" SELECT * FROM reset_data_status WHERE fs_ea = #{ea} AND fs_user_id = #{userId} and type = #{type}")
    ResetDataStatusEntity getResetDataStatusDataByUser(@Param("ea") String ea, @Param("userId") Integer userId, @Param("type") Integer type);

    @Insert(" INSERT INTO reset_data_status(id, fs_ea, fs_user_id, type, status, create_time, update_time) VALUES(#{entity.id}, #{entity.ea}, " +
        "#{entity.fsUserId}, #{entity.type}, #{entity.status}, now(), now())")
    int insert(@Param("entity") ResetDataStatusEntity entity);

    @Update( " UPDATE reset_data_status SET status = #{status}, update_time = now() WHERE id = #{id}")
    int updateStatus(@Param("id") String id,  @Param("status") Integer status);

    @Delete(" DELETE FROM reset_data_status WHERE id=#{id} ")
    int deleteDataById(@Param("id") String id);


}
