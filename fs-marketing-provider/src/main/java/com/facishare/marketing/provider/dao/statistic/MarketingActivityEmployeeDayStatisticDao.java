package com.facishare.marketing.provider.dao.statistic;

import com.facishare.marketing.api.data.OutEmployeeSpreadData;
import com.facishare.marketing.api.result.EmployeeRankingDataResult;
import com.facishare.marketing.provider.dto.CommonStatisticData;
import com.facishare.marketing.provider.dto.SpreadStatisticDataWithSubordinateDTO;
import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeStatisticEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Created by ranluch on 2019/3/4.
 */
public interface MarketingActivityEmployeeDayStatisticDao {
    @Select("<script>" +
            "SELECT COUNT(DISTINCT fs_user_id) FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            "</script>")
    int countByMarketingActivityIdsAndDayRange(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Select("<script>" +
            "SELECT COUNT(DISTINCT fs_user_id) FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            "</script>")
    int countSpreadEmployeeWithDayRangeForPartner(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Select("<script>" +
            "SELECT COUNT(DISTINCT outer_tenant_id) FROM marketing_activity_employee_day_statistic statistic left join user_relation relation on statistic.ea = relation.ea and statistic.fs_user_id = relation.fs_user_id" +
            " WHERE statistic.ea = #{ea} and relation.status = 'NORMAL' and outer_tenant_id is not null AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            "</script>")
    int countSpreadPartnerWithDayRangeForPartner(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);


    @Select("<script>" +
            "SELECT fs_user_id as employee_id, sum(spread_count) as employee_spread_count, sum(forward_count) as forward_count, sum(look_up_count) as pv " +
            " FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            " GROUP BY fs_user_id ORDER BY employee_spread_count DESC" +
            "</script>")
    List<EmployeeRankingDataResult> sumByMarketingActivityIdsAndDayRange(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>" +
            "SELECT fs_user_id as employee_id, sum(spread_count) as employee_spread_count, sum(forward_count) as forward_count, sum(look_up_count) as pv " +
            " FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            " AND fs_user_id IN "+
            " <foreach collection='userIds' item='userId' open='(' close=')' separator=','>#{userId}</foreach>" +
            " GROUP BY fs_user_id ORDER BY employee_spread_count DESC" +
            "</script>")
    List<EmployeeRankingDataResult> sumByMarketingActivityIdsAndDayRangeAndUserIds(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("userIds")List<Integer> userIds);

    @Select("<script>" +
            "SELECT relation.outer_tenant_id as outTenantId, sum(spread_count) as employee_spread_count, sum(forward_count) as forward_count, sum(look_up_count) as pv ,  count(distinct(statistic.fs_user_id)) as outTenantSpreadEmployee" +
            " FROM marketing_activity_employee_day_statistic statistic left join user_relation relation on statistic.ea = relation.ea and statistic.fs_user_id = relation.fs_user_id WHERE statistic.ea = #{upstreamEa} " +
            " and relation.status = 'NORMAL' and relation.outer_tenant_id is not null AND  (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            " AND relation.outer_tenant_id IN "+
            " <foreach collection='outTenantIds' item='outEa' open='(' close=')' separator=','>#{outEa}</foreach>" +
            " GROUP BY relation.outer_tenant_id ORDER BY employee_spread_count DESC" +
            "</script>")
    List<EmployeeRankingDataResult> sumByMarketingActivityIdsAndDayRangeAndOutTenantIds(@Param("upstreamEa") String upstreamEa, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("outTenantIds")List<Long> outTenantIds);

    @Select("<script>" +
            "SELECT ea as outTenantId, count(distinct(fs_user_id)) as outEmployeeSpread" +
            " FROM marketing_activity_employee_day_statistic WHERE up_stream_ea = #{upstreamEa} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            " AND ea IN "+
            " <foreach collection='outTenantIds' item='outEa' open='(' close=')' separator=','>#{outEa}</foreach>" +
            " GROUP BY ea" +
            "</script>")
    List<OutEmployeeSpreadData> sumSpreadNumByMarketingActivityIdsAndDayRangeAndOutTenantIds(@Param("upstreamEa") String upstreamEa, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("outTenantIds")List<String> outTenantIds);



    @Select("<script>" +
            "SELECT sum(spread_count) as employee_spread_count, sum(forward_count) as forward_count, sum(look_up_count) as pv " +
            " FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            " AND fs_user_id IN "+
            " <foreach collection='userIds' item='userId' open='(' close=')' separator=','>#{userId}</foreach>" +
            "</script>")
     SpreadStatisticDataWithSubordinateDTO getEmployeeSpreadCountByMarketingActivityIdsAndDayRange(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds,
                                                                                           @Param("userIds") List<Integer> userIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
    @Select("<script>" +
            "SELECT sum(spread_count) as employee_spread_count, sum(forward_count) as forward_count, sum(look_up_count) as pv " +
            " FROM marketing_activity_employee_day_statistic WHERE ea = #{outTenantId} AND up_stream_ea =#{upstreamEa} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            " AND fs_user_id IN "+
            " <foreach collection='userIds' item='userId' open='(' close=')' separator=','>#{userId}</foreach>" +
            "</script>")
    SpreadStatisticDataWithSubordinateDTO getEmployeeSpreadCountByMarketingActivityIdsAndDayRangeForPartner(@Param("upstreamEa") String upstreamEa,@Param("outTenantId") String outTenantId, @Param("marketingActivityIds") Collection<String> marketingActivityIds,
                                                                                                  @Param("userIds") List<Integer> userIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>" +
           "SELECT T1.fs_user_id as employee_id, COALESCE(sum(spread_count), 0) as employee_spread_count, COALESCE(sum(forward_count), 0) as forward_count, COALESCE(sum(look_up_count), 0) as pv from\n" +
            "(" +
               "select unnest(ARRAY" +
            "           <foreach collection='userIds' item='userId' open='[' close=']' separator=','>#{userId}</foreach>" +
            ") AS fs_user_id )\n" +
            "AS T1 left join marketing_activity_employee_day_statistic AS B ON T1.fs_user_id = B.fs_user_id AND B.marketing_activity_id in"+
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
            "and (B.date BETWEEN #{startTime}::DATE AND #{endTime}::DATE)" +
           " GROUP BY T1.fs_user_id ORDER BY employee_spread_count DESC" +
            "</script>")
    List<EmployeeRankingDataResult> sumByMarketingActivityIdsAndUserIds(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("userIds") List<Integer> userIds,
                                                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("page")Page page);

    @Select("<script>" +
            "SELECT T1.fs_user_id as employee_id, COALESCE(sum(spread_count), 0) as employee_spread_count, COALESCE(sum(forward_count), 0) as forward_count, COALESCE(sum(look_up_count), 0) as pv from\n" +
            "(" +
            "select unnest(ARRAY" +
            "           <foreach collection='userIds' item='userId' open='[' close=']' separator=','>#{userId}</foreach>" +
            ") AS fs_user_id )\n" +
            "AS T1 left join marketing_activity_employee_day_statistic AS B ON T1.fs_user_id = B.fs_user_id AND B.marketing_activity_id in"+
            " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach> " +
            "and (B.date BETWEEN #{startTime}::DATE AND #{endTime}::DATE)" +
            " and B.up_stream_ea = #{upstreamEa} and B.ea = #{outTenantId} "+
            " GROUP BY T1.fs_user_id ORDER BY employee_spread_count DESC" +
            "</script>")
    List<EmployeeRankingDataResult> sumByMarketingActivityIdsAndUserIdsForPartner(@Param("outTenantId") String outTenantId, @Param("upstreamEa") String upstreamEa,@Param("marketingActivityIds") Collection<String> marketingActivityIds, @Param("userIds") List<Integer> userIds,
                                                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("page")Page page);


    @Select("<script>" +
                   "SELECT fs_user_id as employee_id, sum(spread_count) as employee_spread_count, sum(forward_count) as forward_count, sum(look_up_count) as pv " +
                   " FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} AND (\"date\" BETWEEN #{startTime}::DATE AND #{endTime}::DATE) AND marketing_activity_id IN " +
                   " <foreach collection='marketingActivityIds' item='marketingActivityId' open='(' close=')' separator=','>#{marketingActivityId}</foreach>" +
                   " AND fs_user_id IN "+
                   " <foreach collection='userIds' item='userId' open='(' close=')' separator=','>#{userId}</foreach>" +
                   " GROUP BY fs_user_id ORDER BY employee_spread_count DESC" +
                   "</script>")
    List<EmployeeRankingDataResult> getEmployeeSpreadListByMarketingActivityIdsAndDayRange(@Param("ea") String ea, @Param("marketingActivityIds") Collection<String> marketingActivityIds,
                                                            @Param("userIds")List<Integer>userIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("SELECT s.fs_user_id AS fsUserId, s.marketing_activity_id, COALESCE(s.spread_count, 0) AS spread_count,  COALESCE(s.forward_count, 0) AS forward_count,\n"
            + "COALESCE(s.look_up_count, 0) AS look_up_count, COALESCE(s.forward_user_count, 0) AS forward_user_count, COALESCE(s.look_up_user_count, 0) AS look_up_user_count,\n"
            + "COALESCE(s.lead_accumulation_count, 0) AS lead_accumulation_count from marketing_activity_employee_statistic s \n"
            + "WHERE s.marketing_activity_id=#{marketingActivityId} AND s.ea=#{ea} order by spread_count desc")
    List<MarketingActivityEmployeeStatisticEntity> listMarketingActivitySpreadRanking(@Param("ea")String ea, @Param("marketingActivityId") String marketingActivityId, @Param("page") Page page);
}
