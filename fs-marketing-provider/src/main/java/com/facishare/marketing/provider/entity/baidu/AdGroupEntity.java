package com.facishare.marketing.provider.entity.baidu;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2021/2/23.
 */
@Data
public class AdGroupEntity implements Serializable{
    private String id;            //主键
    private String ea;            //企业账号
    private String adAccountId;   // 广告账号表的主键id
    private Long campaignId;      //推广计划id
    private Long adGroupId;       //推广单元id
    private String adGroupName;   //推广单元名称
    private String marketingEventId;  //市场活动id
    private Integer status;       //推广单元状态
    private String source;        //广告来源：baidu, 360...
    private Date createTime;      //创建时间
    private Date updateTime;      //更新时间
}
