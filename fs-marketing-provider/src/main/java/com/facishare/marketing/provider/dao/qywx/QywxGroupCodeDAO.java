package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.dao.param.qywx.QywxGroupCodeQueryParam;
import com.facishare.marketing.provider.dto.qywx.QywxGroupCodeDTO;
import com.facishare.marketing.provider.entity.qywx.QywxGroupCodeEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface QywxGroupCodeDAO {
    @Insert("INSERT INTO qywx_group_code (id, ea, group_code_name, scene, remark, auto_create_room, room_base_name, room_base_id, chat_id_list, state, config_id, qr_code_url, create_time, update_time, marketing_event_id,status,create_by) "
            + " VALUES (#{obj.id}, #{obj.ea}, #{obj.groupCodeName}, #{obj.scene}, #{obj.remark}, #{obj.autoCreateRoom}, #{obj.roomBaseName}, #{obj.roomBaseId}, #{obj.chatIdList}, #{obj.state}, #{obj.configId}, #{obj.qrCodeUrl},now(), now(), #{obj.marketingEventId},#{obj.status},#{obj.createBy})")
    int insert(@Param("obj") QywxGroupCodeEntity qywxGroupCodeEntity);

    @Select("select * from qywx_group_code where id = #{id}")
    QywxGroupCodeEntity queryById(@Param("id") String id);

    @Update("<script>"
            + "UPDATE qywx_group_code "
            +"           <set>\n"
            +"            <if test=\"groupCodeName != null\">\n"
            +"                \"group_code_name\" = #{groupCodeName},\n"
            +"            </if>\n"
            +"            <if test=\"scene != null\">\n"
            +"                \"scene\" = #{scene},\n"
            +"            </if>\n"
            +"            <if test=\"remark != null\">\n"
            +"                \"remark\" = #{remark},\n"
            +"            </if>\n"
            +"            <if test=\"autoCreateRoom != null\">\n"
            +"                \"auto_create_room\" = #{autoCreateRoom},\n"
            +"            </if>\n"
            +"            <if test=\"roomBaseName != null\">\n"
            +"                \"room_base_name\" = #{roomBaseName},\n"
            +"            </if>\n"
            +"            <if test=\"chatIdList != null\">\n"
            +"                \"chat_id_list\" = #{chatIdList},\n"
            +"            </if>\n"
            +"            <if test=\"marketingEventId != null\">\n"
            +"                \"marketing_event_id\" = #{marketingEventId},\n"
            +"            </if>\n"
            +"            \"room_base_id\" = #{roomBaseId},\n"
            +"            \"update_time\" = now()\n"
            +"        </set>\n"
            +" WHERE id = #{id}"
            + "</script>")
    void updateQywxGroupCode(QywxGroupCodeEntity qywxGroupCodeEntity);

    @Update("<script>" +
            "update qywx_group_code set status = 1 where id in "
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + " </script>")
    void deleteGroupCode(@Param("ids") List<String> ids);

    @Select("<script>" +
            "select * from qywx_group_code where ea = #{ea} and status != 1 "
            +	"<if test=\"groupCodeName != null\">"
            +	    "AND group_code_name like concat(concat('%',#{groupCodeName}),'%')\n"
            +   "</if>"
            + "order by update_time desc"
            + " </script>")
    List<QywxGroupCodeEntity> queryGroupCodeListByPage(@Param("ea") String ea,@Param("groupCodeName") String groupCodeName, @Param("page") Page page);

    @Select("select * from qywx_group_code where ea = #{ea} and state = #{state}")
    QywxGroupCodeEntity getByEaAndState(@Param("ea") String ea, @Param("state") String state);

    @Select("<script> " +
            "SELECT * FROM qywx_group_code WHERE id IN  " +
            "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'> " +
            "#{item} " +
            "</foreach> " +
            "</script>")
    List<QywxGroupCodeEntity> getByIds(@Param("ids") List<String> ids);


    @Select("<script>"
            + "select count(*) from ("
            + "SELECT qywx_group_code.id FROM qywx_group_code WHERE ea = #{ea} AND status != 1 \n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 37)"
            + "UNION "
            + "SELECT qywx_group_code.id FROM qywx_group_code WHERE ea = #{ea} AND create_by = #{userId}  AND status != 1 "
            + " ) ct"
            + "</script>")
    int queryUnGroupAndCreateByMeCount(@Param("ea") String ea,@Param("userId") int userId);

    @Select("SELECT COUNT(*) FROM qywx_group_code WHERE ea = #{ea} AND create_by = #{userId}  AND status != 1")
    int queryCountCreateByMe(@Param("ea")String ea, @Param("userId")Integer userId);

    @Select("<script>"
            + "SELECT count(*) FROM qywx_group_code WHERE ea= #{ea} AND status != 1 \n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 37)"
            + "</script>")
    int queryCountByUnGrouped(@Param("ea")String ea);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT qywx_group_code.id FROM qywx_group_code JOIN object_group_relation on  qywx_group_code.ea = object_group_relation.ea AND qywx_group_code.id = object_group_relation.object_id"
            + " WHERE qywx_group_code.ea = #{ea} AND qywx_group_code.status != 1 \n"
            + " AND object_group_relation.group_id IN\n"
            + "<foreach collection = 'groupIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "UNION"
            + " select qywx_group_code.id from qywx_group_code  where ea = #{ea} and status != 1 "
            + " and id not in (select object_id from object_group_relation where ea = #{ea} and object_type = 37)"
            + "UNION "
            + "SELECT qywx_group_code.id FROM qywx_group_code WHERE ea = #{ea} AND create_by = #{userId} AND status != 1"
            + " ) hex"
            + "</script>")
    int queryAccessibleCount(@Param("ea") String ea, @Param("groupIdList") List<String> groupIdList, @Param("userId") int userId);

    @Select(
            "<script>"
                    + "select qywx_group_code.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_group_code\n"
                    + "left join object_group_relation on qywx_group_code.id = object_group_relation.object_id and qywx_group_code.ea = object_group_relation.ea\n"
                    + "left join object_top on qywx_group_code.id = object_top.object_id and object_top.ea = qywx_group_code.ea\n"
                    + "where qywx_group_code.ea = #{queryParam.ea} and status != 1 \n"
                    + " <if test=\"queryParam.name != null and queryParam.name !='' \">\n"
                    + "  AND qywx_group_code.group_code_name LIKE CONCAT('%',#{queryParam.name},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.marketingEventId != null and queryParam.marketingEventId !='' \">\n"
                    + "  AND qywx_group_code.marketing_event_id = #{queryParam.marketingEventId}\n"
                    + " </if>\n"
                    + "<if test=\"queryParam.groupIdList != null and queryParam.groupIdList.size != 0\">"
                    + " and object_group_relation.group_id in "
                    + "<foreach collection = 'queryParam.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    + "#{queryParam.groupIdList[${index}]}"
                    + "</foreach>"
                    + "</if>"
                    + " and ( "
                    + "qywx_group_code.create_by = #{queryParam.userId} \n"
                    + "or object_group_relation.group_id is null"
                    + "<if test=\"queryParam.permissionGroupIdList != null and queryParam.permissionGroupIdList.size != 0\">"
                    + "or object_group_relation.group_id in "
                    +     "<foreach collection = 'queryParam.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    +     "#{queryParam.permissionGroupIdList[${index}]}"
                    +     "</foreach>"
                    + "</if>"
                    + ")"
                    + "order by object_top.create_time desc nulls last, qywx_group_code.update_time desc"
                    + "</script>"
    )
    List<QywxGroupCodeDTO> getAccessiblePage(@Param("queryParam") QywxGroupCodeQueryParam param, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qywx_group_code.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_group_code\n"
                    + "left join object_top on qywx_group_code.id = object_top.object_id and object_top.ea = qywx_group_code.ea\n"
                    + "where qywx_group_code.ea = #{queryParam.ea} and status != 1 \n"
                    + " <if test=\"queryParam.name != null and queryParam.name !='' \">\n"
                    + "  AND qywx_group_code.group_code_name LIKE CONCAT('%',#{queryParam.name},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.marketingEventId != null and queryParam.marketingEventId !='' \">\n"
                    + "  AND qywx_group_code.marketing_event_id = #{queryParam.marketingEventId}\n"
                    + " </if>\n"
                    + " AND qywx_group_code.create_by = #{queryParam.userId}"
                    + "order by object_top.create_time desc nulls last, qywx_group_code.update_time desc"
                    + "</script>"
    )
    List<QywxGroupCodeDTO> getCreateByMePage(@Param("queryParam") QywxGroupCodeQueryParam queryParam, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qywx_group_code.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qywx_group_code\n"
                    + "left join object_group_relation on qywx_group_code.id = object_group_relation.object_id and qywx_group_code.ea = object_group_relation.ea\n"
                    + "left join object_top on qywx_group_code.id = object_top.object_id and object_top.ea = qywx_group_code.ea\n"
                    + "where qywx_group_code.ea = #{queryParam.ea} and status != 1 \n"
                    + " <if test=\"queryParam.name != null and queryParam.name !='' \">\n"
                    + "  AND qywx_group_code.group_code_name LIKE CONCAT('%',#{queryParam.name},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.marketingEventId != null and queryParam.marketingEventId !='' \">\n"
                    + "  AND qywx_group_code.marketing_event_id = #{queryParam.marketingEventId}\n"
                    + " </if>\n"
                    + " AND ( object_group_relation.group_id is null )"
                    + "order by object_top.create_time desc nulls last, qywx_group_code.update_time desc"
                    + "</script>"
    )
    List<QywxGroupCodeDTO> noGroupPage(@Param("queryParam") QywxGroupCodeQueryParam queryParam,@Param("page") Page page);


    @Update("update qywx_group_code set status = 2 where id = #{id}")
    int invalidCode(@Param("id") String id);
}
