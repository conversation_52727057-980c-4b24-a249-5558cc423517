package com.facishare.marketing.provider.manager.kis;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.InvalidMarketingActivityByIdsArg;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPersonalNoticeSendArg;
import com.facishare.marketing.api.vo.MarketingActivityExternalConfigVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingEventCommonSettingDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityStatisticDao;
import com.facishare.marketing.provider.dao.kis.SpreadTaskDAO;
import com.facishare.marketing.provider.dao.mail.MailTaskStatisticsDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.statistic.MarketingActivityDayStatisticDao;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.dto.kis.GetMarketingActivityIdsByObjectDTO;
import com.facishare.marketing.provider.dto.kis.GetMarketingActivityInfoDTO;
import com.facishare.marketing.provider.dto.kis.GetMarketingActivitySendScopeDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO.ActivityObjectInfo;
import com.facishare.marketing.provider.entity.ExternalConfig;
import com.facishare.marketing.provider.entity.MarketingActivityEmployeeDayEntity;
import com.facishare.marketing.provider.entity.MarketingEventCommonSettingEntity;
import com.facishare.marketing.provider.entity.data.MarketingActivityNoticeSendData;
import com.facishare.marketing.provider.entity.mail.MailTaskStatisticsEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.innerData.MarketingActivityStatisticData;
import com.facishare.marketing.provider.innerResult.ding.DingStaffInfoResult;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasAddMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasUpdateMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.GetMarketingActivityDetailVo;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.marketing.statistic.outapi.service.MarketingActivityStatisticService;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/03/05
 **/
@Service
@Slf4j
public class MarketingActivityManager {
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private MarketingActivityDAO marketingActivityDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingActivityStatisticService marketingActivityStatisticService;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private SpreadTaskDAO spreadTaskDAO;
    @Autowired
    private MailTaskStatisticsDAO mailTaskStatisticsDAO;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;
    @Autowired
    private MarketingActivityDayStatisticDao marketingActivityDayStatisticDao;
    @Autowired
    private MarketingActivityStatisticDao marketingActivityStatisticDao;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;
    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    /**
     * 根据物料id查询对应全员营销活动数据 （只查询全员营销）
     */
    public Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> getActivityIdsByObject(Map<String, Integer> object, String ea, Integer fsUid) {
        if (MapUtils.isEmpty(object) || StringUtils.isEmpty(ea)) {
            log.info("MarketingActivityManager.getActivityIdsByObject param error object:{}, ea:{}", object, ea);
            return Maps.newHashMap();
        }
        List<GetMarketingActivityIdsByObjectDTO> getMarketingActivityIdsByObjectDTOList = Lists.newArrayList();
        List<String> objectIds = Lists.newArrayList();
        List<String> activityIds = Lists.newArrayList();
        Map<String, GetMarketingActivityInfoDTO> marketingActivityResultMap;
        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> result = Maps.newHashMap();
        object.forEach((key, value) -> objectIds.add(key));
        // 查询企业数据（按照config表中create_time 倒序）
        getMarketingActivityIdsByObjectDTOList.addAll(marketingActivityDAO.getCompanyActivityIdsByObject(ea, objectIds, Lists.newArrayList(15), new Date().getTime() + ""));
        if (CollectionUtils.isEmpty(getMarketingActivityIdsByObjectDTOList)) {
            return Maps.newHashMap();
        }
        // 取出所有活动id
        for (GetMarketingActivityIdsByObjectDTO getMarketingActivityIdsByObjectDTO : getMarketingActivityIdsByObjectDTOList) {
            activityIds.addAll(getMarketingActivityIdsByObjectDTO.getActivityIds());
        }
        activityIds = activityIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityIds)) {
            return Maps.newHashMap();
        }
        // 过滤发送范围
        List<GetMarketingActivitySendScopeDTO> getMarketingActivitySendScopeDTOList = marketingActivityDAO.getMarketingActivitySendScope(activityIds);
        Map<String, NoticeSendArg.NoticeVisibilityVO> marketingActivityVisibilityMap = new HashMap<>();
        for (GetMarketingActivitySendScopeDTO getMarketingActivitySendScopeDTO : getMarketingActivitySendScopeDTOList) {
            NoticeSendArg.NoticeVisibilityVO visibilityVO = GsonUtil.getGson().fromJson(getMarketingActivitySendScopeDTO.getSendScope(), NoticeSendArg.NoticeVisibilityVO.class);
            marketingActivityVisibilityMap.put(getMarketingActivitySendScopeDTO.getMarketingActivityId(), visibilityVO);
            /*
            if (!this.isInSendScope(visibilityVO, ea, fsUid)) {
                activityIds.remove(getMarketingActivitySendScopeDTO.getMarketingActivityId());
            }
             */
        }
        Optional<Map<String, Boolean>> marketingScopeOpt = isInSendScopeByBatch(marketingActivityVisibilityMap, ea, fsUid);
        if (!marketingScopeOpt.isPresent()){
            return Maps.newHashMap();
        }

        for (Map.Entry<String, Boolean> entry : marketingScopeOpt.get().entrySet()){
            if (!entry.getValue()){
                activityIds.remove(entry.getKey());
            }
        }

        if (CollectionUtils.isEmpty(activityIds)) {
            return Maps.newHashMap();
        }

        // 根据审核状态二次过滤
        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
        if (marketingEventCommonSettingEntity != null && marketingEventCommonSettingEntity.getMarketingActivityAudit()) {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 10000);
            paasQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), activityIds);
            paasQueryArg.addFilter("life_status", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList("normal"));
            queryFilterArg.setQuery(paasQueryArg);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, fsUid, queryFilterArg);
            if (objectDataInnerPage != null) {
                activityIds = objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(activityIds)) {
            return Maps.newHashMap();
        }

        // 查询活动详情
        List<GetMarketingActivityInfoDTO> getMarketingActivityInfoDTOList = marketingActivityDAO.getMarketingActivityInfo(activityIds);
        if (CollectionUtils.isEmpty(getMarketingActivityInfoDTOList)) {
            return Maps.newHashMap();
        }
        marketingActivityResultMap = getMarketingActivityInfoDTOList.stream().collect(Collectors.toMap(GetMarketingActivityInfoDTO::getId, data -> data));
        // 组装数据(将各个活动赋值)
        for (GetMarketingActivityIdsByObjectDTO getMarketingActivityIdsByObjectDTO : getMarketingActivityIdsByObjectDTOList) {
            List<ActivityObjectInfo> activityObjectInfoList = Lists.newArrayList();
            for (String activityId : getMarketingActivityIdsByObjectDTO.getActivityIds()) {
                GetMarketingActivityInfoDTO getMarketingActivityInfoDTO = marketingActivityResultMap.get(activityId);
                if (getMarketingActivityInfoDTO != null) {
                    MarketingActivityObjectInfoDTO.ActivityObjectInfo activityObjectInfo = BeanUtil.copy(getMarketingActivityInfoDTO, MarketingActivityObjectInfoDTO.ActivityObjectInfo.class);
                    activityObjectInfoList.add(activityObjectInfo);
                }
            }
            result.put(getMarketingActivityIdsByObjectDTO.getObjectId(), activityObjectInfoList);
        }
        return result;
    }

    public String getMarketingActivityIdAndCreateIfNeed(String ea, Integer fsUserId, MarketingActivityPersonalNoticeSendArg arg) {
        if (arg.getAssociateId() == null || arg.getAssociateIdType() == null || arg.getTitle() == null) {
            log.info("getMarketingActivityIdAndCreateIfNeed associateId,{} associateIdType,{} title ,{}", arg.getAssociateId(), arg.getAssociateIdType(), arg.getTitle());
            return null;
        }
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = null;
        if (StringUtils.isEmpty(arg.getMarketingEventId())){
            marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getMarketingActivityExternalConfigEntityWithOutMarketingEventId(ea, arg.getAssociateId(), arg.getAssociateIdType());
        }else {
            marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                    .getMarketingActivityExternalConfigEntityWithMarketingEventId(ea, arg.getAssociateId(), arg.getAssociateIdType(),arg.getMarketingEventId());
        }
        log.info("getMarketingActivityIdAndCreateIfNeed marketingActivityExternalConfigEntity[{}]", marketingActivityExternalConfigEntity);
        if (marketingActivityExternalConfigEntity != null) {
            if (marketingActivityExternalConfigEntity.getExternalConfig() == null || marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO() == null){
                ExternalConfig externalConfig = new ExternalConfig();
                MarketingActivityNoticeSendData marketingActivityNoticeSendData = new MarketingActivityNoticeSendData();
                marketingActivityNoticeSendData.setTitle(arg.getTitle());
                marketingActivityNoticeSendData.setContentType(arg.getAssociateIdType());
                marketingActivityNoticeSendData.setContent(arg.getAssociateId());
                externalConfig.setMarketingActivityNoticeSendVO(marketingActivityNoticeSendData);
                marketingActivityExternalConfigEntity.setExternalConfig(externalConfig);
                marketingActivityExternalConfigDao.updateExternalConfig(marketingActivityExternalConfigEntity, ea);
            }
            return marketingActivityExternalConfigEntity.getMarketingActivityId();
        }
        PaasAddMarketingActivityArg paasAddMarketingActivityArg = new PaasAddMarketingActivityArg();
        paasAddMarketingActivityArg.setName(arg.getTitle());
        int spreadType = arg.isPartner() ? MarketingActivitySpreadTypeEnum.PARENT_SPREAD.getSpreadType() : MarketingActivitySpreadTypeEnum.EMPLOYEE_SPREAD.getSpreadType();
        paasAddMarketingActivityArg.setSpreadType(String.valueOf(spreadType));
        paasAddMarketingActivityArg.setStatus(SendStatusEnum.FINISHED.getStatus() + "");
        paasAddMarketingActivityArg.setMarketingEventId(arg.getMarketingEventId());
        if (Lists.newArrayList(6,26,9999).contains(arg.getAssociateIdType())) {
            //营销内容处理
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("field_micro_page_id", OperatorConstants.IN,Lists.newArrayList(arg.getAssociateId()));
            queryFilterArg.setQuery(paasQueryArg);
            queryFilterArg.setSelectFields( Lists.newArrayList("_id"));
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, 1, 1);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                ObjectData objectData = objectDataInnerPage.getDataList().get(0);
                paasAddMarketingActivityArg.setMarketingContentObjId(objectData.getId());
            }
        }
        Result<String> marketingActivityIdResult = marketingActivityCrmManager.addMarketingActivity(ea, -10000, paasAddMarketingActivityArg);
        if (!marketingActivityIdResult.isSuccess()){
            return null;
        }
        String marketingActivityId =marketingActivityIdResult.getData();
        /**
         * 个人推广保存信息
         * */
        if (marketingActivityIdResult.isSuccess()) {
            MarketingActivityExternalConfigVO marketingActivityExternalConfigVO = new MarketingActivityExternalConfigVO();
            marketingActivityExternalConfigVO.setAssociateId(arg.getAssociateId());
            marketingActivityExternalConfigVO.setAssociateIdType(arg.getAssociateIdType());
            marketingActivityExternalConfigVO.setMarketingActivityId(marketingActivityId);
            marketingActivityExternalConfigVO.setMarketingEventId(arg.getMarketingEventId());
            MarketingActivityExternalConfigEntity entity = BeanUtil.copyByGson(marketingActivityExternalConfigVO, MarketingActivityExternalConfigEntity.class);
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setExternalConfig(new ExternalConfig());
            entity.setMarketingActivityType(MarketingActivityTypeEnum.PERSONAL.getType());

            ExternalConfig externalConfig = new ExternalConfig();
            MarketingActivityNoticeSendData marketingActivityNoticeSendData = new MarketingActivityNoticeSendData();
            marketingActivityNoticeSendData.setTitle(arg.getTitle());
            marketingActivityNoticeSendData.setContentType(arg.getAssociateIdType());
            marketingActivityNoticeSendData.setContent(arg.getAssociateId());
            externalConfig.setMarketingActivityNoticeSendVO(marketingActivityNoticeSendData);
            entity.setExternalConfig(externalConfig);

            marketingActivityExternalConfigDao.insert(entity);
        }
        return marketingActivityId;
    }

    /**
     * 是否在发送范围内
     */
    private boolean isInSendScope(NoticeSendArg.NoticeVisibilityVO vo, String ea, Integer fsUid) {
        Set<Integer> fsUidList = Sets.newHashSet();
        if (vo == null) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
            if (vo.getDepartmentIds().contains(Constant.WHOLE_COMPANY_ID)) {
                return true;
            }
            // 将部门转换为员工
            fsUidList.addAll(fsAddressBookManager.getEmployeeIdsByCircleIds(ea, vo.getDepartmentIds()));
        }
        if (null != vo.getUserIds()) {
            fsUidList.addAll(vo.getUserIds());
        }
        return fsUidList.contains(fsUid);
    }

    private Optional<Map<String, Boolean>> isInSendScopeByBatch(Map<String, NoticeSendArg.NoticeVisibilityVO> marketingActivityVisibilityMap, String ea, Integer fsUid){
        if (MapUtils.isEmpty(marketingActivityVisibilityMap) || fsUid == null){
            return Optional.empty();
        }

        Map<String, Boolean> marketingActivityUserIdScopeMap = new HashMap<>();
        for (Map.Entry<String, NoticeSendArg.NoticeVisibilityVO> entry : marketingActivityVisibilityMap.entrySet()){
            marketingActivityUserIdScopeMap.put(entry.getKey(), Boolean.FALSE);
        }

        //钉钉通讯录
        List<Integer> deptList = null;
        List<String> roleIds = null;
        if (dingManager.isDingAddressbook(ea)){
            //获取钉钉accessToken
            Optional<DingStaffInfoResult> dingStaffInfoResult = dingManager.getDingStaffDetail(ea,fsUid);
            if (dingStaffInfoResult.isPresent() && dingStaffInfoResult.get().getResult() != null){
                deptList = dingStaffInfoResult.get().getResult().getDeptIdList();
            }
        }else {
            FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = qywxUserManager.getQywxInfoByEaAndUserId(ea, fsUid);
            if (fsEmployeeMsg == null){
                return Optional.empty();
            }
            deptList = fsEmployeeMsg.getDepartmentIds();
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (agentConfig != null){
                deptList = deptList.stream().map(dept -> dept + QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
            }
            if (agentConfig == null){
                List<UserRoleVo> userRoleVos = userRoleManager.queryMarketingUserRole(ea, fsUid);
                if (CollectionUtils.isNotEmpty(userRoleVos)){
                    roleIds = userRoleVos.stream().map(UserRoleVo::getRoleCode).collect(Collectors.toList());
                }
            }
        }

        for (Map.Entry<String, NoticeSendArg.NoticeVisibilityVO> entry : marketingActivityVisibilityMap.entrySet()){
            String marketingActivityId = entry.getKey();
            NoticeSendArg.NoticeVisibilityVO noticeVisibilityVO = entry.getValue();
            if (CollectionUtils.isNotEmpty(noticeVisibilityVO.getUserIds()) && noticeVisibilityVO.getUserIds().contains(fsUid)){
                marketingActivityUserIdScopeMap.put(marketingActivityId, Boolean.TRUE);
                continue;
            }
            if (CollectionUtils.isEmpty(noticeVisibilityVO.getDepartmentIds())){
                continue;
            }
            if (noticeVisibilityVO.getDepartmentIds().contains(Constant.WHOLE_COMPANY_ID)){
                marketingActivityUserIdScopeMap.put(marketingActivityId, Boolean.TRUE);
                continue;
            }
            if (CollectionUtils.isNotEmpty(deptList) && CollectionUtils.isNotEmpty(noticeVisibilityVO.getDepartmentIds())){
                Collection intersectionList = CollectionUtils.intersection(deptList, noticeVisibilityVO.getDepartmentIds());
                if (CollectionUtils.isNotEmpty(intersectionList)){
                    marketingActivityUserIdScopeMap.put(marketingActivityId, Boolean.TRUE);
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(noticeVisibilityVO.getRoles()) && CollectionUtils.isNotEmpty(roleIds)){
                List<String> sendRole = noticeVisibilityVO.getRoles().stream().map(NoticeSendArg.roleItem::getRoleCode).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(CollectionUtils.intersection(roleIds, sendRole))){
                    marketingActivityUserIdScopeMap.put(marketingActivityId, Boolean.TRUE);
                }
            }
        }

        return Optional.of(marketingActivityUserIdScopeMap);
    }

    public void updateMarketingActivityStatus(String ea, String marketingActivityId, String status) {
        if (StringUtils.isBlank(marketingActivityId)) {
            return;
        }
        GetMarketingActivityDetailVo getMarketingActivityDetailVo = marketingActivityCrmManager.getByIdMarketingActivity(ea, -10000, marketingActivityId);
        if (getMarketingActivityDetailVo == null) {
            return;
        }
        PaasUpdateMarketingActivityArg paasUpdateMarketingActivityArg = new PaasUpdateMarketingActivityArg();
        paasUpdateMarketingActivityArg.setStatus(status);
        paasUpdateMarketingActivityArg.setId(getMarketingActivityDetailVo.getId());
        paasUpdateMarketingActivityArg.setName(getMarketingActivityDetailVo.getName());
        paasUpdateMarketingActivityArg.setSpreadType(String.valueOf(getMarketingActivityDetailVo.getSpreadType()));
        paasUpdateMarketingActivityArg.setMarketingEventId(getMarketingActivityDetailVo.getMarketingEventId());
        log.info("updateMarketingActivityStatus  status ,{} ,marketingActivityId ,{} ,paasUpdateMarketingActivityArg ,{}", status, getMarketingActivityDetailVo.getId(),
            paasUpdateMarketingActivityArg);
        marketingActivityCrmManager.updateMarketingActivity(ea, -10000, paasUpdateMarketingActivityArg);
    }

    public Map<String, MarketingActivityStatisticData> getMarketingActivityStatisticDataByMarketingActivityIds(String ea, Map<String, Integer> marketingActivityIdToTypeMap){
        if (marketingActivityIdToTypeMap.isEmpty()){
            return new HashMap<>(0);
        }

        Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(ea, marketingActivityIdToTypeMap.keySet()).getData();
        pvMap = pvMap == null ? new HashMap<>() : pvMap;
        Map<String, Integer> leadCountMap = customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(ea, new ArrayList<>(marketingActivityIdToTypeMap.keySet()), null, null, false).stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getMarketingActivityId, CustomizeFormClueNumDTO::getCount, (v1, v2) -> v1));

        Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(ea, marketingActivityIdToTypeMap.keySet()).getData();
        uvMap = uvMap == null ? new HashMap<>() : uvMap;

        Set<String> allEmployeeSpreadMarketingActivityIds = new HashSet<>();
        Set<String> emailMarketingActivityIds = new HashSet<>();
        Set<String> smsMarketingActivityIds = new HashSet<>();
        marketingActivityIdToTypeMap.forEach((marketingActivityId, type) -> {
            if (type.equals(MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType())){
                allEmployeeSpreadMarketingActivityIds.add(marketingActivityId);
            }
            if (type.equals(MarketingActivitySpreadTypeEnum.MAIL_GROUP_SEND.getSpreadType())){
                emailMarketingActivityIds.add(marketingActivityId);
            }
            if (type.equals(MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType())){
                smsMarketingActivityIds.add(marketingActivityId);
            }
        });

        Map<String, Integer> haveSpreadEmployeeCountMap = new HashMap<>();
        if (!allEmployeeSpreadMarketingActivityIds.isEmpty()){
            haveSpreadEmployeeCountMap = spreadTaskDAO.groupCountSpreadTaskFinishedEmployee(ea, allEmployeeSpreadMarketingActivityIds).stream()
                    .filter(dataCount -> !Strings.isNullOrEmpty(dataCount.getId()) && dataCount.getCount() != null)
                    .collect(Collectors.toMap(DataCount::getId, DataCount::getCount, (v1, v2) -> v1));
        }

        Map<String, Integer> emailTotalSendUserCountMap = new HashMap<>();
        if (!emailMarketingActivityIds.isEmpty()){
            emailTotalSendUserCountMap = marketingActivityExternalConfigDao.groupCountEmailTotalSend(ea, emailMarketingActivityIds).stream()
                    .filter(dataCount -> !Strings.isNullOrEmpty(dataCount.getId()) && dataCount.getCount() != null)
                    .collect(Collectors.toMap(DataCount::getId, DataCount::getCount, (v1, v2) -> v1));
        }
        Map<String, Integer> emailOpenUserCountMap = new HashMap<>();
        for (String emailMarketingActivityId : emailMarketingActivityIds) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(emailMarketingActivityId);
            if (marketingActivityExternalConfigEntity != null){
                MailTaskStatisticsEntity mailTaskStatisticsEntity = mailTaskStatisticsDAO.getByEaAndTaskId(ea, marketingActivityExternalConfigEntity.getAssociateId());
                emailOpenUserCountMap.put(emailMarketingActivityId, mailTaskStatisticsEntity != null ? mailTaskStatisticsEntity.getOpenUserNum().intValue() : 0);
            }
        }

        Map<String, Integer> smsTotalSendUserCountMap = new HashMap<>();
        if (!smsMarketingActivityIds.isEmpty()){
            smsTotalSendUserCountMap = marketingActivityExternalConfigDao.groupCountSmsTotalSend(ea, smsMarketingActivityIds).stream()
                    .filter(dataCount -> !Strings.isNullOrEmpty(dataCount.getId()) && dataCount.getCount() != null)
                    .collect(Collectors.toMap(DataCount::getId, DataCount::getCount, (v1, v2) -> v1));
        }
        Map<String, MarketingActivityStatisticData> result = new HashMap<>();
        for (String marketingActivityId : marketingActivityIdToTypeMap.keySet()) {
            MarketingActivityStatisticData data = new MarketingActivityStatisticData();
            data.setId(marketingActivityId);
            data.setPv(pvMap.get(marketingActivityId));
            data.setUv(uvMap.get(marketingActivityId));
            data.setLeadUserCount(leadCountMap.get(marketingActivityId));
            data.setSpreadEmployeeCount(haveSpreadEmployeeCountMap.get(marketingActivityId));
            if (emailTotalSendUserCountMap.get(marketingActivityId) != null){
                data.setTotalSendUserCount(emailTotalSendUserCountMap.get(marketingActivityId));
                data.setOpenEmailUserCount(emailOpenUserCountMap.get(marketingActivityId));
            }
            if (smsTotalSendUserCountMap.get(marketingActivityId) != null){
                data.setTotalSendUserCount(smsTotalSendUserCountMap.get(marketingActivityId));
            }
            result.put(marketingActivityId, data);
        }
        return result;
    }

    public Result<Void> invalidMarketingActivityByIds(InvalidMarketingActivityByIdsArg arg){
        crmV2Manager.bulkInvalid(arg.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), arg.getIds());
        return Result.newSuccess();
    }

    public void fixQywxEmployeeMarketingActivtyStatData(String ea){
        //删除fs_user_id >= 100000000的数据
        marketingActivityDayStatisticDAO.deleteMarketingActivityEmployeeStatisticErrorEmployeeId(ea);
        marketingActivityDayStatisticDAO.deleteMarketingActivityEmployeeDayStatisticErrorEmployeeId(ea);

        // 用表marketing_activity_employee_day_statistic --- 》 marketing_activity_day_statistic
        Map<String, List<MarketingActivityEmployeeDayEntity>> marketingActivityMap = new HashMap<>();
        List<MarketingActivityEmployeeDayEntity> employeeDayEntityList = marketingActivityDayStatisticDAO.getMarketingActivityEmployeeDayDate(ea); //每人/每天/每个营销活动的数据
        for (MarketingActivityEmployeeDayEntity employeeDayEntity : employeeDayEntityList){
            if (marketingActivityMap.get(employeeDayEntity.getMarketingActivityId()) == null){
                marketingActivityMap.put(employeeDayEntity.getMarketingActivityId(), Lists.newArrayList());
            }
            marketingActivityMap.get(employeeDayEntity.getMarketingActivityId()).add(employeeDayEntity);
        }

        List<String> marketingActivityIds = marketingActivityDayStatisticDao.getMarketingActivityIdByEa(ea);
        Set<String> marketingActivitySet = new HashSet<>(marketingActivityIds);
        for (String marketingActivityId : marketingActivitySet){
            Map<Date, List<MarketingActivityEmployeeDayEntity>> dayDateMap = new HashMap<>();
            List<MarketingActivityEmployeeDayEntity> marketingActivityEmployeeDayEntityList  = marketingActivityMap.get(marketingActivityId);
            if (CollectionUtils.isEmpty(marketingActivityEmployeeDayEntityList)){
                continue;
            }

            for (MarketingActivityEmployeeDayEntity employeeDayEntity : marketingActivityEmployeeDayEntityList){
                if (dayDateMap.get(employeeDayEntity.getDate()) == null){
                    dayDateMap.put(employeeDayEntity.getDate(), Lists.newArrayList());
                }
                dayDateMap.get(employeeDayEntity.getDate()).add(employeeDayEntity);
            }

            //更新
            for (Map.Entry<Date, List<MarketingActivityEmployeeDayEntity>> entry : dayDateMap.entrySet()){
                Date day = entry.getKey();
                List<MarketingActivityEmployeeDayEntity> dayStat = entry.getValue();
                int spreadCount = 0;
                Set<Integer> spreadUserSet = new HashSet<>();
                int spreadUserCount = 0;
                int fowardCount = 0;
                int lookupCount = 0;

                if (CollectionUtils.isNotEmpty(dayStat)){
                    for (MarketingActivityEmployeeDayEntity employeeDayEntity : dayStat){
                        spreadCount += employeeDayEntity.getSpreadCount();
                        if (!spreadUserSet.contains(employeeDayEntity.getFsUserId()) && employeeDayEntity.getSpreadCount() > 0){
                            spreadUserSet.add(employeeDayEntity.getFsUserId());
                        }
                        fowardCount += employeeDayEntity.getForwardCount();
                        lookupCount += employeeDayEntity.getLookUpCount();
                    }
                    if (spreadUserSet.size() > 0){
                        spreadUserCount = spreadUserSet.size();
                    }
                    if (spreadCount > 0) {
                        marketingActivityDayStatisticDao.updateMarketingActivityDayStatSpreadCount(ea, marketingActivityId, day, spreadCount);
                    }
                    if (spreadUserCount > 0){
                        marketingActivityDayStatisticDao.updateMarketingActivityDayStatSpreadUserCount(ea, marketingActivityId, day, spreadUserCount);
                    }
                    if (fowardCount > 0){
                        marketingActivityDayStatisticDao.updateMarketingActivityDayStatForwardCount(ea, marketingActivityId, day, fowardCount);
                    }
                    if (lookupCount > 0){
                        marketingActivityDayStatisticDao.updateMarketingActivityDayStatlookupCount(ea, marketingActivityId, day, lookupCount);
                    }

                    log.info("update marketing_activity_day_statistic ea:{} marketingActivityId:{} day:{} spreadCount:{} spreadUserCount:{} forwardCount:{} lookupCount:{}", ea, marketingActivityId, day, spreadCount, spreadUserCount, fowardCount, lookupCount);
                }
            }
        }

        //用表marketing_activity_employee_day_statistic ---》marketing_activity_statistic
        for (Map.Entry<String, List<MarketingActivityEmployeeDayEntity>> entry : marketingActivityMap.entrySet()){
            String marketingActivityId = entry.getKey();
            int spreadCount = 0;
            Set<Integer> spreadUserSet = new HashSet<>();
            int spreadUserCount = 0;
            int forwardCount = 0;
            int lookupCount = 0;
            if (CollectionUtils.isNotEmpty(entry.getValue())){
                for (MarketingActivityEmployeeDayEntity employeeDayEntity : entry.getValue()){
                    spreadCount += employeeDayEntity.getSpreadCount();
                    if (!spreadUserSet.contains(employeeDayEntity.getFsUserId()) && employeeDayEntity.getSpreadCount() > 0){
                        spreadUserSet.add(employeeDayEntity.getFsUserId());
                    }
                    forwardCount += employeeDayEntity.getForwardCount();
                    lookupCount += employeeDayEntity.getLookUpCount();
                }
                if (spreadUserSet.size() > 0){
                    spreadUserCount = spreadUserSet.size();
                }
            }

            if (spreadCount > 0){
                marketingActivityStatisticDao.updateMarketingActivityStatSpreadCount(ea, entry.getKey(), spreadCount);
            }
            if (spreadUserCount > 0){
                marketingActivityStatisticDao.updateMarketingActivityStatSpreadUserCount(ea, entry.getKey(), spreadUserCount);
            }
            if (forwardCount > 0){
                marketingActivityStatisticDao.updateMarketingActivityStatForwardCount(ea, entry.getKey(), forwardCount);
            }
            if (lookupCount > 0){
                marketingActivityStatisticDao.updateMarketingActivityStatlookupCount(ea, entry.getKey(), lookupCount);
            }

            log.info("update marketing_activity_statistic ea:{} marketingActivityId:{} spreadCount:{} spreadUserCount:{} forwardCount:{} lookupCount:{}", ea, marketingActivityId, spreadCount, spreadUserCount, forwardCount, lookupCount);
        }
    }

    @FilterLog
    public String getSpreadType(String ea, String marketingActivityId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(marketingActivityId)) {
            return null;
        }
        ObjectData objectData = crmMetadataManager.getById(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), marketingActivityId);
        if (objectData == null) {
            return null;
        }
        return objectData.getString("spread_type");
    }

    public Map<String, String> getSpreadTypeLabelMap(String ea) {
        Map<String, String> result = Maps.newHashMap();
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (!getDescribeResultResult.isSuccess() || getDescribeResultResult.getData() == null) {
            return result;
        }
        FieldDescribe fieldDescribe = getDescribeResultResult.getData().getDescribe().getFields().get("spread_type");
        if (fieldDescribe != null) {
            for (Map<String, Object> option : fieldDescribe.getOptions()) {
                String i18nKey = String.format("MarketingActivityObj.field.spread_type.option.%s", option.get("value"));
                String label = I18nUtil.get(i18nKey, option.get("label").toString());
                result.put(option.get("value").toString(), label);
            }
        }
        return result;
    }
}
