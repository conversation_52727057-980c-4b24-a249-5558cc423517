package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.kis.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2020/05/15
 **/
public interface BindAppUserInfoDAO {

    @Update(" UPDATE account SET phone = #{phone} WHERE uid = #{uid}")
    int updateAccountPhone(@Param("phone") String phone, @Param("uid") String uid);

    @Select(" SELECT B.id FROM activity AS A JOIN activity_enroll_data AS B ON A.id = B.activity_id WHERE A.ea = #{ea} AND  B.spread_fs_user_id = #{virtualUserId}")
    List<String> queryActivityEnrollIdByEaAndUserId(@Param("ea") String ea, @Param("virtualUserId") Integer virtualUserId);

    @Update("<script>"
        + " UPDATE activity_enroll_data SET spread_fs_user_id = #{fsUserId} WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int updateActivityEnrollData(@Param("fsUserId") Integer fsUserId, @Param("ids") List<String> ids);

    @Update(" UPDATE article SET fs_user_id = #{fsUserId} WHERE fs_ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateArticleFsUserId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE conference_review_employee SET user_id = #{fsUserId} WHERE ea = #{ea} AND user_id = #{virtualUserId}")
    int updateConferenceReviewEmployeeFsUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Select(" SELECT B.id FROM customize_form_data AS A JOIN customize_form_data_user AS B ON A.id = B.form_id WHERE A.ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    List<String> queryCustomizeFormUserByEaAndUserId(@Param("ea") String ea, @Param("virtualUserId") Integer virtualUserId);

    @Update("<script>"
        + " UPDATE customize_form_data_user SET spread_fs_uid = #{fsUserId} WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int updateCustomizeFormDataUserData(@Param("fsUserId") Integer fsUserId, @Param("ids") List<String> ids);


    @Update(" UPDATE enterprise_employee_amount_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateEnterpriseEmployeeAmountStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Delete(" DELETE FROM enterprise_employee_amount_statistic WHERE id = #{id}")
    int deleteEnterpriseEmployeeAmountStatisticUser(@Param("id") String id);

    @Select(" SELECT * FROM enterprise_employee_day_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<EnterpriseEmployeeDayStatisticEntity> getEnterpriseEmployeeDayStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE enterprise_employee_day_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateEnterpriseEmployeeDayStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE enterprise_employee_day_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateEnterpriseEmployeeDayStatisticUserById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);

    @Delete("<script>"
        + "DELETE FROM enterprise_employee_day_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteEnterpriseEmployeeDayStatisticData(@Param("ids") List<String> ids);

    @Select(" SELECT * FROM enterprise_employee_object_amount_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<EnterpriseEmployeeObjectAmountStatisticEntity> getEnterpriseEmployeeObjectAmountStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE enterprise_employee_object_amount_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateEnterpriseEmployeeObjectAmountStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE enterprise_employee_object_amount_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateEnterpriseEmployeeObjectAmountStatisticUserById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);

    @Delete("<script>"
        + "DELETE FROM enterprise_employee_object_amount_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteEnterpriseEmployeeObjectAmountData(@Param("ids") List<String> ids);

    @Select(" SELECT * FROM enterprise_employee_object_day_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<EnterpriseEmployeeObjectDayStatisticEntity> getEnterpriseEmployeeObjectDayStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE enterprise_employee_object_day_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateEnterpriseEmployeeObjectDayStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE enterprise_employee_object_day_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateEnterpriseEmployeeObjectDayStatisticUserById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);


    @Delete("<script>"
        + "DELETE FROM enterprise_employee_object_day_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteEnterpriseEmployeeObjectDayStatisticData(@Param("ids") List<String> ids);

    @Select(" SELECT * FROM enterprise_employee_object_uv_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<EnterpriseEmployeeObjectUVStatisticEntity> getEnterpriseEmployeeObjectUVStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE enterprise_employee_object_uv_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateEnterpriseEmployeeObjectUVStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE enterprise_employee_object_uv_statistic SET uv = #{uv} WHERE id = #{id}")
    int updateEnterpriseEmployeeObjectUvStatisticUV(@Param("id") Integer id, @Param("uv") Integer uv);

    @Update(" UPDATE enterprise_employee_object_uv_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateEnterpriseEmployeeObjectUvStatisticUserById(@Param("fsUserId") Integer fsUserId, @Param("id") Integer id);

    @Delete("<script>"
        + "DELETE FROM enterprise_employee_object_uv_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteEnterpriseEmployeeObjectUvStatisticData(@Param("ids") List<Integer> ids);

    @Update(" UPDATE fs_bind SET fs_user_id = #{fsUserId} WHERE fs_ea = #{ea} AND fs_user_id = #{virtualUserId} AND type = 1 ")
    int updateFsBind(@Param("fsUserId") Integer fsUserId, @Param("ea") String ea, @Param("virtualUserId") Integer virtualUserId);

    @Select(" SELECT * FROM fs_bind WHERE fs_ea = #{ea} and fs_user_id = #{fsUserId} and type=1")
    List<FSBindEntity> queryByEaAndFsUserId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId);

    @Select(" SELECT * FROM marketing_activity_employee_day_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<MarketingActivityEmployeeDayStatisticEntity> getMarketingActivityEmployeeDayStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE marketing_activity_employee_day_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateMarketingActivityEmployeeDayStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update("UPDATE marketing_activity_employee_day_statistic "
        + "SET spread_count = #{spreadCount},"
        + "forward_count =  #{forwardCount}, "
        + "look_up_count = #{lookUpCount}, "
        + "forward_user_count =  #{forwardUserCount}, "
        + "look_up_user_count =  #{lookUpUserCount}, "
        + "lead_increment_count = #{leadIncrementCount}, "
        + "customer_increment_count = #{customerIncrementCount}"
        + "WHERE id = #{id}")
    int incMarketingActivityEmployeeDayByAddEntity(MarketingActivityEmployeeDayStatisticEntity entity);


    @Update(" UPDATE marketing_activity_employee_day_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateMarketingActivityEmployeeDayStatisticUserById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);


    @Delete("<script>"
        + "DELETE FROM marketing_activity_employee_day_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteMarketingActivityEmployeeDayStatisticData(@Param("ids") List<String> ids);


    @Select(" SELECT * FROM marketing_activity_employee_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<MarketingActivityEmployeeStatisticEntity> getMarketingActivityEmployeeStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE marketing_activity_employee_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateMarketingActivityEmployeeStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update("UPDATE marketing_activity_employee_statistic "
        + "SET spread_count = #{spreadCount},"
        + "forward_count = #{forwardCount}, "
        + "look_up_count = #{lookUpCount}, "
        + "forward_user_count = #{forwardUserCount}, "
        + "look_up_user_count = #{lookUpUserCount}, "
        + "lead_accumulation_count = #{leadAccumulationCount}, "
        + "customer_accumulation_count = #{customerAccumulationCount}"
        + "WHERE id = #{id}")
    int incMarketingActivityEmployeeByAddEntity(MarketingActivityEmployeeStatisticEntity addEntity);

    @Update(" UPDATE marketing_activity_employee_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateMarketingActivityEmployeeStatisticById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);

    @Delete("<script>"
        + " DELETE FROM marketing_activity_employee_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach>"
        + "</script>")
    int deleteMarketingActivityEmployeeStatisticData(@Param("ids") List<String> ids);
    
    @Select(" SELECT * FROM marketing_event_employee_amount_statistic WHERE ea = #{ea} and fs_user_id = #{userId}")
    List<MarketingEventEmployeeAmountStatisticEntity> getMarketingEventEmployeeAmountStatisticByEaUserId(@Param("ea") String ea, @Param("userId") Integer userId);
    
    @Update(" UPDATE marketing_event_employee_amount_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateMarketingEventEmployeeAmountStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);
    
    @Update("UPDATE marketing_event_employee_amount_statistic "
        + "SET spread_count = #{spreadCount},"
        + "forward_count = #{forwardCount}, "
        + "look_up_count = #{lookUpCount} "
        + "WHERE id = #{id}")
    int incMarketingEventEmployeeAmountByAddEntity(MarketingEventEmployeeAmountStatisticEntity addEntity);
    
    @Update(" UPDATE marketing_event_employee_amount_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateMarketingEventEmployeeAmountStatisticById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);
    
    @Delete("<script>"
        + " DELETE FROM marketing_event_employee_amount_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach>"
        + "</script>")
    int deleteMarketingEventEmployeeAmountStatisticData(@Param("ids") List<String> ids);

    @Select(" SELECT * FROM marketing_activity_external_config  WHERE external_config -> 'marketingActivityNoticeSendVO' is not null AND ea = #{ea}")
    List<MarketingActivityExternalConfigEntity> getMarketingActivityExternalConfigByEa(@Param("ea") String ea);

    @Update(" UPDATE marketing_activity_external_config SET external_config = #{entity.externalConfig} WHERE id = #{entity.id} ")
    void updateMarketingActivityExternalConfigData(@Param("entity") MarketingActivityExternalConfigEntity entity);

    @Select(" SELECT * FROM marketing_activity_spread_record WHERE ea = #{ea} AND user_id = #{userId}")
    List<MarketingActivitySpreadRecordEntity> getMarketingActivitySpreadRecordByEaAndUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE marketing_activity_spread_record SET user_id = #{fsUserId} WHERE ea = #{ea} AND user_id = #{virtualUserId}")
    int updateMarketingActivitySpreadRecordUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update("UPDATE marketing_activity_spread_record SET create_time = #{createTime}, update_time = #{updateTime} WHERE id = #{id}")
    int updateMarketingActivitySpreadRecordUpdateTime(@Param("id") String id, @Param("createTime") Date createTime, @Param("updateTime") Date updateTime);

    @Update(" UPDATE marketing_activity_spread_record SET user_id = #{fsUserId} WHERE id = #{id}")
    int updateMarketingActivitySpreadRecordById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);

    @Delete("<script>"
        + " DELETE FROM marketing_activity_spread_record WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteMarketingActivitySpreadRecordData(@Param("ids") List<String> ids);

    @Select(" SELECT * FROM marketing_event_employee_statistic WHERE ea = #{ea} AND fs_user_id = #{userId}")
    List<MarketingEventEmployeeStatisticEntity> getMarketingEventEmployeeStatisticByEaAndUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE marketing_event_employee_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateMarketingEventEmployeeStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update("UPDATE marketing_event_employee_statistic "
        + "SET spread_count = #{spreadCount},"
        + "forward_count = #{forwardCount}, "
        + "look_up_count = #{lookUpCount}, "
        + "forward_user_count = #{forwardUserCount}, "
        + "look_up_user_count = #{lookUpUserCount} "
        + " WHERE id = #{id}")
    int incrementMarketingEventEmployeeStatistic(MarketingEventEmployeeStatisticEntity addEntity);

    @Update(" UPDATE marketing_event_employee_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateMarketingEventEmployeeStatisticUserById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);

    @Delete("<script>"
        + "DELETE FROM marketing_event_employee_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteMarketingEventEmployeeStatisticData(@Param("ids") List<String> ids);

    @Select(" SELECT * FROM marketing_object_employee_statistic WHERE ea = #{ea} AND fs_user_id = #{userId}")
    List<MarketingObjectEmployeeStatisticEntity> getMarketingObjectEmployeeStatisticByEaAndUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE marketing_object_employee_statistic SET fs_user_id = #{fsUserId} WHERE ea = #{ea} AND fs_user_id = #{virtualUserId}")
    int updateMarketingObjectEmployeeStatisticUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);


    @Update("UPDATE marketing_object_employee_statistic "
        + "SET spread_count = #{spreadCount},"
        + "forward_count = #{forwardCount}, "
        + "look_up_count = #{lookUpCount}, "
        + "forward_user_count = #{forwardUserCount}, "
        + "look_up_user_count = #{lookUpUserCount}"
        + "WHERE id = #{id}")
    int incrementeMarketingObjectEmployeeStatisc(MarketingObjectEmployeeStatisticEntity addEntity);

    @Update(" UPDATE marketing_object_employee_statistic SET fs_user_id = #{fsUserId} WHERE id = #{id}")
    int updateMarketingObjectEmployeeUserById(@Param("fsUserId") Integer fsUserId, @Param("id") String id);


    @Delete("<script>"
        + "DELETE FROM marketing_object_employee_statistic WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteMarketingObjectEmployeeData(@Param("ids") List<String> ids);


    @Update(" UPDATE moment_poster_user SET user_id = #{fsUserId} WHERE ea = #{ea} AND user_id = #{virtualUserId}")
    int updateMomentPosterUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Select(" SELECT * FROM notice WHERE fs_ea = #{ea}")
    List<NoticeEntity> getNoticeByEa(@Param("ea") String ea);

    @Update(" UPDATE notice SET send_scope = #{sendScope} WHERE id = #{id}")
    int updateNoticeSendScope(@Param("sendScope") String sendScope, @Param("id") String id);

    @Update(" UPDATE qr_poster_user SET user_id = #{fsUserId} WHERE ea = #{ea} AND user_id = #{virtualUserId}")
    int updateQrPosterUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_1 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat1(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_2 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat2(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_3 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat3(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_4 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat4(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_5 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat5(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_6 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat6(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_7 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat7(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_8 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat8(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_9 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat9(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_stat_10 SET spread_fs_uid = #{fsUserId} WHERE ea = #{ea} AND spread_fs_uid = #{virtualUserId}")
    int updateSpreadStat10(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Select(" SELECT * FROM spread_task WHERE ea = #{ea} AND user_id = #{userId}")
    List<SpreadTaskEntity> getSpreadTaskByEaAndUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update(" UPDATE spread_task SET user_id = #{fsUserId} WHERE ea = #{ea} AND user_id = #{virtualUserId}")
    int updateSpreadTaskByEaAndUser(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("virtualUserId") Integer virtualUserId);

    @Update(" UPDATE spread_task SET user_id = #{fsUserId} WHERE id = #{id}")
    int updateSpreadTaskUser(@Param("fsUserId") Integer fsUserId, @Param("id") String id);

    @Delete("<script>"
        + " DELETE FROM spread_task WHERE id IN "
        + " <foreach open='(' close=')' separator=',' collection='ids' item='item'>"
        + " #{item}"
        + " </foreach> "
        + "</script>")
    int deleteSpreadTaskData(@Param("ids") List<String> ids);

    @Update(" UPDATE qywx_virtual_fs_user SET user_id = #{fsUserId}, old_user_id = #{virtualUserId} WHERE ea = #{ea} AND user_id = #{virtualUserId}")
    int updateQywxVirtualFsUser(@Param("ea") String ea, @Param("virtualUserId") Integer virtualUserId, @Param("fsUserId") Integer fsUserId);
}
