package com.facishare.marketing.provider.dao.ticket;

import com.facishare.marketing.provider.entity.ticket.CustomizeTicketReceiveEntity;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2019/10/17
 **/
public interface CustomizeTicketDAO {

    @Select("SELECT * FROM customize_ticket_receive WHERE association_id = #{associationId} AND form_data_user_id = #{formDataUserId}")
    CustomizeTicketReceiveEntity getTicketByAssociationAndDataUserId(@Param("associationId") String associationId, @Param("formDataUserId") String formDataUserId);

    @Insert("INSERT INTO customize_ticket_receive (\n"
        + "        \"id\",\n"
        + "        \"ea\",\n"
        + "        \"ticket_type\",\n"
        + "        \"association_id\",\n"
        + "        \"code\",\n"
        + "        \"form_data_user_id\",\n"
        + "        \"status\",\n"
        + "        \"create_time\"\n"
        + "        )\n"
        + "        VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.ea},\n"
        + "        #{obj.ticketType},\n"
        + "        #{obj.associationId},\n"
        + "        #{obj.code},\n"
        + "        #{obj.formDataUserId},\n"
        + "        #{obj.status},\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;")
    int insertCustomizeTicket(@Param("obj") CustomizeTicketReceiveEntity customizeTicketReceiveEntity);

    @Select("SELECT * FROM customize_ticket_receive WHERE ea = #{ea} AND ticket_type = #{ticketType} ORDER BY code DESC LIMIT 1")
    CustomizeTicketReceiveEntity getLatestTicketByAssociationAndDataUserId(@Param("ea") String ea, @Param("ticketType") Integer ticketType);

    @Select("SELECT * FROM customize_ticket_receive WHERE ea = #{ea} AND ticket_type = #{ticketType} AND association_id = #{associationId} AND code = #{code}")
    CustomizeTicketReceiveEntity getTicketByAssociationAndDataUserIdAndCode(@Param("ea") String ea, @Param("ticketType") Integer ticketType, @Param("associationId") String associationId,
        @Param("code") Long code);

    @Update("UPDATE customize_ticket_receive SET status = #{status}, receive_time = now() WHERE id = #{id}")
    int updateCustomizeTicketReceiveStatus(@Param("status") Integer status, @Param("id") String id);

    @Delete("DELETE FROM customize_ticket_receive WHERE id = #{id}")
    int deleteCustomizeTicketReceiveById(@Param("id") String id);

    @Delete(" DELETE FROM customize_ticket_receive WHERE form_data_user_id = #{oldFormDataUserId}")
    int deleteCustomizeTicketFormDataUser(@Param("oldFormDataUserId") String oldFormDataUserId);

    @Update("  UPDATE customize_ticket_receive SET form_data_user_id = #{newFormDataUserId} WHERE form_data_user_id = #{oldFormDataUserId}")
    int updateCustomizeTicketFormDataUser(@Param("newFormDataUserId") String newFormDataUserId, @Param("oldFormDataUserId") String oldFormDataUserId);


    @Select("<script>" +
            "select * from customize_ticket_receive " +
            "where association_id = #{associationId} " +
            "and form_data_user_id in " +
            "<foreach collection='formDataUserIds' item='item'  open='(' separator=',' close=')' >#{item}</foreach>" +
            "</script>")
    List<CustomizeTicketReceiveEntity> getTicketByAssociationAndDataUserIds(@Param("associationId") String associationId, @Param("formDataUserIds") List<String> formDataUserIds);

    @Select("SELECT * FROM customize_ticket_receive WHERE ea = #{ea} AND ticket_type = #{ticketType} AND association_id = #{associationId} AND  CAST(code AS TEXT ) LIKE CONCAT('%', #{code})")
    CustomizeTicketReceiveEntity getTicketByAssociationAndDataUserIdAndCodeLike(@Param("ea") String ea, @Param("ticketType") Integer ticketType, @Param("associationId") String associationId,
                                                                            @Param("code") String code);
}
