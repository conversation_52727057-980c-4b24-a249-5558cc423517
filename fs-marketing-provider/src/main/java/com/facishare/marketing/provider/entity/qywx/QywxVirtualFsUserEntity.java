package com.facishare.marketing.provider.entity.qywx;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created  By zhoux 2020/04/07
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QywxVirtualFsUserEntity implements Serializable {

    private String id;

    // 纷享ea
    private String ea;

    private Integer userId;

    // 企业微信corpId
    private String corpId;

    // 企业微信userId
    private String qyUserId;

    // 创建时间
    private Date createTime;

    // 旧虚拟身份
    private Integer oldUserId;

    // crm绑定时间
    private Date crmBindTime;
}
