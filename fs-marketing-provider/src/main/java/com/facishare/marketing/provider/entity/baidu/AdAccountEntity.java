package com.facishare.marketing.provider.entity.baidu;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/11/26.
 */
@Data
public class AdAccountEntity implements Serializable {
    private String id;
    private String ea;
    private String username;
    private String password;
    private String accessKey;
    private String secretKey;
    private String token;
    private String refreshToken;
    private String source; //账户类型
    private String bceUserId; //bce用户id
    private Integer status;
    private Integer userStat;
    private Long accountId;
    private Double balance;       //推广共享包金额
    private Double pcBalance;     //基准资金包金额
    private Double mobileBalance; //无线优惠资金包金额
    private Double cost;
    private Double budget;
    private Integer budgetType;
    private Date createTime;
    private Date updateTime;
    private Long authAccountId; //授权的账户id 获得的token对于其下的广告主账户可共用
    private Long qqAccountId; // 授权的推广帐号对应的 QQ 号
    private String wechatAccountId; // 授权的推广帐号对应的微信帐号 id
    // 这是授权用户的用户名, 上面username是用户下面账号的账号名
    private String authUserName;
    private Integer type; // 账户角色类型 对应AccountRoleEnum枚举值
}
