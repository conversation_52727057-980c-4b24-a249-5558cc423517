package com.facishare.marketing.provider.entity.mail;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2020/7/2.
 */
@Data
public class MailAddressListEntity implements Serializable{
    private String id;             //主键
    private String ea;             //公司账号
    private String address;        //别称地址, 使用该别称地址进行调用, 格式为**************************
    private String name;           //地址列表名称，默认和id一样
    private String description;           //地址描述
    private int memberCount;       //地址列表包含的地址数量
    private Date createTime;       //创建时间
    private Date updateTime;       //更新时间
}

