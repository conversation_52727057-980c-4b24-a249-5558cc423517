package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.api.arg.advertiser.BaiduFeedListArg;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.bo.advertise.BaiduAdGroupFeedBO;
import com.facishare.marketing.provider.entity.baidu.BaiduAdGroupFeedEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


public interface BaiduAdGroupFeedDAO {

    @Insert("<script>"
            + "INSERT INTO baidu_ad_group_feed(\"id\", \"ea\", \"ad_account_id\", \"marketing_event_id\", \"ad_group_feed_id\", \"campaign_feed_id\", \"ad_group_feed_name\", \"pause\", \"add_time\", \"modify_time\", \"status\", \"create_time\", \"update_time\") VALUES"
            + "  <foreach collection='baiduAdGroupFeedEntityList' item='item' separator=','>"
            + "   (#{item.id}, #{item.ea}, #{item.adAccountId}, #{item.marketingEventId}, #{item.adgroupFeedId}, #{item.campaignFeedId}, #{item.adgroupFeedName}, #{item.pause}, #{item.addTime}, #{item.modifyTime}, #{item.status}, now(), now())"
            + "  </foreach>"
            + "ON CONFLICT DO NOTHING;"
            + "</script>")
    int batchInsert(@Param("baiduAdGroupFeedEntityList") List<BaiduAdGroupFeedEntity> baiduAdGroupFeedEntityList);

    @Select("<script>"
            + "SELECT * FROM baidu_ad_group_feed WHERE ea = #{ea} AND ad_account_id = #{adAccountId} AND ad_group_feed_id = ANY(ARRAY\n"
            + "<foreach collection = 'adGroupFeedIdList' item = 'item' open = '[' separator = ',' close = ']'>"
            + "#{item}"
            + "</foreach>"
            + " )"
            + "</script>")
    List<BaiduAdGroupFeedEntity> getByAdGroupFeedIdList(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("adGroupFeedIdList") List<Long> adGroupFeedIdList);

    @Update("<script>update baidu_ad_group_feed as c SET ad_group_feed_name = tmp.adgroupFeedName, pause = tmp.pause, add_time = tmp.addTime,\n"
            + "modify_time = tmp.modifyTime, status = tmp.status, update_time = now() FROM (values"
            + "<foreach separator=',' collection='baiduAdGroupFeedEntityList' item='item'>"
            + "(#{item.id}, #{item.adgroupFeedName}, #{item.pause}::Boolean, #{item.addTime}::Timestamp, #{item.modifyTime}::Timestamp, #{item.status}::int)"
            + "</foreach>"
            + ") as tmp(id, adgroupFeedName, pause, addTime, modifyTime, status) where c.ea = #{ea} AND c.ad_account_id=#{adAccountId} and c.id = tmp.id"
            + "</script>")
    void batchUpdate(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("baiduAdGroupFeedEntityList") List<BaiduAdGroupFeedEntity> baiduAdGroupFeedEntityList);

    @Select("<script>"
            + "SELECT * FROM baidu_ad_group_feed WHERE ea = #{ea} AND ad_group_feed_name = ANY(ARRAY\n"
            +   "<foreach collection = 'nameList' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + "</script>")
    @FilterLog
    List<BaiduAdGroupFeedEntity> getByNameList(@Param("ea") String ea, @Param("nameList") List<String> nameList);

    @Update("<script>update baidu_ad_group_feed as c SET marketing_event_id = tmp.marketingEventId, update_time = now() FROM (values"
            + "<foreach separator=',' collection='adGroupFeedEntityList' item='item'>"
            +   "(#{item.id}, #{item.marketingEventId})"
            + "</foreach>"
            + ") as tmp(id, marketingEventId) WHERE c.ea = #{ea} AND c.ad_account_id = #{adAccountId} AND c.id = tmp.id"
            + "</script>")
    int batchUpdateMarketingEventById(@Param("ea")String ea, @Param("adAccountId") String adAccountId, @Param("adGroupFeedEntityList") List<BaiduAdGroupFeedEntity> adGroupFeedEntityList);


    @FilterLog
    @Select("<script>"
            + "select * from baidu_ad_group_feed  where ea = #{ea} and ad_account_id = #{adAccountId} "
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<BaiduAdGroupFeedEntity> scanByAdAccountId(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("lastId") String lastId, @Param("limit") int limit);


    @Select("SELECT count(*) FROM baidu_ad_group_feed WHERE ea=#{ea} AND ad_account_id=#{adAccountId}")
    int count(@Param("ea") String ea, @Param("adAccountId") String adAccountId);


    @Select("<script>"
            + "select adGroup.id id , adGroup.ad_group_feed_id adgroupFeedId, adGroup.status status, adGroup.ad_group_feed_name adgroupFeedName, adGroup.marketing_event_id marketingEventId, campaign.campaign_feed_id campaignFeedId, campaign.campaign_feed_name campaignFeedName,campaign.marketing_event_id campaignFeedMarketingEventId, project.project_feed_id projectFeedId, project.project_feed_name projectFeedName, project.marketing_event_id projectFeedMarketingEventId  from baidu_ad_group_feed adGroup "
            + " left join baidu_campaign_feed campaign on adGroup.ea = campaign.ea and adGroup.ad_account_id = campaign.ad_account_id and adGroup.campaign_feed_id = campaign.campaign_feed_id"
            + " left join baidu_project_feed project on campaign.ea = project.ea and project.project_feed_id = campaign.project_feed_id"
            + " where adGroup.ea = #{arg.ea} and adGroup.ad_account_id = #{arg.adAccountId} "
            + " <if test=\"arg.status != null \">\n"
            + "    and adGroup.status = #{arg.status} \n"
            + " </if>\n"
            + " <if test=\"arg.keywordType != null and arg.keywordType != '' and arg.keyword != null and arg.keyword != ''\">\n"
                + " <if test=\"arg.keywordType == 'adGroupFeed' \">\n"
                    + "AND adGroup.ad_group_feed_name like concat(concat('%',#{arg.keyword}),'%')\n"
                + " </if>\n"
                + " <if test=\"arg.keywordType == 'campaignFeed' \">\n"
                    + "AND campaign.campaign_feed_name like concat(concat('%',#{arg.keyword}),'%')\n"
                + " </if>\n"
                    + " <if test=\"arg.keywordType == 'projectFeed' \">\n"
                + "AND project.project_feed_name like concat(concat('%',#{arg.keyword}),'%')\n"
                + " </if>\n"
            + " </if>\n"
            + " order by adGroup.create_time desc, adGroup.id asc"
            + "</script>"
    )
    List<BaiduAdGroupFeedBO> pageAdGroupFeed(@Param("arg") BaiduFeedListArg arg, @Param("page")  Page page);

    @Select("<script>"
            + "SELECT marketing_event_id FROM baidu_ad_group_feed WHERE ea=#{ea} AND ad_account_id=#{adAccountId}"
            + " <if test=\"keyword != null and keyword != ''\">\n"
            + " AND ad_group_feed_name like concat(concat('%',#{keyword}),'%')\n"
            + " </if>\n"
            + "</script>")
    List<String> getAllMarketingEventIdList(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("keyword") String keyword);

    @Select("SELECT * FROM baidu_ad_group_feed WHERE ea = #{ea} AND id = #{id}")
    BaiduAdGroupFeedEntity getById(@Param("ea") String ea, @Param("id") String id);
}