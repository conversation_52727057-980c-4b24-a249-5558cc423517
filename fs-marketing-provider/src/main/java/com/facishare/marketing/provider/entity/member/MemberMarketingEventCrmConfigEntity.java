package com.facishare.marketing.provider.entity.member;

import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2020/12/25
 **/
@Data
public class MemberMarketingEventCrmConfigEntity implements Serializable {

    private String id;

    private String ea;

    private String marketingEventId;

    private FieldMappings memberToLeadFieldMappings;

    private String leadPoolId;

    private String crmRecordType;

    private Date createTime;

    private Date updateTime;

}
