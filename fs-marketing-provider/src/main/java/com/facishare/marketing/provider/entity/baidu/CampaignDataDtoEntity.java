package com.facishare.marketing.provider.entity.baidu;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/11/27.
 */
@Data
public class CampaignDataDtoEntity implements Serializable {
    private String id;
    private Long campaignId;
    private String campaignName;
    private Double budget;
    private Integer campaignType;
    private Integer status;
    private String marketingEventId;
    private Date actionDate;
    private Integer device;
    private Double cost;
    private Long pv;
    private Long click;
    private Integer leads;
    private Date refreshTime;
}
