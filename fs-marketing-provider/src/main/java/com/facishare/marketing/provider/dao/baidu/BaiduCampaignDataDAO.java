package com.facishare.marketing.provider.dao.baidu;

import com.facishare.marketing.provider.bo.advertise.AdCampaignDataStatisticsBO;
import com.facishare.marketing.provider.bo.advertise.AdConvertCostStatisticsBO;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignDataEntity;
import com.facishare.marketing.provider.entity.baidu.CampaignDataDtoEntity;
import com.facishare.marketing.provider.entity.baidu.CampaignDataOverviewDTOEntity;
import com.facishare.marketing.provider.entity.baidu.TrendGraphDataDTOEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * Created by ranluch on 2019/11/27.
 */
public interface BaiduCampaignDataDAO {
    @Insert("INSERT INTO baidu_campaign_data(id, ea, ad_account_id, campaign_id, \"action_date\") VALUES (#{id}, #{ea}, #{adAccountId}, #{campaignId}, #{actionDate}) ON CONFLICT DO NOTHING;")
    void insertCampaignDataIgnore(@Param("id") String id, @Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("campaignId") Long campaignId, @Param("actionDate") Date actionDate);

    @Insert("<script>"
            + "INSERT INTO baidu_campaign_data(\"id\", \"ea\", \"campaign_id\", \"action_date\", \"pv\", \"click\", \"cost\", \"leads\", \"create_time\", \"update_time\", \"ad_account_id\", \"ocpc_conversions\", \"deep_ocpc_conversions\") VALUES"
            +  "  <foreach collection='campaignEntityList' item='item' separator=','>"
            +  "   (#{item.id}, #{item.ea}, #{item.campaignId}, #{item.actionDate}, #{item.pv}, #{item.click}, #{item.cost}, #{item.leads}, now(), now(), #{item.adAccountId}, #{item.ocpcConversions}, #{item.deepOcpcConversions})"
            +  "  </foreach>"
            +  "ON CONFLICT DO NOTHING;"
            + "</script>")
    int batchInsert(@Param("campaignEntityList") List<BaiduCampaignDataEntity> campaignDataEntityList);

    @Update("<script>  " +
        "UPDATE baidu_campaign_data "
        + "SET "
        + "    <if test=\"pv != null\">\n"
        + "         \"pv\" = #{pv},\n"
        + "    </if>\n"
        + "    <if test=\"click != null\">\n"
        + "         \"click\" = #{click},\n"
        + "    </if>\n"
        + "    <if test=\"cost != null\">\n"
        + "         \"cost\" = #{cost},\n"
        + "    </if>\n"
        + "    <if test=\"leads != null\">\n"
        + "         \"leads\" = #{leads},\n"
        + "    </if>\n"
        + "    <if test=\"ocpcConversions != null\">\n"
        + "         \"ocpc_conversions\" = #{ocpcConversions},\n"
        + "    </if>\n"
        + "    <if test=\"deepOcpcConversions != null\">\n"
        + "         \"deep_ocpc_conversions\" = #{deepOcpcConversions},\n"
        + "    </if>\n"
        + "    <if test=\"adAccountId != null\">\n"
        + "         \"ad_account_id\" = #{adAccountId},\n"
        + "    </if>\n"
        + "update_time = now() "
        + "WHERE id = #{id}"
        + "</script>")
    int updateCampaignRefreshData(@Param("id") String id, @Param("pv") Long pv, @Param("click") Long click, @Param("cost") Double cost, @Param("leads") Integer leads,
                                  @Param("adAccountId") String adAccountId, @Param("ocpcConversions") Long ocpcConversions, @Param("deepOcpcConversions") Long deepOcpcConversions);

    @Select("<script>  "
        +"SELECT * FROM baidu_campaign_data \n"
        + "WHERE ea = #{ea} and campaign_id = #{campaignId} and action_date = #{actionDate}::DATE\n"
        + "</script>")
    BaiduCampaignDataEntity queryCampaignDatasByDate(@Param("ea") String ea, @Param("campaignId") Long campaignId, @Param("actionDate") Date actionDate);


    @Select("<script>  "
            +"SELECT * FROM baidu_campaign_data \n"
            + "WHERE ea = #{ea} and ad_account_id=#{adAccountId} and campaign_id = #{campaignId} and action_date = #{actionDate}::DATE\n"
            + "</script>")
    BaiduCampaignDataEntity queryCampaignDataByDate(@Param("ea") String ea,@Param("adAccountId") String adAccountId, @Param("campaignId") Long campaignId, @Param("actionDate") Date actionDate);



    @Select("<script> SELECT * FROM baidu_campaign_data WHERE id=#{id} </script>")
    BaiduCampaignDataEntity queryCampaignDataById(@Param("id") String id);

    @Select("<script>  "
        + "SELECT bc.id, bc.campaign_id as campaignId, bc.campaign_name as campaignName,\n"
        + "bc.budget, bc.campaign_type as campaignType, bc.\"status\", \n"
        + "bc.marketing_event_id as marketingEventId, bc.device, bc.refresh_time as refreshTime,\n"
        + " COALESCE(sum(bcd.pv),0) as pv , COALESCE(sum(bcd.click),0) as click,  COALESCE(sum(bcd.cost),0) as cost,  COALESCE(sum(bcd.leads),0) as leads\n"
        + "from baidu_campaign bc\n"
        + "left JOIN baidu_campaign_data bcd on bcd.campaign_id = bc.campaign_id  and bcd.ea = bc.ea \n"
        + "    <if test=\"startTime != null\">\n"
        + "         AND bcd.action_date &gt;= #{startTime}\n"
        + "    </if>\n"
        + "    <if test=\"endTime != null\">\n"
        + "         AND bcd.action_date &lt;= #{endTime}\n"
        + "    </if>\n"
        + "WHERE bc.ea = #{ea} \n"
        +	"<if test=\"nameKey != null\">"
        +	    "AND bc.campaign_name like concat(concat('%',#{nameKey}),'%')\n"
        +   "</if>"
        + "GROUP BY bc.id, bc.ea, bc.campaign_id, bcd.ea, bcd.campaign_id\n"
        + "ORDER BY pv desc, budget desc"
        + "</script>")
    List<CampaignDataDtoEntity> pageCampaignData(@Param("ea") String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("nameKey") String nameKey, @Param("page") Page page);

    @Select("<script>  "
        + "SELECT bc.id, bc.campaign_id as campaignId, bc.campaign_name as campaignName,\n"
        + "bc.budget, bc.campaign_type as campaignType, bc.\"status\", \n"
        + "bc.marketing_event_id as marketingEventId, bc.device, bc.refresh_time as refreshTime,\n"
        + "COALESCE(sum(bcd.pv),0) as pv , COALESCE(sum(bcd.click),0) as click,  COALESCE(sum(bcd.cost),0) as cost,  COALESCE(sum(bcd.leads),0) as leads\n"
        + "from baidu_campaign bc\n"
        + "left JOIN baidu_campaign_data bcd on bcd.campaign_id = bc.campaign_id  and bcd.ea = bc.ea\n"
        + "    <if test=\"startTime != null\">\n"
        + "         AND bcd.action_date &gt;= #{startTime}\n"
        + "    </if>\n"
        + "    <if test=\"endTime != null\">\n"
        + "         AND bcd.action_date &lt;= #{endTime}\n"
        + "    </if>\n"
        + "WHERE bc.id = ANY(ARRAY \n"
        +   "<foreach collection = 'ids' item = 'item' open = '[' separator = ',' close = ']'>"
        +       "#{item}"
        +   "</foreach>"
        + " )"
        + "GROUP BY bc.id\n"
        + "ORDER BY bc.marketing_event_id, bc.campaign_name"
        + "</script>")
    List<CampaignDataDtoEntity> queryCampaignData(@Param("ids") List<String> ids, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("<script>  "
        + "SELECT sum(bcd.pv) as pv, sum(bcd.click) as click, sum(bcd.cost) as cost\n"
        + "FROM baidu_campaign_data bcd \n"
        + "JOIN baidu_campaign bc on bc.campaign_id = bcd.campaign_id and bcd.ea = bc.ea\n"
        + "WHERE bcd.ea=#{ea} "
        + "    <if test=\"adAccountId != null\">\n"
        + "         AND bc.ad_account_id=#{adAccountId}\n"
        + "    </if>\n"
        + "    <if test=\"startTime != null\">\n"
        + "         AND bcd.action_date &gt;= #{startTime}\n"
        + "    </if>\n"
        + "    <if test=\"endTime != null\">\n"
        + "         AND bcd.action_date &lt;= #{endTime}\n"
        + "    </if>\n"
        + " <if test=\"marketingEventId != null\">\n"
        + "     and bc.marketing_event_id = #{marketingEventId} \n"
        + " </if>\n"
        + " <if test=\"keyword != null and keyword != ''\">\n"
        + " AND bc.campaign_name like concat(concat('%',#{keyword}),'%')\n"
        + " </if>\n"
        + "</script>")
    CampaignDataOverviewDTOEntity queryCampaignDataOverview(@Param("ea") String ea, @Param("adAccountId") String adAccountId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("marketingEventId") String marketingEventId,  @Param("keyword") String keyword);

    @Select("<script>  "
        + "SELECT bcd.action_date as actionDate, bcd.pv, bcd.click, bcd.cost\n"
        + "FROM baidu_campaign_data bcd \n"
        + "JOIN baidu_campaign bc on bc.campaign_id = bcd.campaign_id and bcd.ea = bc.ea\n"
        + "WHERE bc.ea=#{ea} and bc.marketing_event_id = #{marketingEventId} and bc.source = #{source}"
        + "    <if test=\"startTime != null\">\n"
        + "         AND bcd.action_date &gt;= #{startTime}\n"
        + "    </if>\n"
        + "    <if test=\"endTime != null\">\n"
        + "         AND bcd.action_date &lt;= #{endTime}\n"
        + "    </if>\n"
        + "ORDER BY bcd.action_date"
        + "</script>")
    List<TrendGraphDataDTOEntity> queryTrendGraphData(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("source") String source);

    @Select("<script>"
            + "SELECT bc.campaign_name as campaignName\n"
            + "from baidu_campaign bc\n"
            + "left JOIN baidu_campaign_data bcd on bcd.campaign_id = bc.campaign_id  and bcd.ea = bc.ea\n"
            + "    <if test=\"startTime != null\">\n"
            + "         AND bcd.action_date &gt;= #{startTime}\n"
            + "    </if>\n"
            + "    <if test=\"endTime != null\">\n"
            + "         AND bcd.action_date &lt;= #{endTime}\n"
            + "    </if>\n"
            + "WHERE bc.ea =#{ea} \n"
            + "GROUP BY bc.id\n"
            + "ORDER BY bc.marketing_event_id, bc.campaign_name"
            + "</script>")
    List<String> getAllCampaignDataByEa(@Param("ea")String ea, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select( " select * from (select sum(pv) totalPv, sum(cost) totalCost, sum(click) totalClick, ad_account_id from baidu_campaign_data "
            + " where ea = #{ea} and action_date >= #{beginTime} and action_date <= #{endTime} group by ad_account_id) "
            + " as a "
            + " full outer join "
            + " (select sum(pv) relativeTotalPv, sum(cost) relativeTotalCost, sum(click) relativeTotalClick, ad_account_id as relativeAdAccountId from baidu_campaign_data "
            + " where ea = #{ea} and action_date >= #{relativeBeginTime} and action_date <= #{relativeEndTime} group by ad_account_id) "
            + " as b on ( a.ad_account_id = b.relativeAdAccountId) "
    )
    List<AdCampaignDataStatisticsBO> statisticsCampaignData(@Param("ea")String ea, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                                            @Param("relativeBeginTime") Date relativeBeginTime, @Param("relativeEndTime") Date relativeEndTime);

    @Select( "select sum(cost) totalCost, action_date actionDate from baidu_campaign_data "
            + " where ea = #{ea} and action_date >= #{beginTime} and action_date <= #{endTime} group by action_date "
    )
    List<AdCampaignDataStatisticsBO> getTotalCostGroupByActionData(@Param("ea")String ea, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);


    @Select( "<script>" +
             " select * from (select sum(pv) totalPv, sum(cost) totalCost, sum(click) totalClick, campaign_id campaignId from baidu_campaign_data "
            + " where ea = #{ea} and action_date >= #{beginTime} and action_date  <![CDATA[<=]]> #{endTime} "
            + " AND campaign_id = ANY(ARRAY "
            +   "<foreach collection = 'campaignIdList' index='index' item = 'item' open = '[' separator = ',' close = ']'>"
            +       "#{item}"
            +   "</foreach>"
            + " )"
            + " group by campaign_id ) "
            + " as a "
            + " left join "
            + " (select sum(pv) relativeTotalPv, sum(cost) relativeTotalCost, sum(click) relativeTotalClick, campaign_id relativeCampaignId from baidu_campaign_data "
            + " where ea = #{ea} and action_date >= #{relativeBeginTime} and action_date <![CDATA[<=]]> #{relativeEndTime} "
            + " AND campaign_id = ANY(ARRAY "
            +   "<foreach collection = 'campaignIdList' index='index' item = 'item' open = '[' separator = ',' close = ']'>"
            +        "#{item}"
            +   "</foreach>"
            + " )"
            + " group by campaign_id) "
            + " as b on ( a.campaignId = b.relativeCampaignId) "
            + " </script>"
    )
    List<AdCampaignDataStatisticsBO> statisticsCampaignDataGroupByCampaign(@Param("ea")String ea, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                                            @Param("relativeBeginTime") Date relativeBeginTime, @Param("relativeEndTime") Date relativeEndTime,
                                                                           @Param("campaignIdList") List<Long> campaignIdList);


    @Select("<script>"
            + "select * from baidu_campaign_data  where ea = #{ea} "
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "    and id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<BaiduCampaignDataEntity> scanById( @Param("ea") String ea, @Param("lastId") String lastId, @Param("limit") int limit);

    @Select( "select count(*) from baidu_campaign_data  where ea = #{ea}")
    int countByEa( @Param("ea") String ea);

    @Select("<script>  "
            +"SELECT sum(click) FROM baidu_campaign_data \n"
            + "WHERE ea = #{ea} and ad_account_id=#{adAccountId} and action_date = #{actionDate}::DATE\n"
            + "</script>")
    Long sumClickByActionDate(@Param("ea") String ea,@Param("adAccountId") String adAccountId, @Param("actionDate") Date date);

    @Select("<script>  "
            + "select action_date, sum(COALESCE(cost,0)) / sum(ocpc_conversions) as convertCost from baidu_campaign_data where ea = #{ea} and ocpc_conversions <![CDATA[ > ]]>  0 "
            + "and action_date <![CDATA[ >= ]]>  #{beginTime} and action_date <![CDATA[ <= ]]> #{endTime}"
            + "<if test='campaignIdList != null and !campaignIdList.isEmpty()'>"
            + " AND campaign_id = ANY(ARRAY "
                + "<foreach open='[' close=']' separator=',' item='ite' collection='campaignIdList'>"
                +   "#{ite}"
                + "</foreach> )"
            + "</if>"
            + " group by action_date"
            + "</script>")
    List<AdConvertCostStatisticsBO> statisticsConvertCostData(@Param("ea") String ea, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("campaignIdList") List<Long> campaignIdList);
}
