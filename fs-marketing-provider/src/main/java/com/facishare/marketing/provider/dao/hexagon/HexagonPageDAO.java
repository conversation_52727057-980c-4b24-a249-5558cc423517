package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.*;

public interface HexagonPageDAO {

    @Select("select * from hexagon_page where id = #{id} and status != 4")
    @FilterLog
    HexagonPageEntity getById(@Param("id") String id);

    @Select("select * from hexagon_page where form_id = #{formId} and status != 4 and is_homepage = 1")
    @FilterLog
    HexagonPageEntity getByFormId(@Param("formId") String formId);

    @Select("select * from hexagon_page where id = #{id}")
    HexagonPageEntity getInclueDeletedById(@Param("id") String id);

    @Select("select * from hexagon_page where ea = #{ea} and status != 4")
    List<HexagonPageEntity> getByEa(@Param("ea") String ea);

    @Insert("INSERT INTO hexagon_page"
            + "(id, ea, hexagon_site_id, name, share_title, share_desc, share_pic_h5_apath, share_pic_mp_apath, content, is_homepage, form_id, status, create_by, update_by, create_time, update_time)"
            + " VALUES"
            + " (#{obj.id}, #{obj.ea}, #{obj.hexagonSiteId}, #{obj.name}, #{obj.shareTitle}, #{obj.shareDesc}, #{obj.sharePicH5Apath}, #{obj.sharePicMpApath}, #{obj.content}, #{obj.isHomepage}, #{obj.formId}, 1, #{obj.createBy}, #{obj.updateBy}, now(), now())")
    int insert(@Param("obj") HexagonPageEntity hexagonPageEntity);

    @Update("<script>"
            + "UPDATE hexagon_page"
            + "    <set>"
            + "         <if test=\"name != null\">\n"
            + "             name = #{name},\n"
            + "          </if>\n"
            + "         <if test=\"shareTitle != null\">\n"
            + "             share_title = #{shareTitle},\n"
            + "         </if>\n"
            + "         <if test=\"shareDesc != null\">\n"
            + "             share_desc = #{shareDesc},\n"
            + "         </if>\n"
            + "         <if test=\"sharePicH5Apath != null\">\n"
            + "             share_pic_h5_apath = #{sharePicH5Apath},\n"
            + "         </if>\n"
            + "         <if test=\"sharePicMpApath != null\">\n"
            + "             share_pic_mp_apath = #{sharePicMpApath},\n"
            + "         </if>\n"
            + "         <if test=\"content != null\">\n"
            + "             content = #{content},\n"
            + "         </if>\n"
            + "         <if test=\"status != null\">\n"
            + "             status = #{status},\n"
            + "          </if>\n"
            + "         <if test=\"isHomepage != null\">\n"
            + "             is_homepage = #{isHomepage},\n"
            + "          </if>\n"
            + "         form_id = #{formId},\n"
            + "         <if test=\"updateBy != null\">\n"
            + "             update_by = #{updateBy},\n"
            + "          </if>\n"
            + "         update_time = now()\n"
            + "    </set>"
            + "    WHERE id = #{id}"
            + "</script>")
    int update(HexagonPageEntity hexagonPageEntity);

    @Update("<script>"
        + " UPDATE hexagon_page"
        + "    <set>"
        + "      share_pic_h5_apath = #{sharePicH5Apath},\n"
        + "      share_pic_mp_apath = #{sharePicMpApath},\n"
        + "      update_time = now()\n"
        + "    </set>"
        + "    WHERE id = #{id}"
        + "</script>")
    int updatePageSharePic(HexagonPageEntity hexagonPageEntity);

    @Update("update hexagon_page set status = 4 where id = #{id}")
    int deleteById(@Param("id") String id);

    @Update("<script>"
        + "update hexagon_page set status = 4 where id IN "
        +   "<foreach collection = 'ids' item = 'id' open = '(' separator = ',' close = ')'>"
        +       "#{id}"
        +   "</foreach>"
        + "</script>")
    int deleteByIds(@Param("ids") Collection<String> ids);

    @Select("<script>"
            + "select * from hexagon_page where hexagon_site_id = #{hexagonSiteId} and status != 4"
            + " ORDER BY is_homepage ASC NULLS LAST, create_time ASC"
            + "</script>")
    List<HexagonPageEntity> getByHexagonSiteId(@Param("hexagonSiteId") String hexagonSiteId);

    @Select("<script>"
            + "select * from hexagon_page where status != 4 AND hexagon_site_id IN\n"
            +   "<foreach collection = 'hexagonSiteIdList' item = 'id' open = '(' separator = ',' close = ')'>"
            +       "#{id}"
            +   "</foreach>"
            + " ORDER BY is_homepage ASC NULLS LAST, create_time ASC"
            + "</script>")
    List<HexagonPageEntity> getByHexagonSiteIdList(@Param("hexagonSiteIdList") List<String> hexagonSiteIdList);

    @Update("update hexagon_page set status = 4 where hexagon_site_id = #{hexagonSiteId}")
    int deleteBySiteId(@Param("hexagonSiteId") String hexagonSiteId);

    @Select("select * from hexagon_page where is_homepage = 1 and status != 4 and hexagon_site_id = #{hexagonSiteId}")
    HexagonPageEntity getHomePage(@Param("hexagonSiteId") String hexagonSiteId);

    @Select("<script>"
            + "SELECT * FROM hexagon_page WHERE is_homepage = 1 AND status != 4 AND ea=#{ea} AND hexagon_site_id IN\n"
            +   "<foreach collection = 'hexagonSiteIds' item = 'id' open = '(' separator = ',' close = ')'>"
            +       "#{id}"
            +   "</foreach>"
            + "</script>")
    List<HexagonPageEntity> getHomePageByIds(@Param("ea")String ea,  @Param("hexagonSiteIds") List<String> hexagonSiteIds);

    @Select("<script>"
            + "select * from hexagon_page where status != 4 AND is_homepage = 1 AND hexagon_site_id IN\n"
            +   "<foreach collection = 'hexagonSiteIdList' item = 'id' open = '(' separator = ',' close = ')'>"
            +       "#{id}"
            +   "</foreach>"
            + "</script>")
    List<HexagonPageEntity> listHomePageBySiteIds(@Param("hexagonSiteIdList") List<String> hexagonSiteId);

    @Update("update hexagon_page set is_homepage = 1 where id = #{id}")
    int setHomePage(@Param("id") String id);

    @Update("update hexagon_page set is_homepage = 2 where hexagon_site_id = #{hexagonSiteId}")
    int removeHomePage(@Param("hexagonSiteId") String hexagonSiteId);

    @Select(" SELECT A.* FROM hexagon_site AS A JOIN hexagon_page AS B ON A.id = B.hexagon_site_id WHERE B.id = #{pageId} LIMIT 1 ")
    HexagonSiteEntity getHexagonSiteByHexagonPageId(@Param("pageId") String pageId);


    @Select("<script>"
        + "SELECT B.id,A.name FROM hexagon_site AS A JOIN hexagon_page AS B ON A.id = B.hexagon_site_id WHERE B.id IN"
        + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        + "#{item}"
        + "</foreach>"
        + "</script>")
    List<HexagonSiteEntity> getHexagonSiteNameByHexagonPageId(@Param("ids") List<String> ids);


    @Update("<script>"
            + "UPDATE hexagon_page"
            + "<trim prefix=\"set\" suffixOverrides=\",\">"
            + " <trim prefix=\"name =case\" suffix=\"end,\">"
            + " 		<foreach collection=\"pageEntityList\" item=\"i\" index=\"index\">"
            + "<if test=\"i.name!=null\">"
            + "when id=#{i.id} then #{i.name}"
            + "          </if>\n"
            + "</foreach>"
            + " </trim>"
            + " <trim prefix=\"content =case\" suffix=\"end,\">"
            + " 		<foreach collection=\"pageEntityList\" item=\"i\" index=\"index\">"
            + "<if test=\"i.content!=null\">"
            + "when id=#{i.id} then #{i.content}"
            + "          </if>\n"
            + "</foreach>"
            + " </trim>"
            + "     </trim>"
            + "    where\n" +
            "    <foreach collection=\"pageEntityList\" separator=\"or\" item=\"i\" index=\"index\" >\n" +
            "              id=#{i.id}\n" +
            "          </foreach>"
            + "</script>")
    int updatePageActionId(@Param("pageEntityList") List<HexagonPageEntity> pageEntityList);

    @Select(" SELECT * FROM hexagon_page WHERE status = 1 AND hexagon_site_id = #{siteId} AND name = #{name}")
    List<HexagonPageEntity> getHexagonPageBySiteIdAndPageName(@Param("siteId") String siteId, @Param("name") String name);
    
    @Update("UPDATE hexagon_page SET content=#{content} WHERE id = #{pageId}")
    int updateContent(@Param("pageId") String pageId, @Param("content") String content);

    @Update("UPDATE hexagon_page SET name=#{shareTitle}, share_title = #{shareTitle}, share_desc = #{shareDesc}, share_pic_h5_apath = #{picPath}, share_pic_mp_apath = #{picPath} WHERE id = #{pageId}")
    int updateShareInfo(@Param("pageId") String pageId, @Param("shareTitle") String shareTitle, @Param("shareDesc") String shareDesc, @Param("picPath") String picPath);

    @Select("SELECT content FROM hexagon_page WHERE id = #{pageId}")
    String getContentById(@Param("pageId") String pageId);

    @Select(" SELECT * FROM hexagon_page WHERE form_id = #{formId}")
    List<HexagonPageEntity> getPageByFormId(@Param("formId") String formId);

    @Select("SELECT id FROM hexagon_page WHERE hexagon_site_id=#{siteId}")
    List<String> getPageIdsBySiteId(@Param("siteId")String siteId);

    @Select(" SELECT * FROM hexagon_page WHERE hexagon_site_id=#{siteId} and form_id = #{formId}")
    List<HexagonPageEntity> getPageByFormIdAndSiteId(@Param("siteId")String siteId,@Param("formId") String formId);


    @Delete("delete from hexagon_page WHERE id=#{id}")
    int deleteHexagonPageById(@Param("id") String id);

    @Update("UPDATE hexagon_page SET content=#{content},update_time = now() WHERE hexagon_site_id = #{siteId} and is_homepage = 1")
    int updateContentBySiteId(@Param("siteId") String siteId, @Param("content") String content);
}