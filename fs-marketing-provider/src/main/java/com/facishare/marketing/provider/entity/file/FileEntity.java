package com.facishare.marketing.provider.entity.file;

import com.facishare.marketing.common.typehandlers.value.TagNameList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FileEntity implements Serializable{
    private String id;
    private String ea;
    private Integer createBy;
    private String fileName;
    private Long fileSize;
    private String filePath;
    private Integer type;
    private String ext;
    private Integer status;
    private TagNameList tagNames;
    private Date createTime;
    private Date updateTime;
}
