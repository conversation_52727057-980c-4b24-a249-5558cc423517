package com.facishare.marketing.provider.entity.sms.mw;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/2/18.
 */
@Data
@Entity
public class MwSmsSignatureEntity implements Serializable {
    private String id;
    private String ea;
    private String svrType;
    private String svrName;
    private Integer templateCheckStatus;
    private String remark;
    private String reply;
    private Integer status;
    private Integer creatorUserId;
    private String creatorName;
    private Date createTime;
    private Date updateTime;
    private String certificate;
    private String accountId;          //生产帐号
    private String marketingAccountId; //营销帐号
    private Integer industry;          //行业 0：普通行业  1：敏感行业
}
