package com.facishare.marketing.provider.dao.enterpriseFeed;

import com.facishare.marketing.provider.entity.enterpriseFeed.EnterpriseFeedCommentEntity;
import com.facishare.marketing.provider.entity.enterpriseFeed.EnterpriseFeedEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Created by ranluch on 2019/1/15
 */
public interface EnterpriseFeedDao {
    @Select("SELECT * FROM enterprise_feed WHERE status = 0 and ea = #{ea} ORDER BY create_time DESC")
    List<EnterpriseFeedEntity> queryEnterpriseFeedResult(@Param("ea") String ea, @Param("page") Page page);

    @Insert("INSERT INTO enterprise_feed(\n" + "\t\"id\",\n" + "\t\"ea\",\n" + "\t\"object_id\",\n" + "\t\"object_type\",\n" + "\t\"recommendation\",\n" + "\t\"user_id\",\n" + "\t\"status\",\n"
        + "\t\"create_time\",\n" + "\t\"update_time\"\n" + ") VALUES (\n" + "\t#{obj.id},\n" + "\t#{obj.ea},\n" + "\t#{obj.objectId},\n" + "\t#{obj.objectType},\n" + "\t#{obj.recommendation},\n"
        + "\t#{obj.userId},\n" + "\t#{obj.status},\n" + "\tnow(),\n" + "\tnow())")
    int addEnterpriseFeed(@Param("obj") EnterpriseFeedEntity enterpriseFeedEntity);

    @Select("SELECT * FROM enterprise_feed_comment WHERE status = 0 and enterprise_feed_id = #{enterpriseFeedId} ORDER BY create_time DESC")
    List<EnterpriseFeedCommentEntity> queryEnterpriseFeedCommentResult(@Param("enterpriseFeedId") String enterpriseFeedId, @Param("page") Page page);

    @Delete("DELETE FROM enterprise_feed WHERE id = #{id}")
    int deleteEnterpriseFeed(@Param("id") String id);

    @Update("UPDATE enterprise_feed SET status = 1 WHERE id = #{id}")
    int updateEnterpriseFeed(@Param("id") String id);

    @Delete("DELETE FROM enterprise_feed_comment WHERE id = #{id}")
    int deleteEnterpriseCommentFeed(@Param("id") String id);

    @Update("UPDATE enterprise_feed_comment SET status = 1 WHERE id = #{id}")
    int updateEnterpriseCommentFeed(@Param("id") String id);

    @Select("select count(1) from enterprise_feed_comment where status = 0 and enterprise_feed_id = #{feedId}")
    int countEnterpriseCommentFeedByFeedId(@Param("feedId") String feedId);

    @Select("select company_name from enterprise_default_card where ea = #{ea}")
    String getCompanyName(@Param("ea") String ea);
}
