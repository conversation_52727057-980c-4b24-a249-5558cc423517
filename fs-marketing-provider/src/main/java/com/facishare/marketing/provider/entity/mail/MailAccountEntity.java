package com.facishare.marketing.provider.entity.mail;

import com.facishare.marketing.common.enums.mail.MailApiUserTypeEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created by zhengh on 2020/6/2.
 */
@Data
public class MailAccountEntity implements Serializable{
    private String id;             //主键
    private String ea;             //公司账号
    private String apiUser;        //用户绑定的邮箱账号
    private String apiKey;         //用户绑定邮箱key，加密数据
    /**
     * {@link MailApiUserTypeEnum}
     */
    private Integer type;
    private String domain;
    private Integer creator; // 创建人userId
    private Date createTime;       //创建时间
    private Date updateTime;       //更新时间
}
