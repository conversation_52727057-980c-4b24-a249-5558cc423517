package com.facishare.marketing.provider.entity.sms.mw;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by ranluch on 2019/2/18.
 */
@Data
@Entity
public class MwSendDetailEntity implements Serializable {
    private String ea;
    private String id;
    private String sendId;
    private String phone;
    private String content;
    private Integer fee;
    private Integer status;
    private Integer errStatus;
    private String errCode;
    private String errDesc;
    private String exdata;
    private Date createTime;
    private Date updateTime;
    private List<String> params;

    private String errCodeDesc; // 错误码描述
    private Integer failCount;

    private String templateId;

    private Integer channelType;
    private String variables;// 变量
}
