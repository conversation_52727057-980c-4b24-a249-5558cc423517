package com.facishare.marketing.provider.dao.officialWebsite;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.result.OfficialWebsiteWxQrCodeResult;
import com.facishare.marketing.provider.entity.officialWebsite.WxOfficialAccountsQrCodeEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface WxOfficialAccountsQrCodeDAO {
    @Insert("INSERT INTO official_website_wx_official_accounts_qr_code (\n"
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"qr_code_name\",\n"
            + "        \"wx_app_id\",\n"
            + "        \"platform_app_id\",\n"
            + "        \"creator\",\n"
            + "        \"tag_name\",\n"
            + "        \"file_url\",\n"
            + "        \"marketing_event_id\",\n"
            + "        \"marketing_event_name\"\n,"
            + "        \"response_msg\"\n,"
            + "        \"scan_count\"\n,"
            + "        \"show_url\"\n,"
            + "        \"subscribe_count\"\n,"
            + "        \"welcome_msg\"\n,"
            + "        \"is_bind_website\"\n,"
            + "        \"is_bind_website_login\"\n,"
            + "        \"is_temp\"\n,"
            + "        \"do_bind_fs_user_id\"\n,"
            + "        \"update_bind_status_time\"\n,"
            + "        \"do_bind_fs_user_name\"\n,"
            + "        \"scene_id\"\n,"
            + "        \"create_time\",\n"
            + "        \"update_time\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.ea},\n"
            + "        #{obj.qrCodeName},\n"
            + "        #{obj.wxAppId},\n"
            + "        #{obj.platformAppId},\n"
            + "        #{obj.creator},\n"
            + "        #{obj.tagNames},\n"
            + "        #{obj.fileUrl},\n"
            + "        #{obj.marketingEventId},\n"
            + "        #{obj.marketingEventName},\n"
            + "        #{obj.responseMsg},\n"
            + "        #{obj.scanCount},\n"
            + "        #{obj.showUrl},\n"
            + "        #{obj.subscribeCount},\n"
            + "        #{obj.welcomeMsg},\n"
            + "        #{obj.isBindWebsite},\n"
            + "        #{obj.isBindWebsiteLogin},\n"
            + "        #{obj.isTemp},\n"
            + "        #{obj.doBindFsUserId},\n"
            + "        #{obj.updateBindStatusTime},\n"
            + "        #{obj.doBindFsUserName},\n"
            + "        #{obj.sceneId},\n"
            + "        now(),\n"
            + "        now()\n"
            + "        ) ON CONFLICT DO NOTHING;")
    int insertWxOfficialAccountsQrCode(@Param("obj") WxOfficialAccountsQrCodeEntity entity);

    @Select("select * from official_website_wx_official_accounts_qr_code where id=#{id}")
    WxOfficialAccountsQrCodeEntity queryQrCodeById(@Param("id") String id);

    @Select("<script>"
            + "select * from official_website_wx_official_accounts_qr_code where id in "
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<WxOfficialAccountsQrCodeEntity> queryQrCodeByIds(@Param("ids") List<String> ids);

    @Select("select * from official_website_wx_official_accounts_qr_code where ea=#{ea} and is_temp=1 ")
    List<WxOfficialAccountsQrCodeEntity> queryTempQrCodeByEa(@Param("ea") String ea);

    @Update("update official_website_wx_official_accounts_qr_code set is_bind_website = 1 where id=#{id}")
    int bindOfficialWebsiteById(@Param("id") String id);

    @Update("update official_website_wx_official_accounts_qr_code set is_bind_website = 0 where id=#{id}")
    int unBindOfficialWebsiteById(@Param("id") String id);

    @Select("select * from official_website_wx_official_accounts_qr_code where ea=#{ea} and is_bind_website=1 order by update_bind_status_time DESC ")
    List<WxOfficialAccountsQrCodeEntity> queryQrCodeByEaAndBindStatus(@Param("ea") String ea);

    @Update("update official_website_wx_official_accounts_qr_code set scan_count = scan_count+1 where id=#{mainSceneId}")
    int increaseScanCount(@Param("ea")String ea, @Param("mainSceneId") String mainSceneId);

    @Update("update official_website_wx_official_accounts_qr_code set subscribe_count = subscribe_count+1 where id=#{mainSceneId}")
    int increaseSubscribCount(@Param("ea")String ea, @Param("mainSceneId") String mainSceneId);

    @Update("update official_website_wx_official_accounts_qr_code set is_bind_website_login = 0 where ea=#{ea}")
    void unBindOfficialWebsiteLoginById(@Param("ea") String ea);

    @Select("select * from official_website_wx_official_accounts_login_setting where ea=#{ea}")
    JSONObject getSettingByEa(@Param("ea") String ea);

    @Insert("INSERT INTO official_website_wx_official_accounts_login_setting\n" +
            "(id, ea, qr_code_id, preview_setting, create_by, create_time, update_by, update_time)\n" +
            "VALUES(#{id}, #{ea}, #{qrCodeId}, #{previewSetting}, #{fsUserId}, now(), #{fsUserId}, now());")
    void saveSetting(@Param("id") String id, @Param("ea") String ea, @Param("qrCodeId") String qrCodeId, @Param("previewSetting") String previewSetting, @Param("fsUserId") Integer fsUserId);

    @Update("UPDATE official_website_wx_official_accounts_login_setting\n" +
            "SET ea=#{ea}, qr_code_id=#{qrCodeId}, preview_setting=#{previewSetting}, update_by=#{fsUserId}, update_time=now()\n" +
            "WHERE id=#{id};")
    void updateSettingById(@Param("id") String id, @Param("ea") String ea, @Param("qrCodeId") String qrCodeId, @Param("previewSetting") String previewSetting, @Param("fsUserId") Integer fsUserId);

    @Update("update official_website_wx_official_accounts_login_setting set qr_code_id = null where id=#{id}")
    int unBindSettingById(@Param("id") String id);
}
