package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/1/11 10:45 上午
 */
public interface HexagonSiteObjectDAO {
    @Select("SELECT * FROM hexagon_site_object WHERE ea = #{ea} AND object_id = #{objectId} AND object_type = #{objectType} AND status = 0 ORDER BY create_time DESC LIMIT 1")
    HexagonSiteObjectEntity getObjectBindingHexagonSite(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM hexagon_site_object WHERE ea = #{ea} AND object_id = #{objectId} AND object_type = #{objectType}")
    List<HexagonSiteObjectEntity> getObjectBindingHexagonSiteList(@Param("ea") String ea, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Select("SELECT * FROM hexagon_site_object WHERE ea = #{ea} AND site_id = #{siteId} AND status = 0")
    List<HexagonSiteObjectEntity> getHexagonSiteObjectListBySiteId(@Param("ea") String ea, @Param("siteId") String siteId);


    @Insert("INSERT INTO hexagon_site_object(id, ea, site_id, object_id, object_type, create_by, create_time, update_by, update_time, form_style_type, form_button_name, button_style, status) VALUES (#{obj.id}, #{obj.ea}, #{obj.siteId}, #{obj.objectId}, #{obj.objectType}, #{obj.createBy}, now(), #{obj.updateBy}, now(), #{obj.formStyleType}, #{obj.formButtonName}, #{obj.buttonStyle, typeHandler=ButtonStyleTypeHandler}, #{obj.status});")
    int insertHexagonSiteObject(@Param("obj") HexagonSiteObjectEntity hexagonSiteObjectEntity);


    @Delete("DELETE FROM hexagon_site_object WHERE ea = #{ea} AND site_id = #{siteId} AND  object_id = #{objectId} AND object_type = #{objectType}")
    int deleteHexagonSiteObject(@Param("ea") String ea, @Param("siteId") String siteId, @Param("objectId") String objectId, @Param("objectType") Integer objectType);

    @Update("UPDATE hexagon_site_object SET status = #{status} WHERE id=#{hexagonSiteObjectId}")
    int updateHexagonSiteObjectStatus(@Param("hexagonSiteObjectId") String hexagonSiteObjectId, @Param("status") Integer status);

    @Select("<script>"
            + "SELECT * FROM hexagon_site_object WHERE ea = #{ea} "
            + "AND object_id IN "
            + " <foreach collection = 'objectIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + " AND object_type = #{objectType}"
            + "</script>")
    List<HexagonSiteObjectEntity> getByObjectIdList(@Param("ea") String ea, @Param("objectIdList") List<String> objectIdList,
                                                                  @Param("objectType") Integer objectType);

}
