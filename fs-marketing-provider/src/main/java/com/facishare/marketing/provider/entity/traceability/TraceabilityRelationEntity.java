package com.facishare.marketing.provider.entity.traceability;


import com.esotericsoftware.minlog.Log;
import com.facishare.marketing.api.arg.RelationConfig;
import com.facishare.marketing.common.util.HashUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.lang.reflect.Field;
import java.util.ArrayList;


/**
 * TraceabilityRelation 实体类
 */
@Data
@Slf4j
public class TraceabilityRelationEntity implements Serializable {
    private String id; // 主键
    private String ea; // 企业账号
    private String aggregateKey; // 聚合键
    private RelationConfig relationConfig; // RelationConfig JSONB 映射
    private LocalDateTime createTime; // 创建时间
    private LocalDateTime updateTime; // 更新时间





    public static String fixAggregateKey(RelationConfig relationConfig) {
        if (relationConfig == null) {
            return null;
        }

        List<String> hashParameters = new ArrayList<>();

        Field[] fields = relationConfig.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true); // 确保可以访问私有字段

                Object value = field.get(relationConfig);

                if (value == null || !isSupportedType(field.getType(), value)) {
                    continue;
                }
                hashParameters.add(field.getName() + ":" + value.toString());

            } catch (IllegalAccessException e) {
                log.warn("Failed to access field: " + field.getName(), e);
            }
        }
        if (hashParameters.isEmpty()) {
            return null;
        }

        return HashUtil.hash(hashParameters.toArray());
    }


    /**
     * 判断字段是否属于支持的类型（基本类型、包装类型和字符串，并过滤空字符串）
     */
    private static boolean isSupportedType(Class<?> fieldType, Object fieldValue) {
        if (fieldType.isPrimitive()) {
            return true;
        }
        if (fieldValue instanceof String) {
            String strValue = ((String) fieldValue).trim();
            return !strValue.isEmpty();
        }
        if (fieldValue instanceof Number || fieldValue instanceof Boolean || fieldValue instanceof Character) {
            return true;
        }
        return false;
    }




}