package com.facishare.marketing.provider.entity.qywx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 21:37 2020/2/11
 * @ModifyBy
 */
@Data
public class QywxMomentTaskEntity implements Serializable {
    private String id;
    private String ea;
    private String marketingActivityId;
    private String name;

    private String userIdList;

    private String currentUserId;

    private Integer momentType;
    //发送类型：实时发送，定时发送
    private Integer sendType;
    private Long sendTime;

    private String content;
    private String imageInfo;
    private int imageNum;
    private Long imageSize;

    private Integer linkType;
    private String linkPicPath;
    private String linkTitle;
    private String linkUrl;
    private String linkPicUrl;

    private String videoName;
    private String videoMediaId;
    private Long videoSize;

    private Long fixTime;

    private Integer createUserId;
    private Date createTime;
    private Date updateTime;

    private String jobId;
    private String momentId;
    //1 待发送 2 发送中 3 发送完成
    private Integer status;

    private String tagIdList; //企微标签列表

    private Long errcode;
    private String errmsg;

    private Integer sendEmployeeCount;

    private Integer sendCustomerCount;

    private Integer unsendCustomerCount;

}
