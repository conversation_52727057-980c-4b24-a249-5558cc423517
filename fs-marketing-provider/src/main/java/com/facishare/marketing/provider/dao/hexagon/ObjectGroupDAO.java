package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.provider.dto.ObjectRelationGroupDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonGroupDTO;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface ObjectGroupDAO {
    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND id=#{id}")
    ObjectGroupEntity getById(@Param("ea")String ea, @Param("id")String id);

    @Select("<script>"
            + "SELECT * FROM object_group WHERE ea=#{ea} AND id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>" +
            " order by create_time asc "
            + "</script>")
    List<ObjectGroupEntity> getByIdList(@Param("ea") String ea, @Param("idList") List<String> idList);

    @Select("<script>"
            + "SELECT parent_id FROM object_group WHERE ea=#{ea} and parent_id is not null AND id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>"
            + "</script>")
    List<String> getParentIdsByIdList(@Param("ea") String ea, @Param("idList") List<String> idList);

    @Select("SELECT COUNT(*) FROM object_group WHERE ea=#{ea} AND name=#{name} AND status=0 AND object_type=#{objectType}")
    int getTotalCountByName(@Param("ea")String ea, @Param("name")String name, @Param("objectType")Integer objectType);

    @Select("SELECT COUNT(*) FROM object_group WHERE ea=#{ea} AND status=0 AND object_type=#{contentType}")
    int getTotalCountByEa(@Param("ea")String ea, @Param("objectType")Integer objectType);

    @Select("SELECT COUNT(*) FROM object_group WHERE ea=#{ea} AND create_by=#{userId} AND status=0 AND object_type=#{contentType}")
    int getTotalCountByCreator(@Param("ea")String ea, @Param("userId")Integer userId, @Param("objectType")Integer objectType);

    @Insert("INSERT INTO object_group(id, ea, name, object_type, create_by, status, create_time, update_time, inner_visible, parent_id, seq_no, level) VALUES(#{entity.id}, "
            + "#{entity.ea}, #{entity.name}, #{entity.objectType}, #{entity.createBy}, #{entity.status}, now(), now(), #{entity.innerVisible}, #{entity.parentId}, #{entity.seqNo}, #{entity.level})")
    int insert(@Param("entity") ObjectGroupEntity entity);

    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND id=#{id}")
    ObjectGroupEntity queryById(@Param("ea")String ea, @Param("id")String id);

    @Select("SELECT * FROM object_group WHERE ea = #{ea} AND parent_id = #{parentId}")
    List<ObjectGroupEntity> queryByParentId(@Param("ea")String ea, @Param("parentId") String parentId);

    @Select("<script>" +
            " SELECT id FROM object_group WHERE ea = #{ea} and inner_visible = 3 AND parent_id in "
            + "<foreach collection = 'parentIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>" +
            "</script>")
    List<String> getNextFollowUpGroupIdsByParentIds(@Param("ea")String ea, @Param("parentIds") List<String> parentIds);

    @Select("<script>" +
            " SELECT id FROM object_group WHERE ea = #{ea} and outer_visible = 3 AND parent_id in "
            + "<foreach collection = 'parentIds' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>" +
            "</script>")
    List<String> getNextFollowUpGroupIdsByParentIds4Outer(@Param("ea")String ea, @Param("parentIds") List<String> parentIds);

    @Update("<script>" +
                "UPDATE object_group SET name=#{name}, " +
                "<if test='seqNo != null'> seq_no = #{seqNo}, </if>" +
                " update_time=now() " +
                " WHERE ea=#{ea} AND id=#{id}" +
            "</script>")
    int updateInfoById(@Param("ea")String ea, @Param("id")String id, @Param("name")String name, @Param("seqNo") Integer seqNo);

    @Update("UPDATE object_group SET status=#{status}, update_time=now() WHERE ea=#{ea} AND id=#{id}")
    int updateStatusById(@Param("ea")String ea, @Param("id")String id, @Param("status")Integer status);

    @Delete("DELETE FROM object_group WHERE ea=#{ea} AND id=#{id}")
    int deleteById(@Param("ea")String ea, @Param("id")String id);

    @Update("UPDATE object_group SET inner_visible = #{innerVisible}, update_time = now() WHERE id = #{id}")
    int updateInnerVisibleById(@Param("id") String id, @Param("innerVisible") Integer innerVisible);

    @Update("UPDATE object_group SET outer_visible = #{outerVisible}, update_time = now() WHERE id = #{id}")
    int updateOuterVisibleById(@Param("id") String id, @Param("outerVisible") Integer outerVisible);

    @Select("<script>"
            + "SELECT * FROM("
            + "SELECT  '-1' AS id, '全部' AS name, 9999999999 AS idx\n"
            + "UNION\n"
            + "SELECT '-2' AS id, '我创建的' AS name, 9999999998 AS idx\n"
            + "UNION\n"
            + "SELECT '-3' AS id, '共享给我的' AS name, 9999999997 AS idx\n"
            + "UNION\n"
            + "SELECT '-4' AS id, '公开的' AS name, 9999999996 AS idx\n"
            + "UNION\n"
            + "SELECT '-5' AS id, '未分组' AS name, 9999999995 AS idx\n"
            + "UNION\n"
            + "select id, name, extract(epoch from create_time) AS idx from object_group o WHERE o.ea=#{ea} AND o.object_type=#{objectType}\n"
            + "<if test=\'keyword != null\'>AND o.name like CONCAT('%', #{keyword}, '%')</if>"
            + "<if test=\'status != null\'> AND o.status=#{status}</if>"
            + ")t\n"
            + " ORDER BY t.idx DESC"
            +"</script>")
    List<HexagonGroupDTO> pageListHexagonSiteGroupByEa(@Param("ea")String ea, @Param("keyword")String keyword, @Param("status")Integer status, @Param("objectType")Integer objectType);

    @Select("<script>"
            + "SELECT COUNT(*) FROM("
            + "SELECT  '-1' AS id, '全部' AS name, 9999999999 AS idx\n"
            + "UNION\n"
            + "SELECT '-2' AS id, '我创建的' AS name, 9999999998 AS idx\n"
            + "UNION\n"
            + "SELECT '-3' AS id, '共享给我的' AS name, 9999999997 AS idx\n"
            + "UNION\n"
            + "SELECT '-4' AS id, '公开的' AS name, 9999999996 AS idx\n"
            + "UNION\n"
            + "SELECT '-5' AS id, '未分组' AS name, 9999999995 AS idx\n"
            + "UNION\n"
            + "select id, name, extract(epoch from create_time) AS idx from object_group o WHERE o.ea=#{ea} AND o.object_type=#{objectType}\n"
            + "<if test=\'keyword != null\'>AND o.name like CONCAT('%', #{keyword}, '%')</if>"
            + "<if test=\'status != null\'> AND o.status=#{status}</if>"
            + ")t\n"
            +"</script>")
    int countPageListHexagonSiteGroupByEa(@Param("ea")String ea, @Param("keyword")String keyword, @Param("objectType")Integer objectType);

    @Select("<script>"
            + "SELECT * FROM object_group WHERE ea=#{ea} AND name IN\n"
            + "<foreach collection = 'names' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>"
            + "</script>")
    List<ObjectGroupEntity> listByNames(@Param("ea")String ea, @Param("names")List<String> names);



    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND object_type=#{objectType}")
    List<ObjectGroupEntity> listGroupByEa(@Param("ea")String ea, @Param("objectType")Integer objectType);

    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND object_type=#{objectType} and inner_visible = 1")
    List<ObjectGroupEntity> listAllInnerVisible(@Param("ea")String ea, @Param("objectType")Integer objectType);

    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND object_type=#{objectType} and outer_visible = 1")
    List<ObjectGroupEntity> listAllOuterVisible(@Param("ea")String ea, @Param("objectType")Integer objectType);

    @Select("<script>"
            + "SELECT * FROM object_group WHERE ea=#{ea} AND object_type=#{objectType} \n"
            + "<if test=\'mobileDisplay != null\'> "
            + "<if test=\'mobileDisplay==1\'> AND mobile_display IS TRUE</if>"
            + "<if test=\'mobileDisplay==2\'> AND mobile_display IS NOT TRUE</if>"
            + "</if>"
            + "</script>")
    List<ObjectGroupEntity> listMobileGroupByEa(@Param("ea")String ea, @Param("objectType")Integer objectType,@Param("mobileDisplay")Integer mobileDisplay);

    @Update("<script>"
            +"UPDATE object_group SET mobile_display=#{mobileDisplay}, update_time=now() WHERE ea=#{ea} AND id in "
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>"
            + "</script>")
    int updateMobileDisplayByIds(@Param("ea")String ea, @Param("ids")List<String> ids, @Param("mobileDisplay")boolean mobileDisplay);

    @Select("<script>"
            + "SELECT * FROM object_group WHERE ea=#{ea} AND id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            + "</foreach>"
            +" AND mobile_display IS TRUE " +
            " order by create_time asc "
            + "</script>"
    )
    List<ObjectGroupEntity> getDisplayByIdList(@Param("ea") String ea, @Param("idList") List<String> idList);

    @Select("<script>"
            + "SELECT * (\n"
            + "SELECT g.id, g.name, count(*) AS cnt FROM object_group g LEFT JOIN object_group_relation r ON g.ea=#{ea}\n"
            + "AND r.ea=#{ea} AND g.id=r.group_id WHERE g.ea=#{ea} AND g.object_type=#{objectType} GROUP BY g.id )t\n"
            + "where t.cnt > 0\n "
            + "</script>")
    List<ObjectRelationGroupDTO> pageListRelationGroupEntity(@Param("ea")String ea, @Param("objectType")Integer objectType, @Param("page")Page page);

    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND object_type in (4,6,26) and inner_visible = 1")
    List<ObjectGroupEntity> getGroupByObjectTypeListAndEa(@Param("ea") String ea);

    @Select("SELECT * FROM object_group WHERE ea=#{ea} AND object_type in (4,6,26)")
    List<ObjectGroupEntity> getAllGroupByObjectTypeListAndEa(@Param("ea") String ea);

}
