package com.facishare.marketing.provider.entity.sms;

import lombok.Data;

import javax.persistence.Entity;
import java.util.Date;

/**
 * Created by zhengh on 2018/12/21.
 */
@Data
@Entity
public class OrderEntity {
    private String id;               //主键 uuid
    private String ea;               //公司ea
    private Integer userId;          //订单创建者的userId
    private String creator;          //订单创建者的名字
    private float price;             //价格
    private float paymentCount;      //购买金额
    private Integer purchaseCount;   //购买短信条数
    private int status;              //订单状态
    private Integer copies;          //购买份数
    private String crmOrderId;       //crm订单id
    private String orderPackage;     //订单套餐
    private Integer orderSource;          //订单来源
    private Date createTime;         //购买时间
    private Date updateTime;         //更新时间
}
