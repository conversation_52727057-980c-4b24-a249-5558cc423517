package com.facishare.marketing.provider.entity.qywx;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 21:37 2020/2/11
 * @ModifyBy
 */
@Data
public class QywxGroupSendTaskEntity implements Serializable {
    private String id;
    /** 公司帐号,如:fs **/
    private String ea;
    /** 公司的id,如:fs对应的公司id是1 **/
    private Long corpId;
    private Integer fsUserId;

    /**推广标题**/
    private String title;

    /** 消息类型 1图文， 2：文本， 3:图片， 4小程序消息**/
    private Integer msgType;

    /**发送给客户或者群，参见QywxGroupSendOjbectTypeEnum**/
    private Integer chatType;

    /**发送给群的群主ids**/
    private String groupMsgSenderIds;
    /**
     * 消息类型为文本消息的文本内容
     */
    private String content;

    /**
     * 发送范围  0-全部 1-筛选条件 2-营销用户对象
     */
    private Integer sendRange;

    /**
     * 当sendRange=3时生效，为营销用户对象的id列表
     */
    private String marketingUserGroupIds;

    /**
     * 筛选条件  只有sendRange=1才有效
     */
    private String filters;
    /**
     * 标签列表
     */
    private String tagIdList;

    /** 需要发送的客户数量 **/
    private Integer customerCount;
    /** 实际发送人数 **/
    private Integer actualCompletedCount;
    /** 需要发送的人数 **/
    private Integer needSendCount;
    /**
     * 已读数
     */
    private Integer readCount;
    //发送类型：实时发送，定时发送
    private Integer sendType;
    /** 定时发送的时间 **/
    private Long fixedTime;
    /** 通知创建时间 **/
    private Date createTime;
    /** 通知更新时间 **/
    private Date updateTime;
    /** 任务状态 **/
    private Integer status;
    /** 腾讯返回的错误码 **/
    private Integer errcode;
    /** 腾讯返回的错误消息 **/
    private String errmsg;
    /** 腾讯返回的失败列表 **/
    private String failList;
    /** 发送成功返回的腾讯消息id **/
    private String msgid;
    /** 企业微信用户id **/
    private String sender;

    /** 图文消息图片path **/
    private String imagePath;
    /** 图文消息的标题 **/
    private String linkTitle;
    /** 图文消息封面图 **/
    private String linkPicPath;
    /** 图文消息描述 **/
    private String linkDesc;
    /** 图文消息的链接 **/
    private String linkUrl;
    /** 小程序消息的标题 **/
    private String miniTitle;
    /** 小程序消息封面图 **/
    private String miniPicPath;
    /** 小程序page路径 **/
    private String miniPage;
    /** 小程序appId **/
    private String appId;
    /** 选择的物料id **/
    private String objectId;
    /** 选择的物料类型 **/
    private Integer objectType;
    /** 市场活动id **/
    private String marketingActivityId;
    /** 过滤近几天用户 **/
    private Integer filterNDaySentUser;
    /** 企微群列表 **/
    private String qywxGroupList;
    /** 员工id列表 **/
    private String userId;
    /** 员工部门列表 **/
    private String departmentId;
    /** 员工标签列表 **/
    private String tagId;
    /**
     * 客户群群发过滤群条件
     */
    private String chatGroupFilters;
    /**
     * 是否允许成员在待发送客户列表中重新进行选择
     */
    private Boolean allowSelect;
}
