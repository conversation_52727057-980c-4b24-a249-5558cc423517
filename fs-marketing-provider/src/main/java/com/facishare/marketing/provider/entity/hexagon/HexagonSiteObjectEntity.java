package com.facishare.marketing.provider.entity.hexagon;

import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/1/11 10:47 上午
 */
@Data
public class HexagonSiteObjectEntity implements Serializable {
    private String id;

    private String ea;

    private String siteId;

    private String objectId;

    private Integer objectType;

    private Integer createBy;

    private Date createTime;

    private Integer updateBy;

    private Date updateTime;

    private Integer formStyleType;

    private String formButtonName;

    private ButtonStyle buttonStyle;

    private Integer status;
}
