package com.facishare.marketing.provider.manager.advertise


import com.facishare.marketing.api.LeadStageOptionVO
import com.facishare.marketing.api.service.MarketingEventCommonSettingService
import com.facishare.marketing.api.vo.marketingevent.MarketingEventAnalysisSettingVO
import com.facishare.marketing.common.enums.advertiser.BigScreenTimeRangeEnum
import com.facishare.marketing.provider.bo.advertise.AdCampaignDataStatisticsBO
import com.facishare.marketing.provider.bo.advertise.OpportunityStatisticsDataBO
import com.facishare.marketing.provider.dao.EnterprseInfoDao
import com.facishare.marketing.provider.dao.advertiser.bigScreen.AdBigScreenSettingDAO
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDataDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDataDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDataDAO
import com.facishare.marketing.provider.dto.AdLeadDataDTO
import com.facishare.marketing.provider.entity.EnterpriseInfoEntity
import com.facishare.marketing.provider.entity.advertiser.AdBigScreenSettingEntity
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.facishare.marketing.provider.manager.advertiser.AdBigScreenManager
import com.facishare.marketing.provider.manager.advertiser.AdLeadDataManager
import com.facishare.marketing.provider.manager.advertiser.AdLeadNewOpportunityDataManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.rest.core.util.JsonUtil
import com.fxiaoke.crmrestapi.common.data.FieldDescribe
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Method

class AdBigScreenManagerSpec extends Specification {

    // 模拟依赖项
    def baiduCampaignDAO = Mock(BaiduCampaignDAO)
    def baiduCampaignDataDAO = Mock(BaiduCampaignDataDAO)
    def adAccountManager = Mock(AdAccountManager)
    def headlinesCampaignDataDAO = Mock(HeadlinesCampaignDataDAO)
    def headlinesCampaignDAO = Mock(HeadlinesCampaignDAO)
    def headlinesAdDAO = Mock(HeadlinesAdDAO)
    def headlinesAdDataDAO = Mock(HeadlinesAdDataDAO)
    def tencentAdGroupDataDAO = Mock(TencentAdGroupDataDAO)
    def tencentAdGroupDAO = Mock(TencentAdGroupDAO)
    def adLeadDataManager = Mock(AdLeadDataManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def adBigScreenSettingDAO = Mock(AdBigScreenSettingDAO)
    def enterprseInfoDao = Mock(EnterprseInfoDao)
    def marketingEventCommonSettingService = Mock(MarketingEventCommonSettingService)
    def adLeadNewOpportunityDataManager = Mock(AdLeadNewOpportunityDataManager)


    // 待测系统
    def adBigScreenManager = new AdBigScreenManager(
            "baiduCampaignDAO": baiduCampaignDAO,
            "baiduCampaignDataDAO": baiduCampaignDataDAO,
            "adAccountManager": adAccountManager,
            "headlinesCampaignDataDAO": headlinesCampaignDataDAO,
            "headlinesCampaignDAO": headlinesCampaignDAO,
            "headlinesAdDAO": headlinesAdDAO,
            "headlinesAdDataDAO": headlinesAdDataDAO,
            "tencentAdGroupDataDAO": tencentAdGroupDataDAO,
            "tencentAdGroupDAO": tencentAdGroupDAO,
            "adLeadDataManager": adLeadDataManager,
            "crmMetadataManager": crmMetadataManager,
            "crmV2Manager": crmV2Manager,
            "adBigScreenSettingDAO": adBigScreenSettingDAO,
            "enterprseInfoDao": enterprseInfoDao,
            "marketingEventCommonSettingService": marketingEventCommonSettingService,
            "adLeadNewOpportunityDataManager": adLeadNewOpportunityDataManager
    )

    @Shared
    def dbSetting = new AdBigScreenSettingEntity(
            id: 1,
            ea: "88146",
            title: "大屏",
            setting: "{\"accountAcquisitionCustomerCompare\":{\"fieldList\":[{\"canEdit\":true,\"hidden\":false,\"id\":\"8b524863784b46f9a46b3b7f6557befb\",\"name\":\"bj-fxiaoke\"},{\"canEdit\":true,\"hidden\":false,\"id\":\"b2f6f708cfd840d787db0879cbef070a\",\"name\":\"用户*************\"},{\"canEdit\":true,\"hidden\":false,\"id\":\"b3a98f88760344d3b64041727d579c97\",\"name\":\"北京易动纷享科技有限责任公司\"},{\"canEdit\":true,\"hidden\":false,\"id\":\"efc63ba004c04c5f91dad784d78c78e6\",\"name\":\"北京易动纷享科技有限责任公司0\"}],\"name\":\"广告账户获客对比\"},\"accountInputOutputAnalysis\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"cost\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsCount\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsSQLCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"opportunityCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgClickPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgLeadAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgMQLAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgSQLAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgOpportunityAcquisitionPrice\"},{\"canEdit\":false,\"fieldValueList\":[{\"selected\":true,\"value\":\"ACCOUNT\"},{\"selected\":false,\"value\":\"KEYWORD\"},{\"selected\":false,\"value\":\"CAMPAIGN\"}],\"hidden\":false,\"name\":\"showDimension\",\"type\":\"select_one\"},{\"canEdit\":false,\"fieldValueList\":[{\"selected\":true,\"value\":\"5\"},{\"selected\":false,\"value\":\"10\"}],\"hidden\":false,\"name\":\"top\",\"type\":\"select_one\"}],\"name\":\"广告账户投入产出分析\"},\"acquisitionCustomerConvertFunnel\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"pv\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"clicks\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsCount\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsMQLCount\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsSQLCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"opportunityCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"winOpportunityCount\"},{\"canEdit\":false,\"fieldValueList\":[{\"label\":\"潜在线索(Lead)\",\"selected\":false,\"value\":\"Lead\"},{\"label\":\"市场认可线索(MQL)\",\"selected\":true,\"value\":\"MQL\"},{\"label\":\"销售认可线索(SQL)\",\"selected\":true,\"value\":\"SQL\"},{\"label\":\"转商机\",\"selected\":false,\"value\":\"OPP\"}],\"hidden\":false,\"name\":\"mqlDefinition\",\"type\":\"select_many\"},{\"canEdit\":false,\"fieldValueList\":[{\"label\":\"潜在线索(Lead)\",\"selected\":false,\"value\":\"Lead\"},{\"label\":\"市场认可线索(MQL)\",\"selected\":false,\"value\":\"MQL\"},{\"label\":\"销售认可线索(SQL)\",\"selected\":false,\"value\":\"SQL\"},{\"label\":\"转商机\",\"selected\":true,\"value\":\"OPP\"}],\"hidden\":false,\"name\":\"sqlDefinition\",\"type\":\"select_many\"}],\"name\":\"广告获客转化漏斗\"},\"convertPeriod\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"avgLeadToMQL\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgLeadMQLToSQL\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgSQLToWinOpportunity\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"leadToWinOpportunity\"}],\"name\":\"转化周期\"},\"customerAcquisitionCost\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"avgClickPrice\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgLeadAcquisitionPrice\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgMQLAcquisitionPrice\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"avgSQLAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgOpportunityAcquisitionPrice\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"avgWinOpportunityAcquisitionPrice\"}],\"name\":\"广告获客成本\"},\"launchEffectTrend\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"COST\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"LEADS\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"WECHAT_FANS\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"QYWX_EXTERNAL_CUSTOMER\"}],\"name\":\"广告投放效果趋势\"},\"leadsArealDistributions\":{\"fieldList\":[{\"canEdit\":false,\"fieldValueList\":[{\"label\":\"全国\",\"selected\":true,\"value\":\"china\"},{\"label\":\"河北省\",\"selected\":false,\"value\":\"hebei\"},{\"label\":\"宁夏\",\"selected\":false,\"value\":\"ningxia\"},{\"label\":\"贵州省\",\"selected\":false,\"value\":\"guizhou\"},{\"label\":\"新疆\",\"selected\":false,\"value\":\"xinjiang\"},{\"label\":\"福建省\",\"selected\":false,\"value\":\"fujian\"},{\"label\":\"海南省\",\"selected\":false,\"value\":\"hainan\"},{\"label\":\"广东省\",\"selected\":false,\"value\":\"guangdong\"},{\"label\":\"广西\",\"selected\":false,\"value\":\"guangxi\"},{\"label\":\"黑龙江省\",\"selected\":false,\"value\":\"heilongjiang\"},{\"label\":\"浙江省\",\"selected\":false,\"value\":\"zhejiang\"},{\"label\":\"青海省\",\"selected\":false,\"value\":\"qinghai\"},{\"label\":\"江苏省\",\"selected\":false,\"value\":\"jiangsu\"},{\"label\":\"河南省\",\"selected\":false,\"value\":\"henan\"},{\"label\":\"山西省\",\"selected\":false,\"value\":\"shanxi\"},{\"label\":\"西藏\",\"selected\":false,\"value\":\"xizang\"},{\"label\":\"云南省\",\"selected\":false,\"value\":\"yunnan\"},{\"label\":\"辽宁省\",\"selected\":false,\"value\":\"liaoning\"},{\"label\":\"湖南省\",\"selected\":false,\"value\":\"hunan\"},{\"label\":\"安徽省\",\"selected\":false,\"value\":\"anhui\"},{\"label\":\"江西省\",\"selected\":false,\"value\":\"jiangxi\"},{\"label\":\"湖北省\",\"selected\":false,\"value\":\"hubei\"},{\"label\":\"甘肃省\",\"selected\":false,\"value\":\"gansu\"},{\"label\":\"陕西省\",\"selected\":false,\"value\":\"shanxi1\"},{\"label\":\"台湾\",\"selected\":false,\"value\":\"taiwan\"},{\"label\":\"山东省\",\"selected\":false,\"value\":\"shandong\"},{\"label\":\"吉林省\",\"selected\":false,\"value\":\"jilin\"},{\"label\":\"四川省\",\"selected\":false,\"value\":\"sichuan\"},{\"label\":\"内蒙古\",\"selected\":false,\"value\":\"neimenggu\"}],\"hidden\":false,\"name\":\"showDimension\",\"type\":\"select_one\"}],\"name\":\"广告线索地域分布\"},\"modulePositions\":[\"customerAcquisitionCost\",\"overView\",\"acquisitionCustomerConvertFunnel\",\"launchEffectTrendList\",\"leadsArealDistributions\",\"leadStageAndSaleSituation\",\"acquisitionCustomerKeywordList\",\"accountInputOutputAnalysisList\",\"convertPeriod\", \"accountAcquisitionCustomerCompareList\"],\"overView\":{\"fieldList\":[{\"canEdit\":false,\"hidden\":false,\"name\":\"costs\"},{\"canEdit\":false,\"hidden\":false,\"name\":\"leadsCount\"},{\"canEdit\":true,\"hidden\":true,\"name\":\"sqlCount\"},{\"canEdit\":true,\"hidden\":false,\"name\":\"winOpportunityMoney\"}],\"name\":\"概览设置\"},\"timeSetting\":{\"beginTime\":*************,\"compareBeginTime\":*************,\"compareEndTime\":*************,\"compareTimeType\":\"YoY\",\"endTime\":*************,\"timeRange\":\"THIS_YEAR\"}}",
            timeRange: "THIS_SEASON",
            relativeTimeRange: "LAST_SEASON"
    )

    @Unroll
    def "getBigScreenSetting"() {
        given:
        String ea = "88146"
        adBigScreenSettingDAO.getByEa(ea) >> eaSetting
        adAccountManager.queryAccountByEa(*_) >> adAccountList
        adBigScreenManager.getLeadStageOption(*_) >> []
        marketingEventCommonSettingService.getAnalysisSetting(*_) >> {
            com.facishare.marketing.common.result.Result<MarketingEventAnalysisSettingVO> result = new com.facishare.marketing.common.result.Result<MarketingEventAnalysisSettingVO>()
            String anaSetting = "{\"mqlDefinition\":[{\"label\":\"市场认可线索(MQL)\",\"value\":\"MQL\",\"selected\":true}],\"sqlDefinition\":[{\"label\":\"转商机\",\"value\":\"OPP\",\"selected\":true}]}";
            MarketingEventAnalysisSettingVO vo = JsonUtil.fromJson(anaSetting, MarketingEventAnalysisSettingVO.class);
            result.setData(vo)
            return result
        }

        when:
        AdBigScreenSettingEntity setting = adBigScreenManager.getBigScreenSetting(ea)
        then:
        setting != null
        where:
        // 测试场景
        eaSetting | adAccountList
        null      | [new AdAccountEntity(id: "ttttt")]
        dbSetting | [new AdAccountEntity(id: "ttttt")]
    }


    @Unroll
    def "getLeadStageOption"() {
        given:
        String ea = "88146"
        crmV2Manager.getCrmDescribeDetail(*_) >> describe
        when:
        List<LeadStageOptionVO> list = adBigScreenManager.getLeadStageOption(ea)
        then:
        list != null
        where:
        // 测试场景
        describe << [
                null, getDescribeResult()

        ]
    }

    def getDescribeResult() {
        ObjectDescribe objectDescribe = new ObjectDescribe()
        objectDescribe.setFields(new HashMap<String, FieldDescribe>())
        FieldDescribe fieldDescribe = new FieldDescribe()
        List<Map<String, Object>> options = new ArrayList<>()
        fieldDescribe.put("options", options)
        options.add(new HashMap<String, Object>() {
            {
                put("label", "1")
                put("value", "1")
            }
        })
        objectDescribe.getFields().put("leads_stage", fieldDescribe)
        return objectDescribe

    }

    @Unroll
    def "getDateByTimeRangeResultByEnum"() {
        given:
        when:
        AdBigScreenManager.TimeRangeResult timeRangeResult = AdBigScreenManager.getDateByTimeRangeResultByEnum(timeRange)
        then:
        timeRangeResult != null
        where:
        // 测试场景
        timeRange << BigScreenTimeRangeEnum.values()
    }

    @Unroll
    def "updateSetting"() {
        given:
        adBigScreenSettingDAO.batchInsert(*_) >> { println "batchInsert" }
        adBigScreenSettingDAO.update(*_) >> { println "update" }
        adBigScreenSettingDAO.getByEa(*_) >> existSetting
        when:
        adBigScreenManager.updateSetting(new AdBigScreenSettingEntity(ea: "88146"))
        then:
        noExceptionThrown()
        where:
        // 测试场景
        existSetting << [null, new AdBigScreenSettingEntity()]
    }

    @Unroll
    def "getAllSetting"() {
        given:
        adBigScreenSettingDAO.getAllSetting() >> []

        when:
        adBigScreenManager.getAllSetting()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getSettingFromDB"() {
        given:
        adBigScreenSettingDAO.getSettingFromDB(_) >> new AdBigScreenSettingEntity()

        when:
        adBigScreenManager.getSettingFromDB("88146")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getSelectableTimeList"() {
        given:
        when:
        adBigScreenManager.getSelectableTimeList()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "bigScreen"() {
        given:
        adBigScreenSettingDAO.getByEa(_) >> dbSetting
        adAccountManager.queryAccountByEa(*_) >> [new AdAccountEntity(id: "baidu", source: "百度"), new AdAccountEntity(id: "tencent", source: "腾讯"), new AdAccountEntity(id: "headline", source: "巨量引擎")]
        adBigScreenSettingDAO.getByEa(_) >> null
        baiduCampaignDataDAO.statisticsCampaignData(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE, relativeTotalClick: BigDecimal.ONE, relativeTotalCost: BigDecimal.ONE)]
        tencentAdGroupDataDAO.statisticsCampaignData(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE, relativeTotalClick: BigDecimal.ONE, relativeTotalCost: BigDecimal.ONE)]
        headlinesCampaignDataDAO.statisticsCampaignData(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE, relativeTotalClick: BigDecimal.ONE, relativeTotalCost: BigDecimal.ONE)]
        adLeadDataManager.getByLeadCreateTime(*_) >>> [
                [new AdLeadDataDTO(leadId: "1"), new AdLeadDataDTO(leadId: "2"), new AdLeadDataDTO(leadId: "3")],
                [new AdLeadDataDTO(leadId: "4"), new AdLeadDataDTO(leadId: "5")],
                [new AdLeadDataDTO(leadId: "6"), new AdLeadDataDTO(leadId: "7")]
        ]
        enterprseInfoDao.queryEnterpriseInfoByEa(_) >> new EnterpriseInfoEntity(fullName: "大芒果")

        baiduCampaignDataDAO.getTotalCostGroupByActionData(*_) >> [new AdCampaignDataStatisticsBO(totalCost: BigDecimal.ONE)]
        tencentAdGroupDataDAO.getTotalCostGroupByActionData(*_) >> [new AdCampaignDataStatisticsBO(totalCost: BigDecimal.ONE)]
        headlinesCampaignDataDAO.getTotalCostGroupByActionData(*_) >> [new AdCampaignDataStatisticsBO(totalCost: BigDecimal.ONE)]

        crmV2Manager.countCrmObjectByFilterV3(*_) >> 0

        adLeadNewOpportunityDataManager.getDistinctStatisticsDataByStgChangedTime(*_) >>> [
                new OpportunityStatisticsDataBO(
                        totalAmount: BigDecimal.ONE,
                        opportunityCount: BigDecimal.ONE,
                        relativeOpportunityCount: BigDecimal.ONE,
                        winOpportunityCount: BigDecimal.ONE,
                        relativeWinOpportunityCount: BigDecimal.ONE,
                        winOpporTotalStgChangedTime: BigDecimal.ONE,
                        relativeWinOpporTotalStgChangedTime: BigDecimal.ONE,
                        leadIdToWinOpporSet: new HashSet<String>(Arrays.asList("1", "2", "3")),
                        relativeLeadIdToWinOpporSet: new HashSet<String>(Arrays.asList("4", "5", "6"))),
                new OpportunityStatisticsDataBO(
                        totalAmount: BigDecimal.ONE,
                        opportunityCount: BigDecimal.ONE,
                        relativeOpportunityCount: BigDecimal.ONE,
                        winOpportunityCount: BigDecimal.ONE,
                        relativeWinOpportunityCount: BigDecimal.ONE,
                        winOpporTotalStgChangedTime: BigDecimal.ONE,
                        relativeWinOpporTotalStgChangedTime: BigDecimal.ONE,
                        leadIdToWinOpporSet: new HashSet<String>(Arrays.asList("1", "2", "3")),
                        relativeLeadIdToWinOpporSet: new HashSet<String>(Arrays.asList("4", "5", "6")))
        ]
        adLeadDataManager.countByTransformTime(*_) >>> [1, 2]

        adLeadDataManager.countByChangedToMqlTime(*_) >> 1

        adLeadNewOpportunityDataManager.getDistinctStatisticsDataByOpportunityCreateTime(*_) >> new OpportunityStatisticsDataBO(
                totalAmount: BigDecimal.ONE,
                opportunityCount: BigDecimal.ONE,
                relativeOpportunityCount: BigDecimal.ONE,
                winOpportunityCount: BigDecimal.ONE,
                relativeWinOpportunityCount: BigDecimal.ONE,
                winOpporTotalStgChangedTime: BigDecimal.ONE,
                relativeWinOpporTotalStgChangedTime: BigDecimal.ONE,
                leadIdToWinOpporSet: new HashSet<String>(Arrays.asList("1", "2", "3")),
                relativeLeadIdToWinOpporSet: new HashSet<String>(Arrays.asList("4", "5", "6")))
        def spy = Spy(adBigScreenManager)
        spy.getOpportunityStatisticsDataFromDb(*_) >> new OpportunityStatisticsDataBO(
                totalAmount: BigDecimal.ONE,
                opportunityCount: BigDecimal.ONE,
                relativeOpportunityCount: BigDecimal.ONE,
                winOpportunityCount: BigDecimal.ONE,
                relativeWinOpportunityCount: BigDecimal.ONE,
                winOpporTotalStgChangedTime: BigDecimal.ONE,
                relativeWinOpporTotalStgChangedTime: BigDecimal.ONE,
                leadIdToWinOpporSet: new HashSet<String>(Arrays.asList("1", "2", "3")),
                relativeLeadIdToWinOpporSet: new HashSet<String>(Arrays.asList("4", "5", "6")))

        crmMetadataManager.batchGetByIdsV3(*_) >> []

        baiduCampaignDAO.queryByMarketingEventIdList(*_) >> [new BaiduCampaignEntity(campaignId: 11L, marketingEventId: "event_id")]
        baiduCampaignDataDAO.statisticsCampaignDataGroupByCampaign(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE, relativeTotalClick: BigDecimal.ONE, relativeTotalCost: BigDecimal.ONE)]

        tencentAdGroupDAO.queryBySubMarketingEventIdList(*_) >> [new TencentAdGroupEntity(adgroupId: 12L, subMarketingEventId: "event_id2")]
        tencentAdGroupDataDAO.statisticsCampaignDataGroupByCampaign(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE, relativeTotalClick: BigDecimal.ONE, relativeTotalCost: BigDecimal.ONE)]

        headlinesCampaignDAO.getByMarketingEventIdList(*_) >> [new HeadlinesCampaignEntity(campaignId: 13L, marketingEventId: "event_id3")]
        headlinesCampaignDataDAO.statisticsCampaignDataGroupByCampaign(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE, relativeTotalClick: BigDecimal.ONE, relativeTotalCost: BigDecimal.ONE)]

        headlinesAdDAO.queryBySubMarketingIdList(*_) >> [new HeadlinesAdEntity(adId: 14L, subMarketingEventId: "event_id4")]
        headlinesAdDataDAO.statisticsAdDataGroupByAdIdList(*_) >> [new AdCampaignDataStatisticsBO(totalPv: BigDecimal.ONE, totalClick: BigDecimal.ONE, totalCost: BigDecimal.ONE, relativeTotalPv: BigDecimal.ONE)]

        marketingEventCommonSettingService.getAnalysisSetting(*_) >> {
            com.facishare.marketing.common.result.Result<MarketingEventAnalysisSettingVO> result = new com.facishare.marketing.common.result.Result<MarketingEventAnalysisSettingVO>()
            String anaSetting = "{\"mqlDefinition\":[{\"label\":\"市场认可线索(MQL)\",\"value\":\"MQL\",\"selected\":true}],\"sqlDefinition\":[{\"label\":\"转商机\",\"value\":\"OPP\",\"selected\":true}]}";
            MarketingEventAnalysisSettingVO vo = JsonUtil.fromJson(anaSetting, MarketingEventAnalysisSettingVO.class);
            result.setData(vo)
            return result
        }

        when:
        Method method = adBigScreenManager.getClass().getDeclaredMethod("init");
        method.setAccessible(true);
        method.invoke(adBigScreenManager);
        adBigScreenManager.bigScreen("88146")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getKeywordStatisticsMap"() {
        given:
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 1
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> {
            com.fxiaoke.crmrestapi.common.data.InnerPage<ObjectData> page = new com.fxiaoke.crmrestapi.common.data.InnerPage<ObjectData>()
            ObjectData objectData = new ObjectData()
            objectData.put("marketing_keyword_id", "hhhh")
            objectData.put("advertising_costs", BigDecimal.ONE)
            objectData.put("advertising_costs", BigDecimal.ONE)
            page.setDataList([objectData])
        }
        when:
        adBigScreenManager.getKeywordStatisticsMap("88146", ["a"], new Date(), new Date())
        then:
        noExceptionThrown()
    }
}
