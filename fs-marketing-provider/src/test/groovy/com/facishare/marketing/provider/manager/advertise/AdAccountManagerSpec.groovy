package com.facishare.marketing.provider.manager.advertise

import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

class AdAccountManagerSpec extends Specification {

    // 模拟依赖项
    def accountDAO = Mock(BaiduAccountDAO)

    def adPrototypeRoomAccount = "[{\"accessKey\":\"123456\",\"accountId\":123456,\"balance\":52013.14,\"budget\":150000.0,\"cost\":90013.56,\"ea\":\"88146\",\"id\":\"3efa51633cc141d9a717065dcbd047f1\",\"mobileBalance\":20013.14,\"password\":\"123456\",\"pcBalance\":30000.0,\"refreshToken\":\"123456\",\"secretKey\":\"123456\",\"source\":\"百度\",\"status\":0,\"token\":\"123456\",\"username\":\"样板间百度账号\"},{\"accessKey\":\"123456\",\"accountId\":123456,\"balance\":5201314,\"budget\":150000,\"cost\":90013.56,\"ea\":\"82255\",\"id\":\"1add3aa21c2943568d3dabfd21fc0cbc\",\"mobileBalance\":20013.14,\"password\":\"123456\",\"pcBalance\":30000,\"refreshToken\":\"123456\",\"secretKey\":\"123456\",\"source\":\"腾讯\",\"status\":0,\"token\":\"123456\",\"username\":\"样板间百腾讯账号\"},{\"accessKey\":\"123456\",\"accountId\":123456,\"balance\":52013.14,\"budget\":150000,\"cost\":90013.56,\"ea\":\"82255\",\"id\":\"bbc95214863448d396d553483a39352d\",\"mobileBalance\":20013.14,\"password\":\"123456\",\"pcBalance\":30000,\"refreshToken\":\"123456\",\"secretKey\":\"123456\",\"source\":\"巨量引擎\",\"status\":0,\"token\":\"123456\",\"username\":\"样板间巨量引擎账号\"}]";


    // 待测系统
    def adAccountManager = new AdAccountManager(
            "accountDAO": accountDAO,
            "adPrototypeRoomAccount": adPrototypeRoomAccount
    )


    @Unroll
    def "添加广告账号"() {
        given:
        accountDAO.addAccount(_) >> true
        when:
        def result = adAccountManager.addAccount(new AdAccountEntity(id: "test"))
        then:
        result
    }

    @Unroll
    def "更新广告账号"() {
        given:
        accountDAO.updateAccount(_) >> true
        when:
        def result = adAccountManager.updateAccount(new AdAccountEntity(id: "test"))
        then:
        result
    }

    @Unroll
    def "更新广告余额"() {
        given:
        accountDAO.updateBalanceAndCostById(*_) >> true
        when:
        def result = adAccountManager.updateBalanceAndCostById("iddd", 33D)
        then:
        result
    }

    @Unroll
    def "更新广告refreshToken"() {
        given:
        accountDAO.updateRefreshTokenByAccountId(*_) >> true
        when:
        def result = adAccountManager.updateRefreshTokenByAccountId("refresh", "token", 11L)
        then:
        result
    }

    @Unroll
    def "更新广告refreshToken2"() {
        given:
        accountDAO.updateRefreshTokenById(*_) >> true
        when:
        def result = adAccountManager.updateRefreshTokenById("", "", "")
        then:
        result
    }

    @Unroll
    def "更新广告刷新数据"() {
        given:
        accountDAO.updateAccountRefreshData(*_) >> true
        when:
        def result = adAccountManager.updateAccountRefreshData(new AdAccountEntity(id: "test"))
        then:
        result
    }

    @Unroll
    def "查询广告账号"() {
        given:
        accountDAO.queryAccountByEaAndAccountId(*_) >> new AdAccountEntity(id: "test_id")
        when:
        def result = adAccountManager.queryAccountByEaAndAccountId("88146", 112L, "test_id")
        then:
        result.getId().equals("test_id")
    }

    @Unroll
    def "查询所有状态的广告账号"() {
        given:
        accountDAO.queryAccountWithoutStatus(*_) >> new AdAccountEntity(id: "test_id", accountId: 112)
        when:
        def result = adAccountManager.queryAccountWithoutStatus("88146", 112, "test_id")
        then:
        result.getId().equals("test_id") && result.getAccountId() == 112
    }

    @Unroll
    def "查询启用的广告账号"() {
        given:
        accountDAO.queryEnableAccountById(*_) >> new AdAccountEntity(id: "test_id")
        when:
        def result = adAccountManager.queryEnableAccountById("test_id")
        then:
        result.getId().equals("test_id")
    }

    @Unroll
    def "查询企业启用的广告账号"() {
        given:
        accountDAO.queryEnableAccountByEa(*_) >> Lists.newArrayList(new AdAccountEntity(id: "test_id"))
        when:
        def result = adAccountManager.queryEnableAccountByEa("test_id")
        then:
        result.size() > 0
    }

    @Unroll
    def "查询企业启用的广告账号2"() {
        given:
        accountDAO.queryAccountByEaAndSource(*_) >> Lists.newArrayList(new AdAccountEntity(id: "test_id"))
        when:
        def result = adAccountManager.queryAccountByEaAndSource("88146", "test_id", "百度", true)
        then:
        result.size() > 0
    }

    @Unroll
    def "查询企业启用的广告账号3"() {
        given:
        accountDAO.queryAccountByEa(*_) >> Lists.newArrayList(new AdAccountEntity(id: "test_id"))
        when:
        def result = adAccountManager.queryAccountByEa("88146", true)
        then:
        result.size() > 0
    }


    @Unroll
    def "查询广告账号byId"() {
        given:
        accountDAO.queryAccountById(*_) >> new AdAccountEntity(id: "test_id")
        when:
        def result = adAccountManager.queryAccountById("test_id")
        then:
        result != null
    }

    @Unroll
    def "查询广告账号byId2"() {
        given:
        accountDAO.queryAccountByIds(*_) >> [new AdAccountEntity(id: "test_id")]
        when:
        def result = adAccountManager.queryAccountByIds(["test_id"])
        then:
        result.size() > 0
    }

    @Unroll
    def "查询所有账号"() {
        given:
        accountDAO.getAllAdAccount(*_) >> [new AdAccountEntity(id: "test_id")]
        when:
        def result = adAccountManager.getAllAdAccount(true)
        then:
        result.size() > 0
    }

    @Unroll
    def "查询百度所有账号"() {
        given:
        accountDAO.getBaiduAdAccountWithOutStatus() >> ["test"]
        when:
        def result = adAccountManager.getBaiduAdAccountWithOutStatus()
        then:
        result.size() > 0
    }

    @Unroll
    def "查询头条所有账号"() {
        given:
        accountDAO.getHeadlinesAdAccountWithOutStatus() >> ["test"]
        when:
        def result = adAccountManager.getHeadlinesAdAccountWithOutStatus()
        then:
        result.size() > 0
    }

    @Unroll
    def "更新账号状态"() {
        given:
        accountDAO.updateAdAccountStatus(_) >> 1
        when:
        def result = adAccountManager.updateAdAccountStatus(new AdAccountEntity())
        then:
        result > 0
    }

    @Unroll
    def "更新账号状态2"() {
        given:
        accountDAO.updateAdAccountStatusById(*_) >> 1
        when:
        def result = adAccountManager.updateAdAccountStatusById(1, "test")
        then:
        result > 0
    }

    @Unroll
    def "查询所有企业账号"() {
        given:
        accountDAO.findAllEa() >> ["11"]
        when:
        def result = adAccountManager.findAllEa(true)
        then:
        result.size() > 0
    }

    @Unroll
    def "获取样板间账号"() {
        given:
        when:
        def result = adAccountManager.getAdPrototypeRoomAccount("88146", "百度")
        then:
        result.size() > 0
    }

    @Unroll
    def "获取样板间账号2"() {
        given:
        accountDAO.getAdPrototypeRoomAccountById(*_) >> new AdAccountEntity()
        when:
        def result = adAccountManager.getAdPrototypeRoomAccountById("88146", "3efa51633cc141d9a717065dcbd047f1")
        then:
        result != null

    }

    @Unroll
    def "获取样板间账号3"() {
        given:
        accountDAO.getAdPrototypeRoomAccountBySource(*_) >> [new AdAccountEntity()]
        when:
        def result = adAccountManager.getAdPrototypeRoomAccountBySource("百度")
        then:
        result.size() > 0
    }

    @Unroll
    def "获取样板间账号4"() {
        given:
        accountDAO.getAdPrototypeRoomAccountByEa(*_) >> [new AdAccountEntity()]
        when:
        def result = adAccountManager.getAdPrototypeRoomAccountByEa("88146")
        then:
        result.size() > 0
    }

    @Unroll
    def "获取样板间账号5"() {
        given:
        accountDAO.getAllAdPrototypeRoomAccount() >> [new AdAccountEntity()]
        when:
        def result = adAccountManager.getAllAdPrototypeRoomAccount()
        then:
        result.size() > 0
    }

    @Unroll
    def "是否是样板间账号"() {
        given:
        accountDAO.isPrototypeRoomAccount() >> true
        when:
        def result = adAccountManager.isPrototypeRoomAccount("88146", "3efa51633cc141d9a717065dcbd047f1")
        then:
        result
    }

    @Unroll
    def "通过用户名获取账号"() {
        given:
        accountDAO.queryAccountByUsername(*_) >> [new AdAccountEntity(ea: "88146", username: "test")]
        when:
        def result = adAccountManager.getByUserName("88146", "test")
        then:
        result.size() > 0
    }

    @Unroll
    def "获取所有企业账号"() {
        given:
        accountDAO.getAllByEa(*_) >> []
        when:
        def result = adAccountManager.getAllByEa("88146")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "queryByName"() {
        given:
        accountDAO.queryByName(*_) >> []
        when:
        def result = adAccountManager.queryByName("88146")
        then:
        noExceptionThrown()
    }
}
