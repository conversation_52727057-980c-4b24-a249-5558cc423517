package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.data.MarketingEventData
import com.facishare.marketing.api.service.conference.ConferenceService
import com.facishare.marketing.api.vo.conference.AddCampaignMembersObjVO
import com.facishare.marketing.common.contstant.campaign.CampaignConstants
import com.facishare.marketing.common.enums.MarketingEventEnum
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll
import com.facishare.marketing.common.typehandlers.value.DataCount
import com.facishare.marketing.common.typehandlers.value.FieldMappings
import com.facishare.marketing.outapi.service.ClueDefaultSettingService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationUserDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO
import com.facishare.marketing.provider.dao.ticket.CustomizeTicketDAO
import com.facishare.marketing.provider.dao.ticket.WxTicketReceiveDAO
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignDataMailDTO
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity
import com.facishare.marketing.provider.entity.MarketingEventCommonSettingEntity
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationUserEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity
import com.facishare.marketing.provider.entity.ticket.CustomizeTicketReceiveEntity
import com.facishare.marketing.provider.entity.ticket.WxTicketReceiveEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.mq.handler.dto.CrmEventDTO
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.CrmV2MappingManager
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService
import com.google.common.collect.Maps
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

import static com.facishare.marketing.common.enums.MarketingEventEnum.*

class CampaignMergeDataManagerSpec extends Specification {

    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def marketingEventManager = Mock(MarketingEventManager)
    def activityDAO = Mock(ActivityDAO)
    def activityManager = Mock(ActivityManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def redisManager = Mock(RedisManager)
    def conferenceManager = Mock(ConferenceManager)
    def customizeTicketManager = Mock(CustomizeTicketManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def customizeTicketDAO = Mock(CustomizeTicketDAO)
    def wxTicketReceiveDAO = Mock(WxTicketReceiveDAO)
    def eieaConverter = Mock(EIEAConverter)
    def objectDescribeCrmService = Mock(ObjectDescribeCrmService)
    def conferenceInvitationUserDAO = Mock(ConferenceInvitationUserDAO)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def memberAccessibleCampaignDAO = Mock(MemberAccessibleCampaignDAO)
    def campaignPayOrderDao = Mock(CampaignPayOrderDao)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def triggerInstanceManager = Mock(TriggerInstanceManager)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingWxServiceAccountRelationDao = Mock(UserMarketingWxServiceAccountRelationDao)
    def fileV2Manager = Mock(FileV2Manager)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def marketingEventCommonSettingDAO = Mock(MarketingEventCommonSettingDAO)
    def clueDefaultSettingService = Mock(ClueDefaultSettingService)
    def crmV2MappingManager = Mock(CrmV2MappingManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def conferenceService = Mock(ConferenceService)

    def campaignMergeDataManager = new CampaignMergeDataManager(customizeFormDataUserDAO: customizeFormDataUserDAO,
            customizeFormDataDAO: customizeFormDataDAO,
            customizeFormDataManager: customizeFormDataManager,
            marketingEventManager: marketingEventManager,
            activityDAO: activityDAO,
            activityManager: activityManager,
            campaignMergeDataDAO: campaignMergeDataDAO,
            crmV2Manager: crmV2Manager,
            redisManager: redisManager,
            conferenceManager: conferenceManager,
            customizeTicketManager: customizeTicketManager,
            conferenceDAO: conferenceDAO,
            activityEnrollDataDAO: activityEnrollDataDAO,
            customizeTicketDAO: customizeTicketDAO,
            wxTicketReceiveDAO: wxTicketReceiveDAO,
            eieaConverter: eieaConverter,
            objectDescribeCrmService: objectDescribeCrmService,
            conferenceInvitationUserDAO: conferenceInvitationUserDAO,
            metadataControllerServiceManager: metadataControllerServiceManager,
            memberAccessibleCampaignDAO: memberAccessibleCampaignDAO,
            campaignPayOrderDao: campaignPayOrderDao,
            marketingLiveDAO: marketingLiveDAO,
            triggerInstanceManager: triggerInstanceManager,
            userMarketingAccountDAO: userMarketingAccountDAO,
            userMarketingAccountManager: userMarketingAccountManager,
            userMarketingBrowserUserRelationDao: userMarketingBrowserUserRelationDao,
            userMarketingMiniappAccountRelationDao: userMarketingMiniappAccountRelationDao,
            userMarketingWxServiceAccountRelationDao: userMarketingWxServiceAccountRelationDao,
            fileV2Manager: fileV2Manager,
            hexagonPageDAO: hexagonPageDAO,
            marketingEventCommonSettingDAO: marketingEventCommonSettingDAO,
            clueDefaultSettingService: clueDefaultSettingService,
            crmV2MappingManager: crmV2MappingManager,
            crmMetadataManager: crmMetadataManager,
            conferenceService: conferenceService)

    @Unroll
    def "addCampaignMergeDataByUserEnroll"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> customizeFormDataUserEntity
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> customizeFormDataEntity
        activityManager.getActivityIdByObject(*_) >> "id"
        activityDAO.getById(*_) >> activityEntity
        customizeFormDataManager.getFormEnrollDataBindObject(*_) >> bindObjectMap
        marketingEventCommonSettingDAO.getSettingByEa(*_) >> new MarketingEventCommonSettingEntity(openMergePhone: openMergePhone)
        campaignMergeDataDAO.getCampaignMergeDataByBindObject(*_) >> campaignMergeDataEntity
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> campaignMergeDataEntityList
        def spy = Spy(campaignMergeDataManager)
        spy.getCampaignMergeDataByRule(*_) >> new CampaignMergeDataEntity(id: "id")
        spy.saveCampaignDataAndAddCrmObj(*_) >> "id"
        spy.bindFormDataUserCrmObj(*_) >> { printf "dd" }
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 1
        activityEnrollDataDAO.deleteActivityEnrollDataFormDataUser(*_) >> 1
        customizeTicketDAO.deleteCustomizeTicketFormDataUser(*_) >> 1
        activityEnrollDataDAO.updateActivityEnrollDataFormDataUser(*_) >> 1
        customizeTicketDAO.updateCustomizeTicketFormDataUser(*_) >> 1
        customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(*_) >> { printf "dd" }
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity(campaignMembersObjId: "Id")
        crmV2Manager.getDetail(*_) >> new ObjectData()
        when:
        spy.addCampaignMergeDataByUserEnroll("id", true)
        then:
        noExceptionThrown()
        where:
        customizeFormDataUserEntity                                                                                                   | customizeFormDataEntity       | activityEntity                                  | bindObjectMap      | campaignMergeDataEntity       | campaignMergeDataEntityList     | openMergePhone
        null                                                                                                                          | null                          | null                                            | null               | null                          | null                            | false
        new CustomizeFormDataUserEntity()                                                                                             | null                          | null                                            | null               | null                          | null                            | false
        new CustomizeFormDataUserEntity(objectType: 13)                                                                               | new CustomizeFormDataEntity() | null                                            | null               | null                          | null                            | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll())                                 | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | ["LeadsObj": "id"] | null                          | null                            | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"))                     | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | ["LeadsObj": "id"] | null                          | [new CampaignMergeDataEntity()] | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"))                     | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | ["LeadsObj": "id"] | null                          | [new CampaignMergeDataEntity()] | true
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"), campaignId: "id22") | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | ["LeadsObj": "id"] | null                          | [new CampaignMergeDataEntity()] | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll())                                 | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | ["LeadsObj": "id"] | null                          | [new CampaignMergeDataEntity()] | true
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll())                                 | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | ["LeadsObj": "id"] | new CampaignMergeDataEntity() | [new CampaignMergeDataEntity()] | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"))                     | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | null               | new CampaignMergeDataEntity() | [new CampaignMergeDataEntity()] | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"), campaignId: "id")   | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | null               | new CampaignMergeDataEntity() | []                              | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"))                     | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | null               | new CampaignMergeDataEntity() | []                              | false
        new CustomizeFormDataUserEntity(objectType: 13, submitContent: new CustomizeFormDataEnroll(phone: "110"), campaignId: "id22") | new CustomizeFormDataEntity() | new ActivityEntity(marketingEventId: "eventId") | null               | new CampaignMergeDataEntity() | []                              | false
    }

    @Unroll
    def "triggerCampaignEnrollAction"() {
        given:
        conferenceDAO.getConferenceByEaAndMarketingEventId(*_) >> conference
        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(*_) >> { printf "ddd" }
        triggerInstanceManager.startInstanceByCampaignIdAndSceneAndTargetObject(*_) >> { printf "ddd" }
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> marketingLive
        hexagonPageDAO.getById(*_) >> new HexagonPageEntity()
        when:
        campaignMergeDataManager.triggerCampaignEnrollAction(new CampaignMergeDataEntity(), new CustomizeFormDataUserEntity(objectType: objetType, objectId: "id"))
        then:
        noExceptionThrown()
        where:
        conference                             | marketingLive             | objetType
        new ActivityEntity(enrollReview: true) | new MarketingLiveEntity() | null
        null                                   | null                      | 27
        null                                   | null                      | 26
    }

    @Shared
    def shareObjectData = new ObjectData()

    @Unroll
    def "updateCampaignMergeDataByCampaignObj"() {
        given:
        campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(*_) >> new ActivityEnrollDataEntity()
        campaignMergeDataDAO.getCampaignMergeDataByObjId(*_) >> new CampaignMergeDataEntity()
        activityEnrollDataDAO.updateSignInStatusById(*_) >> 1
        activityEnrollDataDAO.updateApprovalStatusById(*_) >> 1
        conferenceDAO.getConferenceByEaAndMarketingEventId(*_) >> new ActivityEntity()
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 1L
        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(*_) >> { printf "dd" }
        shareObjectData.put("sign_in_status", signStatus)
        shareObjectData.put("approval_status", approvalStatus)
        when:
        campaignMergeDataManager.updateCampaignMergeDataByCampaignObj("ea", body)
        then:
        noExceptionThrown()
        where:
        body                                                     | signStatus | approvalStatus
        new CrmEventDTO.Body(afterTriggerData: new ObjectData()) | null       | null
        new CrmEventDTO.Body(afterTriggerData: shareObjectData)  | "test"     | null
        new CrmEventDTO.Body(afterTriggerData: shareObjectData)  | "sighed"   | "test"
        new CrmEventDTO.Body(afterTriggerData: shareObjectData)  | "sighed"   | "approved"
        new CrmEventDTO.Body(afterTriggerData: shareObjectData)  | "sighed"   | "rejected"
    }

    @Unroll
    def "deleteCampaignMergeDataByCampaignObj"() {
        given:
        conferenceManager.deleteParticipants(*_) >> null
        campaignMergeDataDAO.getCampaignMergeDataByObjId(*_) >> campaignMergeDataEntity
        when:
        campaignMergeDataManager.deleteCampaignMergeDataByCampaignObj("ea", "id")
        then:
        noExceptionThrown()
        where:
        campaignMergeDataEntity << [null, new CampaignMergeDataEntity()]
    }

    @Unroll
    def "addCampaignMergeDataByCampaignObj"() {
        given:
        metadataControllerServiceManager.detail(*_) >> detailMock
        crmV2Manager.getDetail(*_) >> crmDetailMock
        customizeFormDataUserDAO.getCustomizeFormDataUserByLeadIdOrderByCreateTime(*_) >> new CustomizeFormDataUserEntity()
        customizeFormDataUserDAO.getContactOrCustomerByTypeAndId(*_) >> new CustomizeFormDataUserEntity()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> getCustomizeFormDataByIdMock
        crmV2MappingManager.createCustomizeFormDataToCampaignFieldDataMap(*_) >> []
        crmV2Manager.updateCampaign(*_) >> null;
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity()
        conferenceManager.createConferenceEnrollAttachedInfoByCampaignObj(*_) >> null
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 1L
        marketingEventCommonSettingDAO.getSettingByEa(*_) >> new MarketingEventCommonSettingEntity()
        def spy = Spy(campaignMergeDataManager)
        spy.addCampaignDataByLock(*_) >> "aa"
        when:
        spy.addCampaignMergeDataByCampaignObj("ea", 1000, "aaa", objectDataArg)
        then:
        noExceptionThrown()
        where:
        objectDataArg                                                                                                                                                                                                                               | detailMock | crmDetailMock                                                                                                 | getCustomizeFormDataByIdMock
        null                                                                                                                                                                                                                                        | null       | null                                                                                                          | null
        new ObjectData(["_id": "id1", "marketing_event_id": "marketingEventId1", "campaign_members_type": "LeadsObj"])                                                                                                                              | null       | null                                                                                                          | new CustomizeFormDataEntity()
        new ObjectData(["_id": "id1", "marketing_event_id": "marketingEventId1", "campaign_members_type": "LeadsObj", "campaign_members_status": "sighed", "leads_id": "leadsId1"])                                                                 | null       | null                                                                                                          | new CustomizeFormDataEntity()
        new ObjectData(["_id": "id1", "marketing_event_id": "marketingEventId1", "campaign_members_type": "LeadsObj", "campaign_members_status": "sighed", "leads_id": "leadsId1", "create_time": (System.currentTimeMillis() - 24 * 3600 * 1000)]) | null       | new ObjectData("mobile": "131", "tel": "132", "create_time": (System.currentTimeMillis() - 48 * 3600 * 1000)) | new CustomizeFormDataEntity()
    }

    @Unroll
    def "addCampaignMergeDataByMember"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        memberAccessibleCampaignDAO.addMemberAccessibleCampaignData(*_) >> 0
        memberAccessibleCampaignDAO.updateMemberAccessibleCampaign(*_) >> null
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(*_) >> new MemberAccessibleCampaignEntity(campaignId: "campaignId1")
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 0
        memberAccessibleCampaignDAO.bindMemberAccessibleCampaignData(*_) >> null
        activityEnrollDataDAO.deleteActivityEnrollDataFormDataUser(*_) >> 0
        customizeTicketDAO.deleteCustomizeTicketFormDataUser(*_) >> 0
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 0
        memberAccessibleCampaignDAO.bindMemberAccessibleCampaignData(*_) >> null
        activityEnrollDataDAO.updateActivityEnrollDataFormDataUser(*_) >> 0
        customizeTicketDAO.updateCustomizeTicketFormDataUser(*_) >> 0
        memberAccessibleCampaignDAO.bindMemberAccessibleCampaignData(*_) >> null
        def spy = Spy(campaignMergeDataManager)
        spy.handleMemberMergeData(*_) >> handleMemberMergeDataMock
        when:
        String result = spy.addCampaignMergeDataByMember(arg)
        then:
        result == resultMock
        where:
        arg                                                                                                                                                                                                                                                                                                                                         | resultMock    | handleMemberMergeDataMock
        null                                                                                                                                                                                                                                                                                                                                        | null          | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer()
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(ea: "ea", marketingEventId: "marketingEventId1", memberId: "memberId1", name: "name1", objectId: "objectId1")                                                                                                                                                         | null          | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer()
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(memberAccessibleCampaignId: "memberAccessibleCampaignId1", ea: "ea", marketingEventId: "marketingEventId1", memberId: "memberId1", name: "name1", objectId: "objectId1", objectType: 1, saveCrmStatus: 1)                                                             | "campaignId1" | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer()
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(ea: "ea", marketingEventId: "marketingEventId1", memberId: "memberId1", name: "name1", objectId: "objectId1", objectType: 1, saveCrmStatus: 1)                                                                                                                        | null          | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer()
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(memberAccessibleCampaignId: "memberAccessibleCampaignId1", ea: "ea", marketingEventId: "marketingEventId1", memberId: "memberId1", name: "name1", objectId: "objectId1", objectType: 1, saveCrmStatus: 1, needCreateDataIfNoBindCRM: true)                            | null          | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer()
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(memberAccessibleCampaignId: "memberAccessibleCampaignId1", ea: "ea", marketingEventId: "marketingEventId1", memberId: "memberId1", name: "name1", objectId: "objectId1", objectType: 1, saveCrmStatus: 1, needCreateDataIfNoBindCRM: true, campaignId: "campaignId1") | "campaignId2" | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(campaignMergeDataId: "campaignId2")
    }

    @Unroll
    def "bindSamePhoneAndWithoutBindData"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> [new CampaignMergeDataEntity(campaignMembersObjId: "c1"), new CampaignMergeDataEntity(campaignMembersObjId: "c2")]
        customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndPhone(*_) >> getCustomizeFormDataUserByEventIdAndPhoneMock
        customizeFormDataManager.getFormEnrollDataBindObject(*_) >> getFormEnrollDataBindObjectMock
        def spy = Spy(campaignMergeDataManager)
        spy.batchBindCustomizeFormData(*_) >> null
        when:
        spy.bindSamePhoneAndWithoutBindData(arg1, "ea", "m1", arg2)
        then:
        noExceptionThrown()
        where:
        arg1  | arg2                          | getCustomizeFormDataUserByEventIdAndPhoneMock                                                                                        | getFormEnrollDataBindObjectMock
        null  | null                          | null                                                                                                                                 | null
        "159" | new CampaignMergeDataEntity() | [new CustomizeFormDataUserEntity(marketingEventId: "m1", campaignId: "c1"), new CustomizeFormDataUserEntity(marketingEventId: "m2")] | null
        "159" | new CampaignMergeDataEntity(bindCrmObjectType: 1, bindCrmObjectId: "b") | [new CustomizeFormDataUserEntity(marketingEventId: "m1", campaignId: "c1"), new CustomizeFormDataUserEntity(marketingEventId: "m2")] | ["1":"b"]

    }

    @Unroll
    def "batchBindCustomizeFormData"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(*_) >> [new CustomizeFormDataUserEntity()]
        customizeFormDataUserDAO.bindCustomizeFormDataUserAndCampaign(*_) >> null
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 0
        activityEnrollDataDAO.deleteActivityEnrollDataFormDataUser(*_) >> 0
        customizeTicketDAO.deleteCustomizeTicketFormDataUser(*_) >> 0
        def spy = Spy(campaignMergeDataManager)
        spy.bindFormDataUserCrmObj(*_) >> null
        when:
        spy.batchBindCustomizeFormData(arg,  new CampaignMergeDataEntity(id:"id1"), true)
        then:
        noExceptionThrown()
        where:
        arg << [null, [new CampaignMergeDataEntity(id:"id1"), new CampaignMergeDataEntity(id:"id2")]]

    }

    @Unroll
    def "handleMemberMergeData"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByBindObject(*_) >> getCampaignMergeDataByBindObjectMock
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> getCampaignMergeDataByPhoneMock
        def spy = Spy(campaignMergeDataManager)
        spy.saveCampaignDataAndAddCrmObj(*_) >> "a"
        spy.getCampaignMergeDataByRule(*_) >> new CampaignMergeDataEntity()
        when:
        CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer result = spy.handleMemberMergeData(arg)
        then:
        result == resultMock
        where:
        arg                                                                                                                                 | getCampaignMergeDataByBindObjectMock | getCampaignMergeDataByPhoneMock | resultMock
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(bindObjectType: 1, bindObjectId: "objectId1")                 | null                                 | null                            | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(campaignMergeDataId: "a", createNewData: true)
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(bindObjectType: 1, bindObjectId: "objectId1", "phone": "133") | null                                 | null                            | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(campaignMergeDataId: "a", createNewData: true)
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(bindObjectType: 1, bindObjectId: "objectId1", "phone": "133") | null                                 | [new CampaignMergeDataEntity()] | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(bindOldData: true)
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(bindObjectType: 1, bindObjectId: "objectId1", "phone": "133") | new CampaignMergeDataEntity()        | [new CampaignMergeDataEntity()] | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(bindOldData: true)
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer("phone": "133")                                               | new CampaignMergeDataEntity()        | [new CampaignMergeDataEntity()] | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(bindOldData: true)
        new CampaignMergeDataManager.AddCampaignMergeDataByMemberArgContainer(needCreateDataIfNoBindCRM: true)                              | new CampaignMergeDataEntity()        | [new CampaignMergeDataEntity()] | new CampaignMergeDataManager.AddCampaignMergeDataByMemberResultContainer(campaignMergeDataId: "a", bindOldData: false, createNewData: true)

    }

    @Unroll
    def "getCampaignMergeDataByRule"() {
        when:
        campaignMergeDataManager.getCampaignMergeDataByRule(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                    | resultMock
        null                                                                                                   | new CampaignMergeDataEntity()
        [new CampaignMergeDataEntity()]                                                                        | new CampaignMergeDataEntity()
        [new CampaignMergeDataEntity(bindCrmObjectType: 1), new CampaignMergeDataEntity(bindCrmObjectType: 1)] | new CampaignMergeDataEntity()
    }

    @Unroll
    def "saveCampaignDataAndAddCrmObj"() {
        given:
        crmV2Manager.addCampaignMembersObjByLock(*_) >> addCampaignMembersObjByLockMock
        def spy = Spy(campaignMergeDataManager)
        spy.getCampaignMergeObjIdByEntity(*_) >> null
        spy.campaignMergeDataEntityToCampaignMergeObjMap(*_) >> ["aa": "bb"]
        when:
        String result = spy.saveCampaignDataAndAddCrmObj(arg1, true, CampaignMembersObjMemberStatusEnum.TO_BE_INVITED, "a", 1,null,null)
        then:
        result == resultMock
        where:
        arg1                          | addCampaignMembersObjByLockMock | resultMock
        null                          | null                            | null
        new CampaignMergeDataEntity() | null                            | null
        new CampaignMergeDataEntity() | "id"                            | null
    }

    @Unroll
    def "bindFormDataUserCrmObj"() {
        when:
        campaignMergeDataManager.bindFormDataUserCrmObj(arg, new CustomizeFormDataUserEntity())
        then:
        noExceptionThrown()
        where:
        arg << [new CampaignMergeDataEntity(bindCrmObjectType: CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType()), new CampaignMergeDataEntity(bindCrmObjectType: CampaignMergeDataObjectTypeEnum.CONTACT_OBJ.getType())]
    }

    @Unroll
    def "campaignMergeDataEntityToCampaignMergeObjMap"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        customizeFormDataManager.checkAddLeadsObjectAuth(*_) >> checkAddLeadsObjectAuthMock
        clueDefaultSettingService.getClueCreator(*_) >> 1001
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity(enrollReview: false)
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2, signInTime: new Date())]
        when:
        campaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap("ea", arg, CampaignMembersObjMemberStatusEnum.TO_BE_INVITED, "id1", arg2)
        then:
        noExceptionThrown()
        where:
        arg                                                                                 | arg2 | getDetailMock                                  | checkAddLeadsObjectAuthMock | resultMock
        new CampaignMergeDataEntity(bindCrmObjectType: 4)                                   | null | null                                           | true                        | Maps.newHashMap()
        new CampaignMergeDataEntity(bindCrmObjectType: 1)                                   | null | null                                           | true                        | Maps.newHashMap()
        new CampaignMergeDataEntity(bindCrmObjectType: 1, marketingPromotionSourceId: "11") | null | new ObjectData("company": "a", "name": "name") | true                        | Maps.newHashMap()
        new CampaignMergeDataEntity(bindCrmObjectType: 1, marketingPromotionSourceId: "11") | 1001 | new ObjectData("company": "a", "name": "name") | false                       | Maps.newHashMap()
    }

    @Unroll
    def "campaignMergeDataEntityToCampaignMergeObjMap2"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        when:
        campaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg                                               | getDetailMock
        new CampaignMergeDataEntity(bindCrmObjectType: 4) | null
        new CampaignMergeDataEntity(bindCrmObjectType: 1) | null
        new CampaignMergeDataEntity(bindCrmObjectType: 1) | new ObjectData("company": "a", "name": "name")
    }

    @Unroll
    def "getCampaignMergeObjIdByEntity"() {
        when:
        campaignMergeDataManager.getCampaignMergeObjIdByEntity("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg << [new CampaignMergeDataEntity(bindCrmObjectType: 4), new CampaignMergeDataEntity(bindCrmObjectType: 2)]
    }

    @Unroll
    def "addCampaignDataByLock"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        campaignMergeDataDAO.getCampaignMergeDataByObjId(*_) >> new CampaignMergeDataEntity()
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        campaignMergeDataDAO.addCampaignMergeData(*_) >> 0
        when:
        campaignMergeDataManager.addCampaignDataByLock(arg1, arg2, arg3)
        then:
        noExceptionThrown()
        where:
        arg1                                                                                                 | arg2 | arg3
        null                                                                                                 | true | 0
        new CampaignMergeDataEntity(campaignMembersObjId: "a", bindCrmObjectId: "id1", bindCrmObjectType: 1) | true | 2
        new CampaignMergeDataEntity(campaignMembersObjId: "a")                                               | true | 2
    }

    @Unroll
    def "addCampaignDataOnlyUnLock"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity(marketingEventId: "aa", bindCrmObjectId: "aa", bindCrmObjectType: 1)
        campaignMergeDataDAO.getCampaignMergeDataByBindObject(*_) >> null
        campaignMergeDataDAO.addCampaignMergeData(*_) >> 0
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        when:
        campaignMergeDataManager.addCampaignDataOnlyUnLock(new CampaignMergeDataEntity(campaignMembersObjId: "11", bindCrmObjectId: "aa", bindCrmObjectType: 1, marketingEventId: "aa"))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "campaignIdToActivityEnrollId"() {
        given:
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> getActivityEnrollDataByFormDataUserIdsMock
        when:
        campaignMergeDataManager.campaignIdToActivityEnrollId(arg)
        then:
        noExceptionThrown()
        where:
        arg    | getActivityEnrollDataByFormDataUserIdsMock
        []     | null
        ["11"] | null
        ["11"] | [new ActivityEnrollDataEntity(id: "id1")]
    }

    @Unroll
    def "campaignIdToActivityEnrollIdMap"() {
        given:
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> getActivityEnrollDataByFormDataUserIdsMock
        when:
        campaignMergeDataManager.campaignIdToActivityEnrollIdMap(arg)
        then:
        noExceptionThrown()
        where:
        arg    | getActivityEnrollDataByFormDataUserIdsMock
        []     | null
        ["11"] | null
        ["11"] | [new ActivityEnrollDataEntity(id: "id1")]

    }

    @Unroll
    def "activityEnrollIdToCampaignId"() {
        given:
        activityEnrollDataDAO.getActivityEnrollDataByIds(*_) >> getActivityEnrollDataByIdsMock
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> getCampaignMergeDataByIdsMock
        when:
        campaignMergeDataManager.activityEnrollIdToCampaignId(arg)
        then:
        noExceptionThrown()
        where:
        arg    | getActivityEnrollDataByIdsMock            | getCampaignMergeDataByIdsMock
        []     | null                                      | null
        ["11"] | null                                      | null
        ["11"] | [new ActivityEnrollDataEntity(id: "id1")] | [new CampaignMergeDataEntity()]
        ["11"] | [new ActivityEnrollDataEntity(id: "id1")] | []
    }

    @Unroll
    def "getLatestActivityEnrollDataByCampaignId"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserListByMarketingEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "campaignId1")]
        marketingEventManager.getMarketingEventData(*_) >> new MarketingEventData(parentId: "id1")
        def spy = Spy(campaignMergeDataManager)
        spy.getParentMarketingEventEnrollDataByCampaignId(*_) >> new HashMap<String, CustomizeFormDataUserEntity>()
        when:
        spy.getLatestActivityEnrollDataByCampaignId("ea", "marketingEventId", "conferenceId", arg)
        then:
        noExceptionThrown()
        where:
        arg << [null, ["11", "22"]]

    }

    @Unroll
    def "getParentMarketingEventEnrollDataByCampaignId"() {
        given:
        marketingEventManager.getMarketingEventData(*_) >> getMarketingEventDataMock
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> getCampaignMergeDataByIdsMock
        customizeFormDataUserDAO.getCustomizeFormDataUserEntityByContentPhone(*_) >> getCustomizeFormDataUserEntityByContentPhoneMock
        when:
        campaignMergeDataManager.getParentMarketingEventEnrollDataByCampaignId("ea", "marketingEventId", arg)
        then:
        noExceptionThrown()
        where:
        arg    | getMarketingEventDataMock                                                                     | getCampaignMergeDataByIdsMock                          | getCustomizeFormDataUserEntityByContentPhoneMock
        []     | new MarketingEventData(parentId: "11")                                                        | null                                                   | null
        ["aa"] | new MarketingEventData(parentId: "11", eventType: TARGET_CROWD_OPERATION_ONCE.getEventType()) | null                                                   | null
        ["aa"] | new MarketingEventData(parentId: "11", eventType: MULTIVENUE_MARKETING.getEventType())        | null                                                   | null
        ["aa"] | new MarketingEventData(parentId: "11", eventType: MULTIVENUE_MARKETING.getEventType())        | [new CampaignMergeDataEntity(id: "id1", phone: "122")] | null
        ["aa"] | new MarketingEventData(parentId: "11", eventType: MULTIVENUE_MARKETING.getEventType())        | [new CampaignMergeDataEntity(id: "id1", phone: "122")] | [new CustomizeFormDataUserEntity(submitContent: new CustomizeFormDataEnroll(phone: "122"))]
    }

    @Unroll
    def "getActivityEnrollDataByCampaignId"() {
        given:
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> getActivityEnrollDataByFormDataUserIdsMock
        when:
        campaignMergeDataManager.getActivityEnrollDataByCampaignId(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getActivityEnrollDataByFormDataUserIdsMock
        null | null
        "1"  | null
        "1"  | [new ActivityEnrollDataEntity(id: "id1")]
    }

    @Unroll
    def "getPhoneByObject"() {
        when:
        String result = campaignMergeDataManager.getPhoneByObject(arg)
        then:
        result == resultMock
        where:
        arg                             | resultMock
        null                            | null
        new ObjectData("mobile": "122") | "122"
        new ObjectData("tel": "133")    | "133"
        new ObjectData()                | null
    }

    @Unroll
    def "addCampaignMembersObjField"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> getAllObjectFieldDescribesListMock
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        campaignMergeDataManager.addCampaignMembersObjField(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getAllObjectFieldDescribesListMock
        null | null
        "ea" | [new CrmUserDefineFieldVo()]
    }

    @Unroll
    def "appendCampaignMembersLiveData"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> getAllObjectFieldDescribesListMock
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        campaignMergeDataManager.appendCampaignMembersLiveData(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getAllObjectFieldDescribesListMock
        null | null
        "ea" | [new CrmUserDefineFieldVo()]
    }

    @Unroll
    def "appendCampaignMembersPayData"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> getAllObjectFieldDescribesListMock
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        campaignMergeDataManager.appendCampaignMembersPayData(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getAllObjectFieldDescribesListMock
        null | null
        "ea" | [new CrmUserDefineFieldVo()]
    }

    @Unroll
    def "appendCampaignMembersSpreadData"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> getAllObjectFieldDescribesListMock
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        campaignMergeDataManager.appendCampaignMembersSpreadData(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getAllObjectFieldDescribesListMock
        null | null
        "ea" | [new CrmUserDefineFieldVo()]
    }

    @Unroll
    def "updateCampaignMembersObjSpreadData"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> getAllObjectFieldDescribesListMock
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        campaignMergeDataManager.updateCampaignMembersObjSpreadData(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getAllObjectFieldDescribesListMock
        null | null
        "ea" | [new CrmUserDefineFieldVo(fieldName: CampaignConstants.SPREAD_USER_ID_API_NAME)]
    }

    @Unroll
    def "appendCampaignMemberDataSaveStatus"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> getAllObjectFieldDescribesListMock
        objectDescribeCrmService.addDescribeCustomField(*_) >> null
        when:
        campaignMergeDataManager.appendCampaignMemberDataSaveStatus(arg)
        then:
        noExceptionThrown()
        where:
        arg  | getAllObjectFieldDescribesListMock
        null | null
        "ea" | [new CrmUserDefineFieldVo()]
    }

    @Unroll
    def "updateCampaignMergeDataInviteStatus"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        campaignMergeDataDAO.updateCampaignMergeDataInviteStatus(*_) >> 0
        crmV2Manager.getDetailIgnoreError(*_) >> getDetailIgnoreErrorMock
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity()
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(*_) >> [new CustomizeFormDataUserEntity()]
        crmV2Manager.editCampaignMembersObj(*_) >> null
        when:
        campaignMergeDataManager.updateCampaignMergeDataInviteStatus(arg1, "1", arg2)
        then:
        noExceptionThrown()
        where:
        arg1 | arg2 | getCampaignMergeDataByIdMock                            | getDetailIgnoreErrorMock
        1    | true | null                                                    | null
        1    | true | new CampaignMergeDataEntity(campaignMembersObjId: "aa") | null
        0    | true | new CampaignMergeDataEntity(campaignMembersObjId: "aa") | null
        0    | true | new CampaignMergeDataEntity(campaignMembersObjId: "aa") | new ObjectData("sign_in_status": "1", "approval_status": "2")
        2    | true | new CampaignMergeDataEntity(campaignMembersObjId: "aa") | new ObjectData("sign_in_status": "2", "approval_status": "2")
    }

    @Unroll
    def "updateConferenceReviewStatus"() {
        given:
        activityEnrollDataDAO.updateReviewStatusByIds(*_) >> 0
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity()]
        crmV2Manager.editCampaignMembersObj(*_) >> null
        def spy = Spy(campaignMergeDataManager)
        spy.updateConferenceReviewStatusInThread(*_) >> null
        when:
        spy.updateConferenceReviewStatus(arg, 1, "msg", true)
        then:
        noExceptionThrown()
        where:
        arg << [null, ["id"]]
    }

    @Unroll
    def "updateConferenceReviewStatusInThread"() {
        given:
        activityEnrollDataDAO.updateReviewStatusByIds(*_) >> 0
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(campaignMembersObjId: "id1")]
        crmV2Manager.editCampaignMembersObj(*_) >> null
        def spy = Spy(campaignMergeDataManager)
        spy.activityEnrollIdToCampaignId(*_) >> ["aa"]
        when:
        spy.updateConferenceReviewStatusInThread(["id"], ConferenceEnrollReviewStatusEnum.PENDING_REVIEW)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "updateCampaignMergeObjUserGroup"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity()]
        when:
        campaignMergeDataManager.updateCampaignMergeObjUserGroup(arg, ["name"])
        then:
        noExceptionThrown()
        where:
        arg << [null, ["Id"]]
    }

    @Unroll
    def "updateParticipantsPasscode"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        crmV2Manager.updateCampaign(*_) >> null
        when:
        campaignMergeDataManager.updateParticipantsPasscode(arg, "1111")
        then:
        noExceptionThrown()
        where:
        arg   | getCampaignMergeDataByIdMock
        null  | null
        "id1" | null
        "id1" | new CampaignMergeDataEntity(campaignMembersObjId: "aa")
    }

    @Unroll
    def "updateSignInStatus"() {
        given:
        when:
        campaignMergeDataManager.updateSignInStatus(arg, 1, true)
        then:
        noExceptionThrown()
        where:
        arg << [null, ["id"]]
    }

    @Unroll
    def "asyncupdateSignInStatus"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(campaignMembersObjId: "id1")]
        crmV2Manager.editCampaignMembersObj(*_) >> null
        campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(*_) >> new ActivityEnrollDataEntity()
        customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignId(*_) >> new CustomizeFormDataUserEntity()
        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(*_) >> null
        conferenceDAO.getConferenceByEaAndMarketingEventId(*_) >> new ActivityEntity()
        def spy = Spy(campaignMergeDataManager)
        spy.activityEnrollIdToCampaignId(*_) >> activityEnrollIdToCampaignIdMock
        when:
        spy.asyncUpdateSignInStatus(arg, 2, true)
        then:
        noExceptionThrown()
        where:
        arg    | activityEnrollIdToCampaignIdMock
        null   | null
        ["id"] | null
        ["id"] | ["aa"]
    }

    @Unroll
    def "updateCampaignMembersObjByCampaignMergeId"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        crmV2Manager.editCampaignMembersObj(*_) >> null
        when:
        campaignMergeDataManager.updateCampaignMembersObjByCampaignMergeId("id", ["a": "a"])
        then:
        noExceptionThrown()
        where:
        getCampaignMergeDataByIdMock << [null, new CampaignMergeDataEntity(campaignMembersObjId: "1")]

    }

    @Unroll
    def "addCampaignMembersObjByBindObj"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        crmV2Manager.addCampaignMembersObj(*_) >> null
        when:
        campaignMergeDataManager.addCampaignMembersObjByBindObj([new AddCampaignMembersObjVO.MemberObjDetail(apiName: "LeadsObj"), new AddCampaignMembersObjVO.MemberObjDetail(apiName: "c")], "ea", marketingEventIdArg, "1", 100)
        then:
        noExceptionThrown()
        where:
        marketingEventIdArg | getDetailMock
        null                | null
        "marketingEventId"  | null
        "marketingEventId"  | new ObjectData("company": "c", "name": "a", "email": "b")
    }

    @Unroll
    def "createOrUpdateConferenceInvitationUser"() {
        given:
        conferenceInvitationUserDAO.getConferenceInvitationUserUnique(*_) >> getConferenceInvitationUserUniqueMock
        conferenceInvitationUserDAO.addConferenceInvitationUser(*_) >> 0
        conferenceInvitationUserDAO.updateConferenceInvitationUserStartTime(*_) >> 0
        when:
        campaignMergeDataManager.createOrUpdateConferenceInvitationUser("ea", "conferenceId", 1000, "campaignMergeDataId", 1L, 2L, "id")
        then:
        noExceptionThrown()
        where:
        getConferenceInvitationUserUniqueMock << [null, new ConferenceInvitationUserEntity()]
    }

    @Unroll
    def "getUserEmailByCampaignId"() {
        given:
        campaignMergeDataDAO.queryCampaignMailFromSubmitContent(*_) >> [new CampaignDataMailDTO(campaignId: "id")]
        campaignMergeDataDAO.queryCampaignFromMemberContent(*_) >> [new CampaignDataMailDTO(memberId: "memberId", campaignId: "id1")]
        crmV2Manager.getList(*_) >> getListMock
        def spy = Spy(campaignMergeDataManager)
        spy.getObjectDataByCampaignMergeData(*_) >> ["a": new ObjectData("email": "email")]
        when:
        spy.getUserEmailByCampaignId("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg            | getListMock
        null           | null
        ["id"]         | null
        ["id1"]        | null
        ["id1", "id2"] | new Page(dataList: [new ObjectData("_id": "memberId", "memberId": "memberId", "campaignId": "id1", "email": "email")])
    }

    @Unroll
    def "getMarketingUserIdByCampaignIds"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> getCampaignMergeDataByIdsMock
        eieaConverter.enterpriseAccountToId(*_) >> 1
        userMarketingAccountDAO.getUserMarketingAccountByPhones(*_) >> [new UserMarketingAccountEntity(id: "uid", phone: "133"), new UserMarketingAccountEntity(id: "uid2", phone: "156")]
        userMarketingAccountManager.associateAncGetObjectIdToMarketingUserIdMap(*_) >> ["leadId1": "uid3"]
        memberAccessibleCampaignDAO.getLatestAccessibleMemberByCampaignIds(*_) >> [new MemberAccessibleCampaignEntity()]
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(*_) >> [new CustomizeFormDataUserEntity(fingerPrint: "f1"), new CustomizeFormDataUserEntity(uid: "userId1"), new CustomizeFormDataUserEntity(wxAppId: "appid1", openId: "o1")]
        userMarketingBrowserUserRelationDao.listByBrowserUserIds(*_) >> [new UserMarketingBrowserUserRelationEntity(browserUserId: "f1", userMarketingId: "u1")]
        userMarketingMiniappAccountRelationDao.listAllByUids(*_) >> [new UserMarketingMiniappAccountRelationEntity(uid: "userId1", userMarketingId: "u2")]
        userMarketingWxServiceAccountRelationDao.listByWxOpenIds(*_) >> [new UserMarketingWxServiceAccountRelationEntity(wxAppId: "appid1", wxOpenId: "o1", userMarketingId: "u3")]
        when:
        campaignMergeDataManager.getMarketingUserIdByCampaignIds("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg      | getCampaignMergeDataByIdsMock
        null     | [new CampaignMergeDataEntity(id: "id1", phone: "133"), new CampaignMergeDataEntity(id: "id12", phone: "155", bindCrmObjectType: 1, bindCrmObjectId: "leadId1")]
        ["cid1"] | [new CampaignMergeDataEntity(id: "id1", phone: "133")]
        ["cid1"] | [new CampaignMergeDataEntity(id: "id1", phone: "133"), new CampaignMergeDataEntity(id: "id12", phone: "155", bindCrmObjectType: 1, bindCrmObjectId: "leadId1"), new CampaignMergeDataEntity(id: "id13", phone: "159", bindCrmObjectType: 1, bindCrmObjectId: "leadId2")]

    }

    @Unroll
    def "getObjectDataByCampaignMergeData"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> getCampaignMergeDataByIdsMock
        crmV2Manager.getList(*_) >> getListMock
        when:
        campaignMergeDataManager.getObjectDataByCampaignMergeData(["c1", "c2"])
        then:
        noExceptionThrown()
        where:
        getCampaignMergeDataByIdsMock                                                                                             | getListMock
        null                                                                                                                      | null
        [new CampaignMergeDataEntity(), new CampaignMergeDataEntity(id: "id1", bindCrmObjectId: "leadid1", bindCrmObjectType: 1)] | null
        [new CampaignMergeDataEntity(), new CampaignMergeDataEntity(id: "id1", bindCrmObjectId: "leadid1", bindCrmObjectType: 1)] | new Page(total: 1, dataList: [new ObjectData("id": "leadid1", "email": "email")])
    }

    @Unroll
    def "countPayOrderNumber"() {
        given:
        campaignPayOrderDao.groupCountPayOrderByCampaignIds(*_) >> [new DataCount(id: "id1", count: 1)]
        when:
        campaignMergeDataManager.countPayOrderNumber("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg << [null, ["1"]]
    }

    @Unroll
    def "queryWxUserInfo"() {
        given:
        crmV2Manager.getWechatFanByOpenIds(*_) >> getWechatFanByOpenIdsMock
        fileV2Manager.getUrlByPath(*_) >> "url"
        when:
        campaignMergeDataManager.queryWxUserInfo("ea", "wxappid", arg)
        then:
        noExceptionThrown()
        where:
        arg        | getWechatFanByOpenIdsMock
        null       | null
        ["openid"] | null
        ["openid"] | ["m1": new ObjectData("name": "H")]
    }

    @Unroll
    def "getCampaignMergeCountByMarketingIds"() {
        given:
        campaignMergeDataDAO.getCampaignMergeCountByMarketingIds(*_) >> getCampaignMergeCountByMarketingIdsMock
        when:
        campaignMergeDataManager.getCampaignMergeCountByMarketingIds("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg     | getCampaignMergeCountByMarketingIdsMock
        null    | null
        ["id1"] | [new CampaignStatisticDTO(marketingEventId: "m1", campaignCount: 1)]
    }

    @Unroll
    def "queryCampaignMergeDataWithTry"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByBindObject(*_) >> getCampaignMergeDataByBindObjectMock
        when:
        campaignMergeDataManager.queryCampaignMergeDataWithTry("ea", "marketingEventId", 1, arg1, arg2, 1l)
        then:
        noExceptionThrown()
        where:
        arg1 | arg2 | getCampaignMergeDataByBindObjectMock
        null | 0    | null
        null | 1    | new CampaignMergeDataEntity()
        "id" | 1    | new CampaignMergeDataEntity(id: "id")
    }

    @Unroll
    def "updateSignInStatusAndUid"() {
        given:
        activityEnrollDataDAO.updateSignInTypeAndUidByIds(*_) >> 0
        def spy = Spy(campaignMergeDataManager)
        spy.asyncUpdateSignInStatusAndUid(*_) >> null
        when:
        spy.updateSignInStatusAndUid(activityEnrollDataId, 1, "uid")
        then:
        noExceptionThrown()
        where:
        activityEnrollDataId << [null, ["id"]]
    }

    @Unroll
    def "asyncUpdateSignInStatusAndUid"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(id: "id", campaignMembersObjId: "c1")]
        conferenceDAO.getConferenceByEaAndMarketingEventId(*_) >> new ActivityEntity()
        crmV2Manager.editCampaignMembersObj(*_) >> null
        conferenceDAO.getConferenceByEaAndMarketingEventId(*_) >> new ActivityEntity()
        campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(*_) >> new ActivityEnrollDataEntity()
        customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignId(*_) >> new CustomizeFormDataUserEntity()
        triggerInstanceManager.startInstanceByCampaignIdAndSceneMsgAndTriggerAction(*_) >> null
        def spy = Spy(campaignMergeDataManager)
        spy.activityEnrollIdToCampaignId(*_) >> ["id1"]
        when:
        spy.asyncUpdateSignInStatusAndUid(arg1, arg2, "uid");
        then:
        noExceptionThrown()
        where:
        arg1   | arg2
        null   | 8
        ["aa"] | 2

    }

    @Unroll
    def "updateCampaignMembersObj"() {
        given:
        crmV2Manager.editCampaignMembersObj(*_) >> null
        when:
        campaignMergeDataManager.updateCampaignMembersObj(null, null, null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getCampaignMembersObjByMaketingEventIds"() {
        given:
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> null
        when:
        campaignMergeDataManager.getCampaignMembersObjByMaketingEventIds("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg << [null, ["1"]]
    }

    @Unroll
    def "getCampaignMembersObjByMaketingEventIdsWithFields"() {
        given:
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> null
        when:
        List<ObjectData> result = campaignMergeDataManager.getCampaignMembersObjByMaketingEventIdsWithFields("ea", arg)
        then:
        result == resultMock
        where:
        arg   | resultMock
        null  | null
        ["1"] | null
    }

    @Unroll
    def "addCampaignMemberByCrmData"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        crmV2Manager.addCampaignMembersObjByLock(*_) >> "aa"
        def spy = Spy(campaignMergeDataManager)
        spy.campaignMergeDataEntityToCampaignMergeObjMap(*_) >> ["id1": new CampaignMergeDataEntity()]
        when:
        spy.addCampaignMemberByCrmData("ea", 1000, "m1", arg, "lead1")
        then:
        noExceptionThrown()
        where:
        arg | getDetailMock                | resultMock
        3   | null                         | null
        1   | null                         | null
        1   | new ObjectData("_id": "id1") | null
    }

    @Unroll
    def "listByIds"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> null
        when:
        List<CampaignMergeDataEntity> result = campaignMergeDataManager.listByIds("ea", ["id"])
        then:
        result == null
    }

    @Unroll
    def "handleCampaignMembersStatusChangeEvent"() {
        given:
        crmV2Manager.getDetailIgnoreError(*_) >> getDetailIgnoreErrorMock
        conferenceDAO.getConferenceByMarketingEventId(*_) >> getConferenceByMarketingEventId
        campaignMergeDataDAO.getCampaignMergeDataByObjId(*_) >> getCampaignMergeDataByObjIdMock
        redisManager.incCampaignMembersStatusChangeCount(*_) >> incCampaignMembersStatusChangeCountMock
        crmV2Manager.editCampaignMembersObj(*_) >> null
        campaignMergeDataDAO.queryActivityEnrollDataByCampaignMembersObjId(*_) >> queryActivityEnrollDataByCampaignMembersObjIdMock
        activityEnrollDataDAO.updateSignInTypeByIds(*_) >> 0
        conferenceService.changeConferenceParticipantsReviewStatus(*_) >> null
        def spy = Spy(campaignMergeDataManager)
        spy.updateConferenceReviewStatus(*_) >> null
        spy.updateCampaignMergeDataInviteStatus(*_) >> null
        campaignMergeDataManager.campaignStatusEnable = 1
        when:
        campaignMergeDataManager.handleCampaignMembersStatusChangeEvent("ea", arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                | getDetailIgnoreErrorMock                   | getConferenceByMarketingEventId | getCampaignMergeDataByObjIdMock | incCampaignMembersStatusChangeCountMock | queryActivityEnrollDataByCampaignMembersObjIdMock
        new CrmEventDTO.Body()                                                                             | null                                       | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1"))                               | null                                       | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 6)) | null                                       | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 1)) | null                                       | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 1)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 1)) | new ObjectData("marketing_event_id": "M1") | null                            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 1)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | null                            | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 1)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 2)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 3)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 4)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
        new CrmEventDTO.Body(afterTriggerData: new ObjectData("_id": "id1", "campaign_members_status": 5)) | new ObjectData("marketing_event_id": "M1") | new ActivityEntity()            | new CampaignMergeDataEntity()   | 1l                                      | new ActivityEnrollDataEntity()
    }

    @Unroll
    def "delete"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(*_) >> [new CustomizeFormDataUserEntity(saveCrmStatus: 0)]
        customizeFormDataUserDAO.deleteById(*_) >> 0
        crmV2Manager.bulkInvalidWithResult(*_) >> null
        activityEnrollDataDAO.getActivityEnrollDataById(*_) >> new ActivityEnrollDataEntity()
        customizeTicketDAO.getTicketByAssociationAndDataUserId(*_) >> new CustomizeTicketReceiveEntity()
        wxTicketReceiveDAO.getWxTicketReceiveByFormDataUserId(*_) >> new WxTicketReceiveEntity()
        activityDAO.decEnrollCount(*_) >> 0
        activityEnrollDataDAO.deleteById(*_) >> 0
        customizeTicketDAO.deleteCustomizeTicketReceiveById(*_) >> 0
        wxTicketReceiveDAO.deleteById(*_) >> 0
        memberAccessibleCampaignDAO.deleteMemberAccessibleCampaignDataByCampaignId(*_) >> null
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 0
        crmV2Manager.bulkInvalidWithResult(*_) >> null
        def spy = Spy(campaignMergeDataManager)
        spy.campaignIdToActivityEnrollId(*_) >> ["aa"]
        when:
        com.facishare.marketing.common.result.Result result = spy.delete(arg, "ea", 1000)
        then:
        result == resultMock
        where:
        arg  | getCampaignMergeDataByIdMock                            | resultMock
        null | null                                                    | com.facishare.marketing.common.result.Result.newSuccess()
        "id" | null                                                    | com.facishare.marketing.common.result.Result.newSuccess()
        "id" | new CampaignMergeDataEntity(campaignMembersObjId: "c1") | com.facishare.marketing.common.result.Result.newSuccess()

    }


}