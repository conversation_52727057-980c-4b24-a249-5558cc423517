package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg
import com.facishare.marketing.api.arg.MemberEnrollArg
import com.facishare.marketing.api.data.MarketingEventData
import com.facishare.marketing.api.result.BuildCrmObjectByEnrollDataResult
import com.facishare.marketing.api.result.MemberCheckResult
import com.facishare.marketing.api.result.hexagon.CreateSiteResult
import com.facishare.marketing.api.result.member.MemberEnrollResult
import com.facishare.marketing.api.result.memberCenter.QueryMemberContentResult
import com.facishare.marketing.api.service.hexagon.HexagonService
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum
import com.facishare.marketing.common.enums.MemberApprovalStatusEnum
import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.common.enums.SystemPromotionChannelEnum
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll
import com.facishare.marketing.common.typehandlers.value.FieldMappings
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.common.typehandlers.value.TagNameList
import com.facishare.marketing.outapi.service.ClueDefaultSettingService
import com.facishare.marketing.provider.dao.ActivityEnrollDataDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.MemberAccessibleObjectDao
import com.facishare.marketing.provider.dao.MemberConfigDao
import com.facishare.marketing.provider.dao.ObjectTagDAO
import com.facishare.marketing.provider.dao.WxMiniAppUserMemberBindDao
import com.facishare.marketing.provider.dao.WxServiceUserMemberBindDao
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO
import com.facishare.marketing.provider.dao.member.MemberMarketingEventCrmConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxVirtualFsUserDAO
import com.facishare.marketing.provider.dto.CustomizeFormDataUserObjectDTO
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity
import com.facishare.marketing.provider.entity.MemberConfigEntity
import com.facishare.marketing.provider.entity.ObjectTagEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity
import com.facishare.marketing.provider.entity.member.MemberMarketingEventCrmConfigEntity
import com.facishare.marketing.provider.innerResult.AssociationResult
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MemberDescribeManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.CrmV2MappingManager
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager
import com.fxiaoke.crmrestapi.common.data.FieldDescribe
import com.fxiaoke.crmrestapi.common.data.HeaderObj
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ActionAddResult
import com.fxiaoke.crmrestapi.result.ActionEditResult
import com.fxiaoke.crmrestapi.result.CountAllAvailableIntegralResult
import com.fxiaoke.crmrestapi.result.MemberStatusResult
import com.fxiaoke.crmrestapi.service.MemberService
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.paasauthrestapi.service.PaasShareRuleService
import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import com.google.common.collect.Maps
import spock.lang.*

import java.util.concurrent.TimeUnit

class MemberManagerTest extends Specification {

    def memberManager = new MemberManager()

    def memberService = Mock(MemberService)
    def eiEaConverter = Mock(EIEAConverter)
    def crmV2Manager = Mock(CrmV2Manager)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def memberAccessibleObjectDao = Mock(MemberAccessibleObjectDao)
    def crmV2MappingManager = Mock(CrmV2MappingManager)
    def metadataActionService = Mock(MetadataActionService)
    def memberConfigDao = Mock(MemberConfigDao)
    def hexagonService = Mock(HexagonService)
    def wxMiniAppUserMemberBindDao = Mock(WxMiniAppUserMemberBindDao)
    def wxServiceUserMemberBindDao = Mock(WxServiceUserMemberBindDao)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def paasShareRuleService = Mock(PaasShareRuleService)
    def memberMarketingEventCrmConfigDAO = Mock(MemberMarketingEventCrmConfigDAO)
    def memberAccessibleCampaignDAO = Mock(MemberAccessibleCampaignDAO)
    def redisManager = Mock(RedisManager)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def fileV2Manager = Mock(FileV2Manager)
    def conferenceDAO = Mock(ConferenceDAO)
    def photoManager = Mock(PhotoManager)
    def conferenceManager = Mock(ConferenceManager)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def eieaConverter = Mock(EIEAConverter)
    def userMarketingAccountRelationManager = Mock(UserMarketingAccountRelationManager)
    def memberObjectCrmConfigDAO = Mock(MemberMarketingEventCrmConfigDAO)
    def objectTagManager = Mock(ObjectTagManager)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def clueManagementManager = Mock(ClueManagementManager)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def userMarketingAccountAssociationManager = Mock(UserMarketingAccountAssociationManager)
    def spreadChannelManager = Mock(SpreadChannelManager)
    def userRoleManager = Mock(UserRoleManager)
    def clueDefaultSettingService = Mock(ClueDefaultSettingService)
    def memberDescribeManager = Mock(MemberDescribeManager)
    def qyweixinAccountBindManager = Mock(QyweixinAccountBindManager)
    def objectManager = Mock(com.facishare.marketing.provider.manager.kis.ObjectManager)
    def qywxVirtualFsUserDAO = Mock(QywxVirtualFsUserDAO)
    def marketingPromotionSourceObjManager = Mock(MarketingPromotionSourceObjManager)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def objectTagDAO = Mock(ObjectTagDAO)
    def metadataTagManager = Mock(MetadataTagManager)
    def staffMiniappUserBindManager = Mock(StaffMiniappUserBindManager)

    def setup() {
        memberManager.memberService = memberService
        memberManager.eiEaConverter = eiEaConverter
        memberManager.crmV2Manager = crmV2Manager
        memberManager.customizeFormDataDAO = customizeFormDataDAO
        memberManager.customizeFormDataUserDAO = customizeFormDataUserDAO
        memberManager.memberAccessibleObjectDao = memberAccessibleObjectDao
        memberManager.crmV2MappingManager = crmV2MappingManager
        memberManager.metadataActionService = metadataActionService
        memberManager.memberConfigDao = memberConfigDao
        memberManager.hexagonService = hexagonService
        memberManager.wxMiniAppUserMemberBindDao = wxMiniAppUserMemberBindDao
        memberManager.wxServiceUserMemberBindDao = wxServiceUserMemberBindDao
        memberManager.campaignMergeDataManager = campaignMergeDataManager
        memberManager.hexagonPageDAO = hexagonPageDAO
        memberManager.paasShareRuleService = paasShareRuleService
        memberManager.memberMarketingEventCrmConfigDAO = memberMarketingEventCrmConfigDAO
        memberManager.memberAccessibleCampaignDAO = memberAccessibleCampaignDAO
        memberManager.redisManager = redisManager
        memberManager.customizeFormDataManager = customizeFormDataManager
        memberManager.campaignMergeDataDAO = campaignMergeDataDAO
        memberManager.fileV2Manager = fileV2Manager
        memberManager.conferenceDAO = conferenceDAO
        memberManager.photoManager = photoManager
        memberManager.conferenceManager = conferenceManager
        memberManager.marketingLiveDAO = marketingLiveDAO
        memberManager.eieaConverter = eieaConverter
        memberManager.userMarketingAccountRelationManager = userMarketingAccountRelationManager
        memberManager.memberObjectCrmConfigDAO = memberObjectCrmConfigDAO
        memberManager.objectTagManager = objectTagManager
        memberManager.hexagonSiteDAO = hexagonSiteDAO
        memberManager.clueManagementManager = clueManagementManager
        memberManager.marketingActivityExternalConfigDao = marketingActivityExternalConfigDao
        memberManager.userMarketingAccountAssociationManager = userMarketingAccountAssociationManager
        memberManager.spreadChannelManager = spreadChannelManager
        memberManager.userRoleManager = userRoleManager
        memberManager.clueDefaultSettingService = clueDefaultSettingService
        memberManager.memberDescribeManager = memberDescribeManager
        memberManager.qyweixinAccountBindManager = qyweixinAccountBindManager
        memberManager.objectManager = objectManager
        memberManager.marketingPromotionSourceObjManager = marketingPromotionSourceObjManager
        memberManager.activityEnrollDataDAO = activityEnrollDataDAO
        memberManager.objectTagDAO = objectTagDAO
        memberManager.metadataTagManager = metadataTagManager
        memberManager.staffMiniappUserBindManager = staffMiniappUserBindManager
    }


    def "isOpenMemberTest"() {
        given:
        memberManager.memberOpenedEas = new HashSet<>(Collections.singleton("ea"))
        memberService.enableMember(*_) >> enableMemberMock
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        eieaConverter.enterpriseAccountToId(*_) >> 0

        when:
        boolean result = memberManager.isOpenMember(arg)
        then:
        result == resultMock
        where:
        arg  | enableMemberMock                                                              | resultMock
        "ea" | new Result<MemberStatusResult>(data: new MemberStatusResult(enableStatus: 1)) | true
        "11" | new Result<MemberStatusResult>(data: new MemberStatusResult(enableStatus: 0)) | false
    }


    def "getH5LoginMemberIdTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData()

        when:
        Optional<String> result = memberManager.getH5LoginMemberId(arg, ["ea": "allEaMemberCookies"])
        then:
        result == resultMock
        where:
        arg  | resultMock
        "11" | Optional.empty()
        "ea" | Optional.empty()
    }


    def "getWxMiniAppUserBindMemberIdTest"() {
        given:
        wxMiniAppUserMemberBindDao.getMemberIdByUid(*_) >> "1"

        when:
        Optional<String> result = memberManager.getWxMiniAppUserBindMemberId("ea", "uid")
        then:
        result == Optional.of("1")
    }


    def "getWxServiceUserBindMemberIdTest"() {
        given:
        wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(*_) >> "1"

        when:
        Optional<String> result = memberManager.getWxServiceUserBindMemberId("ea", "wxAppId", "wxOpenId")
        then:
        result == Optional.of("1")
    }


    def "getWxOpenIdByMemberIdTest"() {
        given:
        wxServiceUserMemberBindDao.getWxOpenIdByMemberId(*_) >> "1"

        when:
        Optional<String> result = memberManager.getWxOpenIdByMemberId("ea", "wxAppId", "memberId")
        then:
        result == Optional.of("1")
    }


    def "countAllAvailableIntegralTest"() {
        given:
        memberService.countAllAvailableIntegral(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<CountAllAvailableIntegralResult>(data: new CountAllAvailableIntegralResult(allAvailableIntegralCount: 0d))
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        eieaConverter.enterpriseAccountToId(*_) >> 0

        when:
        double result = memberManager.countAllAvailableIntegral("ea")
        then:
        result == 0d
    }


    def "getMemberByEaAndPhoneTest"() {
        given:
        crmV2Manager.listByEaAndPhone(*_) >> []

        when:
        Optional<ObjectData> result = memberManager.getMemberByEaAndPhone("ea", "phone")
        then:
        result == Optional.empty()
    }


    def "saveMemberByFormDataTest"() {
        given:
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getObjectFieldDescribesList(*_) >> getObjectFieldDescribesListMock
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> getCustomizeFormDataByIdMock
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["createCustomizeFormDataToCrmLeadFieldDataMapResponse": "createCustomizeFormDataToCrmLeadFieldDataMapResponse"]
        metadataActionService.add(*_) >> addMock
        memberConfigDao.getByEa(*_) >> getByEaMock
        eieaConverter.enterpriseAccountToId(*_) >> 0
        spreadChannelManager.queryChannelMapData(*_) >> ["queryChannelMapDataResponse": "queryChannelMapDataResponse"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "getChannelLabelByChannelValueResponse"
        memberDescribeManager.getMemberObjDescribe(*_) >> new ObjectDescribe(fields: ["approval_status": new FieldDescribe()])
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new com.facishare.marketing.common.result.Result<Map<String, String>>(0, "errMsg", ["data": "data"])
        objectManager.getObjectName(*_) >> "getObjectNameResponse"
        objectManager.getObjectCreateUser(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"
        marketingPromotionSourceObjManager.tryGetOrCreateObj(*_) >> "111"

        when:
        com.facishare.marketing.common.result.Result<String> result = memberManager.saveMemberByFormData("ea", new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()), "addSource", "avatar", null)
        then:
        result == resultMock
        where:
        getCustomizeFormDataByIdMock                                                                                                                                                    | getObjectFieldDescribesListMock | getByEaMock                                  | addMock                                                                                                                               | resultMock
        new CustomizeFormDataEntity(crmFormFieldMapV2: new ArrayList<FieldMappings.FieldMapping>())                                                                                     | [new CrmUserDefineFieldVo()]    | new MemberConfigEntity()                     | null                                                                                                                                  | com.facishare.marketing.common.result.Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR)
        new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | [new CrmUserDefineFieldVo()]    | new MemberConfigEntity()                     | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()                                                                    | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SERVER_BUSY.getErrorCode(), null, null)
        new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | [new CrmUserDefineFieldVo()]    | new MemberConfigEntity(registerReview: true) | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(data: new ActionAddResult(objectData: new ObjectData("_id": "111"))) | com.facishare.marketing.common.result.Result.newSuccess("111")
    }


    def "updateMemberByFormDataTest"() {
        given:
        crmV2Manager.getObjectFieldDescribesList(*_) >> getObjectFieldDescribesListMock
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> getCustomizeFormDataByIdMock
        crmV2Manager.createHeaderObj(*_) >> new HeaderObj(0, 0, "peerName", Boolean.TRUE)
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["createCustomizeFormDataToCrmLeadFieldDataMapResponse": "createCustomizeFormDataToCrmLeadFieldDataMapResponse"]
        metadataActionService.edit(*_) >> editMock

        when:
        com.facishare.marketing.common.result.Result result = memberManager.updateMemberByFormData("ea", "memberId", arg)
        then:
        result == resultMock
        where:
        arg                                                                                                                          | getObjectFieldDescribesListMock | getCustomizeFormDataByIdMock                                                                                                                                                    | editMock                                                                                                                   | resultMock
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | new CustomizeFormDataEntity(crmFormFieldMapV2: new ArrayList<FieldMappings.FieldMapping>())                                                                                     | null                                                                                                                       | com.facishare.marketing.common.result.Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR)
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | new Result<ActionEditResult>(code: -1, message: null, data: null)                                                          | com.facishare.marketing.common.result.Result.newError(-1, null, null)
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | new Result<ActionEditResult>(code: 0, message: "ok", data: new ActionEditResult(objectData: new ObjectData("_id": "111"))) | com.facishare.marketing.common.result.Result.newSuccess()

    }


    def "saveMemberByLoginFormDataTest"() {
        given:
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getObjectFieldDescribesList(*_) >> getObjectFieldDescribesListMock
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> getCustomizeFormDataByIdMock
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["createCustomizeFormDataToCrmLeadFieldDataMapResponse": "createCustomizeFormDataToCrmLeadFieldDataMapResponse"]
        metadataActionService.add(*_) >> addMock
        memberConfigDao.getByEa(*_) >> getByEaMock
        eieaConverter.enterpriseAccountToId(*_) >> 0
        spreadChannelManager.queryChannelMapData(*_) >> ["queryChannelMapDataResponse": "queryChannelMapDataResponse"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "getChannelLabelByChannelValueResponse"
        memberDescribeManager.getMemberObjDescribe(*_) >> new ObjectDescribe(fields: ["approval_status": new FieldDescribe()])
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new com.facishare.marketing.common.result.Result<Map<String, String>>(0, "errMsg", ["data": "data"])
        objectManager.getObjectName(*_) >> "getObjectNameResponse"
        objectManager.getObjectCreateUser(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"

        when:
        com.facishare.marketing.common.result.Result<String> result = memberManager.saveMemberByLoginFormData("ea", arg, "addSource", "avatar")
        then:
        result == resultMock
        where:
        arg                                                                                                                          | getObjectFieldDescribesListMock | getCustomizeFormDataByIdMock                                                                                                                                                    | getByEaMock                                   | addMock                                                                                                                                                       | resultMock
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | null                                                                                                                                                                            | new MemberConfigEntity(registerReview: true)  | new com.fxiaoke.crmrestapi.common.result.Result()                                                                                                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND)
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | new CustomizeFormDataEntity(crmFormFieldMapV2: new ArrayList<FieldMappings.FieldMapping>())                                                                                     | new MemberConfigEntity(registerReview: true)  | new com.fxiaoke.crmrestapi.common.result.Result()                                                                                                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR)
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | new MemberConfigEntity(registerReview: true)  | new com.fxiaoke.crmrestapi.common.result.Result()                                                                                                             | com.facishare.marketing.common.result.Result.newError(-2, null, null)
        new CustomizeFormDataEnrollArg(spreadFsUid: 1000, userAgent: "1", ipAddr: "2", submitContent: new CustomizeFormDataEnroll()) | [new CrmUserDefineFieldVo()]    | new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | new MemberConfigEntity(registerReview: false) | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: 0, message: "ok", data: new ActionAddResult(objectData: new ObjectData("_id": "111"))) | com.facishare.marketing.common.result.Result.newSuccess("111")

    }


    def "getSpreadChannelValueTest"() {
        given:
        spreadChannelManager.queryChannelMapData(*_) >> ["queryChannelMapDataResponse": "queryChannelMapDataResponse"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "getChannelLabelByChannelValueResponse"
        when:
        memberManager.getSpreadChannelValue("ea", "channelValue", new ObjectData())
        then:
        noExceptionThrown() // todo - validate something

    }


    def "buildMemberInfoByFormTest"() {
        given:
        crmV2Manager.getObjectFieldDescribesList(*_) >> getObjectFieldDescribesListMock
        crmV2Manager.getDetail(*_) >> getDetailMock
        fileV2Manager.getUrlByPath(*_) >> "url"

        when:
        com.facishare.marketing.common.result.Result<Map<String, Object>> result = memberManager.buildMemberInfoByForm(arg, "memberId")
        then:
        result.getErrCode() == resultMock
        where:
        arg                                                                                                                                                                                       | getDetailMock                                                     | getObjectFieldDescribesListMock                                       | resultMock
        null                                                                                                                                                                                      | null                                                              | null                                                                  | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR).getErrCode()
        new CustomizeFormDataEntity(ea: "ea")                                                                                                                                                     | null                                                              | null                                                                  | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS).getErrCode()
        new CustomizeFormDataEntity(ea: "ea")                                                                                                                                                     | new ObjectData()                                                  | null                                                                  | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR).getErrCode()
        new CustomizeFormDataEntity(ea: "ea", crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | new ObjectData("name": "[{\"ext\":\"png\",\"path\":\"N_1223\"}]") | [new CrmUserDefineFieldVo(fieldName: "name", fieldTypeName: "image")] | com.facishare.marketing.common.result.Result.newSuccess("name": "[{\"ext\":\"png\",\"path\":\"N_1223\", \"show\":\"url\"}]").getErrCode()
    }


    def "saveMemberToLeadTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getCrmLeadByPhone(*_) >> getCrmLeadByPhoneMock
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> convertObjectDataByFieldMappingMock
        campaignMergeDataManager.addCampaignMembersObjByBindObj(*_) >> addCampaignMembersObjByBindObjMock
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        metadataActionService.add(*_) >> addMock
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> ["addCustomizeFormOrganizationDataResponse": "addCustomizeFormOrganizationDataResponse"]
        clueManagementManager.getDefaultDataOwnOrganization(*_) >> ["getDefaultDataOwnOrganizationResponse"]
        marketingPromotionSourceObjManager.getById(*_) >> new ObjectData()
        def spy = Spy(memberManager)
        spy.tryInitMemberConfig(*_) >> tryInitMemberConfigMock

        when:
        Optional<String> result = spy.saveMemberToLead("ea", "memberId", "marketingActivityId", "marketingEventId", 0, "channelValue", "marketingPromotionSourceId", "formId", "keywordId", null)
        then:
        result == resultMock
        where:
        tryInitMemberConfigMock                                                                                                                                                                                                                      | getCrmLeadByPhoneMock                    | convertObjectDataByFieldMappingMock          | addCampaignMembersObjByBindObjMock | addMock                                                                                                                              | resultMock
        null                                                                                                                                                                                                                                         | null                                     | null                                         | null                               | null                                                                                                                                 | Optional.empty()
        new MemberConfigEntity(ea: "88146", memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")]))                                              | null                                     | null                                         | null                               | null                                                                                                                                 | Optional.empty()
        new MemberConfigEntity(ea: "88146", memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")]))                                              | Optional.of(new ObjectData("_id": "11")) | new ObjectData("_id": "id", "phone": "133")  | null                               | new ObjectData()                                                                                                                     | Optional.empty()
        new MemberConfigEntity(ea: "88146", leadPoolId: "leadpool", leadRecordType: "1", memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | Optional.of(new ObjectData("_id": "11")) | new ObjectData("_id": "id", "mobile": "133") | null                               | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(data: new ActionAddResult(objectData: new ObjectData("_id": "11"))) | Optional.of("11")
        new MemberConfigEntity(ea: "88146", leadPoolId: "leadpool", leadRecordType: "1", memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping(mankeepFieldName: "name", defaultValue: "11", crmFieldName: "name")])) | Optional.of(new ObjectData("_id": "11")) | new ObjectData("_id": "id")                  | null                               | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(data: new ActionAddResult(objectData: new ObjectData("_id": "11"))) | Optional.of("11")

    }


    def "handleUtmParamTest"() {
        given:
        marketingPromotionSourceObjManager.getById(*_) >> new ObjectData()

        when:
        memberManager.handleUtmParam("ea", "marketingPromotionSourceId", "marketingEventId", new ObjectData())
        then:
        noExceptionThrown() // todo - validate something

    }


    def "saveMemberToCampaignMergeDataTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> new ObjectData()
        crmV2Manager.leadDuplicateSearchByObject(*_) >> new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult())
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        metadataActionService.add(*_) >> new Result<ActionAddResult>()
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        campaignMergeDataManager.addCampaignMergeDataByMember(*_) >> "memberId"
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(*_) >> new MemberAccessibleCampaignEntity(campaignId: "campaignId")
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        customizeFormDataManager.checkAddLeadsObjectAuth(*_) >> true
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        eieaConverter.enterpriseAccountToId(*_) >> 0
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        clueManagementManager.addCustomizeFormCommonData(*_) >> ["addCustomizeFormCommonDataResponse": "addCustomizeFormCommonDataResponse"]
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> ["addCustomizeFormOrganizationDataResponse": "addCustomizeFormOrganizationDataResponse"]
        clueDefaultSettingService.getClueCreator(*_) >> 0
        clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(*_) >> 0
        objectManager.getObjectCreateUser(*_) >> 0
        def spy = Spy(memberManager)
        spy.saveMemberToLeadForCampaign(*_) >> new MemberManager.SaveMemberToLeadForCampaignContainer(leadId: "leadId")

        when:
        MemberManager.SaveMemberToCampaignMergeDataResultContainer result = spy.saveMemberToCampaignMergeData("uid", "wxAppId", "openId", "fingerPrint", ea, "memberId", "marketingActivityId", "marketingEventId", 0, "channelValue", "objectId", 0, true, "marketingPromotionSourceId")
        then:
        result == resultMock
        where:
        ea   | getCampaignMergeDataByIdMock                             | resultMock
        null | null                                                     | null
        "ea" | new CampaignMergeDataEntity()                            | new MemberManager.SaveMemberToCampaignMergeDataResultContainer(leadId: "leadId", campaignMergeDataId: "memberId")
        "ea" | new CampaignMergeDataEntity(campaignMembersObjId: "id1") | null
    }


    def "saveMemberToLeadForCampaignTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> convertObjectDataByFieldMappingMock
        crmV2Manager.leadDuplicateSearchByObject(*_) >> leadDuplicateSearchByObjectMock
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        metadataActionService.add(*_) >> addMock
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        customizeFormDataManager.checkAddLeadsObjectAuth(*_) >> checkAddLeadsObjectAuthMock
        eieaConverter.enterpriseAccountToId(*_) >> 0
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        clueManagementManager.addCustomizeFormCommonData(*_) >> ["addCustomizeFormCommonDataResponse": "addCustomizeFormCommonDataResponse"]
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> ["addCustomizeFormOrganizationDataResponse": "addCustomizeFormOrganizationDataResponse"]
        clueDefaultSettingService.getClueCreator(*_) >> 0
        clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(*_) >> 0
        objectManager.getObjectCreateUser(*_) >> 0
        def spy = Spy(memberManager)
        spy.getMemberCrmConfig(*_) >> getMemberCrmConfigMock
        when:
        MemberManager.SaveMemberToLeadForCampaignContainer result = spy.saveMemberToLeadForCampaign("ea", "memberId", "marketingActivityId", "marketingEventId", spreadFsUserId, "channelValue", "objectId", 0, "marketingPromotionSourceId")
        then:
        result == resultMock
        where:
        spreadFsUserId | getMemberCrmConfigMock                                                                                                                                  | convertObjectDataByFieldMappingMock | leadDuplicateSearchByObjectMock                                                                                                                                                            | addMock                                                                                                                                       | checkAddLeadsObjectAuthMock | resultMock
        null           | null                                                                                                                                                    | null                                | null                                                                                                                                                                                       | null                                                                                                                                          | true                        | new MemberManager.SaveMemberToLeadForCampaignContainer(saveCrmErrorMessage: "会员一键报名设置失败", saveCrmLeadError: true)
        null           | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]))                                                     | null                                | null                                                                                                                                                                                       | null                                                                                                                                          | true                        | new MemberManager.SaveMemberToLeadForCampaignContainer(saveCrmErrorMessage: "会员一键报名映射配置错误", saveCrmLeadError: true)
        null           | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]))                                                     | new ObjectData()                    | new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(errCode: -1)                                                                                      | null                                                                                                                                          | true                        | new MemberManager.SaveMemberToLeadForCampaignContainer(saveCrmErrorMessage: "系统错误", saveCrmLeadError: true)
        null           | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]))                                                     | new ObjectData()                    | new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(errCode: 0, data: new CrmV2Manager.LeadDuplicateSearchResult(leadId: "leadId", duplicate: true))  | null                                                                                                                                          | true                        | new MemberManager.SaveMemberToLeadForCampaignContainer(leadDuplicateSearchResultData: new CrmV2Manager.LeadDuplicateSearchResult(leadId: "leadId", duplicate: true))
        null           | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]))                                                     | new ObjectData()                    | new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(errCode: 0, data: new CrmV2Manager.LeadDuplicateSearchResult(leadId: "leadId", duplicate: false)) | null                                                                                                                                          | true                        | new MemberManager.SaveMemberToLeadForCampaignContainer(leadId: "leadId", leadDuplicateSearchResultData: new CrmV2Manager.LeadDuplicateSearchResult(leadId: "leadId", duplicate: false))
        1              | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]), leadRecordType: "recordType", leadPoolId: "poolId") | new ObjectData()                    | new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(errCode: 0, data: new CrmV2Manager.LeadDuplicateSearchResult(duplicate: false))                   | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: 0, data: new ActionAddResult(objectData: new ObjectData("_id": "id"))) | false                       | new MemberManager.SaveMemberToLeadForCampaignContainer(leadId: "id", leadDuplicateSearchResultData: new CrmV2Manager.LeadDuplicateSearchResult(duplicate: false))
        1              | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]), leadRecordType: "recordType", leadPoolId: "poolId") | new ObjectData()                    | new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(errCode: 0, data: new CrmV2Manager.LeadDuplicateSearchResult(duplicate: false))                   | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: -1)                                                                    | false                       | new MemberManager.SaveMemberToLeadForCampaignContainer(saveCrmLeadError: true)
        null           | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()]), leadRecordType: "recordType", leadPoolId: "poolId") | new ObjectData()                    | new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(errCode: 0, data: new CrmV2Manager.LeadDuplicateSearchResult(duplicate: false))                   | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: -1)                                                                    | false                       | new MemberManager.SaveMemberToLeadForCampaignContainer(saveCrmLeadError: true)

    }


    def "saveMemberToLeadByCampaignIdsTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> new ObjectData()
        crmV2Manager.leadDuplicateSearchByObject(*_) >> new com.facishare.marketing.common.result.Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult())
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        metadataActionService.add(*_) >> new Result<ActionAddResult>()
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        campaignMergeDataManager.addCampaignMergeDataByMember(*_) >> "addCampaignMergeDataByMemberResponse"
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(*_) >> new MemberAccessibleCampaignEntity()
        memberAccessibleCampaignDAO.queryMemberAccessibleCampaignSaveErrorData(*_) >> queryMemberAccessibleCampaignSaveErrorDataMock
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        customizeFormDataManager.checkAddLeadsObjectAuth(*_) >> true
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        eieaConverter.enterpriseAccountToId(*_) >> 0
        userMarketingAccountRelationManager.bindBrowserUserAndLead(*_) >> null
        userMarketingAccountRelationManager.bindWxUserAndLead(*_) >> null
        userMarketingAccountRelationManager.bindMiniappUserAndLead(*_) >> null
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        clueManagementManager.addCustomizeFormCommonData(*_) >> ["addCustomizeFormCommonDataResponse": "addCustomizeFormCommonDataResponse"]
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> ["addCustomizeFormOrganizationDataResponse": "addCustomizeFormOrganizationDataResponse"]
        clueDefaultSettingService.getClueCreator(*_) >> 0
        clueDefaultSettingService.getClueDefaultSettingTypeByChannelValue(*_) >> 0
        objectManager.getObjectCreateUser(*_) >> 0
        def spy = Spy(memberManager)
        spy.getMemberCrmConfig(*_) >> getMemberCrmConfigMock
        spy.saveMemberToCampaignMergeData(*_) >> saveMemberToCampaignMergeDataMock

        when:
        boolean result = spy.saveMemberToLeadByCampaignIds(arg)
        then:
        result == resultMock
        where:
        arg    | queryMemberAccessibleCampaignSaveErrorDataMock                                                                             | getMemberCrmConfigMock                                                                                                                                                           | getCampaignMergeDataByIdMock  | saveMemberToCampaignMergeDataMock                                                | resultMock
        null   | null                                                                                                                       | null                                                                                                                                                                             | null                          | null                                                                             | true
        ["id"] | null                                                                                                                       | null                                                                                                                                                                             | null                          | null                                                                             | true
        ["id"] | [new MemberAccessibleCampaignEntity(ea: "ea", marketingEventId: "marketingEventId")]                                       | new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings()]))                                                                              | null                          | null                                                                             | false
        ["id"] | [new MemberAccessibleCampaignEntity(ea: "ea", marketingEventId: "marketingEventId")]                                       | new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings()]), memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()])) | null                          | null                                                                             | true
        ["id"] | [new MemberAccessibleCampaignEntity(ea: "ea", marketingEventId: "marketingEventId")]                                       | new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings()]), memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()])) | new CampaignMergeDataEntity() | new MemberManager.SaveMemberToCampaignMergeDataResultContainer()                 | true
        ["id"] | [new MemberAccessibleCampaignEntity(ea: "ea", marketingEventId: "marketingEventId", uid: "uid")]                           | new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings()]), memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()])) | new CampaignMergeDataEntity() | new MemberManager.SaveMemberToCampaignMergeDataResultContainer(leadId: "leadId") | true
        ["id"] | [new MemberAccessibleCampaignEntity(ea: "ea", marketingEventId: "marketingEventId", wxAppId: "wxAppId", openId: "openId")] | new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings()]), memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()])) | new CampaignMergeDataEntity() | new MemberManager.SaveMemberToCampaignMergeDataResultContainer(leadId: "leadId") | true
        ["id"] | [new MemberAccessibleCampaignEntity(ea: "ea", marketingEventId: "marketingEventId", fingerPrint: "fingerPrint")]           | new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings()]), memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings()])) | new CampaignMergeDataEntity() | new MemberManager.SaveMemberToCampaignMergeDataResultContainer(leadId: "leadId") | true

    }


    def "getMemberCrmConfigTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        def spy = Spy(memberManager)
        spy.tryInitMemberConfig(*_) >> tryInitMemberConfigMock
        when:
        MemberConfigEntity result = spy.getMemberCrmConfig("ea", "marketingEventId")
        then:
        result == resultMock
        where:
        tryInitMemberConfigMock  | resultMock
        null                     | null
        new MemberConfigEntity() | new MemberConfigEntity()

    }


    def "marketingEventHasMemberToLeadMappingTest"() {
        given:
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()

        when:
        boolean result = memberManager.marketingEventHasMemberToLeadMapping("ea", "marketingEventId")
        then:
        result == false
    }


    def "buildCrmObjectByMemberDataTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getDetail(*_) >> getDetailMock
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> new ObjectData()
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(*_) >> getMemberAccessibleCampaignDataByIdMock
        eieaConverter.enterpriseAccountToId(*_) >> 0
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        def spy = Spy(memberManager)
        spy.getMemberCrmConfig(*_) >> getMemberCrmConfigMock
        when:
        com.facishare.marketing.common.result.Result<BuildCrmObjectByEnrollDataResult> result = spy.buildCrmObjectByMemberData("ea", "memberAccessibleCampaignId")
        then:
        result.getErrCode() == resultMock
        where:
        getMemberAccessibleCampaignDataByIdMock | getMemberCrmConfigMock                                                                                           | getDetailMock    | resultMock
        null                                    | null                                                                                                             | null             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NO_DATA).getErrCode()
        new MemberAccessibleCampaignEntity()    | null                                                                                                             | null             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR).getErrCode()
        new MemberAccessibleCampaignEntity()    | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()])) | null             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR).getErrCode()
        new MemberAccessibleCampaignEntity()    | new MemberConfigEntity(memberToLeadFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()])) | new ObjectData() | com.facishare.marketing.common.result.Result.newSuccess(new BuildCrmObjectByEnrollDataResult(new HashMap<>())).getErrCode()

    }


    def "bindMemberAndCrmObjTest"() {
        given:
        crmV2Manager.getDetail(*_) >>> getDetailMock
        campaignMergeDataManager.addCampaignMergeDataByMember(*_) >> "addCampaignMergeDataByMemberResponse"
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(*_) >> getMemberAccessibleCampaignDataByIdMock
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        userMarketingAccountRelationManager.bindLoginIdentityAndCrmObject(*_) >> null
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> new MemberMarketingEventCrmConfigEntity()
        def spy = Spy(memberManager)
        when:
        com.facishare.marketing.common.result.Result result = spy.bindMemberAndCrmObj("ea", "memberAccessibleCampaignId", "bindObjectId", arg)
        then:
        result == resultMock
        where:
        arg  | getMemberAccessibleCampaignDataByIdMock | getDetailMock | resultMock
        "aa" | null                                    | null          | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NO_DATA)
        "aa" | new MemberAccessibleCampaignEntity()    | [null, null]  | com.facishare.marketing.common.result.Result.newError(SHErrorCode.CUSTOMIZE_FORM_BIND_CRM_DATA_ERROR);
        "aa" | new MemberAccessibleCampaignEntity() | [new ObjectData("_id": "id1"), new ObjectData("_id": "id2")] | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR)
        "LeadsObj" | new MemberAccessibleCampaignEntity(uid: "uid") | [new ObjectData("_id": "id1"), null] | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NO_DATA);
        "LeadsObj" | new MemberAccessibleCampaignEntity(uid: "uid") | [new ObjectData("_id": "id1"), new ObjectData("_id": "id2")] | com.facishare.marketing.common.result.Result.newSuccess()
        "LeadsObj" | new MemberAccessibleCampaignEntity(wxAppId: "wxAppId", openId: "openid") | [new ObjectData("_id": "id1"), new ObjectData("_id": "id2")] | com.facishare.marketing.common.result.Result.newSuccess()

    }


    def "saveLeadToMemberTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.listByEaAndPhone(*_) >> listByEaAndPhoneMock
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> convertObjectDataByFieldMappingMock
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        metadataActionService.add(*_) >> addMock
        memberConfigDao.getByEa(*_) >> getByEaMock
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        userMarketingAccountAssociationManager.associate(*_) >> new AssociationResult("userMarketingAccountId")
        memberDescribeManager.getMemberObjDescribe(*_) >> new ObjectDescribe(fields: ["aa": new FieldDescribe(), "approval_status": "1"])
        def spy = Spy(memberManager)
        spy.tryInitMemberConfig(*_) >> tryInitMemberConfigMock

        when:
        Optional<String> result = spy.saveLeadToMember("ea", "leadId", "marketingPromotionSourceId")
        then:
        result == resultMock
        where:

        tryInitMemberConfigMock                                                                                                                  | convertObjectDataByFieldMappingMock | listByEaAndPhoneMock                                 | getByEaMock                                   | addMock                                                                                                                                                              | resultMock
        null                                                                                                                                     | null                                | [new ObjectData("phone": "133", "_id": "111")]       | new MemberConfigEntity()                      | null                                                                                                                                                                 | Optional.empty()
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()]))                         | null                                | [new ObjectData("phone": "133", "_id": "111")]       | new MemberConfigEntity()                      | null                                                                                                                                                                 | Optional.empty()
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()]))                         | new ObjectData("phone": "133")      | [new ObjectData("phone": "133", "_id": "111")]       | new MemberConfigEntity()                      | null                                                                                                                                                                 | Optional.of("111")
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()]), memberRecordType: "11") | new ObjectData("phone": "133")      | [new ObjectData("_id": "111", "approval_status": 1)] | new MemberConfigEntity(registerReview: false) | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()                                                                                                   | Optional.empty()
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()]), memberRecordType: "11") | new ObjectData("phone": "133")      | [new ObjectData("_id": "111", "approval_status": 1)] | new MemberConfigEntity(registerReview: true)  | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()                                                                                                   | Optional.empty()
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()]), memberRecordType: "11") | new ObjectData("phone": "133")      | [new ObjectData("_id": "111", "approval_status": 1)] | new MemberConfigEntity(registerReview: true)  | new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: 0, data: new ActionAddResult(objectData: new ObjectData("_id": "111", "approval_status": 1))) | Optional.of("111")

    }


    def "saveLeadToMemberAndSpreadDataTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.listByEaAndPhone(*_) >> listByEaAndPhoneMock
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> convertObjectDataByFieldMappingMock
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        metadataActionService.add(*_) >> addMock
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        userMarketingAccountAssociationManager.associate(*_) >> new AssociationResult("userMarketingAccountId")
        spreadChannelManager.queryChannelMapData(*_) >> ["queryChannelMapDataResponse": "queryChannelMapDataResponse"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "getChannelLabelByChannelValueResponse"
        memberDescribeManager.getMemberObjDescribe(*_) >> getMemberObjDescribeMock
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new com.facishare.marketing.common.result.Result<Map<String, String>>(0, "errMsg", ["data": "data"])
        objectManager.getObjectName(*_) >> "getObjectNameResponse"
        objectManager.getObjectCreateUser(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"
        def spy = Spy(memberManager)
        spy.tryInitMemberConfig(*_) >> tryInitMemberConfigMock

        when:
        Optional<String> result = spy.saveLeadToMemberAndSpreadData("ea", new CustomizeFormDataUserEntity(), "memberId")
        then:
        result == resultMock
        where:
        tryInitMemberConfigMock                                                                                          | convertObjectDataByFieldMappingMock         | listByEaAndPhoneMock                           | getMemberObjDescribeMock | addMock | resultMock
        null                                                                                                             | null                                        | null                                           | null                     | null    | Optional.empty()
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()])) | null                                        | null                                           | null                     | null    | Optional.empty()
        new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()])) | new ObjectData("phone": "111", "id": "id1") | [new ObjectData("phone": "111", "_id": "111")] | null                     | null    | Optional.of("111")
        //     new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping()])) | new ObjectData("phone": "111", "id": "id1") | []                                             | null                     | null    | Optional.of("111")

    }


    def "tryInitMemberConfigTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        memberAccessibleObjectDao.insertIgnore(*_) >> 1
        memberConfigDao.getByEa(*_) >>> getByEaMock
        memberConfigDao.insertIgnore(*_) >> 1
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult(id: "id1", pageId: "id2"))
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity(content: "")]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> hexagonCopySiteMock

        when:
        MemberConfigEntity result = memberManager.tryInitMemberConfig("ea")
        then:
        noExceptionThrown() // todo - validate something
        where:
        getByEaMock                             | hexagonCopySiteMock
        [new MemberConfigEntity(initStatus: 1)] | null
        [null, null, null, null, null, null]    | null
    }


    def "queryMemberContentListTest"() {
        given:
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        customizeFormDataUserDAO.getByMarketingEventIds(*_) >> [new CustomizeFormDataUserObjectDTO()]
        hexagonPageDAO.getHexagonSiteByHexagonPageId(*_) >> new HexagonSiteEntity()
        memberAccessibleCampaignDAO.getByMarketingEventIds(*_) >> [new CustomizeFormDataUserObjectDTO()]
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        fileV2Manager.batchGetUrlByPath(*_) >> ["batchGetUrlByPathResponse": "batchGetUrlByPathResponse"]
        conferenceDAO.getActivityByEaAndMarketingEventIds(*_) >> [new ActivityEntity()]
        photoManager.getDefaultCoverApath(*_) >> "getDefaultCoverApathResponse"
        photoManager.queryPhotosByTypeAndTargetIds(*_) >> [new PhotoEntity()]
        conferenceManager.getConferenceTimeFlowStatus(*_) >> 0
        marketingLiveDAO.queryMarketingLiveByMarketingEventIds(*_) >> [new MarketingLiveEntity()]
        eieaConverter.enterpriseAccountToId(*_) >> 0
        def spy = Spy(memberManager)
        spy.configMemberConferenceData(*_) >> null
        spy.configMemberLiveData(*_) >> null
        spy.configMemberContentData(*_) >> null
        when:
        Optional<List<QueryMemberContentResult>> result = spy.queryMemberContentList("ea", arg, "memberId", "memberPhone")
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg                                                                                                                                         | resultMock
        null                                                                                                                                        | Optional.empty()
        [new MarketingEventData(beginTime: System.currentTimeMillis(), endTime: System.currentTimeMillis() + 1000000l, cover: "cover1", id: "id1")] | Optional.of([new QueryMemberContentResult(marketingEventId: "id1", marketingStatus: "进行中",)])
    }


    def "configMemberContentDataTest"() {
        given:
        customizeFormDataUserDAO.getByMarketingEventIds(*_) >> getByMarketingEventIdsMock
        hexagonPageDAO.getHexagonSiteByHexagonPageId(*_) >> new HexagonSiteEntity()
        memberAccessibleCampaignDAO.getByMarketingEventIds(*_) >> getByMarketingEventIdsMock2

        when:
        memberManager.configMemberContentData("ea", arg, "memberId", "memberPhone")
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg                                                                                     | getByMarketingEventIdsMock                                                    | getByMarketingEventIdsMock2
        null                                                                                    | null                                                                          | null
        [new QueryMemberContentResult()]                                                        | null                                                                          | null
        [new QueryMemberContentResult(marketingEventId: "id1")]                                 | [new CustomizeFormDataUserObjectDTO(objectType: 27, marketingEventId: "id1")] | null
        [new QueryMemberContentResult(marketingEventId: "id1", eventType: "3")]                 | [new CustomizeFormDataUserObjectDTO(objectType: 27, marketingEventId: "id1")] | [new CustomizeFormDataUserObjectDTO(objectType: 27, marketingEventId: "id2")]
        [new QueryMemberContentResult(marketingEventId: "id1", eventType: "content_marketing")] | [new CustomizeFormDataUserObjectDTO(objectType: 27, marketingEventId: "id1")] | [new CustomizeFormDataUserObjectDTO(objectType: 27, marketingEventId: "id2")]

    }


    def "sendActivityNotificationSmsByCampaignMergeIdTest"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity()

        when:
        memberManager.sendActivityNotificationSmsByCampaignMergeId("campaignMergeDataId")
        then:
        noExceptionThrown() // todo - validate something

    }

    def "asyncSendActivityNotificationSmsByCampaignMergeId"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        conferenceDAO.getConferenceByMarketingEventId(*_) >> getConferenceByMarketingEventIdMock
        conferenceManager.sendConferenceNotification(*_) >> null

        when:
        memberManager.asyncSendActivityNotificationSmsByCampaignMergeId("campaignMergeDataId")
        then:
        noExceptionThrown() // todo - validate something
        where:
        getCampaignMergeDataByIdMock              | getConferenceByMarketingEventIdMock
        null                                      | null
        new CampaignMergeDataEntity()             | null
        new CampaignMergeDataEntity(phone: "123") | null
        new CampaignMergeDataEntity(phone: "123") | new ActivityEntity(enrollReview: false)
        new CampaignMergeDataEntity(phone: "123") | new ActivityEntity(enrollReview: true)
    }


    def "sendReviewMessageByCampaignMergeIdTest"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity()

        when:
        memberManager.asyncSendReviewMessageByCampaignMergeId("campaignMergeDataId")
        then:
        noExceptionThrown() // todo - validate something

    }

    def "asyncSendReviewMessageByCampaignMergeId"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        conferenceDAO.getConferenceByMarketingEventId(*_) >> getConferenceByMarketingEventIdMock

        when:
        memberManager.asyncSendReviewMessageByCampaignMergeId("campaignMergeDataId")
        then:
        noExceptionThrown() // todo - validate something
        where:
        getCampaignMergeDataByIdMock  | getConferenceByMarketingEventIdMock
        null                          | null
        new CampaignMergeDataEntity() | null
        new CampaignMergeDataEntity() | new ActivityEntity()
    }

    def "sendConferenceQywxReviewMessageByCampaignMergeIdTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity()

        when:
        memberManager.sendConferenceQywxReviewMessageByCampaignMergeId("campaignMergeDataId")
        then:
        noExceptionThrown() // todo - validate something

    }

    def "asyncsendConferenceQywxReviewMessageByCampaignMergeIdTest"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> getCampaignMergeDataByIdMock
        conferenceDAO.getConferenceByMarketingEventId(*_) >> getConferenceByMarketingEventIdMock

        when:
        memberManager.asyncSendConferenceQywxReviewMessageByCampaignMergeId("campaignMergeDataId")
        then:
        noExceptionThrown() // todo - validate something
        where:
        getCampaignMergeDataByIdMock  | getConferenceByMarketingEventIdMock
        null                          | null
        new CampaignMergeDataEntity() | null
        new CampaignMergeDataEntity() | new ActivityEntity()
    }

    def "configMemberLiveDataTest"() {
        given:
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        fileV2Manager.batchGetUrlByPath(*_) >> ["cover": "url"]
        marketingLiveDAO.queryMarketingLiveByMarketingEventIds(*_) >> queryMarketingLiveByMarketingEventIdsMock
        eieaConverter.enterpriseAccountToId(*_) >> 0

        when:
        memberManager.configMemberLiveData("ea", arg)
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg                                                                                  | queryMarketingLiveByMarketingEventIdsMock
        null                                                                                 | null
        [new QueryMemberContentResult(eventType: "live_marketing", marketingEventId: "id1")] | null
        [new QueryMemberContentResult(eventType: "live_marketing", marketingEventId: "id1")] | [new MarketingLiveEntity(id: "aa", marketingEventId: "id1", cover: "cover", platform: 1)]
        [new QueryMemberContentResult(eventType: "live_marketing", marketingEventId: "id1")] | [new MarketingLiveEntity(id: "aa", marketingEventId: "id1", cover: "cover", platform: 2, startTime: new Date(), endTime: new Date())]
        [new QueryMemberContentResult(eventType: "live_marketing", marketingEventId: "id1")] | [new MarketingLiveEntity(id: "aa", marketingEventId: "id1", cover: "cover", platform: 2, startTime: new Date(System.currentTimeMillis() + 1000000l), endTime: new Date())]
        [new QueryMemberContentResult(eventType: "live_marketing", marketingEventId: "id1")] | [new MarketingLiveEntity(id: "aa", marketingEventId: "id1", cover: "cover", platform: 2, startTime: new Date(System.currentTimeMillis() - 1000000l), endTime: new Date(System.currentTimeMillis() + 1000000l))]

    }


    def "configMemberConferenceDataTest"() {
        given:
        conferenceDAO.getActivityByEaAndMarketingEventIds(*_) >> getActivityByEaAndMarketingEventIdsMock
        photoManager.queryPhotosByTypeAndTargetIds(*_) >> [new PhotoEntity(targetId: "aa")]
        conferenceManager.getConferenceTimeFlowStatus(*_) >> 0

        when:
        memberManager.configMemberConferenceData("ea", arg)
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg                                                                     | getActivityByEaAndMarketingEventIdsMock
        null                                                                    | null
        [new QueryMemberContentResult(eventType: "3", marketingEventId: "id1")] | null
        [new QueryMemberContentResult(eventType: "3", marketingEventId: "id1")] | [new ActivityEntity(id: "aa", marketingEventId: "id1")]
        [new QueryMemberContentResult(eventType: "3", marketingEventId: "id1")] | [new ActivityEntity(id: "aa", marketingEventId: "id2")]
    }


    def "getLatestAccessibleMemberIdByCampaignIdsTest"() {
        given:
        memberAccessibleCampaignDAO.getLatestAccessibleMemberByCampaignIds(*_) >> getLatestAccessibleMemberByCampaignIdsMock

        when:
        Map<String, String> result = memberManager.getLatestAccessibleMemberIdByCampaignIds(arg)
        then:
        result == resultMock
        where:
        arg    | getLatestAccessibleMemberByCampaignIdsMock                             | resultMock
        null   | null                                                                   | Maps.newHashMap()
        ["aa"] | [new MemberAccessibleCampaignEntity(campaignId: "aa", memberId: "bb")] | ["aa": "bb"]
        ["aa"] | []                                                                     | Maps.newHashMap()
    }


    def "getSystemPromotionChannelTypeTest"() {
        given:
        spreadChannelManager.getChannelByMarketingActivityId(*_) >> getChannelByMarketingActivityIdMock

        when:
        String result = memberManager.getSystemPromotionChannelType(arg, "wxAppId", "wxOpenId", "uid")
        then:
        result == resultMock
        where:
        arg                                             | getChannelByMarketingActivityIdMock | resultMock
        new MemberEnrollArg(channelValue: "1")          | null                                | "1"
        new MemberEnrollArg(marketingActivityId: "id1") | "2"                                 | "2"
        new MemberEnrollArg()                           | "2"                                 | SystemPromotionChannelEnum.WECHAT.getValue()
    }


    def "memberNeedMemberEnrollTest"() {
        given:
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(*_) >> getMemberAccessibleCampaignDataMock

        when:
        boolean result = memberManager.memberNeedMemberEnroll(arg, "marketingEventId", "memberId")
        then:
        result == resultMock
        where:
        arg  | getMemberAccessibleCampaignDataMock | resultMock
        null | null                                | false
        "ea" | null                                | true

    }


    def "memberDataToMapTest"() {
        given:
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity(leadToMemberFieldMappings: FieldMappings.newInstance([new FieldMappings.FieldMapping("name", "xiao", "name", false, null)]))

        when:
        Map<String, Object> result = memberManager.memberDataToMap(arg1, arg2)
        then:
        result == resultMock
        where:
        arg1                                                       | arg2                                                                                                                                             | resultMock
        null                                                       | null                                                                                                                                             | Maps.newHashMap()
        new MemberCheckResult(id: "id1", name: "aa", phone: "122") | new CustomizeFormDataEntity(crmFormFieldMapV2: FieldMappings.newInstance([new FieldMappings.FieldMapping("name", "xiao", "name", false, null)])) | ["name": "aa"]
    }


    def "deleteSystemMemberHexagonSiteDataTest"() {
        given:
        customizeFormDataDAO.deleteCustomizeFormData(*_) >> 0
        memberConfigDao.deleteByEa(*_) >> 0
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity(formId: "aa")]
        hexagonPageDAO.deleteHexagonPageById(*_) >> 0
        hexagonSiteDAO.getById(*_) >> new HexagonSiteEntity()
        hexagonSiteDAO.deleteHexagonSiteById(*_) >> 0

        when:
        memberManager.deleteSystemMemberHexagonSiteData("ea")
        then:
        noExceptionThrown() // todo - validate something

    }

    def "asyncMemberConfigToMarketingEventTest"() {
        given:
        def spy = Spy(memberManager)
        when:
        spy.asyncMemberConfigToMarketingEvent("ea", "marketingEventId")
        then:
        noExceptionThrown()
    }

    def "memberConfigToMarketingEventTest"() {
        given:
        memberConfigDao.getByEa(*_) >> getByEaMock
        memberMarketingEventCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> getMemberMarketingEventCrmConfigByEventIdMock
        memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(*_) >> getMemberMarketingEventCrmConfigByEventId
        when:
        memberManager.memberConfigToMarketingEvent("ea", "marketingEventId")
        then:
        noExceptionThrown() // todo - validate something
        where:
        getMemberMarketingEventCrmConfigByEventIdMock                                           | getByEaMock                                                            | getMemberMarketingEventCrmConfigByEventId
        new MemberMarketingEventCrmConfigEntity(memberToLeadFieldMappings: new FieldMappings()) | null                                                                   | null
        null                                                                                    | null                                                                   | null
        null                                                                                    | new MemberConfigEntity(memberToLeadFieldMappings: new FieldMappings()) | null
    }

    def "getDetailTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData()
        when:
        ObjectData result = memberManager.getDetail("id", "ea")
        then:
        result != null
    }


    def "checkWxServiceUserIsMemberTest"() {
        given:
        wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(*_) >> "getMemberIdByWxUserInfoResponse"
        def spy = Spy(memberManager)
        spy.getWxServiceUserBindMemberId(*_) >> getWxServiceUserBindMemberIdMock
        when:
        com.facishare.marketing.common.result.Result<String> result = spy.checkWxServiceUserIsMember("ea", "wxAppId", "wxOpenId")
        then:
        result == resultMock
        where:
        getWxServiceUserBindMemberIdMock | resultMock
        Optional.empty()                 | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NOT_MEMBER);
        Optional.of("11") | com.facishare.marketing.common.result.Result.newSuccess("11");

    }


    def "checkH5UserIsMemberTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData()
        def spy = Spy(memberManager)
        spy.getH5LoginMemberId(*_) >> getH5LoginMemberIdMock
        when:
        com.facishare.marketing.common.result.Result<String> result = spy.checkH5UserIsMember("ea", ["allMemberCookieInfos": "allMemberCookieInfos"])
        then:
        result == resultMock
        where:
        getH5LoginMemberIdMock | resultMock
        Optional.empty()       | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NOT_MEMBER);
        Optional.of("11") | com.facishare.marketing.common.result.Result.newSuccess("11");

    }


    def "checkWxMiniAppUserHaveMemberAuthTest"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        memberAccessibleObjectDao.countByEaAndObjectId(*_) >> 0
        wxMiniAppUserMemberBindDao.getMemberIdByUid(*_) >> "getMemberIdByUidResponse"
        hexagonPageDAO.getInclueDeletedById(*_) >> new HexagonPageEntity()
        objectManager.getObjectEa(*_) >> "ea"
        def spy = Spy(memberManager)
        spy.getWxMiniAppUserBindMemberId(*_) >> Optional.of("aa")
        spy.isObjectNotNeedMemberAuth(*_) >> true
        when:
        com.facishare.marketing.common.result.Result<String> result = spy.checkWxMiniAppUserHaveMemberAuth(0, "objectId", "uid", arg)
        then:
        result == resultMock
        where:
        arg  | getDetailMock | resultMock
        true | null          | com.facishare.marketing.common.result.Result.newSuccess();
        false | null | com.facishare.marketing.common.result.Result.newError(SHErrorCode.NOT_MEMBER);
        false | new ObjectData("approval_status": "0") | com.facishare.marketing.common.result.Result.newSuccess("aa");
    }


    def "isObjectNotNeedMemberAuthTest"() {
        given:
        memberAccessibleObjectDao.countByEaAndObjectId(*_) >> 0
        hexagonPageDAO.getInclueDeletedById(*_) >> new HexagonPageEntity()

        when:
        boolean result = memberManager.isObjectNotNeedMemberAuth(ObjectTypeEnum.HEXAGON_PAGE.getType(), "objectId", "ea")
        then:
        result == true
    }


    def "getMemberApproveStatusTest"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        memberAccessibleObjectDao.countByEaAndObjectId(*_) >> 0
        memberAccessibleObjectDao.listAccessibleHexagonSitesByEa(*_) >> listAccessibleHexagonSitesByEa
        memberConfigDao.getByEa(*_) >> getByEaMock
        wxMiniAppUserMemberBindDao.getMemberIdByUid(*_) >> "getMemberIdByUidResponse"
        wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(*_) >> "getMemberIdByWxUserInfoResponse"
        hexagonPageDAO.getInclueDeletedById(*_) >> new HexagonPageEntity()
        objectManager.getObjectEa(*_) >> "getObjectEaResponse"
        def spy = Spy(memberManager)
        spy.checkWxServiceUserIsMember(*_) >> com.facishare.marketing.common.result.Result.newSuccess("AA")
        spy.checkH5UserIsMember(*_) >> com.facishare.marketing.common.result.Result.newSuccess("AA")
        spy.checkWxMiniAppUserHaveMemberAuth(*_) >> com.facishare.marketing.common.result.Result.newSuccess("AA")
        when:
        Optional<Integer> result = spy.getMemberApproveStatus("ea", "siteId", "wxAppId", "openId", "uid", ["allMemberCookieInfos": "allMemberCookieInfos"], arg)
        then:
        result == resultMock
        where:
        arg                                       | getByEaMock                                                                       | getDetailMock                          | listAccessibleHexagonSitesByEa | getMemberAccessibleCampaignDataMock | getConferenceByMarketingEventIdMock | resultMock
        IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS   | null                                                                              | null                                   | null                           | new MemberConfigEntity()            | null                                | Optional.empty()
        IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS   | new MemberConfigEntity(ea: "ea", registrationSiteId: "11", loginSiteId: "22")     | null                                   | ["22"]                         | new MemberConfigEntity()            | null                                | Optional.empty()
        IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS   | new MemberConfigEntity(ea: "ea", registrationSiteId: "siteId", loginSiteId: "22") | new ObjectData("approval_status": "1") | ["22"]                         | new MemberConfigEntity()            | null                                | Optional.of(1)
        IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS   | new MemberConfigEntity(ea: "ea", registrationSiteId: "siteId", loginSiteId: "22") | new ObjectData()                       | ["22"]                         | new MemberConfigEntity()            | null                                | Optional.of(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType())
        IdentityCheckTypeEnum.BROWSER_FINGERPRINT | new MemberConfigEntity(ea: "ea", registrationSiteId: "siteId", loginSiteId: "22") | new ObjectData()                       | ["22"]                         | new MemberConfigEntity()            | null                                | Optional.of(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType())
        null                                      | new MemberConfigEntity(ea: "ea", registrationSiteId: "siteId", loginSiteId: "22") | new ObjectData()                       | ["22"]                         | new MemberConfigEntity()            | null                                | Optional.of(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType())

    }


    def "checkEnrollReviewStatusTest"() {
        given:
        crmV2Manager.getDetail(*_) >> getDetailMock
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(*_) >> getMemberAccessibleCampaignDataMock
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> [new CampaignMergeDataEntity(id: "aa", createTime: new Date())]
        conferenceDAO.getConferenceByMarketingEventId(*_) >> getConferenceByMarketingEventIdMock
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(*_) >> new ActivityEnrollDataEntity(reviewStatus: 1)

        when:
        memberManager.checkEnrollReviewStatus(arg, "marketingEventId", "memberId", new MemberEnrollResult())
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg  | getDetailMock                  | getMemberAccessibleCampaignDataMock                  | getConferenceByMarketingEventIdMock    | listByEaAndPhoneMock
        null | null                           | null                                                 | null                                   | null
        "ea" | new ObjectData()               | null                                                 | null                                   | null
        "ea" | new ObjectData()               | new MemberAccessibleCampaignEntity(campaignId: "aa") | new ActivityEntity(enrollReview: true) | null
        "ea" | new ObjectData()               | null                                                 | new ActivityEntity(enrollReview: true) | null

        "ea" | new ObjectData("phone": "122") | null                                                 | new ActivityEntity(enrollReview: true) | null

    }


    def "checkCustomizeFormDataEnrollReviewTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity(enrollReview: true)
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(*_) >> new ActivityEnrollDataEntity(reviewStatus: 3)

        when:
        memberManager.checkCustomizeFormDataEnrollReview("ea", arg1, arg2, arg3, new MemberEnrollResult())
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg1 | arg2                                                | arg3
        null | null                                                | null
        "ea" | [new CustomizeFormDataUserEntity()]                 | [new CampaignMergeDataEntity()]
        "ea" | [new CustomizeFormDataUserEntity()]                 | null
        "ea" | [new CustomizeFormDataUserEntity(campaignId: "aa")] | null

    }


    def "checkActivityEnrollReviewTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> getConferenceByMarketingEventIdMock

        when:
        memberManager.checkActivityEnrollReview(arg, "marketingEventId", new MemberEnrollResult())
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg  | getConferenceByMarketingEventIdMock
        null | null
        "ea" | new ActivityEntity(enrollReview: true)

    }


    def "doWxMiniAppUserMemberRegisterTest"() {
        given:
        memberService.enableMember(*_) >> new Result<MemberStatusResult>()
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo()]
        crmV2Manager.listByEaAndPhone(*_) >> listByEaAndPhoneMock
        crmV2Manager.getCrmLeadByPhone(*_) >> null
        crmV2Manager.convertObjectDataByFieldMapping(*_) >> new ObjectData()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()
        memberAccessibleObjectDao.insertIgnore(*_) >> 0
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["createCustomizeFormDataToCrmLeadFieldDataMapResponse": "createCustomizeFormDataToCrmLeadFieldDataMapResponse"]
        metadataActionService.add(*_) >> new Result<ActionAddResult>()
        memberConfigDao.getByEa(*_) >> new MemberConfigEntity()
        memberConfigDao.insertIgnore(*_) >> 0
        memberConfigDao.updateSiteId(*_) >> 0
        memberConfigDao.updateCrmMemberShareRulePresetStatus(*_) >> 0
        hexagonService.hexagonCopySite(*_) >> new com.facishare.marketing.common.result.Result<CreateSiteResult>(0, "errMsg", new CreateSiteResult())
        wxMiniAppUserMemberBindDao.getMemberIdByUid(*_) >> "m1"
        wxMiniAppUserMemberBindDao.insertIgnore(*_) >> 0
        wxMiniAppUserMemberBindDao.deleteByMiniAppUser(*_) >> 0
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity()]
        hexagonPageDAO.updateContent(*_) >> 0
        paasShareRuleService.batchCreateEntityShare(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<Void>()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        userMarketingAccountRelationManager.bindMiniappUserAndLead(*_) >> null
        userMarketingAccountRelationManager.bindMiniappUserAndMember(*_) >> null
        hexagonSiteDAO.markAsSystemSite(*_) >> 0
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> ["addCustomizeFormOrganizationDataResponse": "addCustomizeFormOrganizationDataResponse"]
        clueManagementManager.getDefaultDataOwnOrganization(*_) >> ["getDefaultDataOwnOrganizationResponse"]
        spreadChannelManager.queryChannelMapData(*_) >> ["queryChannelMapDataResponse": "queryChannelMapDataResponse"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "getChannelLabelByChannelValueResponse"
        memberDescribeManager.getMemberObjDescribe(*_) >> new ObjectDescribe()
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new com.facishare.marketing.common.result.Result<Map<String, String>>(0, "errMsg", ["data": "data"])
        objectManager.getObjectName(*_) >> "getObjectNameResponse"
        objectManager.getObjectCreateUser(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"
        marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(*_) >> "tryGetOrCreateObjByFormEnrollResponse"
        marketingPromotionSourceObjManager.tryGetOrCreateObj(*_) >> "tryGetOrCreateObjResponse"
        marketingPromotionSourceObjManager.getById(*_) >> new ObjectData()
        objectTagDAO.getObjectTag(*_) >> new ObjectTagEntity(tagNameList: TagNameList.convert([new TagName(firstTagName: "firstTagName", secondTagName: "secondTagName")]))
        staffMiniappUserBindManager.checkAndAddStaffUserBindSync(*_) >> checkAndAddStaffUserBindSyncMock
        def spy = Spy(memberManager)
        spy.saveMemberByFormData(*_) >> saveMemberByFormDataMock
        spy.saveMemberToLead(*_) >> Optional.of("bb")
        when:
        com.facishare.marketing.common.result.Result<Boolean> result = spy.doWxMiniAppUserMemberRegister("ea", "uid", arg)
        then:
        result == resultMock
        where:
        arg                                                                          | listByEaAndPhoneMock | saveMemberByFormDataMock | checkAndAddStaffUserBindSyncMock | resultMock
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll()) | null                 | null                     | true                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.PARAMS_ERROR, true);
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | [new ObjectData("phone": "133")] | null | true | com.facishare.marketing.common.result.Result.newError(SHErrorCode.PHONE_HAVE_BEEN_REGISTERED, true);
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | [new ObjectData()] | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR) | true | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR, true);
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | [new ObjectData()] | com.facishare.marketing.common.result.Result.newSuccess("aa") | true | com.facishare.marketing.common.result.Result.newError(SHErrorCode.STAFF_MEMBER_LOGIN_REFRESH_TOKEN, true);
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | [new ObjectData()] | com.facishare.marketing.common.result.Result.newSuccess("aa") | false | com.facishare.marketing.common.result.Result.newSuccess(true);

    }


    def "doWxMiniAppUserMemberLoginTest"() {
        given:
        eiEaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo()]
        crmV2Manager.listByEaAndPhone(*_) >> listByEaAndPhoneMock
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["createCustomizeFormDataToCrmLeadFieldDataMapResponse": "createCustomizeFormDataToCrmLeadFieldDataMapResponse"]
        metadataActionService.add(*_) >> new Result<ActionAddResult>()
        memberConfigDao.getByEa(*_) >> getByEaMock
        wxMiniAppUserMemberBindDao.getMemberIdByUid(*_) >> "getMemberIdByUidResponse"
        wxMiniAppUserMemberBindDao.insertIgnore(*_) >> 0
        wxMiniAppUserMemberBindDao.deleteByMiniAppUser(*_) >> 0
        eieaConverter.enterpriseAccountToId(*_) >> 0
        spreadChannelManager.queryChannelMapData(*_) >> ["queryChannelMapDataResponse": "queryChannelMapDataResponse"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "getChannelLabelByChannelValueResponse"
        memberDescribeManager.getMemberObjDescribe(*_) >> new ObjectDescribe()
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new com.facishare.marketing.common.result.Result<Map<String, String>>(0, "errMsg", ["data": "data"])
        objectManager.getObjectName(*_) >> "getObjectNameResponse"
        objectManager.getObjectCreateUser(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"
        objectTagDAO.getObjectTag(*_) >> new ObjectTagEntity(tagNameList: TagNameList.convert([new TagName(firstTagName: "firstTagName", secondTagName: "secondTagName")]))
        staffMiniappUserBindManager.checkAndAddStaffUserBindSync(*_) >> checkAndAddStaffUserBindSyncMock
        def spy = Spy(memberManager)
        spy.saveMemberByLoginFormData(*_) >> saveMemberByLoginFormDataMock

        when:
        com.facishare.marketing.common.result.Result<Boolean> result = spy.doWxMiniAppUserMemberLogin("ea", "uid", arg)
        then:
        result == resultMock
        where:
        arg                                                                                      | listByEaAndPhoneMock             | getByEaMock                                              | saveMemberByLoginFormDataMock                                                   | checkAndAddStaffUserBindSyncMock | resultMock
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll())             | null                             | null                                                     | null                                                                            | true                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.PARAMS_ERROR, true)
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | []                               | null                                                     | null                                                                            | true                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED, true)
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | []                               | new MemberConfigEntity(directLoginAndRegistration: true) | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR) | true                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.SYSTEM_ERROR, true)
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | []                               | new MemberConfigEntity(directLoginAndRegistration: true) | com.facishare.marketing.common.result.Result.newSuccess("aa")                   | true                             | com.facishare.marketing.common.result.Result.newSuccess(true)
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | []                               | new MemberConfigEntity(directLoginAndRegistration: true) | com.facishare.marketing.common.result.Result.newSuccess("aa")                   | false                            | com.facishare.marketing.common.result.Result.newSuccess(false)

        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | [new ObjectData("phone": "133")] | new MemberConfigEntity(directLoginAndRegistration: true) | com.facishare.marketing.common.result.Result.newSuccess("aa")                   | false                            | com.facishare.marketing.common.result.Result.newSuccess(true)
        new CustomizeFormDataEnrollArg(submitContent: new CustomizeFormDataEnroll(phone: "133")) | [new ObjectData("phone": "133")] | new MemberConfigEntity(directLoginAndRegistration: true) | com.facishare.marketing.common.result.Result.newSuccess("aa")                   | true                             | com.facishare.marketing.common.result.Result.newError(SHErrorCode.STAFF_MEMBER_LOGIN_REFRESH_TOKEN, true)

    }

}