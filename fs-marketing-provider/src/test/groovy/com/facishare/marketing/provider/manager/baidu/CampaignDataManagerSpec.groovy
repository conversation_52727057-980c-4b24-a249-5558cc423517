package com.facishare.marketing.provider.manager.baidu

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.common.util.DateUtil
import com.facishare.marketing.provider.advertiser.headlines.HeadlinesRequestResult
import com.facishare.marketing.provider.baidu.RequestResult
import com.facishare.marketing.provider.baidu.ResultHeader
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao
import com.facishare.marketing.provider.dao.UserMarketingWxServiceAccountRelationDao
import com.facishare.marketing.provider.dao.baidu.BaiduDataStatusDAO
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.facishare.marketing.provider.manager.advertiser.AdMarketingHandlerActionManager
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import org.apache.commons.lang.time.DateUtils
import spock.lang.Specification
import spock.lang.Unroll

class CampaignDataManagerSpec extends Specification {

    def adAccountManager = Mock(AdAccountManager)
    def dataStatusDAO = Mock(BaiduDataStatusDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def eieaConverter = Mock(EIEAConverter)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def adMarketingHandlerActionManager = Mock(AdMarketingHandlerActionManager)
    def headlinesAdMarketingManager = Mock(HeadlinesAdMarketingManager)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def userMarketingWxServiceAccountRelationDao = Mock(UserMarketingWxServiceAccountRelationDao)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def memberAccessibleCampaignDAO = Mock(MemberAccessibleCampaignDAO)
    def tencentAdMarketingManager = Mock(TencentAdMarketingManager)
    def redisManager = Mock(RedisManager)


    def campaignDataManager = new CampaignDataManager(
            "adAccountManager": adAccountManager,
            "dataStatusDAO": dataStatusDAO,
            "crmV2Manager": crmV2Manager,
            "eieaConverter": eieaConverter,
            "metadataControllerServiceManager": metadataControllerServiceManager,
            "adMarketingHandlerActionManager": adMarketingHandlerActionManager,
            "headlinesAdMarketingManager": headlinesAdMarketingManager,
            "marketingActivityRemoteManager": marketingActivityRemoteManager,
            "appVersionManager": appVersionManager,
            "customizeFormDataUserDAO": customizeFormDataUserDAO,
            "userMarketingBrowserUserRelationDao": userMarketingBrowserUserRelationDao,
            "userMarketingWxServiceAccountRelationDao": userMarketingWxServiceAccountRelationDao,
            "userMarketingAccountDAO": userMarketingAccountDAO,
            "memberAccessibleCampaignDAO": memberAccessibleCampaignDAO,
            "tencentAdMarketingManager": tencentAdMarketingManager,
            "redisManager": redisManager
    )


    @Unroll
    def "refreshPeriodically"() {
        given:
        dataStatusDAO.listForRefresh() >> refreshList
        marketingActivityRemoteManager.enterpriseStop(_) >> stop
        appVersionManager.getCurrentAppVersion(*_) >> "fff"
        when:
        campaignDataManager.refreshPeriodically()
        then:
        noExceptionThrown()
        where:
        refreshList                   | stop
        null                          | false
        [new BaiduDataStatusEntity()] | true
        [new BaiduDataStatusEntity()] | false
    }


    @Unroll
    def "refreshPeriodicallyByStatusId"() {
        given:
        dataStatusDAO.listForRefresh() >> refreshList
        marketingActivityRemoteManager.enterpriseStop(_) >> stop
        appVersionManager.getCurrentAppVersion(*_) >> "fff"
        dataStatusDAO.queryById(*_) >> new BaiduDataStatusEntity(ea: "ea");
        adAccountManager.queryAccountById(*_) >> new AdAccountEntity(status: status)
        redisManager.lock(*_) >> lock
        redisManager.unLock(*_) >> true
        when:
        campaignDataManager.refreshPeriodicallyByStatusId("id1")
        then:
        noExceptionThrown()
        where:
        refreshList                   | stop  | status | lock
        null                          | false | 0      | true
        [new BaiduDataStatusEntity()] | true  | 0      | false
        [new BaiduDataStatusEntity()] | true  | 0      | false
        [new BaiduDataStatusEntity()] | false | 1      | true
    }

    @Unroll
    def "refreshPeriodicallyByStatusIdInner"() {
        given:
        dataStatusDAO.listForRefresh() >> refreshList
        marketingActivityRemoteManager.enterpriseStop(_) >> stop
        appVersionManager.getCurrentAppVersion(*_) >> "fff"
        dataStatusDAO.queryById(*_) >> new BaiduDataStatusEntity(ea: "ea");
        adAccountManager.queryAccountById(*_) >> new AdAccountEntity(status: status)
        redisManager.lock(*_) >> lock
        redisManager.unLock(*_) >> true
        when:
        campaignDataManager.refreshPeriodicallyByStatusIdInner("id1")
        then:
        noExceptionThrown()
        where:
        refreshList                   | stop  | status | lock
        null                          | false | 0      | true
        [new BaiduDataStatusEntity()] | true  | 0      | false
        [new BaiduDataStatusEntity()] | true  | 0      | false
        [new BaiduDataStatusEntity()] | false | 1      | true
    }

    @Unroll
    def "refresh"() {
        given:
        adAccountManager.queryEnableAccountById(*_) >> adAccount
        dataStatusDAO.updateRefreshResult(*_) >> true
        dataStatusDAO.updateDataStatusForFail(*_) >> true
        dataStatusDAO.updateDataStatus(*_) >> true
        headlinesAdMarketingManager.refreshHeadlinesPromotion(*_) >> { println "ffff" }
        headlinesAdMarketingManager.refreshHeadlinePromotionData(*_) >> { println "ffff" }
        headlinesAdMarketingManager.refreshHeadlinesAd(*_) >> false
        headlinesAdMarketingManager.refreshHeadlinesAdData(*_) >> false
        tencentAdMarketingManager.refreshTencentAdGroupPeriodically(*_) >> { printf "fffff" }
        def spy = Spy(campaignDataManager)
        spy.refreshAccountInfo(*_) >> false
        spy.refreshCampaignInfo(*_) >> false
        spy.refreshCampaignData(*_) >> false
        spy.refreshMarketingEventLeads(*_) >> false

        when:
        spy.refresh(dataStatus, 1, source)
        then:
        noExceptionThrown()
        where:
        source     | adAccount                                          | dataStatus
        "百度"     | null                                               | new BaiduDataStatusEntity(adAccountId: "11")
        "百度"     | new AdAccountEntity(status: 0, source: "百度")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 3)
        "百度"     | new AdAccountEntity(status: 0, source: "百度")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 0, refreshTime: new Date(System.currentTimeMillis() - 1000 * 60 * 10))
        "百度"     | new AdAccountEntity(status: 0, source: "百度")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 0, refreshTime: new Date(System.currentTimeMillis() - 1000 * 50))
        "百度"     | new AdAccountEntity(status: 0, source: "百度")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: null, refreshTime: new Date(System.currentTimeMillis() - 1000 * 60 * 70))
        "百度"     | new AdAccountEntity(status: 0, source: "百度")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 1, refreshTime: new Date(System.currentTimeMillis() - 1000 * 60 * 70))
        "百度"     | new AdAccountEntity(status: 0, source: "百度")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 1, refreshTime: new Date(System.currentTimeMillis() - 1000 * 60))
        "巨量引擎" | new AdAccountEntity(status: 0, source: "巨量引擎") | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 1)
        "腾讯"     | new AdAccountEntity(status: 0, source: "腾讯")     | new BaiduDataStatusEntity(adAccountId: "11", refreshStatus: 1)
    }


    @Unroll
    def "queryAccountInvalidCode"() {
        given:
        when:
        campaignDataManager.queryAccountInvalidCode(result)
        then:
        noExceptionThrown()
        where:
        result << [new RequestResult<>(header: getHeader(0, [])),
                   new RequestResult<>(header: getHeader(1, [])),
                   new RequestResult<>(header: getHeader(1, getFailures(8102))),
                   new RequestResult<>(header: getHeader(1, getFailures(8201))),
                   new RequestResult<>(header: getHeader(1, getFailures(8401))),
                   new RequestResult<>(header: getHeader(1, getFailures(9999)))

        ]
    }

    ResultHeader getHeader(status, failList) {
        ResultHeader header = new ResultHeader()
        header.setStatus(status)
        header.setFailures(failList)
        return header
    }

    List<ResultHeader.Failure> getFailures(int code) {
        return [new ResultHeader.Failure(code: code)]
    }

    @Unroll
    def "queryHeadlinesAccountInvalidCode"() {
        given:
        when:
        campaignDataManager.queryHeadlinesAccountInvalidCode(result)
        then:
        noExceptionThrown()
        where:
        result << [
                new HeadlinesRequestResult(code: 0),
                new HeadlinesRequestResult(code: 40001),
                new HeadlinesRequestResult(code: 40002),
                new HeadlinesRequestResult(code: 40003),
                new HeadlinesRequestResult(code: 40100),
                new HeadlinesRequestResult(code: 40101),
                new HeadlinesRequestResult(code: 40102),
                new HeadlinesRequestResult(code: 40103),
                new HeadlinesRequestResult(code: 40104),
                new HeadlinesRequestResult(code: 40105),
                new HeadlinesRequestResult(code: 40106),
                new HeadlinesRequestResult(code: 40107),
                new HeadlinesRequestResult(code: 40108),
                new HeadlinesRequestResult(code: 40109),
                new HeadlinesRequestResult(code: 40200),
                new HeadlinesRequestResult(code: 40201),
                new HeadlinesRequestResult(code: 40202),
                new HeadlinesRequestResult(code: 40300),
                new HeadlinesRequestResult(code: 40301),
                new HeadlinesRequestResult(code: 50000),
                new HeadlinesRequestResult(code: 61002),
                new HeadlinesRequestResult(code: 61003),
                new HeadlinesRequestResult(code: 40900),
                new HeadlinesRequestResult(code: 44444),

        ]
    }


    @Unroll
    def "getCampaignMembersObjId"() {
        given:
        customizeFormDataUserDAO.getMarketingUserAccountByEaAndUid(*_) >> "gg"
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity(phone: "ffff")
        userMarketingWxServiceAccountRelationDao.getMarketingUserAccountByEaAndWxAppIdAndWxOpenId(*_) >> new UserMarketingAccountEntity(phone: "ffff")
        userMarketingBrowserUserRelationDao.getMarketingUserAccountByEaAndBrowserUserId(*_) >> new UserMarketingAccountEntity(phone: "ffff")
        memberAccessibleCampaignDAO.getCampaignIdByPhone(*_) >> "ffefefef"
        when:
        campaignDataManager.getCampaignMembersObjId("88146", "", uid, openId, wxAppId, fingerPrint)
        then:
        noExceptionThrown()
        where:
        uid     | openId | wxAppId   | fingerPrint   | result
        "12345" | "1234" | "wxAppId" | "fingerPrint" | 0
        null    | "1234" | "wxAppId" | "fingerPrint" | 0
        null    | null   | null      | "fingerPrint" | 0
    }

    @Unroll
    def "getLeadsDateListByMarketingEventId"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        metadataControllerServiceManager.getTotal(*_) >> 1
        when:
        campaignDataManager.getLeadsDateListByMarketingEventId("88146", "eventId", new Date(), DateUtil.plusDay(new Date(), 3), null)
        then:
        noExceptionThrown()
        where:
        uid     | openId | wxAppId   | fingerPrint   | result
        "12345" | "1234" | "wxAppId" | "fingerPrint" | 0
        null    | "1234" | "wxAppId" | "fingerPrint" | 0
        null    | null   | null      | "fingerPrint" | 0
    }

    @Unroll
    def "querySpreadChannelStatByMarketingEventId"() {
        given:
        def spy = Spy(campaignDataManager)
        spy.getLeadsCountBySpreadChannel(*_) >> 1
        when:
        spy.querySpreadChannelStatByMarketingEventId("88146", "eventId", ["1": "2"])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getCampaignClueMap"() {
        given:
        def spy = Spy(campaignDataManager)
        spy.getLeadsCountByMarketingEvent(*_) >> 1
        when:
        spy.getCampaignClueMap("88146", ["eventId"], System.currentTimeMillis(), System.currentTimeMillis(), null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getCagetLeadsCountBySpreadChannelpaignClueMap"() {
        given:
        metadataControllerServiceManager.getTotal(*_) >> 1
        when:
        campaignDataManager.getLeadsCountBySpreadChannel("88146", "eventId", "channel")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getLeadsCountByMarketingEvent"() {
        given:
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 10
        when:
        campaignDataManager.getLeadsCountByMarketingEvent("88146", eventIdList, System.currentTimeMillis(), System.currentTimeMillis(), "百度")
        then:
        noExceptionThrown()
        where:
        eventIdList << [null, [], ["id1"]]
    }

    @Unroll
    def "refreshMarketingEventLeads"() {
        given:
        adMarketingHandlerActionManager.getAdMarketingActionManager(*_) >> new BaiduAdMarketingManager()
        when:
        campaignDataManager.refreshMarketingEventLeads(account, day, dateList)
        then:
        noExceptionThrown()
        where:
        account               | day  | dateList
        null                  | null | null
        new AdAccountEntity() | null | null
        new AdAccountEntity() | 1    | [new Date()]
    }

    @Unroll
    def "refreshCampaignData"() {
        given:
        adMarketingHandlerActionManager.getAdMarketingActionManager(*_) >> new BaiduAdMarketingManager()
        when:
        campaignDataManager.refreshCampaignData(new AdAccountEntity(source: "百度"), 1)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "refreshCampaignInfo"() {
        given:
        adMarketingHandlerActionManager.getAdMarketingActionManager(*_) >> new BaiduAdMarketingManager()
        when:
        campaignDataManager.refreshCampaignInfo(new AdAccountEntity(source: "百度"))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "refreshAccountInfo"() {
        given:
        adMarketingHandlerActionManager.getAdMarketingActionManager(*_) >> new BaiduAdMarketingManager()
        when:
        campaignDataManager.refreshAccountInfo(new AdAccountEntity(source: "百度"))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "isCanRefresh"() {
        given:
        dataStatusDAO.updateRefreshResult(*_) >> true
        dataStatusDAO.updateDataStatusForFail(*_) >> true
        dataStatusDAO.updateDataStatus(*_) >> true
        when:
        campaignDataManager.isCanRefresh(dataStatus)
        then:
        noExceptionThrown()
        where:
        dataStatus << [new BaiduDataStatusEntity(refreshStatus: 3),
                       new BaiduDataStatusEntity(refreshStatus: 0, refreshTime: DateUtil.plusDay(new Date(), -1)),
                       new BaiduDataStatusEntity(refreshStatus: 0, refreshTime: null),
                       new BaiduDataStatusEntity(refreshStatus: null, refreshTime: DateUtil.plusDay(new Date(), -1)),
                       new BaiduDataStatusEntity(refreshStatus: 1, refreshTime: new Date()),
                       new BaiduDataStatusEntity(refreshStatus: 1, refreshTime: DateUtil.plusDay(new Date(), -1))
        ]
    }

    @Unroll
    def "refreshByEaAndAdAccountId"() {
        given:
        dataStatusDAO.queryRefreshStatus(*_) >> data
        when:
        campaignDataManager.refreshByEaAndAdAccountId("ea", "ad", "sss", 1)
        then:
        noExceptionThrown()
        where:
        data << [null, new BaiduDataStatusEntity()]
    }
}
