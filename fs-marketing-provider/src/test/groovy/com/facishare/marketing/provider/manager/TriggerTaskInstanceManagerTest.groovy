package com.facishare.marketing.provider.manager

import com.alibaba.fastjson.JSONObject
import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult
import com.facishare.marketing.api.result.sms.GroupSendResult
import com.facishare.marketing.api.service.AccountService
import com.facishare.marketing.api.service.sms.SendService
import com.facishare.marketing.common.enums.MarketingSceneType
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.BoardCardArg
import com.facishare.marketing.common.typehandlers.value.Email
import com.facishare.marketing.common.typehandlers.value.IntegerList
import com.facishare.marketing.common.typehandlers.value.SendSopSetting
import com.facishare.marketing.common.typehandlers.value.SendUnionMessageArg
import com.facishare.marketing.common.typehandlers.value.SendUnionMessageExtendArg
import com.facishare.marketing.common.typehandlers.value.SendWxTemplateMsgArg
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.common.typehandlers.value.TagNameList
import com.facishare.marketing.common.util.GsonUtil
import com.facishare.marketing.outapi.result.MaterialWxPresentMsg
import com.facishare.marketing.outapi.service.MaterialService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.mail.MailSendReplyDAO
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO
import com.facishare.marketing.provider.dao.mail.MailSendTaskResultDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendGroupResultDAO
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao
import com.facishare.marketing.provider.dto.MarketingUserWithEmail
import com.facishare.marketing.provider.dto.conference.ConferenceEnrollBaseInfoDTO
import com.facishare.marketing.provider.entity.*
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity
import com.facishare.marketing.provider.innerArg.qywx.AddNewMsgTemplateArg
import com.facishare.marketing.provider.innerResult.mail.SendEmailResult
import com.facishare.marketing.provider.innerResult.qywx.AddMsgTemplateResult
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupListResult
import com.facishare.marketing.provider.innerResult.qywx.SpreadQywxMiniappMessageResult
import com.facishare.marketing.provider.innerResult.qywx.UploadMediaResult
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.mail.MailManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.*
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatMessageRestService
import com.facishare.wechat.proxy.common.result.ModelResult
import com.facishare.wechat.proxy.service.WechatMessageService
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService
import com.fxiaoke.crmrestapi.common.data.Filter
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.google.common.collect.Lists
import org.junit.platform.commons.util.StringUtils
import spock.lang.Specification
import spock.lang.Unroll

class TriggerTaskInstanceManagerTest extends Specification {

    def triggerTaskInstanceManager = new TriggerTaskInstanceManager()

    def sceneTriggerDao = Mock(SceneTriggerDao)
    def triggerSnapshotDao = Mock(TriggerSnapshotDao)
    def triggerInstanceDao = Mock(TriggerInstanceDao)
    def triggerTaskSnapshotDao = Mock(TriggerTaskSnapshotDao)
    def triggerTaskInstanceDao = Mock(TriggerTaskInstanceDao)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def sendService = Mock(SendService)
    def materialService = Mock(MaterialService)
    def groupMessageDefaultCoverPath = "test";
    def fileManager = Mock(FileManager)
    def userMarketingWxServiceAccountRelationDao = Mock(UserMarketingWxServiceAccountRelationDao)
    def marketingUserGroupManager = Mock(MarketingUserGroupManager)
    def mailManager = Mock(MailManager)
    def wechatMessageService = Mock(WechatMessageService)
    def marketingTriggerDao = Mock(MarketingTriggerDao)
    def conferenceDAO = Mock(ConferenceDAO)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def outerServiceWechatService = Mock(OuterServiceWechatService)
    def host = "test"
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def boardCardDao = Mock(BoardCardDao)
    def boardManager = Mock(BoardManager)
    def webHookManager = Mock(WebHookManager)
    def displayOrderManager = Mock(DisplayOrderManager)
    def userMarketingCrmLeadAccountRelationDao = Mock(UserMarketingCrmLeadAccountRelationDao)
    def employeeMsgSender = Mock(EmployeeMsgSender)
    def objectManager = Mock(com.facishare.marketing.provider.manager.kis.ObjectManager)
    def sceneTriggerManager = Mock(SceneTriggerManager)
    def mailSendTaskDAO = Mock(MailSendTaskDAO)
    def mailSendReplyDAO = Mock(MailSendReplyDAO)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def wechatMessageRestService = Mock(WechatMessageRestService)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def redisManager = Mock(RedisManager)
    def fileV2Manager = Mock(FileV2Manager)
    def momentManager = Mock(MomentManager)
    def qywxManager = Mock(QywxManager)
    def httpManager = Mock(HttpManager)
    def userMarketingWxWorkExternalUserRelationDao = Mock(UserMarketingWxWorkExternalUserRelationDao)
    def eaWechatAccountBindDao = Mock(EaWechatAccountBindDao)
    def qywxCustomerAppInfoDAO = Mock(QywxCustomerAppInfoDAO)
    def qywxVirtualFsUserManager = Mock(QywxVirtualFsUserManager)
    def customerGroupManager = Mock(CustomerGroupManager)
    def mwSmsTemplateDao = Mock(MwSmsTemplateDao)
    def accountService = Mock(AccountService)
    def groupSendMessageManager = Mock(GroupSendMessageManager)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def eieaConverter = Mock(EIEAConverter)
    def qywxAddressBookManager = Mock(QywxAddressBookManager)
    def qywxAttachmentsRelationDAO = Mock(QywxAttachmentsRelationDAO)
    def qywxGroupSendGroupResultDAO = Mock(QywxGroupSendGroupResultDAO)
    def emailSendRecordDetailObjManager = Mock(EmailSendRecordDetailObjManager)
    def qywxUserManager = Mock(QywxUserManager)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def outLinkMktParamManager = Mock(OutLinkMktParamManager)
    def enterpriseSpreadRecordManager = Mock(EnterpriseSpreadRecordManager)

    def setup() {
        triggerTaskInstanceManager.sceneTriggerDao = sceneTriggerDao
        triggerTaskInstanceManager.triggerSnapshotDao = triggerSnapshotDao
        triggerTaskInstanceManager.triggerInstanceDao = triggerInstanceDao
        triggerTaskInstanceManager.triggerTaskSnapshotDao = triggerTaskSnapshotDao
        triggerTaskInstanceManager.triggerTaskInstanceDao = triggerTaskInstanceDao
        triggerTaskInstanceManager.campaignMergeDataManager = campaignMergeDataManager
        triggerTaskInstanceManager.userMarketingAccountManager = userMarketingAccountManager
        triggerTaskInstanceManager.campaignMergeDataDAO = campaignMergeDataDAO
        triggerTaskInstanceManager.userMarketingAccountDAO = userMarketingAccountDAO
        triggerTaskInstanceManager.sendService = sendService
        triggerTaskInstanceManager.materialService = materialService
        triggerTaskInstanceManager.groupMessageDefaultCoverPath = groupMessageDefaultCoverPath
        triggerTaskInstanceManager.fileManager = fileManager
        triggerTaskInstanceManager.userMarketingWxServiceAccountRelationDao = userMarketingWxServiceAccountRelationDao
        triggerTaskInstanceManager.marketingUserGroupManager = marketingUserGroupManager
        triggerTaskInstanceManager.mailManager = mailManager
        triggerTaskInstanceManager.wechatMessageService = wechatMessageService
        triggerTaskInstanceManager.marketingTriggerDao = marketingTriggerDao
        triggerTaskInstanceManager.conferenceDAO = conferenceDAO
        triggerTaskInstanceManager.marketingLiveDAO = marketingLiveDAO
        triggerTaskInstanceManager.outerServiceWechatService = outerServiceWechatService
        triggerTaskInstanceManager.host = host
        triggerTaskInstanceManager.activityEnrollDataDAO = activityEnrollDataDAO
        triggerTaskInstanceManager.crmV2Manager = crmV2Manager
        triggerTaskInstanceManager.boardCardDao = boardCardDao
        triggerTaskInstanceManager.boardManager = boardManager
        triggerTaskInstanceManager.webHookManager = webHookManager
        triggerTaskInstanceManager.displayOrderManager = displayOrderManager
        triggerTaskInstanceManager.userMarketingCrmLeadAccountRelationDao = userMarketingCrmLeadAccountRelationDao
        triggerTaskInstanceManager.employeeMsgSender = employeeMsgSender
        triggerTaskInstanceManager.objectManager = objectManager
        triggerTaskInstanceManager.sceneTriggerManager = sceneTriggerManager
        triggerTaskInstanceManager.mailSendTaskDAO = mailSendTaskDAO
        triggerTaskInstanceManager.mailSendReplyDAO = mailSendReplyDAO
        triggerTaskInstanceManager.contentMarketingEventMaterialRelationDAO = contentMarketingEventMaterialRelationDAO
        triggerTaskInstanceManager.wechatMessageRestService = wechatMessageRestService
        triggerTaskInstanceManager.marketingActivityRemoteManager = marketingActivityRemoteManager
        triggerTaskInstanceManager.appVersionManager = appVersionManager
        triggerTaskInstanceManager.agentConfigDAO = agentConfigDAO
        triggerTaskInstanceManager.redisManager = redisManager
        triggerTaskInstanceManager.fileV2Manager = fileV2Manager
        triggerTaskInstanceManager.momentManager = momentManager
        triggerTaskInstanceManager.qywxManager = qywxManager
        triggerTaskInstanceManager.httpManager = httpManager
        triggerTaskInstanceManager.userMarketingWxWorkExternalUserRelationDao = userMarketingWxWorkExternalUserRelationDao
        triggerTaskInstanceManager.eaWechatAccountBindDao = eaWechatAccountBindDao
        triggerTaskInstanceManager.qywxCustomerAppInfoDAO = qywxCustomerAppInfoDAO
        triggerTaskInstanceManager.qywxVirtualFsUserManager = qywxVirtualFsUserManager
        triggerTaskInstanceManager.customerGroupManager = customerGroupManager
        triggerTaskInstanceManager.mwSmsTemplateDao = mwSmsTemplateDao
        triggerTaskInstanceManager.accountService = accountService
        triggerTaskInstanceManager.groupSendMessageManager = groupSendMessageManager
        triggerTaskInstanceManager.fsAddressBookManager = fsAddressBookManager
        triggerTaskInstanceManager.wechatWorkExternalUserObjManager = wechatWorkExternalUserObjManager
        triggerTaskInstanceManager.eieaConverter = eieaConverter
        triggerTaskInstanceManager.qywxAddressBookManager = qywxAddressBookManager
        triggerTaskInstanceManager.qywxAttachmentsRelationDAO = qywxAttachmentsRelationDAO
        triggerTaskInstanceManager.qywxGroupSendGroupResultDAO = qywxGroupSendGroupResultDAO
        triggerTaskInstanceManager.emailSendRecordDetailObjManager = emailSendRecordDetailObjManager
        triggerTaskInstanceManager.qywxUserManager = qywxUserManager
        triggerTaskInstanceManager.customizeFormDataUserDAO = customizeFormDataUserDAO
        triggerTaskInstanceManager.outLinkMktParamManager = outLinkMktParamManager
        triggerTaskInstanceManager.enterpriseSpreadRecordManager = enterpriseSpreadRecordManager

    }

    def randomVal(List list) {
        return list.get(new Random().nextInt(list.size()))
    }

    @Unroll
    def "startTaskInstanceByMarketingUserIdTest"() {
        given:
        triggerTaskInstanceDao.listTaskInstanceByUserMarketingId(*_) >> [new TriggerTaskInstanceEntity()]
        triggerInstanceDao.getById(*_) >> new TriggerInstanceEntity()
        sceneTriggerDao.getByTriggerIdAndSceneInfo(*_) >> new SceneTriggerEntity()
        def spy = Spy(triggerTaskInstanceManager)
        spy.finishTriggerTask(*_) >> {
            if (throwException) {
                throw new Exception("some exception")
            }
        }

        when:
        spy.startTaskInstanceByMarketingUserId(ea, marketingUserId, ["paramMap": "paramMap"])
        then:
        1 == 1

        where:
        ea     || marketingUserId || throwException
        "test" || null            || false
        "test" || "test"          || false
        "test" || "test"          || true

    }


    @Unroll
    def "finishTriggerTaskTest xxljob"() {
        given:
        triggerTaskInstanceDao.getById(*_) >> triggerTaskInstance
        marketingActivityRemoteManager.enterpriseStop(*_) >> isStop
        appVersionManager.getCurrentAppVersion(*_) >> "1"
        triggerInstanceDao.getById(*_) >> new TriggerInstanceEntity()
        sceneTriggerDao.getByTriggerIdAndSceneInfo(*_) >> sceneTrigger
        triggerSnapshotDao.getById(*_) >> new TriggerSnapshotEntity(snapshotStatus: 'enabled')
        triggerTaskInstanceDao.cancelDelayTask(*_) >> 1
        def spy = Spy(triggerTaskInstanceManager)
        spy.finishTriggerTask(_, _, _) >> false


        when:
        spy.finishTriggerTask("triggerTaskInstanceId")
        then:
        1 == 1

        where:
        triggerTaskInstance                                       || isStop || sceneTrigger
        null                                                      || false  || null
        new TriggerTaskInstanceEntity()                           || true   || null
        new TriggerTaskInstanceEntity(executeStatus: "executing") || false  || null
        new TriggerTaskInstanceEntity(executeStatus: "todo")      || false  || new SceneTriggerEntity(ea: "ea", lifeStatus: "disabled")
        new TriggerTaskInstanceEntity(executeStatus: "todo")      || false  || new SceneTriggerEntity(ea: "ea", lifeStatus: "enabled")
    }

    @Unroll
    def "buildTriggerTaskInstanceTest"() {
        given:
        triggerInstanceDao.getById(*_) >> new TriggerInstanceEntity(createTime: new Date())

        when:
        TriggerTaskInstanceEntity result = triggerTaskInstanceManager.buildTriggerTaskInstance("ea", paramsMap, new TriggerSnapshotEntity(triggerType: "single_timing"), "triggerInstanceId", triggerTaskSnapshot)
        then:
        1 == 1

        where:
        paramsMap                || triggerTaskSnapshot
        ["paramMap": "paramMap"] || new TriggerTaskSnapshotEntity(executeType: "delayed", executeDelayMinutes: 440, taskOffsetDay: 1, taskOffsetMinute: 440)
        ["paramMap": "paramMap"] || new TriggerTaskSnapshotEntity(taskOffsetDay: 1, taskOffsetMinute: 440)
    }

    @Unroll
    def "finishTriggerTaskTest"() {
        given:
        triggerTaskInstanceDao.updateExecuteStatus(*_) >> updated
        triggerTaskInstanceDao.getById(*_) >> triggerTaskInstance
        triggerInstanceDao.getById(*_) >> new TriggerInstanceEntity()
        triggerTaskSnapshotDao.getById(*_) >> triggerTaskSnapshot
        triggerTaskSnapshotDao.findNextNode(*_) >> nextNode
        triggerTaskInstanceDao.updateExecuteResult(*_) >> 0
        triggerInstanceDao.incrementSuccessTaskCount(*_) >> 0
        triggerInstanceDao.incrementFailTaskCount(*_) >> 0
        triggerTaskSnapshotDao.getNextTriggerTaskSnapshot(*_) >> new TriggerTaskSnapshotEntity()

        and:
        def spy = Spy(triggerTaskInstanceManager)
        spy.addTag(*_) >> executeResult
        spy.sendSmsMsg(*_) >> executeResult
        spy.sendWxMsg(*_) >> executeResult
        spy.sendWxTemplateMsg(*_) >> executeResult
        spy.sendEmailMsg(*_) >> executeResult
        spy.addBoard(*_) >> executeResult
        spy.invokeWebHook(*_) >> executeResult
        spy.sendUnionMsg(*_) >> executeResult
        spy.sendWorkWxMsg(*_) >> executeResult
        spy.sendWorkWxSop(*_) >> executeResult
        spy.branchJudgment(*_) >> nextNode
        spy.invokeNextNodeTask(*_) >> null


        when:
        spy.finishTriggerTask("ea", "triggerTaskInstanceId", paramMap)
        then:
        1 == 1

        where:
        updated || triggerTaskInstance                                                                                    || triggerTaskSnapshot                                                                                             || paramMap || executeResult                                            || nextNode
        0       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity()                                                                                 || null     || null                                                     || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "add_tag")                                                              || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_sms_msg")                                                         || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_wx_graphic_message")                                              || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_wx_text_msg")                                                     || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_wx_image_msg")                                                    || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_wx_news_msg")                                                     || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_wx_template_msg")                                                 || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_email_msg")                                                       || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "add_board")                                                            || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "invoke_webhook")                                                       || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "send_union_msg")                                                       || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "qywx_msg")                                                             || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "qywx_sop_task")                                                        || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity()                                                                        || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["waitingTime": 60])                       || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity(paramMap: "{}")                                                          || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["waitingTime": 60])                       || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity(executeStatus: "executing")                                              || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": true, "waitingTime": 60])  || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity(executeStatus: "listening", executeTime: new Date().getTime() + 100_000) || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": true, "waitingTime": 60])  || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity(executeStatus: "listening", executeTime: new Date().getTime() - 100_000) || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": true, "waitingTime": 60])  || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity(executeStatus: "listening")                                              || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": true, "waitingTime": 60])  || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || new TriggerTaskSnapshotEntity()
        1       || new TriggerTaskInstanceEntity(executeStatus: "listening")                                              || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": false, "waitingTime": 60]) || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || null
        1       || new TriggerTaskInstanceEntity(executeStatus: "listening")                                              || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": false, "waitingTime": 60]) || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || new TriggerTaskSnapshotEntity()
        1       || new TriggerTaskInstanceEntity(executeStatus: "listening")                                              || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": false])                    || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || new TriggerTaskSnapshotEntity()
        1       || new TriggerTaskInstanceEntity(paramMap: "{\"isQywxTiming\":true}",
                executeStatus: "listening")                                                                               || new TriggerTaskSnapshotEntity(taskType: "branch_judgment", property: ["enableOther": false])                    || null     || TriggerTaskInstanceManager.ExecuteResult.successResult() || new TriggerTaskSnapshotEntity()
    }

    @Unroll
    def 'Test invokeNextNodeTask'() {
        given:
        triggerSnapshotDao.getById(*_) >> null
        triggerTaskInstanceDao.batchInsert(*_) >> 1
        def spy = Spy(triggerTaskInstanceManager)
        spy.finishTriggerTask(*_) >> null
        spy.buildTriggerTaskInstance(*_) >> new TriggerTaskInstanceEntity(executeType: "immediately")

        when:
        spy.invokeNextNodeTask("ea", new TriggerInstanceEntity(), new TriggerTaskSnapshotEntity(), [:])
        then:
        noExceptionThrown()
    }

    def 'Test branchJudgment'() {
        given:
        triggerTaskSnapshotDao.getNextTriggerTaskSnapshot(*_) >> null
        def spy = Spy(triggerTaskInstanceManager)
        spy.branchConditionsMatch(*_) >> hit

        when:
        spy.branchJudgment("ea", new TriggerTaskSnapshotEntity(), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), [:])
        then:
        noExceptionThrown()

        when:
        def property = ["branchList": [["nextSerialNumber": nextSerialNumber]]]
        spy.branchJudgment("ea", new TriggerTaskSnapshotEntity(property: property), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), [:])
        then:
        noExceptionThrown()

        where:
        hit   | nextSerialNumber
        false | null
        true  | null
        true  | 1

    }

    @Unroll
    def "test branchConditionsMatch with branchType"() {
        given:
        def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
        triggerTaskInstanceManager.customizeFormDataDAO = customizeFormDataDAO
        def customizeFormDataManager = Mock(CustomizeFormDataManager)
        triggerTaskInstanceManager.customizeFormDataManager = customizeFormDataManager
        def userMarketingAccountRelationManager = Mock(UserMarketingAccountRelationManager)
        triggerTaskInstanceManager.userMarketingAccountRelationManager = userMarketingAccountRelationManager
        def objectGroupRelationDAO = Mock(ObjectGroupRelationDAO)
        triggerTaskInstanceManager.objectGroupRelationDAO = objectGroupRelationDAO

        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> new CustomizeFormDataUserEntity()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()
        customizeFormDataManager.conversionEnrollDataPic(*_) >> new HashMap<>()
        customizeFormDataManager.buildAreaInfoByEnrollData(*_) >> null
        userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(*_) >> ["test"]

        and:
        def branch = null
        def submitMap = null
        def params = null

        when:
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "OTHER", null, null)
        then:
        thrown(IllegalArgumentException.class)

        when: '表单值为空'
        branch = JSONObject.parse("{\"children\":[{\"name\":\"发送短信\",\"uuid\":\"********-6c31-4252-a446-d89b4cdc1c73\",\"nextNode\":\"\",\"nodeType\":\"action\",\"prevNode\":\"a8141712-320a-458f-9208-2c726cb98111\",\"taskType\":\"send_sms_msg\",\"actionIcon\":\"iconhuifuwenben\",\"actionType\":\"sms\",\"executeType\":\"immediately\",\"serialNumber\":1,\"smsTemplateId\":\"c77acf56b5044098b23b5c704917824f\",\"executeTimeType\":\"day\",\"preSerialNumber\":0,\"smsTemplateName\":\"测试短链\",\"smsTemplateContent\":\"点击fs8.ceshi112.com/8rq9d6\",\"executeDelayMinutes\":1440,\"executeTimeTypeValue\":1}],\"nextNode\":\"********-6c31-4252-a446-d89b4cdc1c73\",\"branchName\":\"香蕉和唱歌\",\"conditions\":[{\"key\":\"texts1_4f8df38cbe49b15a\",\"value\":[\"唱歌\"],\"operator\":\"CONTAINS\"},{\"key\":\"text7_4d966375975e44bc\",\"value\":\"香蕉\",\"operator\":\"EQ\"}],\"readableText\":[\"多选包含唱歌\",\"单选等于香蕉\"],\"nextSerialNumber\":1,\"selectedMaterial\":{\"targetObjectId\":\"01adcf8154244bbb96a784cc1212510c\",\"targetObjectName\":\"726-2表单\",\"targetObjectType\":16}}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_SUBMIT_FORM", branch, null)
        then:
        noExceptionThrown()

        when:
        customizeFormDataManager.generateEnrollData(*_) >>> [
                GsonUtil.fromJson("{\"text7_4d966375975e44bc\":\"香蕉\",\"user_enroll_time\":\"2024-07-26 16:08:02\",\"address\":\"测试\",\"province\":\"四川省\",\"phone\":\"13762692877\",\"texts1_4f8df38cbe49b15a\":[\"唱歌\"],\"city\":\"泸州市\",\"district\":\"合江县\",\"name\":\"小张\"}", HashMap.class),
                GsonUtil.fromJson("{}", HashMap.class)
        ]
        branch = JSONObject.parse("{\"conditions\":[{\"key\":\"texts1_4f8df38cbe49b15a\",\"value\":[\"唱歌\"],\"operator\":\"CONTAINS\"},{\"key\":\"texts1_4f8df38cbe49b15a\",\"value\":[\"唱歌\"],\"operator\":\"EQ\"},{\"key\":\"text7_4d966375975e44bc\",\"value\":\"香蕉\",\"operator\":\"EQ\"},{\"key\":\"text7_4d966375975e44bc\",\"value\":\"香蕉\",\"operator\":\"CONTAINS\"}],\"nextSerialNumber\":1,\"selectedMaterial\":{\"targetObjectId\":\"01adcf8154244bbb96a784cc1212510c\",\"targetObjectName\":\"726-2表单\",\"targetObjectType\":16}}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_SUBMIT_FORM", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when: '未命中'
        branch = JSONObject.parse("{\"conditions\":[{\"key\":\"texts1_4f8df38cbe49b15a\",\"value\":[\"唱歌\"],\"operator\":\"CONTAINS\"},{\"key\":\"texts1_4f8df38cbe49b15a\",\"value\":[\"唱歌\"],\"operator\":\"EQ\"},{\"key\":\"text7_4d966375975e44bc\",\"value\":\"香蕉\",\"operator\":\"EQ\"},{\"key\":\"text7_4d966375975e44bc\",\"value\":\"香蕉\",\"operator\":\"CONTAINS\"}],\"nextSerialNumber\":1,\"selectedMaterial\":{\"targetObjectId\":\"01adcf8154244bbb96a784cc1212510c\",\"targetObjectName\":\"726-2表单\",\"targetObjectType\":16}}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_SUBMIT_FORM", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when://自定义对象
        branch = JSONObject.parse("{\"children\":[{\"name\":\"发送短信\",\"uuid\":\"71070aa8-e006-40b2-ad6e-db972c785ec0\",\"nextNode\":\"\",\"nodeType\":\"action\",\"prevNode\":\"1fd9e844-2b9d-4b73-9c8d-f3988168ed33\",\"taskType\":\"send_sms_msg\",\"actionIcon\":\"iconhuifuwenben\",\"actionType\":\"sms\",\"executeType\":\"immediately\",\"smsTemplateId\":\"c77acf56b5044098b23b5c704917824f\",\"executeTimeType\":\"day\",\"smsTemplateName\":\"测试短链\",\"smsTemplateContent\":\"点击fs8.ceshi112.com/8rq9d6\",\"executeDelayMinutes\":1440,\"executeTimeTypeValue\":1}],\"nextNode\":\"71070aa8-e006-40b2-ad6e-db972c785ec0\",\"branchName\":\"分支1\",\"conditions\":[{\"query\":{\"filters\":[{\"operator\":\"LIKE\",\"fieldName\":\"name\",\"fieldType\":1,\"fieldValues\":[\"漆漆黑黑\"],\"fieldNameLabel\":\"微信昵称\"}]},\"objectAPIName\":\"ProductObj\"}],\"readableText\":[\"微信用户的微信昵称字段条件共1个\"],\"objectApiName\":\"ProductObj\",\"nextSerialNumber\":2}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_OBJECT_VALUE", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when:
        crmV2Manager.countCrmObjectByFilterV3(*_) >>> [0, 1]
        branch = JSONObject.parse("{\"children\":[{\"name\":\"发送短信\",\"uuid\":\"71070aa8-e006-40b2-ad6e-db972c785ec0\",\"nextNode\":\"\",\"nodeType\":\"action\",\"prevNode\":\"1fd9e844-2b9d-4b73-9c8d-f3988168ed33\",\"taskType\":\"send_sms_msg\",\"actionIcon\":\"iconhuifuwenben\",\"actionType\":\"sms\",\"executeType\":\"immediately\",\"smsTemplateId\":\"c77acf56b5044098b23b5c704917824f\",\"executeTimeType\":\"day\",\"smsTemplateName\":\"测试短链\",\"smsTemplateContent\":\"点击fs8.ceshi112.com/8rq9d6\",\"executeDelayMinutes\":1440,\"executeTimeTypeValue\":1}],\"nextNode\":\"71070aa8-e006-40b2-ad6e-db972c785ec0\",\"branchName\":\"分支1\",\"conditions\":[{\"query\":{\"filters\":[{\"operator\":\"LIKE\",\"fieldName\":\"name\",\"fieldType\":1,\"fieldValues\":[\"漆漆黑黑\"],\"fieldNameLabel\":\"微信昵称\"}]},\"objectAPIName\":\"WechatFanObj\"}],\"readableText\":[\"微信用户的微信昵称字段条件共1个\"],\"objectApiName\":\"WechatFanObj\",\"nextSerialNumber\":2}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_OBJECT_VALUE", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when:
        branch = JSONObject.parse("{\"children\":[{\"name\":\"发送短信\",\"uuid\":\"71070aa8-e006-40b2-ad6e-db972c785ec0\",\"nextNode\":\"\",\"nodeType\":\"action\",\"prevNode\":\"1fd9e844-2b9d-4b73-9c8d-f3988168ed33\",\"taskType\":\"send_sms_msg\",\"actionIcon\":\"iconhuifuwenben\",\"actionType\":\"sms\",\"executeType\":\"immediately\",\"smsTemplateId\":\"c77acf56b5044098b23b5c704917824f\",\"executeTimeType\":\"day\",\"smsTemplateName\":\"测试短链\",\"smsTemplateContent\":\"点击fs8.ceshi112.com/8rq9d6\",\"executeDelayMinutes\":1440,\"executeTimeTypeValue\":1}],\"nextNode\":\"71070aa8-e006-40b2-ad6e-db972c785ec0\",\"branchName\":\"分支1\",\"conditions\":[{\"query\":{\"filters\":[{\"operator\":\"LIKE\",\"fieldName\":\"name\",\"fieldType\":1,\"fieldValues\":[\"漆漆黑黑\"],\"fieldNameLabel\":\"微信昵称\"}]},\"objectAPIName\":\"WechatFanObj\"}],\"readableText\":[\"微信用户的微信昵称字段条件共1个\"],\"objectApiName\":\"WechatFanObj\",\"nextSerialNumber\":2}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_OBJECT_VALUE", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when:
        userMarketingAccountManager.listTagNameListByUserMarketingAccountIds(*_) >>> [
                ["aa": [new TagName(firstTagName: "客户等级", secondTagName: "一般")]],
                ["aa": [new TagName(firstTagName: "客户等级", secondTagName: "一般")]],
                [:]
        ]
        branch = JSONObject.parse("{\"nextNode\":\"f24858dc-21df-4665-9db4-3f485e4ad93f\",\"tagNames\":[{\"nameid\":\"客户等级:一般\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"一般\"},{\"nameid\":\"客户等级:重要\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"重要\"}],\"branchName\":\"一般 重要\",\"excludeTags\":true,\"tagOperator\":\"HASANYOF\",\"excludeTagNames\":[{\"nameid\":\"客户等级:不一般\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"不一般\"},{\"nameid\":\"客户等级:不重要\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"不重要\"}],\"nextSerialNumber\":1}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(marketingUserId: "aa"), "BASE_TAG", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when:
        branch = JSONObject.parse("{\"nextNode\":\"f24858dc-21df-4665-9db4-3f485e4ad93f\",\"tagNames\":[{\"nameid\":\"客户等级:一般\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"一般\"}],\"branchName\":\"一般 重要\",\"excludeTags\":true,\"tagOperator\":\"IN\",\"excludeTagNames\":[{\"nameid\":\"客户等级:不一般\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"不一般\"},{\"nameid\":\"客户等级:不重要\",\"firstTagName\":\"客户等级\",\"secondTagName\":\"不重要\"}],\"nextSerialNumber\":1}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(marketingUserId: "aa"), "BASE_TAG", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when:
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(marketingUserId: "aa"), "BASE_TAG", branch, new HashMap<>())
        then:
        noExceptionThrown()

        when:
        branch = JSONObject.parse("{ \"nextNode\": \"e5a5dbff-088f-4ffa-ab1a-9c20f1dc7b9c\", \"actionType\": \"MAIL\", \"branchName\": \"邮件发送成功\", \"actionStatus\": \"success\", \"readableText\": [\"邮件发送成功\"], \"nextSerialNumber\": 2}")
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_ACTION_RESULT", branch, ["actionType": "MAIL", "actionStatus": "success"])
        then:
        noExceptionThrown()

        when:
        branch = JSONObject.parse("{\"wxAppId\":\"\",\"nextNode\":\"94d6fa90-f0f2-428b-84b7-c81693d37442\",\"actionType\":39,\"branchName\":\"1\",\"objectList\":[],\"emailSubject\":[],\"materialScope\":\"all\",\"objectGroupId\":\"\",\"websitePageId\":\"\",\"websiteEventId\":\"\",\"nextSerialNumber\":2,\"targetObjectList\":[],\"wxServiceAccountMenu\":\"\"}")
        params = ["actionType": 39]
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_USER_BEHAVIOR", branch, params)
        then:
        noExceptionThrown()

        when:
        objectGroupRelationDAO.getByObjectId(*_) >> new ObjectGroupRelationEntity(groupId: "test")
        branch = JSONObject.parse("{\"wxAppId\":\"test\",\"nextNode\":\"94d6fa90-f0f2-428b-84b7-c81693d37442\",\"actionType\":1004,\"branchName\":\"1\",\"objectList\":[{\"objectType\":15,\"objectId\":\"test\"}],\"emailSubject\":[],\"materialScope\":\"group\",\"objectGroupId\":\"test\",\"websitePageId\":\"\",\"websiteEventId\":\"\",\"nextSerialNumber\":2,\"targetObjectList\":[],\"wxServiceAccountMenu\":\"\"}")
        params = ["actionType": 1004, "wxAppId": "test", "objectId": "test", "objectType": 15]
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_USER_BEHAVIOR", branch, params)
        then:
        noExceptionThrown()

        when:
        objectGroupRelationDAO.getByObjectId(*_) >> new ObjectGroupRelationEntity(groupId: "test")
        branch = JSONObject.parse("{\"wxAppId\":\"test\",\"nextNode\":\"94d6fa90-f0f2-428b-84b7-c81693d37442\",\"actionType\":1004,\"branchName\":\"1\",\"emailSubject\":[\"emailSubject\"],\"materialScope\":\"specify\",\"objectGroupId\":\"test\",\"websitePageId\":\"\",\"websiteEventId\":\"\",\"nextSerialNumber\":2,\"targetObjectList\":[],\"wxServiceAccountMenu\":\"\"}")
        params = ["actionType": 1004, "emailSubject": "emailSubject", "objectId": "test", "objectType": 15]
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_USER_BEHAVIOR", branch, params)
        then:
        noExceptionThrown()

        when:
        branch = JSONObject.parse("{\"wxAppId\":\"test\",\"nextNode\":\"94d6fa90-f0f2-428b-84b7-c81693d37442\",\"actionType\":1003,\"branchName\":\"1\",\"objectList\":[{\"objectType\":15,\"objectId\":\"test\"}],\"emailSubject\":[],\"materialScope\":\"specify\",\"objectGroupId\":\"\",\"websitePageId\":\"\",\"websiteEventId\":\"\",\"nextSerialNumber\":2,\"targetObjectList\":[],\"wxServiceAccountMenu\":\"\"}")
        params = ["actionType": 1003, "wxAppId": "test", "objectId": "test", "objectType": 15]
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_USER_BEHAVIOR", branch, params)
        then:
        noExceptionThrown()

        when:
        branch = JSONObject.parse("{\"wxAppId\":\"test\",\"nextNode\":\"94d6fa90-f0f2-428b-84b7-c81693d37442\",\"actionType\":1004,\"branchName\":\"1\",\"objectList\":[{\"objectType\":15,\"objectId\":\"test\"}],\"emailSubject\":[],\"materialScope\":\"specify\",\"objectGroupId\":\"\",\"websitePageId\":\"\",\"websiteEventId\":\"\",\"nextSerialNumber\":2,\"targetObjectList\":[],\"wxServiceAccountMenu\":\"\"}")
        params = ["actionType": 1004, "wxAppId": "test", "objectId": "test", "objectType": 15]
        triggerTaskInstanceManager.branchConditionsMatch(new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(), "BASE_USER_BEHAVIOR", branch, params)
        then:
        noExceptionThrown()
    }

    @Unroll
    def 'Test sendWorkWxSop 客户群SOP'() {
        given:
        triggerSnapshotDao.getById(*_) >> new TriggerSnapshotEntity(sendRange: 4)
        agentConfigDAO.queryAgentByEa(*_) >>> [null, new QywxCorpAgentConfigEntity()]
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa(*_) >>> [[], [new QywxCustomerAppInfoEntity(agentId: "1")]]
        momentManager.getOrCreateAccessToken(*_) >> null
        qywxManager.sendAgentMessage(*_) >>> [new SpreadQywxMiniappMessageResult(errcode: -1), new SpreadQywxMiniappMessageResult(errcode: 0)]
        customerGroupManager.queryCustomerListNew(*_) >> new CustomerGroupListResult(errcode: 0, groupList: [])
        triggerTaskInstanceDao.updateSubsidiaryData(*_) >> 0

        when:
        triggerTaskInstanceManager.sendWorkWxSop("ea", new TriggerTaskSnapshotEntity(), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity())
        then:
        noExceptionThrown()

        when:
        def i = 0
        while (true) {
            def result = triggerTaskInstanceManager.sendWorkWxSop("ea", new TriggerTaskSnapshotEntity(), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(marketingUserId: "test"))
            i++
            if (result == TriggerTaskInstanceManager.ExecuteResult.successResult() || i > 20) {
                return;
            }
        }
        then:
        1 == 1

    }

    @Unroll
    def 'Test sendWorkWxSop 客户SOP'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        triggerSnapshotDao.getById(*_) >> new TriggerSnapshotEntity(sendRange: 1)
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: "test")]
        spy.getOwnerByRules(*_) >>> [null, 1000]
        qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(*_) >> "1000"
        triggerTaskInstanceDao.hasNoticeOwner(*_) >>> [["success"], ["fail"], []]
        agentConfigDAO.queryAgentByEa(*_) >>> [null, new QywxCorpAgentConfigEntity()]
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa(*_) >>> [[], [new QywxCustomerAppInfoEntity(agentId: "1")]]
        momentManager.getOrCreateAccessToken(*_) >> "token"
        qywxManager.sendAgentMessage(*_) >>> [new SpreadQywxMiniappMessageResult(errcode: -1), new SpreadQywxMiniappMessageResult(errcode: 0)]
        spy.getMarketingAccountUserIds(*_) >> { String ea, TriggerSnapshotEntity triggerSnapshot, Map<Integer, List<String>> map -> map.put(1000, ["a", "b"]) }
        triggerTaskInstanceDao.updateSubsidiaryData(*_) >> 1

        when:
        def i = 0
        while (true) {
            def result = spy.sendWorkWxSop("ea", new TriggerTaskSnapshotEntity(sendSopSetting: new SendSopSetting()), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity())
            i++
            if (result == TriggerTaskInstanceManager.ExecuteResult.successResult() || i > 20) {
                return;
            }
        }
        then:
        1 == 1

    }

    @Unroll
    def 'Test sendWorkWxMsg 客户群'() {
        given:
        def enterpriseSpreadRecordManager = Mock(EnterpriseSpreadRecordManager)
        triggerTaskInstanceManager.enterpriseSpreadRecordManager = enterpriseSpreadRecordManager
        def spy = Spy(triggerTaskInstanceManager)

        agentConfigDAO.queryAgentByEa(*_) >>> [null, new QywxCorpAgentConfigEntity()]
        triggerSnapshotDao.getCurrentUseSnapshot(*_) >> new TriggerSnapshotEntity(sendRange: 4)
        momentManager.getOrCreateAccessToken(*_) >> "token"
        spy.setAttachments(*_) >> { String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, String accessToken, AddNewMsgTemplateArg arg -> arg.setAttachments([new AddNewMsgTemplateArg.Attachments()]) }
        spy.getMarketingEventId(*_) >> null
        qywxAddressBookManager.queryByEaAndUserId(*_) >> new QyWxAddressBookEntity()
        outLinkMktParamManager.sopAttachmentAppendMktParam(*_) >> null
        spy.addMsgTemplate(*_) >>> [null, new AddMsgTemplateResult(errcode: 0)]
        customerGroupManager.queryCustomerListNew(*_) >> new CustomerGroupListResult(errcode: 0, groupList: [new CustomerGroupListResult.GroupItem()])
        qywxGroupSendGroupResultDAO.insert(*_) >> 1

        spy.batchMarketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.marketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.sopFilterAndUpsert(*_) >>> [[], ['test']]
        enterpriseSpreadRecordManager.upsertList(*_) >> null

        when:
        spy.sendWorkWxMsg("ea", new TriggerTaskSnapshotEntity(sendSopSetting: new SendSopSetting()), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity())
        then:
        noExceptionThrown()


        when:
        def i = 0
        while (true) {
            def result = spy.sendWorkWxMsg("ea", new TriggerTaskSnapshotEntity(), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(marketingUserId: "test", chatIdList: ["test"]))
            i++
            if (result == TriggerTaskInstanceManager.ExecuteResult.successResult() || i > 20) {
                return;
            }
        }
        then:
        1 == 1

    }

    @Unroll
    def 'Test sendWorkWxMsg 客户'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)

        agentConfigDAO.queryAgentByEa(*_) >>> [null, new QywxCorpAgentConfigEntity()]
        triggerSnapshotDao.getCurrentUseSnapshot(*_) >>> [
                new TriggerSnapshotEntity(sendRange: 1, triggerType: "repeat_timing"),
                new TriggerSnapshotEntity(sendRange: 1, triggerType: "trigger_by_action")
        ]
        triggerTaskInstanceDao.getSuccessOrFailInstanceByTriggerIdAndSnapshotId(*_) >>> [
                null,
                new TriggerTaskInstanceEntity(executeStatus: "success"),
                new TriggerTaskInstanceEntity(executeStatus: "fail")
        ]
        triggerInstanceDao.listMarketingUserIdByTriggerId(*_) >>> [[], ["test"]]
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >>> [
                null,
                [new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: "test")]
        ]
        eieaConverter.enterpriseAccountToId(*_) >> 0

        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >>
                new com.fxiaoke.crmrestapi.common.result.Result(data: new Page(dataList: [new ObjectData(app_scope: ["Marketing"], external_user_id: "test")]))

        momentManager.getOrCreateAccessToken(*_) >> "token"
        spy.setAttachments(*_) >> { String ea, TriggerTaskSnapshotEntity triggerTaskSnapshot, String accessToken, AddNewMsgTemplateArg arg -> arg.setAttachments([new AddNewMsgTemplateArg.Attachments()]) }
        spy.getMarketingEventId(*_) >> null
        qywxAddressBookManager.queryByEaAndUserId(*_) >> new QyWxAddressBookEntity()
        outLinkMktParamManager.sopAttachmentAppendMktParam(*_) >> null
        qywxManager.handleQywxEmployeeUserId(*_) >> null
        groupSendMessageManager.getEmployeeExternalUserIds(_, _, _, _) >>> [
                [test: []],
                [test: ["test"]]
        ]
        qywxAddressBookManager.queryByEaAndUserId(*_) >> null
        outLinkMktParamManager.sopAttachmentAppendMktParam(*_) >> null
        spy.addMsgTemplate(*_) >>> [null, new AddMsgTemplateResult(errcode: 0)]
        triggerTaskInstanceDao.insertSopQywxMsgTask(*_) >> 0
        groupSendMessageManager.handlerSopQywxMsgTaskResult(*_) >> null
        enterpriseSpreadRecordManager.upsertList(*_) >> null

        spy.batchMarketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.marketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.sopFilterAndUpsert(*_) >>> [[], ['test']]

        when:
        spy.sendWorkWxMsg("ea", new TriggerTaskSnapshotEntity(sendSopSetting: new SendSopSetting()), new TriggerTaskInstanceEntity(), new TriggerInstanceEntity())
        then:
        noExceptionThrown()


        when:
        def i = 0
        while (true) {
            def result = spy.sendWorkWxMsg("ea",
                    new TriggerTaskSnapshotEntity(sendSopSetting: new SendSopSetting(allowSelect: true, userId: ["test"], departmentIds: ["1", "2"])),
                    new TriggerTaskInstanceEntity(), new TriggerInstanceEntity(marketingUserId: "test"))
            i++
            if (result == TriggerTaskInstanceManager.ExecuteResult.successResult() || i > 20) {
                return;
            }
        }
        then:
        1 == 1

    }

    @Unroll
    def 'Test setAttachments'() {
        given:
        qywxAttachmentsRelationDAO.getDetailByTargetId(*_) >>> [
                new QywxAttachmentsRelationEntity(), null
        ]
        qywxManager.builtNewAttachmentsArg(*_) >> []
        eaWechatAccountBindDao.getWxAppIdByEa(*_) >> null
        redisManager.getDefaultCoverPath(*_) >> null
        redisManager.getQywxMediaId(*_) >> null
        fileV2Manager.downloadFileByUrl(*_) >> null
        httpManager.uploadFile(*_) >>> [
                new UploadMediaResult(mediaId: "test"),
                null
        ]
        redisManager.setDefaultCoverPath(*_) >> null
        redisManager.setQywxMediaId(*_) >> null

        when:
        def i = 0
        while (true) {
            try {
                triggerTaskInstanceManager.setAttachments("ea", new TriggerTaskSnapshotEntity(wxMessageContent: '{"title":"testtesttesttest"}'), "token", new AddNewMsgTemplateArg())
            } catch (Exception e) {

            }
            i++
            if (i > 20) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test sendUnionMsg'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        triggerSnapshotDao.getCurrentUseSnapshot(*_) >> new TriggerSnapshotEntity()
        userMarketingAccountManager.getBaseInfosByIds(*_) >> ['testxxxxxxxxxxxxxxxxxxxx': new UserMarketingAccountData()]
        triggerTaskInstanceDao.listByTriggerInfoAndObjectId(*_) >> 0
        spy.getTriggerActionName(_) >> "actionName"
        spy.getTargetName(*_) >> "targetName"
        spy.getMarketingEventId(*_) >> "test"
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity(campaignMembersObjId: "test")
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo(fieldCaption: "t", fieldName: "ts")]
        crmV2Manager.getObjectDataEnTextVal(*_) >> ['t': 't']
        crmV2Manager.getDetailIgnoreError(*_) >> new ObjectData(relevant_team: [['teamMemberPermissionType': '2', 'teamMemberEmployee': ['1']]])
        crmV2Manager.getCrmIdByMarketingUserId(*_) >> "cc"
        crmV2Manager.queryObjectDatas(*_) >> [new ObjectData(user_id: "1000")]
        crmV2Manager.getOneByList(*_) >> new ObjectData(user_id: ["1000"])
        fsAddressBookManager.getEmployeeIdsByCircleIds(*_) >> [1, 2, 3]
        marketingTriggerDao.getById(*_) >> new MarketingTriggerEntity(creator: -10000)
        accountService.isApplyForKIS(*_) >> new Result(data: new AccountIsApplyForKISResult(phone: '1111'))
        sendService.sendGroupSms(*_) >> new Result(data: new GroupSendResult())
        userMarketingCrmLeadAccountRelationDao.getEachDataLastByUserMarketingId(*_) >> new UserMarketingCrmLeadAccountRelationEntity(crmLeadId: '11212')
        userMarketingAccountManager.getLeadAndWeChatAvatarUrlMap(*_) >> [:]
        employeeMsgSender.sendUnionMessage(*_) >> null
        groupSendMessageManager.openQywxH5Notice(*_) >> true
        groupSendMessageManager.sendQywxH5AgentMessage(*_) >> null


        when:
        try {
            spy.sendUnionMsg("ea", triggerTaskSnapshotEntity, triggerTaskInstanceEntity, triggerInstanceEntity)
        } catch (Exception e) {
            // ignor
        }
        then:
        1 == 1

        where:
        triggerTaskSnapshotEntity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | triggerTaskInstanceEntity                                                               | triggerInstanceEntity
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: '{')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | new TriggerTaskInstanceEntity()                                                         | new TriggerInstanceEntity()
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: '{}')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | new TriggerTaskInstanceEntity()                                                         | new TriggerInstanceEntity()
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: '[]')                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | new TriggerTaskInstanceEntity()                                                         | new TriggerInstanceEntity()
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg())))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | new TriggerTaskInstanceEntity()                                                         | new TriggerInstanceEntity()
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg())))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | new TriggerTaskInstanceEntity()                                                         | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx")
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg())))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | new TriggerTaskInstanceEntity(paramMap: '{')                                            | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx")
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg())))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | new TriggerTaskInstanceEntity(paramMap: '{}')                                           | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx")
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg())), sendUnionMessageExtendArg: new SendUnionMessageExtendArg(userExecuteOnce: true))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | new TriggerTaskInstanceEntity(paramMap: '{"objectType":"ttt"}')                         | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx")
        new TriggerTaskSnapshotEntity(noticeType: 'msg', sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg(fieldName: 'SPECIFIED'), new SendUnionMessageArg(fieldName: 'PROMOTER'), new SendUnionMessageArg(fieldName: 'MARKETING_TEAM'), new SendUnionMessageArg(fieldName: 'ALL_WECHATEXTERNALUSER_ADDER'), new SendUnionMessageArg(fieldName: 'user_id', label: 'WechatWorkExternalUserObj'))), sendUnionMessageExtendArg: new SendUnionMessageExtendArg(userExecuteOnce: true, userIdString: '1,2,3', phoneNumberString: '1,2,3', userIds: [1, 2, 3], departmentIds: [1, 2, 3], noticeTemplate: 'custom', noticeContent: ['content': 'xxx']), updateTime: new Date()) | new TriggerTaskInstanceEntity(paramMap: '{"spreadFsUid":1000}', createTime: new Date()) | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx", campaignId: "test")
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg(fieldName: 'SPECIFIED'), new SendUnionMessageArg(fieldName: 'PROMOTER'), new SendUnionMessageArg(fieldName: 'MARKETING_TEAM'), new SendUnionMessageArg(fieldName: 'ALL_WECHATEXTERNALUSER_ADDER'), new SendUnionMessageArg(fieldName: 'user_id', label: 'WechatWorkExternalUserObj'))), sendUnionMessageExtendArg: new SendUnionMessageExtendArg(userExecuteOnce: true, userIdString: '1,2,3', phoneNumberString: '1,2,3', userIds: [1, 2, 3], departmentIds: [1, 2, 3], noticeTemplate: 'custom', noticeContent: ['content': 'xxx', 'title': 'eeee']), updateTime: new Date())   | new TriggerTaskInstanceEntity(paramMap: '{"spreadFsUid":1000}', createTime: new Date()) | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx", campaignId: "test")
        new TriggerTaskSnapshotEntity(sendUnionMessageArg: GsonUtil.toJson(Lists.asList(new SendUnionMessageArg(fieldName: 'SPECIFIED'), new SendUnionMessageArg(fieldName: 'PROMOTER'), new SendUnionMessageArg(fieldName: 'MARKETING_TEAM'), new SendUnionMessageArg(fieldName: 'ALL_WECHATEXTERNALUSER_ADDER'), new SendUnionMessageArg(fieldName: 'user_id', label: 'WechatWorkExternalUserObj'))), sendUnionMessageExtendArg: new SendUnionMessageExtendArg(userExecuteOnce: true, userIdString: '1,2,3', phoneNumberString: '1,2,3', userIds: [1, 2, 3], departmentIds: [1, 2, 3], noticeTemplate: 'other', noticeContent: ['content': 'xxx', 'title': 'eeee']), updateTime: new Date())    | new TriggerTaskInstanceEntity(paramMap: '{"spreadFsUid":1000}', createTime: new Date()) | new TriggerInstanceEntity(marketingUserId: "testxxxxxxxxxxxxxxxxxxxx", campaignId: "test")
    }

    @Unroll
    def 'Test getMarketingEventId'() {
        given:
        conferenceDAO.getConferenceById(*_) >> new ActivityEntity()
        marketingLiveDAO.getById(*_) >> new MarketingLiveEntity()

        when:
        triggerTaskInstanceManager.getMarketingEventId(triggerInstance)

        then:
        1 == 1

        where:
        triggerInstance << [
                new TriggerInstanceEntity(sceneType: 'marketing_event', sceneTargetId: 'xxx'),
                new TriggerInstanceEntity(sceneType: 'conference', sceneTargetId: 'xxx'),
                new TriggerInstanceEntity(sceneType: 'live', sceneTargetId: 'xxx')
        ]
    }

    @Unroll
    def 'Test invokeWebHook'() {
        given:
        marketingLiveDAO.getById(*_) >> new MarketingLiveEntity()
        conferenceDAO.getConferenceById(*_) >> new ActivityEntity()
        webHookManager.invokeWebHook(*_) >> null

        when:
        triggerTaskInstanceManager.invokeWebHook("ea", new TriggerTaskSnapshotEntity(), null, triggerInstance)

        then:
        1 == 1

        where:
        triggerInstance << [
                new TriggerInstanceEntity(sceneType: 'live'),
                new TriggerInstanceEntity(sceneType: 'conference'),
                new TriggerInstanceEntity(sceneType: 'marketing_event')
        ]

    }


    @Unroll
    def 'Test addBoard'() {
        given:
        boardCardDao.insertBoardCard(*_) >> 0
        displayOrderManager.mergeNestedIdToLast(*_) >> null
        boardManager.addBoardCardActivityWhenCreateBoardCard(*_) >> null

        when:
        triggerTaskInstanceManager.addBoard("ea", new TriggerTaskSnapshotEntity(boardCardArg: new BoardCardArg()), new TriggerTaskInstanceEntity(), null)
        then:
        1 == 1

        when:
        def integers = new IntegerList()
        integers.add(1)
        triggerTaskInstanceManager.addBoard("ea", new TriggerTaskSnapshotEntity(boardCardArg:
                new BoardCardArg(boardId: "dd", boardCardListId: ['dd'], boardCardName: 'xxx', principals: integers, startTime: 1, endTime: 1)), new TriggerTaskInstanceEntity(), null)
        then:
        1 == 1
    }

    @Unroll
    def 'Test sendEmailMsg'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        campaignMergeDataManager.getUserEmailByCampaignId(*_) >>> [[:], ['xxx': '<EMAIL>']]
        marketingUserGroupManager.listEmailByMarketingUserIds(*_) >>> [[], [new MarketingUserWithEmail('xx', '<EMAIL>')]]
        spy.startTaskInstanceByMarketingUserId(*_) >> null
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        mailManager.replaceCustomFields(*_) >> '{市场活动名称}{活动开始时间}{活动结束时间}'
        marketingLiveDAO.getById(*_) >> new MarketingLiveEntity()
        conferenceDAO.getConferenceById(*_) >> new ActivityEntity()
        spy.doReplaceConferenceContent(*_) >> null
        spy.doReplaceLiveContent(*_) >> null
        crmV2Manager.getOneByList(*_) >> new ObjectData(begin_time: 123d, end_time: 123d)
        mailManager.sendEmailWithOutSaveDetailV2(*_) >>> [new Result(data: new SendEmailResult(labelId: 123)), new Result(errCode: -1)]
        spy.saveEmailSendRecord(*_) >> null

        spy.batchMarketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.marketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.sopFilterAndUpsert(*_) >>> [[], ['test']]

        and:
        def triggerTaskSnapshot = new TriggerTaskSnapshotEntity(email: new Email(title: 'xxx', sender: '<EMAIL>', html: '{市场活动名称}{活动开始时间}{活动结束时间}', attachments: [new Email.Attachment(size: 10)]))
        def triggerTaskInstance = new TriggerTaskInstanceEntity()

        when:
        def i = 0
        while (true) {
            def triggerInstance = new TriggerInstanceEntity(campaignId: randomVal([null, 'xxx']), sceneType: randomVal(['live', 'conference', 'marketing_event', 'marketingEventId']), marketingUserId: 'xx')
            def result = spy.sendEmailMsg("ea", triggerTaskSnapshot, triggerTaskInstance, triggerInstance)
            i++
            if (result == TriggerTaskInstanceManager.ExecuteResult.successResult() || i > 30) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test sendWxTemplateMsg'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        userMarketingWxServiceAccountRelationDao.getByUserMarketingId(*_) >>> [
                [],
                [new UserMarketingWxServiceAccountRelationEntity(wxAppId: 'test')]
        ]
        spy.doReplaceConferenceContent(*_) >> 'test'
        sceneTriggerDao.getBySceneTargetIdAndTriggerId(*_) >> new SceneTriggerEntity()
        conferenceDAO.getConferenceById(*_) >> new ActivityEntity()
        spy.doReplaceLiveContent(*_) >> 'test'
        sceneTriggerDao.getBySceneTargetIdAndTriggerId(*_) >> new SceneTriggerEntity()
        marketingLiveDAO.getById(*_) >> new MarketingLiveEntity(formHexagonId: 'test1')
        contentMarketingEventMaterialRelationDAO.getApplyObjectIdsByMarketingEventId(*_) >> ['test']
        crmV2Manager.getOneByList(*_) >> new ObjectData(begin_time: 123d, end_time: 123d)
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity(campaignMembersObjId: 'test')
        crmV2Manager.getObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo(fieldProperty: 2, fieldCaption: 'test', fieldName: 'test'), new CrmUserDefineFieldVo(fieldProperty: 2, fieldCaption: 'test', fieldName: 'test')]
        crmV2Manager.getObjectDataEnTextVal(*_) >>> [null, [:]]
        spy.doReplaceWxNickName(*_) >> null
        wechatMessageService.sendTemplateMessageByOpenIdsAsync(*_) >>> [new ModelResult(), new ModelResult(errCode: -1)]

        spy.batchMarketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.marketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.sopFilterAndUpsert(*_) >>> [[], ['test']]

        when:
        def i = 0
        while (true) {
            def triggerTaskSnapshot = new TriggerTaskSnapshotEntity(
                    wxAppId: 'test',
                    wxTemplateMsg: new SendWxTemplateMsgArg(redirectType: randomVal([3, 5, 6, 7]), redirectUrl: randomVal([null, 'test']), msgBody: new SendWxTemplateMsgArg.MsgBody(title: 'test', dataList: [new SendWxTemplateMsgArg.MsgItem(key: 'first', value: 'test'), new SendWxTemplateMsgArg.MsgItem(key: 'remark', value: 'test'), new SendWxTemplateMsgArg.MsgItem(key: 'other1', value: '{市场活动名称}'), new SendWxTemplateMsgArg.MsgItem(key: 'other2', value: '{活动开始时间}'), new SendWxTemplateMsgArg.MsgItem(key: 'other3', value: '{活动结束时间}')]))
            )
            def triggerTaskInstance = new TriggerTaskInstanceEntity()
            def triggerInstance = new TriggerInstanceEntity(campaignId: 'test', sceneType: randomVal(['live', 'conference', 'marketing_event', 'marketingEventId', 'target_crowd_operation']), sceneTargetId: 'test')
            try {
                spy.sendWxTemplateMsg("ea", triggerTaskSnapshot, triggerTaskInstance, triggerInstance)
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test sendWxMsg'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        spy.batchMarketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.marketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.sopFilterAndUpsert(*_) >>> [[], ['test']]
        materialService.getMaterialWxPresentMsg(*_) >>> [
                Result.newError(SHErrorCode.SYSTEM_ERROR),
                Result.newSuccess(new MaterialWxPresentMsg())
        ]
        fileManager.getPictureShareUrl(*_) >>> [null, 'test']
        userMarketingWxServiceAccountRelationDao.getByUserMarketingId(*_) >>> [
                [],
                [new UserMarketingWxServiceAccountRelationEntity(wxAppId: 'test')]
        ]
        outerServiceWechatService.transWxAppIdAndFsEaToAppId(*_) >>> [
                ModelResult.newError(-1, "test"),
                ModelResult.newSuccess("test")
        ]
        wechatMessageRestService.sendCustomerServiceMessage(*_) >> null

        when:
        def i = 0
        while (true) {
            def triggerTaskSnapshot = new TriggerTaskSnapshotEntity(
                    taskType: randomVal(['send_wx_text_msg', 'send_wx_image_msg', 'send_wx_graphic_message', 'send_wx_news_msg']),
                    wxMessageContent: randomVal(['{"materialType":1, "materialId":"test", "url":"test"}', '{"title":"test", "url":"test"}', '{}']),
                    wxAppId: "test"
            )
            def triggerTaskInstance = new TriggerTaskInstanceEntity()
            def triggerInstance = new TriggerInstanceEntity(sceneType: "target_crowd_operation")
            try {
                spy.sendWxMsg("ea", triggerTaskSnapshot, triggerTaskInstance, triggerInstance)
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test sendSmsMsg with #sceneType scene'() {
        given:
        def ea = 'testEA'
        def triggerTaskSnapshot = new TriggerTaskSnapshotEntity(smsTemplateId: 1)
        def triggerTaskInstance = new TriggerTaskInstanceEntity()
        def triggerInstance = new TriggerInstanceEntity(sceneType: sceneType, campaignId: campaignId, marketingUserId: marketingUserId, sceneTargetId: sceneTargetId)
        crmV2Manager.getOneByList(*_) >> new ObjectData()
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity()
        crmV2Manager.getObjectDataEnTextVal(*_) >> ["1": "1", "2": null]
        triggerSnapshotDao.getCurrentUseSnapshot(*_) >> null
        mwSmsTemplateDao.getTemplateById(*_) >> null
        sendService.sendGroupSms(*_) >> new Result(errCode: 0, data: new GroupSendResult())
        def mwSendManager = Mock(MwSendManager)
        triggerTaskInstanceManager.mwSendManager = mwSendManager
        marketingTriggerDao.getById(*_) >> new MarketingTriggerEntity(creator: 1000)

        def spy = Spy(triggerTaskInstanceManager)
        spy.batchMarketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.marketingUserIdSopFilterAndUpsert(*_) >>> [[], ['test']]
        spy.sopFilterAndUpsert(*_) >>> [[], ['test']]

        when:
        if (sceneType == MarketingSceneType.CONFERENCE.getType()) {
            def conference = new ActivityEntity(marketingEventId: 'confEventId')
            conferenceDAO.getConferenceById(_) >> conference
        } else if (sceneType == MarketingSceneType.LIVE.getType()) {
            def live = new MarketingLiveEntity(marketingEventId: 'liveEventId')
            marketingLiveDAO.getById(_) >> live
        } else {
            if (!StringUtils.isBlank(campaignId)) {
                def mergeData = new CampaignMergeDataEntity(phone: '**********')
                campaignMergeDataDAO.getCampaignMergeDataById(_) >> mergeData
            } else if (!StringUtils.isBlank(marketingUserId)) {
                def account = new UserMarketingAccountEntity(phone: '**********')
                userMarketingAccountDAO.getById(_) >> account
            }
        }

        and:
        crmV2Manager.getOneByList(_, _, _, _) >> new ObjectData(name: 'Event Name', begin_time: 1628534400000L, end_time: 1628534400000L)
        mwSendManager.buildMktCustomizeFieldMap(*_) >> [:]

        then:
        spy.sendSmsMsg(ea, triggerTaskSnapshot, triggerTaskInstance, triggerInstance)
        1 == 1

        where:
        sceneType         | campaignId       | marketingUserId | sceneTargetId   || isSuccess
        'conference'      | null             | null            | 'someConfId'    || true
        'conference'      | 'someCampaignId' | null            | 'someConfId'    || true
        'live'            | null             | null            | 'someLiveId'    || true
        'live'            | 'someCampaignId' | null            | 'someLiveId'    || true
        'marketing_event' | null             | 'test'          | 'someEventId'   || true
        'other'           | null             | null            | 'someInvalidId' || false
        'other'           | "nonnull"        | null            | 'someInvalidId' || false
    }

    @Unroll
    def 'Test addTag'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        userMarketingAccountManager.batchAddTagsToUserMarketingAccount(*_) >> null

        when:
        def i = 0
        while (true) {
            def triggerTaskSnapshot = new TriggerTaskSnapshotEntity(
                    tagNameList: randomVal([null, TagNameList.convert([new TagName()])])
            )
            def triggerTaskInstance = new TriggerTaskInstanceEntity()
            def triggerInstance = new TriggerInstanceEntity(marketingUserId: "test")
            try {
                spy.addTag("ea", triggerTaskSnapshot, triggerTaskInstance, triggerInstance)
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test doReplaceConferenceContent'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        campaignMergeDataManager.campaignIdToActivityEnrollIdMap(*_) >>> [[:], ['test': "test"]]
        activityEnrollDataDAO.queryConferenceEnrollBaseInfoByIds(*_) >> [new ConferenceEnrollBaseInfoDTO(startTimeStamp: new Date(), endTimeStamp: new Date())]

        when:
        def i = 0
        while (true) {
            def triggerInstance = new TriggerInstanceEntity(campaignId: "test")
            try {
                spy.doReplaceConferenceContent(triggerInstance, randomVal([null, 'test']), "test")
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test saveEmailSendRecordTask'() {
        given:
        def mailSendTaskResultDAO = Mock(MailSendTaskResultDAO)
        triggerTaskInstanceManager.mailSendTaskResultDAO = mailSendTaskResultDAO
        def spy = Spy(triggerTaskInstanceManager)
        marketingTriggerDao.getById(*_) >>> [new MarketingTriggerEntity(), null]
        mailSendTaskDAO.insert(*_) >> null
        mailSendTaskResultDAO.batchInsert(*_) >> null
        triggerSnapshotDao.getCurrentUseSnapshot(*_) >> new TriggerSnapshotEntity()
        emailSendRecordDetailObjManager.tryCreateOrUpdateObj(*_) >> null

        when:
        def i = 0
        while (true) {
            try {
                spy.saveEmailSendRecordTask("ea", "marketingEventId", "triggerId", ['mail'], new SendEmailResult(taskEntity: new MailSendTaskEntity(), mailIdList: ['id$id']))
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test addMsgTemplate'() {
        given:
        httpManager.executePostHttp(*_) >> null

        when:
        def i = 0
        while (true) {
            try {
                triggerTaskInstanceManager.addMsgTemplate(randomVal([null, "accessToken"]), new AddNewMsgTemplateArg())
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test doReplaceWxNickName'() {
        given:
        crmV2Manager.getWxNickNameByCache(*_) >> new Optional<>("test")

        when:
        def i = 0
        while (true) {
            try {
                triggerTaskInstanceManager.doReplaceWxNickName("ea", "wxAppId", "wxOpenId", randomVal([null, "{微信用户昵称}"]))
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test doReplaceLiveContent'() {
        given:
        marketingLiveDAO.getById(*_) >>> [null,new MarketingLiveEntity()]

        when:
        def i = 0
        while (true) {
            try {
                triggerTaskInstanceManager.doReplaceLiveContent(new TriggerInstanceEntity(), randomVal(["", "test"]))
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test getTargetName'() {
        given:
        marketingLiveDAO.getById(*_) >>> [null, new MarketingLiveEntity()]
        mailSendTaskDAO.getById(*_) >> new MailSendTaskEntity(senderIds: ['test'])
        mailSendReplyDAO.getById(*_) >> new MailSendReplyEntity()
        objectManager.getObjectName(*_) >> ""
        sceneTriggerManager.getEventName(*_) >> ""

        when:
        def i = 0
        while (true) {
            try {
                TriggerSnapshotEntity triggerSnapshotEntity = new TriggerSnapshotEntity(
                        targetObjects: [:],
                        triggerActionType: 'mail_unsubscribe'
                )
                TriggerInstanceEntity triggerInstance = new TriggerInstanceEntity()
                Map<String, Object> paramMap = ['objectId': 'test', 'objectType': 26]
                triggerTaskInstanceManager.getTargetName(triggerSnapshotEntity, triggerInstance, "marketingUserId", "ea", paramMap)
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test getTriggerActionName'() {
        when:
        def i = 0
        while (true) {
            try {
                TriggerSnapshotEntity triggerSnapshotEntity = new TriggerSnapshotEntity(
                        frontEndExtensions: ['triggerRuleDescription':''],
                        triggerActionType: 'mail_unsubscribe'
                )
                triggerTaskInstanceManager.getTriggerActionName(triggerSnapshotEntity)
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test getOwnerByRules'() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >>> [
                new com.fxiaoke.crmrestapi.common.result.Result(code: -1),
                new com.fxiaoke.crmrestapi.common.result.Result(data: new Page(dataList: [new ObjectData()]))
        ]
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> new InnerPage(dataList: [new ObjectData('qywx_user_id': 'test')])
        qywxUserManager.getFsUserIdByQyWxInfo(*_) >> ['test': 1000]
        qywxAddressBookManager.queryEaAndUserId(*_) >> [new QyWxAddressBookEntity(userId: "test", department: '["1"]')]

        when:
        def i = 0
        while (true) {
            try {
                SendSopSetting sendSopSetting = new SendSopSetting(
                        departmentIds: ["10001"],
                        userId: ["test"],
                        firstPriority: ['1'],
                        secondPriority: randomVal(['DESC',''])
                )
                triggerTaskInstanceManager.getOwnerByRules("ea", sendSopSetting, "externalUserId")
            } catch (Exception e) {
                //ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test getMarketingAccountUserIds'() {
        given:
        def dataPermissionManager = Mock(DataPermissionManager)
        triggerTaskInstanceManager.dataPermissionManager = dataPermissionManager
        def spy = Spy(triggerTaskInstanceManager)
        marketingTriggerDao.getById(*_) >> new MarketingTriggerEntity()
        marketingUserGroupManager.listWxWorkExternalUserIdsByMarketingUserGroupIds(*_) >>> [[], ['test']]
        userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(*_) >>> [
                [],
                [new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: 'test', userMarketingId: 'test')]
        ]
        spy.getOwnerToMarketingUserMap(*_) >> null
        dataPermissionManager.getDataPermissionSetting(*_) >> true
        dataPermissionManager.getDataPermission(*_) >> []
        crmV2Manager.getList(*_) >>> [
                new Page(),
                new Page(dataList: [new ObjectData()])
        ]
        userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity()]
        spy.getOwnerToMarketingUserMap(*_) >> null
        userMarketingAccountManager.listUserMarketingAccountIdsByTagNameList(*_) >>> [[],["test"]]
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: 'test')]
        userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: 'test', userMarketingId: 'test')]

        when:
        def i = 0
        while (true) {
            try {
                TriggerSnapshotEntity triggerSnapshot = new TriggerSnapshotEntity(
                        sendRange: randomVal([0, 1, 2, 3, 4, 6]),
                        marketingUserGroupIds: '["test"]',
                        filters: JSONObject.toJSONString([new Filter()]),
                        groupMsgSenderIds: '[',
                        tagIdList: randomVal(['[]',JSONObject.toJSONString(TagNameList.convert([new TagName(firstTagName: 'test', secondTagName: 'test')]))])
                )
                Map<Integer, List<String>> map = [:]
                spy.getMarketingAccountUserIds("ea", triggerSnapshot, map)
            } catch (Exception e) {
//                ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test getOwnerToMarketingUserMap'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        userMarketingAccountManager.getOwnerToExtenalUserMap(*_) >> [1000: ['test']]
        when:
        def i = 0
        while (true) {
            try {
                Map<Integer, List<String>> map = ['test': 'test']
                spy.getOwnerToMarketingUserMap([:], [], "ea", map)
            } catch (Exception e) {
//                ignor
            }
            i++
            if (i > 100) {
                return;
            }
        }
        then:
        1 == 1
    }

    @Unroll
    def 'Test batchMarketingUserIdSopFilterAndUpsert'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        spy.sopFilterAndUpsert(*_) >> null

        when:
        spy.batchMarketingUserIdSopFilterAndUpsert(null, null, false)

        then:
        1 == 1
    }

    @Unroll
    def 'Test marketingUserIdSopFilterAndUpsert'() {
        given:
        def spy = Spy(triggerTaskInstanceManager)
        spy.sopFilterAndUpsert(*_) >> null

        when:
        spy.marketingUserIdSopFilterAndUpsert(null, new TriggerInstanceEntity(), false)

        then:
        1 == 1
    }

    @Unroll
    def 'Test sopFilterAndUpsert'() {
        given:
        def enterpriseSpreadRecordManager = Mock(EnterpriseSpreadRecordManager)
        triggerTaskInstanceManager.enterpriseSpreadRecordManager = enterpriseSpreadRecordManager
        def spy = Spy(triggerTaskInstanceManager)
        enterpriseSpreadRecordManager.filterList(*_) >> []
        enterpriseSpreadRecordManager.upsertList(*_) >> null

        when:
        spy.sopFilterAndUpsert(['test'], new TriggerTaskSnapshotEntity(id:'test', ea: 'ea', property: [:]), true)

        then:
        1 == 1
    }

}
