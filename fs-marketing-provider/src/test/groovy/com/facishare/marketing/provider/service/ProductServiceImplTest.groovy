package com.facishare.marketing.provider.service

import com.facishare.mankeep.api.outService.service.OutCoverService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.arg.AddProductArg
import com.facishare.marketing.api.arg.AddProductInitArg
import com.facishare.marketing.api.arg.CancelMaterialTopArg
import com.facishare.marketing.api.arg.DeleteMaterialArg
import com.facishare.marketing.api.arg.ListProductArg
import com.facishare.marketing.api.arg.PhotoCutOffset
import com.facishare.marketing.api.arg.ProductMoveArg
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg
import com.facishare.marketing.api.arg.TopMaterialArg
import com.facishare.marketing.api.arg.UpdateProductArg
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg
import com.facishare.marketing.api.result.AddEnterpriseProductResult
import com.facishare.marketing.api.result.EditObjectGroupResult
import com.facishare.marketing.api.result.ListObjectGroupResult
import com.facishare.marketing.api.result.ObjectSloganResult
import com.facishare.marketing.api.result.ProductListResult
import com.facishare.marketing.api.result.QueryProductDetailResult
import com.facishare.marketing.api.service.ObjectSloganRelationService
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult
import com.facishare.marketing.common.enums.BindObjectType
import com.facishare.marketing.common.enums.MoveTypeEnum
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum
import com.facishare.marketing.common.enums.PicOperationFlagEnum
import com.facishare.marketing.common.enums.ProductStatusEnum
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.FormHeadSetting
import com.facishare.marketing.common.util.GsonUtil
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataObjectDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO
import com.facishare.marketing.provider.dao.MaterialRelationDao
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO
import com.facishare.marketing.provider.dao.PhotoDAO
import com.facishare.marketing.provider.dao.ProductDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.manager.MaterialRelationDaoManager
import com.facishare.marketing.provider.dao.manager.ProductDAOManager
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao
import com.facishare.marketing.provider.dto.CustomizeFormUserDataCountByObjectIdDTO
import com.facishare.marketing.provider.dto.ObjectStatisticData
import com.facishare.marketing.provider.dto.ProductEntityDTO
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataObjectEntity
import com.facishare.marketing.provider.entity.MaterialRelationEntity
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.ProductEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity
import com.facishare.marketing.provider.manager.CustomizeFormDataManager
import com.facishare.marketing.provider.manager.FileManager
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.MaterialTagManager
import com.facishare.marketing.provider.manager.ObjectGroupManager
import com.facishare.marketing.provider.manager.PhotoManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.statistic.outapi.result.ActionDurationTimeAvgByObjectResult
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import spock.lang.*

import java.util.concurrent.CountDownLatch

class ProductServiceImplTest extends Specification {

    def productServiceImpl = new ProductServiceImpl()

    def productDAO = Mock(ProductDAO)
    def photoDAO = Mock(PhotoDAO)
    def photoManager = Mock(PhotoManager)
    def fileManager = Mock(FileManager)
    def fileV2Manager = Mock(FileV2Manager)
    def materialTagManager = Mock(MaterialTagManager)
    def outCoverService = Mock(OutCoverService)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def productDAOManager = Mock(ProductDAOManager)
    def marketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def marketingActivityManager = Mock(MarketingActivityManager)
    def hexagonSiteManager = Mock(HexagonSiteManager)
    def hexagonSiteObjectDAO = Mock(HexagonSiteObjectDAO)
    def customizeFormDataObjectDAO = Mock(CustomizeFormDataObjectDAO)
    def objectGroupManager = Mock(ObjectGroupManager)
    def objectGroupDAO = Mock(ObjectGroupDAO)
    def objectGroupRelationDAO = Mock(ObjectGroupRelationDAO)
    def objectTopManager = Mock(ObjectTopManager)
    def objectGroupRelationVisibleManager = Mock(ObjectGroupRelationVisibleManager)
    def mktContentMgmtLogObjManager = Mock(MktContentMgmtLogObjManager)
    def photoTAToApathProcessLatch = Mock(CountDownLatch)
    def materialRelationDao = Mock(MaterialRelationDao)
    def materialRelationDaoManager = Mock(MaterialRelationDaoManager)
    def userMarketingStatisticService = Mock(UserMarketingStatisticService)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def marketingObjectAmountStatisticDao = Mock(MarketingObjectAmountStatisticDao)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def objectSloganRelationService = Mock(ObjectSloganRelationService)
    def marketingMaterialInstanceLogManager = Mock(MarketingMaterialInstanceLogManager)
    def appMenuTemplateService = Mock(AppMenuTemplateService)

    def setup() {
        productServiceImpl.productDAO = productDAO
        productServiceImpl.photoDAO = photoDAO
        productServiceImpl.photoManager = photoManager
        productServiceImpl.fileManager = fileManager
        productServiceImpl.fileV2Manager = fileV2Manager
        productServiceImpl.materialTagManager = materialTagManager
        productServiceImpl.outCoverService = outCoverService
        productServiceImpl.customizeFormDataManager = customizeFormDataManager
        productServiceImpl.productDAOManager = productDAOManager
        productServiceImpl.marketingEventMaterialRelationDAO = marketingEventMaterialRelationDAO
        productServiceImpl.marketingActivityManager = marketingActivityManager
        productServiceImpl.hexagonSiteManager = hexagonSiteManager
        productServiceImpl.hexagonSiteObjectDAO = hexagonSiteObjectDAO
        productServiceImpl.customizeFormDataObjectDAO = customizeFormDataObjectDAO
        productServiceImpl.objectGroupManager = objectGroupManager
        productServiceImpl.objectGroupDAO = objectGroupDAO
        productServiceImpl.objectGroupRelationDAO = objectGroupRelationDAO
        productServiceImpl.objectTopManager = objectTopManager
        productServiceImpl.objectGroupRelationVisibleManager = objectGroupRelationVisibleManager
        productServiceImpl.mktContentMgmtLogObjManager = mktContentMgmtLogObjManager
        productServiceImpl.photoTAToApathProcessLatch = photoTAToApathProcessLatch
        productServiceImpl.materialRelationDao = materialRelationDao
        productServiceImpl.materialRelationDaoManager = materialRelationDaoManager
        productServiceImpl.userMarketingStatisticService = userMarketingStatisticService
        productServiceImpl.customizeFormDataUserDAO = customizeFormDataUserDAO
        productServiceImpl.marketingObjectAmountStatisticDao = marketingObjectAmountStatisticDao
        productServiceImpl.contentMarketingEventMaterialRelationDAO = contentMarketingEventMaterialRelationDAO
        productServiceImpl.crmV2Manager = crmV2Manager
        productServiceImpl.objectSloganRelationService = objectSloganRelationService
        productServiceImpl.marketingMaterialInstanceLogManager = marketingMaterialInstanceLogManager
        productServiceImpl.appMenuTemplateService = appMenuTemplateService
        productServiceImpl.prdouctDataApath = "[\"path1\"]"
        productServiceImpl.prdouctDataThumbsPath = "[\"path2\"]"
        productServiceImpl.prdouctDataOldPath = "[\"path3\"]"
    }


    def "addEnterpriseProductTest"() {
        given:
        productDAO.queryProductDetail(*_) >> queryProductDetailMock
        productDAO.findMinByEaOrder(*_) >> findMinByEaOrderMock
        productDAO.getById(*_) >> new ProductEntity()
        productDAO.queryProductCountByName(*_) >> queryProductCountByNameMock
        photoDAO.listProductPhotos(*_) >> [new PhotoEntity()]
        photoDAO.deletePhotos(*_) >> true
        photoDAO.updatePhotoSequenceNumber(*_) >> 0
        photoDAO.listByTargetIdsAndTargetType(*_) >> [new PhotoEntity()]
        photoDAO.listByTargetIdsAndTargetTypeAndUrl(*_) >> [new PhotoEntity()]
        photoManager.addOrUpdatePhotoByCutOffset(*_) >> true
        fileManager.saveTAFileFromTmp(*_) >> ["saveTAFileFromTmpResponse"]
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        fileV2Manager.getApathByTApath(*_) >> new FileV2Manager.FileManagerPicResult()
        fileV2Manager.getCpathByPath(*_) >> "getCpathByPathResponse"
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        outCoverService.createLuckyMoneyIconCoverAsync(*_) >> new ModelResult(0, "errMsg", "data")
        productDAOManager.addProduct(*_) >> addProductMock
        productDAOManager.updateProduct(*_) >> updateProductMock
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> new HexagonSiteObjectEntity()
        hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(*_) >> 0
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 0
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity()
        def spy = Spy(productServiceImpl)
        spy.updateProductPhoto(*_) >> null

        when:
        Result<AddEnterpriseProductResult> result = spy.addEnterpriseProduct("fsEa", 0, arg)
        then:
        result.getErrCode() == resultMock
        where:
        arg                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | queryProductDetailMock           | queryProductCountByNameMock | updateProductMock | findMinByEaOrderMock            | addProductMock | resultMock
        new AddProductArg(id: "aaa", name: "name1")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | null                             | 0                           | false             | null                            | false          | Result.newError(SHErrorCode.PRODUCT_DETAIL_FAIL).getErrCode()
        new AddProductArg(id: "aaa", name: "name1")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | new ProductEntity(name: "name2") | 1                           | false             | null                            | false          | Result.newError(SHErrorCode.PRODUCT_NAME_EXIST).getErrCode()
        new AddProductArg(id: "aaa", name: "name1", isOperation: PicOperationFlagEnum.OPERATION_PIC.getType(), headPics: [], detailPics: [])                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | new ProductEntity(name: "name1") | 1                           | false             | null                            | false          | Result.newError(SHErrorCode.PRODUCT_UPDATE_FAIL).getErrCode()
        new AddProductArg(id: "aaa", name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.CUSTOMIZE_FORM.getType())                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | new ProductEntity(name: "name1") | 1                           | true              | null                            | false          | Result.newSuccess(new AddEnterpriseProductResult(id: "aaa")).getErrCode()
        new AddProductArg(id: "aaa", name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.HEXAGON_SITE.getType(), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType())])                                     | new ProductEntity(name: "name1") | 1                           | true              | null                            | false          | Result.newSuccess(new AddEnterpriseProductResult(id: "aaa")).getErrCode()
        new AddProductArg(name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.HEXAGON_SITE.getType(), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType())])                                                | new ProductEntity(name: "name1") | 1                           | true              | null                            | false          | Result.newError(SHErrorCode.PRODUCT_NAME_EXIST).getErrCode()
        new AddProductArg(name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.HEXAGON_SITE.getType(), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType())])                                                | new ProductEntity(name: "name1") | 0                           | true              | null                            | false          | Result.newError(SHErrorCode.PRODUCT_CREATE_FAIL).getErrCode()
        new AddProductArg(name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.HEXAGON_SITE.getType(), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType())])                                                | new ProductEntity(name: "name1") | 0                           | true              | new ProductEntity(orderNum: 12) | false          | Result.newError(SHErrorCode.PRODUCT_CREATE_FAIL).getErrCode()
        new AddProductArg(name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.CUSTOMIZE_FORM.getType(), formId: "formId", originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType())], sharePosterAPath: "apath") | new ProductEntity(name: "name1") | 0                           | true              | new ProductEntity(orderNum: 12) | true           | Result.newSuccess().getErrCode()
        new AddProductArg(name: "name1", isOperation: 2, headPics: [], detailPics: [], bindObjectType: BindObjectType.HEXAGON_SITE.getType(), formId: "formId", originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: 0, photoTargetType: PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType())], sharePosterAPath: "apath")   | new ProductEntity(name: "name1") | 0                           | true              | new ProductEntity(orderNum: 12) | true           | Result.newSuccess().getErrCode()

    }


    def "deleteEnterpriseProductTest"() {
        given:
        productDAO.queryProductDetail(*_) >> queryProductDetailMock
        productDAOManager.updateStatus(*_) >> 0
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(*_) >> 0
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0
        contentMarketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(*_) >> 0

        when:
        Result result = productServiceImpl.deleteEnterpriseProduct("ea", 0, "productId")
        then:
        result == resultMock
        where:
        queryProductDetailMock                | resultMock
        new ProductEntity(tryOutEnable: true) | new Result(SHErrorCode.PRODUCT_CAN_NOT_DELETE, "已开启试用,无法删除");
        new ProductEntity(tryOutEnable: false) | Result.newSuccess()
    }


    def "addInitProductDataTest"() {
        given:
        productDAO.findMinByEaOrder(*_) >> findMinByEaOrderMock
        productDAO.queryProductByName(*_) >> queryProductByNameMock
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        outCoverService.createLuckyMoneyIconCoverAsync(*_) >> new ModelResult(0, "errMsg", "data")
        productDAOManager.addProduct(*_) >> addProductMock

        when:
        Result<Void> result = productServiceImpl.addInitProductData("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                     | queryProductByNameMock | findMinByEaOrderMock | addProductMock | resultMock
        new AddProductInitArg() | [new ProductEntity()]  | null                 | false          | new Result(SHErrorCode.PRODUCT_NAME_EXIST);
        new AddProductInitArg() | null | new ProductEntity(orderNum: 1) | false | new Result(SHErrorCode.PRODUCT_CREATE_FAIL);
        new AddProductInitArg() | null | null | true | Result.newSuccess()
        new AddProductInitArg(path: ["path1", "path2", ""], thumbsPath: ["thumb1", "thumb2", ""]) | null | null | true | Result.newSuccess()
    }


    def "addProductPhotoTest"() {
        given:
        fileManager.saveTAFileFromTmp(*_) >> ["F1"]
        fileV2Manager.getCpathByPath(*_) >> "C1"
        fileV2Manager.getUrlByPath(*_) >> "U1"
        photoDAO.batchInsert(*_) >> null
        when:
        productServiceImpl.addProductPhoto(arg1, arg2, "id")
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg1 | arg2
        null | null
        null | [new PhotoEntity(id: "id1", path: "TA_2"), new PhotoEntity(id: "id1", path: "A_2"), new PhotoEntity(id: "id1", path: "C_2")]
    }

    def "addProductPhotoTest2"() {
        given:
        fileV2Manager.getApathByTApath(*_) >> new FileV2Manager.FileManagerPicResult(urlAPath: "path", url: "url")
        fileV2Manager.getCpathByPath(*_) >> "C_PATH"
        fileV2Manager.getUrlByPath(*_) >> "URL"
        when:
        productServiceImpl.addProductPhoto("ea", arg, 1, "targetId")
        then:
        noExceptionThrown() // todo - validate something
        where:
        arg                    | resultMock
        null                   | null
        ["TA_1", "C_2", "A_3"] | null
    }

    def "updateProductMiniCoverTest"() {
        given:
        photoManager.listByTargetIdAndTargetType(*_) >> [new PhotoEntity()]

        when:
        productServiceImpl.updateProductMiniCover("productId")
        then:
        noExceptionThrown() // todo - validate something
    }


    def "updateProductPhotoTest"() {
        given:
        productDAO.getById(*_) >> new ProductEntity()
        photoDAO.listProductPhotos(*_) >> listProductPhotosMock
        photoDAO.deletePhotos(*_) >> true
        photoDAO.updatePhotoSequenceNumber(*_) >> 0
        photoDAO.listByTargetIdsAndTargetType(*_) >> listByTargetIdsAndTargetTypeMock
        photoDAO.listByTargetIdsAndTargetTypeAndUrl(*_) >> [new PhotoEntity()]
        fileManager.saveTAFileFromTmp(*_) >> ["saveTAFileFromTmpResponse"]
        fileV2Manager.getCpathByPath(*_) >> "getCpathByPathResponse"
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"

        when:
        productServiceImpl.updateProductPhoto("productId", headArg, detailArg)
        then:
        noExceptionThrown() // todo - validate something
        where:
        headArg | listByTargetIdsAndTargetTypeMock                                                                                   | listProductPhotosMock                                                                                                                                                                           | detailArg
        ["C_"]  | [new PhotoEntity(), new PhotoEntity()]                                                                             | [new PhotoEntity()]                                                                                                                                                                             | null
        ["TC_"] | [new PhotoEntity(), new PhotoEntity()]                                                                             | [new PhotoEntity()]                                                                                                                                                                             | null
        ["C_"]  | [new PhotoEntity(thumbnailUrl: "C_", targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()), new PhotoEntity()] | [new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), thumbnailUrl: "C_"), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), thumbnailUrl: "C_4")] | ["C_", "C_3"]
    }


    def "queryProductDetailTest"() {
        given:
        productDAO.queryProductDetail(*_) >> queryProductDetailMock
        photoDAO.listProductPhotos(*_) >> listProductPhotosMock
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity()
        fileV2Manager.getUrlByPath(*_) >> "url"
        fileV2Manager.getFileTotalSize(*_) >> getFileTotalSizeMock
        customizeFormDataManager.getBindFormDataByObject(*_) >> getBindFormDataByObjectMock
        hexagonSiteManager.getBindHexagonSiteByObject(*_) >> new HexagonSiteEntity()
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> new HexagonSiteObjectEntity()
        materialRelationDao.queryMaterialRelationByObjectId(*_) >> new MaterialRelationEntity(sharePosterAPath: "apath")
        objectSloganRelationService.getSlogan(*_) >> new Result<ObjectSloganResult>(0, "errMsg", new ObjectSloganResult())

        when:
        Result<QueryProductDetailResult> result = productServiceImpl.queryProductDetail("productId")
        then:
        result.getErrCode() == resultMock
        where:
        queryProductDetailMock                                          | listProductPhotosMock                                                                                                                                                                                                                                     | getBindFormDataByObjectMock                                                                     | getFileTotalSizeMock | resultMock
        null                                                            | [new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_HEAD.getType()), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), path: "path1"), new PhotoEntity(targetType: PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL.getType())] | null                                                                                            | 0L                   | new Result<>(SHErrorCode.PRODUCT_DETAIL_FAIL).getErrCode()
        new ProductEntity(id: "id1", name: "产品", tryOutEnable: false) | [new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_HEAD.getType()), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), path: "path1"), new PhotoEntity(targetType: PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL.getType())] | null                                                                                            | 0L                   | Result.newSuccess(new QueryProductDetailResult()).getErrCode()
        new ProductEntity(id: "id1", name: "产品", tryOutEnable: false) | [new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_HEAD.getType()), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), path: "path1"), new PhotoEntity(targetType: PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL.getType())] | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name", title: "title")) | null                 | Result.newSuccess(new QueryProductDetailResult()).getErrCode()
        new ProductEntity(id: "id1", name: "产品", tryOutEnable: false) | [new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_HEAD.getType()), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), path: "path1")]                                                                                       | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name", title: "title")) | 2L                   | Result.newSuccess(new QueryProductDetailResult()).getErrCode()
    }


    @Unroll
    def "listEnterpriseProductsTest"() {
        given:
        productDAO.getAccessiblePage(*_) >> getAccessiblePageMock
        productDAO.getCreateByMePage(*_) >> [new ProductEntityDTO(id: "id1", tryOutEnable: true), new ProductEntityDTO(id: "id2", tryOutEnable: true)]
        productDAO.getUnGroupPage(*_) >> []
        photoManager.listByProductIds(*_) >> [new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), targetId: "id1", thumbnailPath: "path1"), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), targetId: "id1", thumbnailPath: "path2"), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), targetId: "id1", thumbnailPath: "path3"), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), targetId: "id1", thumbnailPath: "path3"), new PhotoEntity(targetType: PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), targetId: "id2", thumbnailPath: "path4")]
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity()
        fileV2Manager.processImageSizes(*_) >> ["path1": 1l, "path2": 1l]
        materialTagManager.buildTagName(*_) >> ["id1": ["tag1"], "id2": ["tag2"]]
        marketingEventMaterialRelationDAO.getContentMarketingByEaAndObjectTypeAndObjectIds(*_) >> [new ContentMarketingEventMaterialRelationEntity(id: "id1", objectId: "id1", marketingEventId: "marketingEventId1")]
        marketingActivityManager.getActivityIdsByObject(*_) >> ["id1": [new MarketingActivityObjectInfoDTO.ActivityObjectInfo()]]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> [new ObjectGroupEntity(id: "groupid1"), new ObjectGroupEntity(id: "groupid2")]
        userMarketingStatisticService.listActionDurationTimeAvgListByObject(*_) >> new com.facishare.marketing.statistic.common.result.Result<List<ActionDurationTimeAvgByObjectResult>>(data: [new ActionDurationTimeAvgByObjectResult(objectId: "id1", avg: 1L, count: 1, sum: 1L)])
        customizeFormDataUserDAO.queryObjectClueCount(*_) >> queryObjectClueCountMock
        marketingObjectAmountStatisticDao.listStatisticData(*_) >> [new ObjectStatisticData(objectId: "id1", lookUpCount: 1)]
        contentMarketingEventMaterialRelationDAO.getContentMarketingByEaAndObjectTypeAndObjectIds(*_) >> [new ContentMarketingEventMaterialRelationEntity(id: "id1", marketingEventId: "marketingEventId1", objectId: "id1")]
        crmV2Manager.getList(*_) >> new Page<ObjectData>(total: 1, dataList: [ObjectData.convert(["_id": "marketingEventId1"])])
        appMenuTemplateService.getMenuTagRule(*_) >> appMenuTagVOResult
        objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(*_) >> [new ObjectGroupEntity(id: "groupid1")]
        objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(*_) >> ["id1"]
        when:
        Result<ProductListResult> result = productServiceImpl.listEnterpriseProducts("ea", 0, arg)
        then:
        result.getErrCode() == resultMock
        where:
        arg                                                                                                                                | getAccessiblePageMock | queryObjectClueCountMock                                                                                              | appMenuTagVOResult                                                                | resultMock
        new ListProductArg(pageNum: 1, pageSize: 2)                                                                                        | null                  | [new CustomizeFormUserDataCountByObjectIdDTO(objectId: "objectId1", marketingEventId: "marketingEventId1", count: 1)] | new Result(errCode: -1)                                                           | Result.newSuccess(new ProductListResult(productDetailResultList: [], totalCount: 0)).getErrCode()
        new ListProductArg(pageNum: 1, pageSize: 2, groupId: DefaultObjectGroupEnum.CREATED_BY_ME.getId(), connectMarketingActivity: true) | null                  | [new CustomizeFormUserDataCountByObjectIdDTO(objectId: "id1", marketingEventId: "marketingEventId1", count: 1)]       | new Result(errCode: -1)                                                           | Result.newSuccess(new ProductListResult(productDetailResultList: [], totalCount: 0)).getErrCode()
        new ListProductArg(pageNum: 1, pageSize: 2, groupId: DefaultObjectGroupEnum.NO_GROUP.getId())                                      | null                  | null                                                                                                                  | new Result(errCode: -1)                                                           | Result.newSuccess(new ProductListResult(productDetailResultList: [], totalCount: 0)).getErrCode()
        new ListProductArg(pageNum: 1, pageSize: 2, groupId: "groupid1")                                                                   | null                  | null                                                                                                                  | new Result(errCode: -1)                                                           | Result.newSuccess(new ProductListResult(productDetailResultList: [], totalCount: 0)).getErrCode()
        new ListProductArg(pageNum: 1, pageSize: 2, groupId: "groupid1")                                                                   | null                  | null                                                                                                                  | new Result(errCode: 0, data: new AppMenuTagVO(tagIdList: ["id"], tagOperator: 1)) | Result.newSuccess(new ProductListResult(productDetailResultList: [], totalCount: 0)).getErrCode()

    }


    def "updateStatusTest"() {
        given:
        productDAOManager.updateStatus(*_) >> 0

        when:
        Result result = productServiceImpl.updateStatus("ea", 0, new UpdateProductArg())
        then:
        result == new Result<>(SHErrorCode.SUCCESS)
    }


    def "moveCompanyProductTest"() {
        given:
        productDAO.queryProductDetail(*_) >> queryProductDetailMock
        productDAO.findMaxByEaOrderWithoutTop(*_) >> findMaxByEaOrderWithoutTopMock
        productDAO.findMinByEaOrderWithoutTop(*_) >> new ProductEntity()
        productDAO.findMinByEaOrder(*_) >> new ProductEntity(orderNum: 2)
        objectTopManager.getByObjectIdList(*_) >> getByObjectIdListMock

        when:
        Result result = productServiceImpl.moveCompanyProduct("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                  | queryProductDetailMock | getByObjectIdListMock | findMaxByEaOrderWithoutTopMock | resultMock
        new ProductMoveArg() | null                   | null                  | null                           | new Result(SHErrorCode.PRODUCT_DETAIL_FAIL);
        new ProductMoveArg(type: MoveTypeEnum.UP_MOVE.getType()) | new ProductEntity() | [new ObjectTopEntity()] | null | Result.newError(SHErrorCode.CANT_MOVE_UP_TOP_PRODUCT)
        new ProductMoveArg(type: MoveTypeEnum.UP_MOVE.getType()) | new ProductEntity() | null | null | new Result(SHErrorCode.SUCCESS)
        new ProductMoveArg(type: MoveTypeEnum.DOWN_MOVE.getType()) | new ProductEntity() | [new ObjectTopEntity()] | null | new Result(SHErrorCode.CANT_MOVE_DOWN_TOP_PRODUCT)
        new ProductMoveArg(type: MoveTypeEnum.DOWN_MOVE.getType()) | new ProductEntity() | [] | new ProductEntity() | new Result(SHErrorCode.SUCCESS)
        new ProductMoveArg(type: MoveTypeEnum.TOP_MOVE.getType()) | new ProductEntity() | [] | new ProductEntity() | new Result(SHErrorCode.SUCCESS)
    }


    def "editProductGroupTest"() {
        given:
        objectGroupManager.editGroup(*_) >> Result.newSuccess(new EditObjectGroupResult())
        objectGroupManager.isAppAdmin(*_) >> isAppAdminMock

        when:
        Result<EditObjectGroupResult> result = productServiceImpl.editProductGroup("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                                  | isAppAdminMock | resultMock
        new EditObjectGroupArg()             | false          | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN)
        new EditObjectGroupArg(name: "全部") | true           | Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME)
        new EditObjectGroupArg(name: "test") | true           | Result.newSuccess(new EditObjectGroupResult())
    }


    def "deleteProductGroupTest"() {
        given:
        objectGroupManager.deleteGroup(*_) >> Result.newSuccess()
        objectGroupManager.isAppAdmin(*_) >> isAppAdminMock

        when:
        Result<Void> result = productServiceImpl.deleteProductGroup("ea", 0, new DeleteObjectGroupArg())
        then:
        result == resultMock
        where:
        isAppAdminMock | resultMock
        false          | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        true | Result.newSuccess();
    }


    def "setProductGroupTest"() {
        given:
        productDAO.getByIds(*_) >> getByIdsMock
        objectGroupDAO.getById(*_) >> getByIdMock
        objectGroupRelationDAO.batchInsert(*_) >> 0
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0

        when:
        Result<Void> result = productServiceImpl.setProductGroup("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                                                 | getByIdsMock          | getByIdMock             | resultMock
        new SetObjectGroupArg(objectIdList: ["111", "222"]) | null                  | null                    | Result.newError(SHErrorCode.PRODUCT_NOT_FOUND)
        new SetObjectGroupArg(objectIdList: ["111", "222"]) | [new ProductEntity()] | null                    | Result.newError(SHErrorCode.PART_PRODUCT_NOT_FOUND)
        new SetObjectGroupArg(objectIdList: ["111"])        | [new ProductEntity()] | null                    | Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST)
        new SetObjectGroupArg(objectIdList: ["111"])        | [new ProductEntity()] | new ObjectGroupEntity() | Result.newSuccess();
    }


    def "deleteEnterpriseProductBatchTest"() {
        given:
        productDAO.getByIdsIgnoreDeleted(*_) >> getByIdsIgnoreDeletedMock
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(*_) >> 0
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0
        contentMarketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(*_) >> 0

        when:
        Result<Void> result = productServiceImpl.deleteEnterpriseProductBatch("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                     | getByIdsIgnoreDeletedMock                                                               | resultMock
        new DeleteMaterialArg() | null                                                                                    | Result.newError(SHErrorCode.PRODUCT_NOT_FOUND)
        new DeleteMaterialArg() | [new ProductEntity(tryOutEnable: true)]                                                 | Result.newError(SHErrorCode.TRY_PRODUCT_CAN_NOT_DELETE)
        new DeleteMaterialArg() | [new ProductEntity(tryOutEnable: false, status: ProductStatusEnum.NORMAL.getStatus())]  | Result.newError(SHErrorCode.CANT_DELETE_NORMAL_PRODUCT)
        new DeleteMaterialArg() | [new ProductEntity(tryOutEnable: false, status: ProductStatusEnum.DELETED.getStatus())] | Result.newSuccess()
    }


    def "topProductTest"() {
        given:
        productDAO.getById(*_) >> getByIdMock

        when:
        Result<Void> result = productServiceImpl.topProduct("ea", 0, new TopMaterialArg())
        then:
        result == resultMock
        where:
        getByIdMock         | resultMock
        null                | Result.newError(SHErrorCode.PRODUCT_NOT_FOUND)
        new ProductEntity() | Result.newSuccess()
    }


    def "cancelTopProductTest"() {

        when:
        Result<Void> result = productServiceImpl.cancelTopProduct("ea", 0, new CancelMaterialTopArg())
        then:
        result == Result.newSuccess();
    }


    def "addProductGroupRoleTest"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdminMock

        when:
        Result<Void> result = productServiceImpl.addProductGroupRole("ea", 0, new SaveObjectGroupVisibleArg())
        then:
        result == resultMock
        where:
        isAppAdminMock | resultMock
        false          | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN)
        true           | Result.newSuccess()
    }


    def "listProductGroupTest"() {
        given:
        productDAO.queryUnGroupAndCreateByMeCount(*_) >> 0
        productDAO.queryCountCreateByMe(*_) >> 0
        productDAO.queryCountByUnGrouped(*_) >> 0
        productDAO.queryAccessibleCount(*_) >> 0
        objectGroupManager.getShowGroup(*_) >> getShowGroupMock

        when:
        Result<ObjectGroupListResult> result = productServiceImpl.listProductGroup("ea", 100, new ListGroupArg(useType: 0))
        then:
        result.getErrCode() == resultMock
        where:
        getShowGroupMock                                                                             | resultMock
        new ObjectGroupListResult(objectGroupList: [new ListObjectGroupResult(groupId: "groupid1")]) | Result.newSuccess().getErrCode()
    }


    def "listProductGroup4OuterTest"() {
        given:
        objectGroupManager.getShowGroup4Outer(*_) >> new ObjectGroupListResult()

        when:
        Result<ObjectGroupListResult> result = productServiceImpl.listProductGroup4Outer("upstreamEA", "outTenantId", "outUserId", new ListGroupArg())
        then:
        result == Result.newSuccess(new ObjectGroupListResult())
    }


    def "getGroupRoleTest"() {
        given:
        objectGroupRelationVisibleManager.getRoleRelationByGroupId(*_) >> getRoleRelationByGroupIdMock

        when:
        Result<List<String>> result = productServiceImpl.getGroupRole("groupId")
        then:
        result == resultMock
        where:
        getRoleRelationByGroupIdMock                       | resultMock
        [new ObjectGroupRoleRelationEntity(roleId: "111")] | Result.newSuccess(["111"])
    }

}