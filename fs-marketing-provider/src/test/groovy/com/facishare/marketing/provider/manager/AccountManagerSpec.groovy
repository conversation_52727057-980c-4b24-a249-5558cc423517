package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.mankeep.api.outService.result.EnterpriseDefaultCard.GetEnterpriseDefaultCardResult
import com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService
import com.facishare.mankeep.api.service.KmVideoService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.entity.*
import com.facishare.marketing.provider.manager.qr.QRCodeManager
import com.facishare.marketing.provider.manager.user.UserManager
import com.facishare.open.ding.api.service.cloud.DingAuthService
import com.facishare.open.ding.api.vo.DingCorpMappingVo
import com.facishare.open.ding.common.result.Result
import spock.lang.Specification
import spock.lang.Unroll

class AccountManagerSpec extends Specification {

    def accountDAO = Mock(AccountDAO)
    def cardDAO = Mock(CardDAO)
    def photoManager = Mock(PhotoManager)
    def coverImageManager = Mock(CoverImageManager)
    def qrCodeManager = Mock(QRCodeManager)
    def userManager = Mock(UserManager)
    def outEnterpriseDefaultCardService = Mock(OutEnterpriseDefaultCardService)
    def fsBindManager = Mock(FsBindManager)
    def eieaConverter = Mock(EIEAConverter)
    def videoDAO = Mock(VideoDAO)
    def kmVideoService = Mock(KmVideoService)
    def fsAddressBookSettingDAO = Mock(FsAddressBookSettingDAO)
    def photoDAO = Mock(PhotoDAO)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def dingAuthService = Mock(DingAuthService)

    def accountManager = new AccountManager(
            accountDAO: accountDAO,
            cardDAO: cardDAO,
            photoManager: photoManager,
            coverImageManager: coverImageManager,
            qrCodeManager: qrCodeManager,
            userManager: userManager,
            outEnterpriseDefaultCardService: outEnterpriseDefaultCardService,
            fsBindManager: fsBindManager,
            eieaConverter: eieaConverter,
            videoDAO: videoDAO,
            kmVideoService: kmVideoService,
            fsAddressBookSettingDAO: fsAddressBookSettingDAO,
            photoDAO: photoDAO,
            fsAddressBookManager: fsAddressBookManager,
            dingAuthService: dingAuthService,
    )

    @Unroll
    def "addFieldToMemberObj"() {
        given:
        accountDAO.queryAccountByUid(*_) >> accountEntity
        cardDAO.queryCardInfoByUid(*_) >> cardEntity
        photoManager.queryPhoto(*_) >> photoEntityList
        when:
        accountManager.needUpdateUserInfo("88146")
        then:
        noExceptionThrown()
        where:
        accountEntity                   | cardEntity       | photoEntityList
        null                            | null             | null
        new AccountEntity(phone: "110") | null             | null
        new AccountEntity(phone: "110") | new CardEntity() | null
        new AccountEntity(phone: "110") | new CardEntity() | [new PhotoEntity()]
    }

    @Unroll
    def "createAccount"() {
        given:
        accountDAO.queryAccountByUid(*_) >> queryAccountEntity
        userManager.updateUser(*_) >> 1
        accountDAO.insert(*_) >> addResult
        accountDAO.update(*_) >> 1
        cardDAO.queryCardInfoByUid(*_) >> cardEntity
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]
        cardDAO.insert(*_) >> addCardResult
        dingAuthService.queryEnterpriseByEA(*_) >> new Result<DingCorpMappingVo>(data: new DingCorpMappingVo(enterpriseName: "name"))
        outEnterpriseDefaultCardService.addDefaultCardInfoFromQywx(*_) >> null
        cardDAO.updateById(*_) >> 1
        when:
        accountManager.createAccount("id", "name", 1, "avatar", "110", "ea", "app", 2)
        then:
        noExceptionThrown()
        where:
        queryAccountEntity | addResult | cardEntity       | addCardResult
        null               | 0         | null             | 0
        null               | 1         | null             | 0
        null               | 1         | null             | 1
        null               | 1         | new CardEntity() | 1
    }

    @Unroll
    def "createWxAccount"() {
        given:
        accountDAO.queryAccountByUid(*_) >> queryAccountEntity
        userManager.updateUser(*_) >> 1
        accountDAO.insert(*_) >> addResult
        accountDAO.update(*_) >> 1
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        eieaConverter.enterpriseAccountToId(*_) >> 1
        fsBindManager.insert(*_) >> 1
        fsBindManager.update(*_) >> 1
        cardDAO.queryCardInfoByUid(*_) >> cardEntity
        fsAddressBookSettingDAO.getFsAddressBookSettingByEa(*_) >> null
        cardDAO.insert(*_) >> 1
        cardDAO.updateById(*_) >> 1
        videoDAO.queryVideoByTargetType(*_) >> [new VideoEntity()]
        kmVideoService.deleteVideo(*_) >> null
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]
        when:
        accountManager.createWxAccount("id", "name", "avatar", "110", "ea", 1, "app")
        then:
        noExceptionThrown()
        where:
        queryAccountEntity  | addResult | fsBindEntity                  | cardEntity       | addCardResult
        null                | 0         | null                          | null             | 0
        null                | 1         | null                          | null             | 0
        null                | 1         | new FSBindEntity(fsEa: "hjh") | null             | 0
        new AccountEntity() | 1         | new FSBindEntity(fsEa: "hjh") | new CardEntity() | 0
    }


    @Unroll
    def "handleKeMaiInitCardInfo"() {
        given:
        fsBindManager.queryUidByFsUserIdAndFsEa(*_) >> oldUid
        accountDAO.queryAccountByPhone(*_) >> accountEntityList
        cardDAO.insert(*_) >> 1
        cardDAO.queryCardInfoByUid(*_) >> cardEntity
        photoDAO.listByTargetIdsAndTargetType(*_) >> [new PhotoEntity()]
        photoDAO.addPhoto(*_) >> true
        videoDAO.queryVideoByTargetType(*_) >> [new VideoEntity()]
        videoDAO.addVideo(*_) >> 1
        when:
        accountManager.handleKeMaiInitCardInfo("ea", 1, "110", "uid", "avatar", "name")
        then:
        noExceptionThrown()
        where:
        oldUid | accountEntityList               | cardEntity
        null   | null                            | null
        null   | [new AccountEntity(uid: "uid")] | null
        "old"  | null                            | new CardEntity()
    }

    @Unroll
    def "buildCardFsBindInfoBuUid"() {
        given:
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        outEnterpriseDefaultCardService.getEnterpriseDefaultCard(*_) >> result
        fsAddressBookManager.getEmployeeInfo(*_) >> fsEmployeeMsg
        when:
        accountManager.buildCardFsBindInfoBuUid(cardEntity, "uid")
        then:
        noExceptionThrown()
        where:
        cardEntity       | fsBindEntity       | fsEmployeeMsg                            | result
        null             | null               | null                                     | null
        new CardEntity() | null               | null                                     | null
        new CardEntity() | new FSBindEntity() | null                                     | null
        new CardEntity() | new FSBindEntity() | new FsAddressBookManager.FSEmployeeMsg() | new ModelResult(errCode: -1)
        new CardEntity() | new FSBindEntity() | new FsAddressBookManager.FSEmployeeMsg() | new ModelResult<>(errCode: 0, data: new GetEnterpriseDefaultCardResult())
    }

    @Unroll
    def "createCardQRCode"() {
        given:
        qrCodeManager.createQRCode(*_) >> qrCode
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> result
        when:
        accountManager.createCardQRCode("uid", "ea")
        then:
        noExceptionThrown()
        where:
        qrCode                                                    | result
        null                                                      | false
        new QRCodeManager.CreateQRCodeResult(qrCodeApath: "path") | false
        new QRCodeManager.CreateQRCodeResult(qrCodeApath: "path") | true
    }


    @Unroll
    def "createMemberMarketingAccount"() {
        given:
        accountDAO.queryAccountByUid(*_) >> queryAccountEntity
        accountDAO.update(*_) >> 1
        userManager.queryByUid(*_) >> new UserEntity()
        accountDAO.insert(*_) >> 1
        when:
        accountManager.createMemberMarketingAccount("uid", "112")
        then:
        noExceptionThrown()
        where:
        queryAccountEntity << [null, new AccountEntity()]
    }

}