package com.facishare.marketing.provider.service.live

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.CreateObjectDataModel
import com.facishare.marketing.api.arg.PhotoCutOffset
import com.facishare.marketing.api.arg.live.CheckThirdRelateArg
import com.facishare.marketing.api.arg.live.GetMuduLiveArg
import com.facishare.marketing.api.arg.live.GetThirdLiveArg
import com.facishare.marketing.api.arg.live.ListMuduEventArg
import com.facishare.marketing.api.arg.live.QueryThirdLiveArg
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg
import com.facishare.marketing.api.data.usermarketingaccount.FilterData
import com.facishare.marketing.api.result.QueryFormUserDataResult
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult
import com.facishare.marketing.api.result.hexagon.CreateSiteResult
import com.facishare.marketing.api.result.live.ChannelsAccountResult
import com.facishare.marketing.api.result.live.GetViewUrlResult
import com.facishare.marketing.api.result.permission.DataPermissionResult
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountDetailsResult
import com.facishare.marketing.api.service.CustomizeFormDataService
import com.facishare.marketing.api.service.MarketingSceneService
import com.facishare.marketing.api.service.MemberService
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService
import com.facishare.marketing.api.service.hexagon.HexagonService
import com.facishare.marketing.api.service.kis.KisActionService
import com.facishare.marketing.api.service.permission.DataPermissionService
import com.facishare.marketing.api.service.sms.SendService
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO
import com.facishare.marketing.api.vo.appMenu.PaasObjectRuleVO
import com.facishare.marketing.api.vo.live.BindChannelsAccountVo
import com.facishare.marketing.api.vo.live.BindMuduAccountVO
import com.facishare.marketing.api.vo.live.BindThirdAccountArg
import com.facishare.marketing.api.vo.live.BindXiaoetongAccountVO
import com.facishare.marketing.api.vo.live.CreateLiveVO
import com.facishare.marketing.api.vo.live.GetLiveViewUrlVO
import com.facishare.marketing.api.vo.live.GetThirdAccountArg
import com.facishare.marketing.api.vo.live.ListVO
import com.facishare.marketing.api.vo.live.LiveLeadDetailVO
import com.facishare.marketing.api.vo.live.LiveMaterialsVO
import com.facishare.marketing.api.vo.live.QueryLiveEnrollListVO
import com.facishare.marketing.common.enums.CrmMemberFieldEnum
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery
import com.facishare.marketing.common.util.DateUtil
import com.facishare.marketing.outapi.arg.result.AssociateCrmLeadModel
import com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO
import com.facishare.marketing.provider.dao.live.*
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO
import com.facishare.marketing.provider.dto.campaignMergeData.PageCampaignLiveDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity
import com.facishare.marketing.provider.entity.MemberConfigEntity
import com.facishare.marketing.provider.entity.ObjectEnrollJumpSettingEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics
import com.facishare.marketing.provider.entity.live.MarketingLiveViewLoginEntity
import com.facishare.marketing.provider.entity.live.ThirdLiveAccountEntity
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData
import com.facishare.marketing.provider.innerData.live.mudu.MuduApiGetEventListResult
import com.facishare.marketing.provider.innerData.live.polyv.PolyvLiveInnerData
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiGetLiveResult
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiGetRoleUrlResult
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiQueryLiveResult
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.live.ChannelsManager
import com.facishare.marketing.provider.manager.sms.VerificationCodeManager
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.IntegralServiceManager
import com.facishare.marketing.provider.remote.rest.ShortUrlManager
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager
import com.facishare.training.outer.api.message.LiveMqTrainingMessage
import com.facishare.training.outer.api.result.live.CreateLiveResult
import com.facishare.training.outer.api.result.live.UpdateResult
import com.facishare.training.outer.api.service.live.LiveCommonService
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.result.ActionAddResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.google.gson.Gson
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class LiveServiceImplSpec extends Specification {

    def crmLeadMarketingAccountAssociationService = Mock(CrmLeadMarketingAccountAssociationService)
    def liveCommonService = Mock(LiveCommonService)
    def userMarketingAccountService = Mock(UserMarketingAccountService)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def materialTagRelationDao = Mock(MaterialTagRelationDao)
    def materialTagManager = Mock(MaterialTagManager)
    def marketingLiveStatisticsDAO = Mock(MarketingLiveStatisticsDAO)
    def marketingLiveViewLoginDAO = Mock(MarketingLiveViewLoginDAO)
    def liveUserStatusDAO = Mock(LiveUserStatusDAO)
    def shortUrlManager = Mock(ShortUrlManager)
    def marketingEventManager = Mock(MarketingEventManager)
    def customizeFormDataService = Mock(CustomizeFormDataService)
    def hexagonService = Mock(HexagonService)
    def fileV2Manager = Mock(FileV2Manager)
    def eieaConverter = Mock(EIEAConverter)
    def metadataTagManager = Mock(MetadataTagManager)
    def hexagonManager = Mock(HexagonManager)
    def vHallManager = Mock(VHallManager)
    def integralServiceManager = Mock(IntegralServiceManager)
    def kisActionService = Mock(KisActionService)
    def metadataActionService = Mock(MetadataActionService)
    def conferenceManager = Mock(ConferenceManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def objectManager = Mock(ObjectManager)
    def liveManager = Mock(LiveManager)
    def memberService = Mock(MemberService)
    def crmV2Manager = Mock(CrmV2Manager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def memberManager = Mock(MemberManager)
    def spreadChannelManager = Mock(SpreadChannelManager)
    def sceneTriggerManager = Mock(SceneTriggerManager)
    def redisManager = Mock(RedisManager)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def photoDAO = Mock(PhotoDAO)
    def xiaoetongManager = Mock(XiaoetongManager)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def liveUserAccountRelationDAO = Mock(LiveUserAccountRelationDAO)
    def memberAccessibleCampaignDAO = Mock(MemberAccessibleCampaignDAO)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def objectEnrollJumpSettingDAO = Mock(ObjectEnrollJumpSettingDAO)
    def hexagonTemplateSiteDAO = Mock(HexagonTemplateSiteDAO)
    def hexagonTemplatePageDAO = Mock(HexagonTemplatePageDAO)
    def hexagonSiteManager = Mock(HexagonSiteManager)
    def hexagonSiteDAOManager = Mock(HexagonSiteDAOManager)
    def marketingSceneService = Mock(MarketingSceneService)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def xiaoetongAccountDAO = Mock(XiaoetongAccountDAO)
    def marketingStatLogPersistorManger = Mock(MarketingStatLogPersistorManger)
    def verificationCodeManager = Mock(VerificationCodeManager)
    def sendService = Mock(SendService)
    def polyvManager = Mock(PolyvManager)
    def polyvAccountDAO = Mock(PolyvAccountDAO)
    def thirdLiveAccountDAO = Mock(ThirdLiveAccountDAO)
    def channelsManager = Mock(ChannelsManager)
    def muduManager = Mock(MuduManager)
    def converter = Mock(EIEAConverter)
    def wechatAccountManager = Mock(WechatAccountManager)
    def customizeMiniAuthorizeManager = Mock(CustomizeMiniAuthorizeManager)
    def eaWechatAccountBindDao = Mock(EaWechatAccountBindDao)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def browserUserRelationManager = Mock(BrowserUserRelationManager)
    def actionManager = Mock(ActionManager)
    def liveProxySerivce = Mock(LiveProxySerivce)
    def gson = new Gson()
    def liveUserH5Url = 'mocked_liveUserH5Url'
    def polyvLiveUserH5Url = 'mocked_polyvLiveUserH5Url'
    def liveLectureUrl = 'mocked_liveLectureUrl'
    def defaultHexagonIds = "id,id2,id3"
    def channelStransitUrl = 'mocked_channelStransitUrl'
    def transitId = 'mocked_transitId'
    def defaultLiveCover = 'mocked_defaultLiveCover'
    def host = 'mocked_host'
    def pushSessionManager = Mock(PushSessionManager)
    def photoManager = Mock(PhotoManager)
    def cdnSharePath = 'mocked_cdnSharePath'
    def dataPermissionService = Mock(DataPermissionService)
    def appMenuTemplateService = Mock(AppMenuTemplateService)

    def liveService = new LiveServiceImpl(
            crmLeadMarketingAccountAssociationService: crmLeadMarketingAccountAssociationService,
            liveCommonService: liveCommonService,
            userMarketingAccountService: userMarketingAccountService,
            marketingLiveDAO: marketingLiveDAO,
            materialTagRelationDao: materialTagRelationDao,
            materialTagManager: materialTagManager,
            marketingLiveStatisticsDAO: marketingLiveStatisticsDAO,
            marketingLiveViewLoginDAO: marketingLiveViewLoginDAO,
            liveUserStatusDAO: liveUserStatusDAO,
            shortUrlManager: shortUrlManager,
            marketingEventManager: marketingEventManager,
            customizeFormDataService: customizeFormDataService,
            hexagonService: hexagonService,
            fileV2Manager: fileV2Manager,
            eieaConverter: eieaConverter,
            metadataTagManager: metadataTagManager,
            hexagonManager: hexagonManager,
            vHallManager: vHallManager,
            integralServiceManager: integralServiceManager,
            kisActionService: kisActionService,
            metadataActionService: metadataActionService,
            conferenceManager: conferenceManager,
            campaignMergeDataDAO: campaignMergeDataDAO,
            campaignMergeDataManager: campaignMergeDataManager,
            contentMarketingEventMaterialRelationDAO: contentMarketingEventMaterialRelationDAO,
            fsAddressBookManager: fsAddressBookManager,
            objectManager: objectManager,
            liveManager: liveManager,
            memberService: memberService,
            crmV2Manager: crmV2Manager,
            crmMetadataManager: crmMetadataManager,
            memberManager: memberManager,
            spreadChannelManager: spreadChannelManager,
            sceneTriggerManager: sceneTriggerManager,
            redisManager: redisManager,
            hexagonSiteDAO: hexagonSiteDAO,
            photoDAO: photoDAO,
            xiaoetongManager: xiaoetongManager,
            customizeFormDataUserDAO: customizeFormDataUserDAO,
            liveUserAccountRelationDAO: liveUserAccountRelationDAO,
            memberAccessibleCampaignDAO: memberAccessibleCampaignDAO,
            hexagonPageDAO: hexagonPageDAO,
            objectEnrollJumpSettingDAO: objectEnrollJumpSettingDAO,
            hexagonTemplateSiteDAO: hexagonTemplateSiteDAO,
            hexagonTemplatePageDAO: hexagonTemplatePageDAO,
            hexagonSiteManager: hexagonSiteManager,
            hexagonSiteDAOManager: hexagonSiteDAOManager,
            marketingSceneService: marketingSceneService,
            customizeFormDataManager: customizeFormDataManager,
            xiaoetongAccountDAO: xiaoetongAccountDAO,
            marketingStatLogPersistorManger: marketingStatLogPersistorManger,
            verificationCodeManager: verificationCodeManager,
            sendService: sendService,
            polyvManager: polyvManager,
            polyvAccountDAO: polyvAccountDAO,
            thirdLiveAccountDAO: thirdLiveAccountDAO,
            channelsManager: channelsManager,
            muduManager: muduManager,
            converter: converter,
            wechatAccountManager: wechatAccountManager,
            customizeMiniAuthorizeManager: customizeMiniAuthorizeManager,
            eaWechatAccountBindDao: eaWechatAccountBindDao,
            userMarketingMiniappAccountRelationDao: userMarketingMiniappAccountRelationDao,
            userMarketingAccountDAO: userMarketingAccountDAO,
            browserUserRelationManager: browserUserRelationManager,
            actionManager: actionManager,
            liveProxySerivce: liveProxySerivce,
            gson: gson,
            liveUserH5Url: liveUserH5Url,
            polyvLiveUserH5Url: polyvLiveUserH5Url,
            liveLectureUrl: liveLectureUrl,
            defaultHexagonIds: defaultHexagonIds,
            channelStransitUrl: channelStransitUrl,
            transitId: transitId,
            defaultLiveCover: defaultLiveCover,
            host: host,
            pushSessionManager: pushSessionManager,
            photoManager: photoManager,
            cdnSharePath: cdnSharePath,
            dataPermissionService: dataPermissionService,
            appMenuTemplateService: appMenuTemplateService
    )

    @Shared
    def shareObjectData = new ObjectData()

    @Unroll
    def "createLive"() {
        given:
        liveProxySerivce.getBean(*_) >> null
        xiaoetongManager.getMarketingLiveByXiaoetongId(*_) >> xiaoetongLive
        fileV2Manager.getNpathByApath(*_) >> "path"
        metadataActionService.add(*_) >> createMarketingEventResult
        fileV2Manager.changeAWarehouseTempToPermanentBybusiness(*_) >> apath
        fileV2Manager.downloadAFile(*_) >> bytes
        liveCommonService.createLive(*_) >> remoteLiveResult
        liveManager.addLiveByLock(*_) >> "id"
        marketingLiveDAO.updateSubEvent(*_) >> 1
        shortUrlManager.createShortUrl(*_) >> Optional.of("www.url.com")
        marketingLiveDAO.updateMarketingLive(*_) >> 1
        polyvManager.setExternalAuthSetting(*_) >> { printf "dd" }
        marketingLiveDAO.updateXiaoetongLiveStatus(*_) >> { printf "dd" }
        marketingLiveStatisticsDAO.insert(*_) >> { printf "dd" }
        marketingLiveStatisticsDAO.getByXiaoetongLiveId(*_) >> [new MarketingLiveStatistics(status: 1)]
        marketingLiveDAO.updateMarketingLive(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> new MarketingLiveEntity()
        hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(*_) >> new HexagonTemplateSiteEntity()
        hexagonTemplateSiteDAO.getById(*_) >> new HexagonTemplateSiteEntity()
        hexagonPageDAO.getHomePage(*_) >> new HexagonPageEntity(content: "{}")
        hexagonService.copyPage(*_) >> new Result(data: "id")
        hexagonPageDAO.getById(*_) >> new HexagonPageEntity(content: "content")
        hexagonManager.updateLiveHexagonParam(*_) >> new HexagonPageEntity(content: "content")
        hexagonTemplatePageDAO.getBySiteId(*_) >> [new HexagonTemplatePageEntity()]
        hexagonSiteManager.copyPageFromTemplate(*_) >> ["newFormId": "id", "newPageId": "id"]
        hexagonPageDAO.updateContent(*_) >> 1
        hexagonSiteDAO.insert(*_) >> 1
        integralServiceManager.asyncRegisterMaterial(*_) >> { printf "ddd" }
        hexagonService.bindMarketing(*_) >> null
        hexagonSiteDAO.updateHexagonSiteSystemStatus(*_) >> 1
        contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(*_) >> 1
        hexagonService.marketingCopySite(*_) >> copySiteResult
        hexagonManager.updateCopiedLiveHexagon(*_) >> { printf "ddd" }
        marketingLiveDAO.updateFormHexagonById(*_) >> { printf "ddd" }
        liveManager.saveMuduSubEvent(*_) >> { printf "ddd" }
        redisManager.deleteHexgonSite(*_) >> true
        redisManager.deleteHexgonPage(*_) >> true
        hexagonPageDAO.getPageIdsBySiteId(*_) >> ["id"]
        contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(*_) >> 1
        hexagonPageDAO.getHomePage(*_) >> new HexagonPageEntity(content: "{}")
        photoManager.querySinglePhoto(*_) >> new PhotoEntity()
        photoDAO.addPhoto(*_) >> true
        photoManager.addOrUpdatePhotoByCutOffset(*_) >> true
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> null
        marketingSceneService.getOrCreateMarketingScene(*_) >> null
        muduManager.getAccessToken(*_) >> "token"
        muduManager.setAccessConfig(*_) >> true
        shareObjectData.put("_id", "id")
        when:
        liveService.createLive(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                                                                                                                                                                                                                                                                                                  | xiaoetongLive                          | createMarketingEventResult                                                                                       | apath   | bytes                 | remoteLiveResult                                                                            | copySiteResult
        new CreateLiveVO(corpId: 1, livePlatform: 3)                                                                                                                                                                                                                                                                                                                                         | Optional.of(new MarketingLiveEntity()) | null                                                                                                             | null    | null                  | null                                                                                        | null
        new CreateLiveVO(corpId: 1, livePlatform: 6, subEvent: 1, coverTaPath: "path", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                            | Optional.ofNullable()                  | null                                                                                                             | null    | null                  | null                                                                                        | null
        new CreateLiveVO(corpId: 1, livePlatform: 6, subEvent: 2, coverTaPath: "path", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                            | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: -1)                                                        | null    | null                  | null                                                                                        | null
        new CreateLiveVO(corpId: 1, livePlatform: 6, subEvent: 2, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | null    | null                  | null                                                                                        | null
        new CreateLiveVO(corpId: 1, livePlatform: 1, subEvent: 2, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | null                  | null                                                                                        | null
        new CreateLiveVO(corpId: 1, livePlatform: 1, subEvent: 2, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024 * 1025] | null                                                                                        | null
        new CreateLiveVO(corpId: 1, livePlatform: 1, subEvent: 2, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: -1)                              | null
        new CreateLiveVO(corpId: 1, livePlatform: 1, subEvent: 2, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult()) | null
        new CreateLiveVO(corpId: 1, originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(photoTargetType: 43), new PhotoCutOffset(photoTargetType: 44), new PhotoCutOffset(photoTargetType: 45)], livePlatform: 6, subEvent: 2, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1) | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult()) | null
        new CreateLiveVO(corpId: 1, livePlatform: 6, subEvent: 1, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult()) | new Result(errCode: 0, data: new CreateSiteResult(id: "id"))
        new CreateLiveVO(corpId: 1, livePlatform: 3, subEvent: 1, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult()) | new Result(errCode: 0, data: new CreateSiteResult(id: "id"))
        new CreateLiveVO(corpId: 1, livePlatform: 4, subEvent: 1, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                                                         | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult()) | new Result(errCode: 0, data: new CreateSiteResult(id: "id"))
        new CreateLiveVO(corpId: 1, livePlatform: 5, marketingTemplateId: "id", subEvent: 1, coverTaPath: "TA_PATH", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()), startTime: 1, endTime: 1)                                                                                                                                              | Optional.ofNullable()                  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new ActionAddResult(objectData: shareObjectData)) | "apath" | new byte[1024]        | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult()) | new Result(errCode: 0, data: new CreateSiteResult(id: "id"))
    }


    @Unroll
    def "updateLive"() {
        given:
        marketingLiveDAO.getById(*_) >> entity
        liveProxySerivce.getBean(*_) >> null
        conferenceManager.queryMarketingEventCountByTitle(*_) >> marketingEventCount


        liveCommonService.createLive(*_) >> remoteLiveResult
        fileV2Manager.downloadAFile(*_) >> bytes
        liveCommonService.updateLiveInfo(*_) >> remoteUpdateResult
        marketingLiveStatisticsDAO.getByLiveId(*_) >> null
        marketingLiveStatisticsDAO.getByXiaoetongLiveId(*_) >> null
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> null
        sceneTriggerManager.handleLiveStartTimeChange(*_) >> true
        sceneTriggerManager.handleLiveEndTimeChange(*_) >> true
        marketingLiveStatisticsDAO.insert(*_) >> { printf "ddd" }
        polyvManager.setExternalAuthSetting(*_) >> { printf "ddd" }
        marketingLiveDAO.updateFormHexagonById(*_) >> { printf "ddd" }
        fileV2Manager.changeAWarehouseTempToPermanentBybusiness(*_) >> apath
        fileV2Manager.getApathByUrl(*_) >> apathByUrl
        shortUrlManager.createShortUrl(*_) >> Optional.of("oss")
        marketingLiveDAO.updateMarketingLive(*_) >> dbRet
        marketingLiveDAO.updateMarketingLive(*_) >> 1
        hexagonSiteDAO.listByIds(*_) >> [new HexagonSiteEntity(name: "报名预约")]
        hexagonPageDAO.getHomePage(*_) >> new HexagonPageEntity(content: "{\"shareOpts\":{}, \"components\":[{\"type\":\"image\",\"images\":[{\"url\":\"url\"}]}] }")
        memberManager.tryInitMemberConfig(*_) >> memberConfigEntity
        fileV2Manager.getUrlByPath(*_) >> urlByPath
        hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(*_) >> new HexagonTemplateSiteEntity()
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity(name: "name")]
        hexagonPageDAO.update(*_) >> 1
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> null
        integralServiceManager.asyncRegisterMaterial(*_) >> { printf "fff" }
        fileV2Manager.getCPathByCdnUrl(*_) >> cdnPath
        eieaConverter.enterpriseAccountToId(*_) >> 1
        fileV2Manager.getNpathByApath(*_) >> "path"
        hexagonService.copyPage(*_) >> new Result(data: "Id")
        metadataActionService.edit(*_) >> marketingResult
        hexagonTemplatePageDAO.getBySiteId(*_) >> [new HexagonTemplatePageEntity()]
        hexagonPageDAO.getById(*_) >> new HexagonPageEntity(content: "content")
        hexagonManager.updateLiveHexagonParam(*_) >> new HexagonPageEntity(content: "content")
        hexagonSiteManager.copyPageFromTemplate(*_) >> ["newFormId": "id", "newPageId": "id"]
        hexagonPageDAO.updateContent(*_) >> 1
        hexagonSiteDAO.insert(*_) >> 1
        integralServiceManager.asyncRegisterMaterial(*_) >> { printf "ddd" }
        hexagonService.bindMarketing(*_) >> null
        hexagonSiteDAO.updateHexagonSiteSystemStatus(*_) >> 1
        contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> new MarketingLiveEntity(corpId: 1)
        eieaConverter.enterpriseIdToAccount(*_) >> 1
        contentMarketingEventMaterialRelationDAO.listByMarketingEventIdAndObjectType(*_) >> [new ContentMarketingEventMaterialRelationEntity(objectId: "id")]
        hexagonTemplatePageDAO.getHomePage(*_) >> new HexagonTemplatePageEntity(name: "name")
        channelsManager.getAccount(*_) >> new Result(data: new ChannelsAccountResult(channelsAvatar: "a", channelsName: "a"))
        memberManager.isOpenMember(*_) >> true
        when:
        liveService.updateLive(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                                                                                                                                                                                                     | entity                                                                                                                                                                               | marketingEventCount | marketingResult                                           | remoteLiveResult                                                                                     | bytes                 | remoteUpdateResult                                                                               | apath  | apathByUrl                 | cdnPath | dbRet | memberConfigEntity       | urlByPath
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                                                                             | null                                                                                                                                                                                 | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: -1) | null                                                                                                 | null                  | null                                                                                             | null   | null                       | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                                                            | new MarketingLiveEntity(corpId: 1, id: "id")                                                                                                                                         | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: -1) | null                                                                                                 | null                  | null                                                                                             | null   | null                       | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 2)                                                                                                                            | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: -1) | null                                                                                                 | null                  | null                                                                                             | null   | null                       | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                                | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1)                                                                                                                            | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: -1) | null                                                                                                 | null                  | null                                                                                             | null   | "mocked_defaultLiveCover"  | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                                | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1)                                                                                                                            | 1                   | new com.fxiaoke.crmrestapi.common.result.Result(code: -1) | null                                                                                                 | null                  | null                                                                                             | null   | "mocked_defaultLiveCover"  | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                   | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 1)  | null                                                                                                 | null                  | null                                                                                             | null   | "mocked_defaultLiveCover2" | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                   | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | null                                                                                                 | null                  | null                                                                                             | null   | "mocked_defaultLiveCover2" | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                   | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | null                                                                                                 | null                  | null                                                                                             | null   | "mocked_defaultLiveCover2" | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                   | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | null                                                                                                 | null                  | null                                                                                             | null   | "mocked_defaultLiveCover2" | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                   | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | null                                                                                                 | new byte[1024 * 1025] | null                                                                                             | null   | "mocked_defaultLiveCover2" | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "mocked_host", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                                                   | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: -1)                                       | new byte[1024 * 1024] | null                                                                                             | null   | "mocked_defaultLiveCover2" | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | null                       | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | null    | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1")                                                                                                               | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | "path"  | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1", liveId: 2)                                                                                                    | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | null                  | null                                                                                             | "path" | "path"                     | "path"  | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1", liveId: 2)                                                                                                    | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1025] | null                                                                                             | "path" | "path"                     | "path"  | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1", liveId: 2)                                                                                                    | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | "path"  | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 1, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 1, title: "t1", liveId: 2)                                                                                                    | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | new com.facishare.training.common.result.Result(errorCode: 0, data: new UpdateResult(liveId: 1)) | "path" | "path"                     | "path"  | 0     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 4, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 4, title: "t1", startTime: new Date(), endTime: new Date())                                                                   | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | "path"  | 1     | null                     | null
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 3, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 3, title: "t1", startTime: new Date(), endTime: new Date())                                                                   | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | "path"  | -1    | null                     | null
        new CreateLiveVO(desc: "11111111111111111111111111111111,", corpId: 1, startTime: 1000000L, id: "id", livePlatform: 5, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>())) | new MarketingLiveEntity(corpId: 1, id: "id", platform: 5, title: "t1", startTime: new Date(), endTime: new Date(), formHexagonId: "id", shortViewUrl: "url", marketingEventId: "Id") | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | "path"  | 0     | new MemberConfigEntity() | "path"
        new CreateLiveVO(corpId: 1, startTime: 1000000L, id: "id", livePlatform: 5, coverTaPath: "TA_mocked_host_mocked_cdnSharePath", title: "t2", createObjectDataModel: new CreateObjectDataModel.Arg(objectData: new HashMap<String, Object>()))                                            | new MarketingLiveEntity(corpId: 1, id: "id", platform: 5, title: "t1", startTime: new Date(), endTime: new Date(), formHexagonId: "id", shortViewUrl: "url", marketingEventId: "Id") | 0                   | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)  | new com.facishare.training.common.result.Result(errorCode: 0, data: new CreateLiveResult(liveId: 1)) | new byte[1024 * 1024] | null                                                                                             | "path" | "path"                     | "path"  | 0     | new MemberConfigEntity() | "path"
    }

    @Unroll
    def "list"() {
        given:
        materialTagRelationDao.queryByAnyTags(*_) >> ids
        materialTagRelationDao.queryByAllTags(*_) >> ids
        marketingEventManager.listMarketingEventsV2(*_) >> marketingEventsBriefResults
        marketingLiveDAO.pageLiveEnrollsByEaAndStatus(*_) >> marketingLiveEntityList
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getLiveByEaAndMarketingEventIds(*_) >>> [[new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")], [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id"), new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3)]]
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> null
        marketingLiveDAO.addMarketingLive(*_) >> 1
        marketingEventManager.listMarketingEvents(*_) >> marketingEventsBriefResults
        hexagonManager.getHexagonBaseInfoById(*_) >> ["id": new HexagonBaseInfoDTO()]
        campaignMergeDataDAO.queryCampaignUserCountByMarketingEventIds(*_) >> [new CampaignStatisticDTO(marketingEventId: "id", userCount: 1)]
        materialTagManager.buildTagName(*_) >> ["id": ["id1"]]
        marketingEventManager.calMarketingEventsPV(*_) >> ["id": 1]
        marketingEventManager.batchCalMarketingEventUVStatistic(*_) >> ["id": 1]
        shareObjectData.put("marketing_event_id", "id")
        campaignMergeDataManager.getCampaignMembersObjByMaketingEventIdsWithFields(*_) >> [shareObjectData]
        fileV2Manager.batchGetUrlByPath(*_) >> ["path": "path"]
        marketingLiveStatisticsDAO.getLiveStatByLiveIds(*_) >> [new MarketingLiveStatistics(status: 1, totalViewUsers: 1, chatTimes: 1)]
        marketingLiveStatisticsDAO.getLiveStatByXiaoetongLiveIds(*_) >> [new MarketingLiveStatistics(status: 1, totalViewUsers: 1, chatTimes: 1)]
        marketingEventManager.listMarketingEventsByPager(*_) >> marketingEventsBriefResultsPage
        when:
        liveService.list(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                                   | ids    | marketingEventsBriefResults                | marketingEventsBriefResultsPage                                                                                                                                                                                                        | marketingLiveEntityList
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 1))     | []     | null                                       | null                                                                                                                                                                                                                                   | null
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | []                                         | null                                                                                                                                                                                                                                   | null
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult()]         | null                                                                                                                                                                                                                                   | null
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | []
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 2, startTime: DateUtil.plusDay(new Date(), 1), endTime: new Date(), formHexagonId: "id", status: 5, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 2, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), 1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 2, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 1, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 3, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, materialTagFilter: new MaterialTagFilterArg(materialTagIds: ["Id"], type: 2))     | ["id"] | [new MarketingEventsBriefResult(id: "id")] | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1)                                                                                   | ["id"] | null                                       | null                                                                                                                                                                                                                                   | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1)                                                                                   | ["id"] | [new MarketingEventsBriefResult(id: "id")] | new com.facishare.marketing.api.result.PageResult(totalCount: 1, data: [new MarketingEventsBriefResult(id: "id", lifeStatus: "normal"), new MarketingEventsBriefResult(lifeStatus: "normal", createTime: System.currentTimeMillis())]) | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, isShowSpread: true)                                                               | ["id"] | []                                         | new com.facishare.marketing.api.result.PageResult(totalCount: 1, data: [new MarketingEventsBriefResult(id: "id", lifeStatus: "normal"), new MarketingEventsBriefResult(lifeStatus: "normal", createTime: System.currentTimeMillis())]) | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, isShowSpread: true)                                                               | ["id"] | [new MarketingEventsBriefResult(id: "id")] | new com.facishare.marketing.api.result.PageResult(totalCount: 1, data: [new MarketingEventsBriefResult(id: "id", lifeStatus: "normal"), new MarketingEventsBriefResult(lifeStatus: "normal", createTime: System.currentTimeMillis())]) | []
        new ListVO(pageNum: 1, pageSize: 1, isShowSpread: true)                                                               | ["id"] | [new MarketingEventsBriefResult(id: "id")] | new com.facishare.marketing.api.result.PageResult(totalCount: 1, data: [new MarketingEventsBriefResult(id: "id", lifeStatus: "normal"), new MarketingEventsBriefResult(lifeStatus: "normal", createTime: System.currentTimeMillis())]) | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, isShowSpread: true, filterData: new FilterData(query: new SearchTemplateQuery())) | ["id"] | []                                         | new com.facishare.marketing.api.result.PageResult(totalCount: 1, data: [new MarketingEventsBriefResult(id: "id", lifeStatus: "normal"), new MarketingEventsBriefResult(lifeStatus: "normal", createTime: System.currentTimeMillis())]) | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
        new ListVO(pageNum: 1, pageSize: 1, isShowSpread: true, filterData: new FilterData(query: new SearchTemplateQuery())) | ["id"] | [new MarketingEventsBriefResult(id: "id")] | new com.facishare.marketing.api.result.PageResult(totalCount: 1, data: [new MarketingEventsBriefResult(id: "id", lifeStatus: "normal"), new MarketingEventsBriefResult(lifeStatus: "normal", createTime: System.currentTimeMillis())]) | [new MarketingLiveEntity(platform: 4, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), formHexagonId: "id", status: 3, marketingEventId: "id")]
    }

    @Unroll
    def "appList"() {
        given:
        dataPermissionService.getDataPermission(*_) >> new Result(errCode: 0, data: new DataPermissionResult(status: true, dataDepartments: [1]))
        appMenuTemplateService.getPaasObjectRule(*_) >> paasObjectRuleResult
        materialTagRelationDao.queryByAnyTags(*_) >> ids
        materialTagRelationDao.queryByAllTags(*_) >> ids
        marketingEventManager.listMarketingEventsByPager(*_) >> marketingEventsBriefResults
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.pageLiveEnrollsByEaAndStatus(*_) >> []
        marketingEventManager.listMarketingEvents(*_) >> marketingEventsBriefResults2
        when:
        liveService.appList(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                             | ids    | paasObjectRuleResult                                                                                                  | marketingEventsBriefResults                                                                 | marketingEventsBriefResults2       | marketingLiveEntityList
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1)                                                | []     | null                                                                                                                  | null                                                                                        | null                               | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id")                                  | []     | new Result(errCode: -1)                                                                                               | null                                                                                        | null                               | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id")                                  | []     | new Result(errCode: 0, data: new PaasObjectRuleVO(appMenuTagVO: new AppMenuTagVO(tagOperator: 1, tagIdList: ["id"]))) | null                                                                                        | null                               | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key", statusList: [1]) | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(appMenuTagVO: new AppMenuTagVO(tagOperator: 2, tagIdList: ["id"]))) | new com.facishare.marketing.api.result.PageResult(data: [])                                 | []                                 | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key", statusList: [1]) | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(appMenuTagVO: new AppMenuTagVO(tagOperator: 1, tagIdList: ["id"]))) | new com.facishare.marketing.api.result.PageResult(data: [new MarketingEventsBriefResult()]) | [new MarketingEventsBriefResult()] | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key", statusList: [1]) | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(appMenuTagVO: new AppMenuTagVO(tagOperator: 1, tagIdList: ["id"]))) | new com.facishare.marketing.api.result.PageResult(data: [new MarketingEventsBriefResult()]) | [new MarketingEventsBriefResult()] | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key", statusList: [1]) | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(appMenuTagVO: new AppMenuTagVO(tagOperator: 1, tagIdList: ["id"]))) | null                                                                                        | [new MarketingEventsBriefResult()] | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key", statusList: [1]) | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(appMenuTagVO: new AppMenuTagVO(tagOperator: 1, tagIdList: ["id"]))) | new com.facishare.marketing.api.result.PageResult(data: [new MarketingEventsBriefResult()]) | [new MarketingEventsBriefResult()] | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key")                  | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(objectAccessibleRule: "ALL"))                                       | new com.facishare.marketing.api.result.PageResult(data: [new MarketingEventsBriefResult()]) | [new MarketingEventsBriefResult()] | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key")                  | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(objectAccessibleRule: "OBJECT_FILTER", filters: []))                | new com.facishare.marketing.api.result.PageResult(data: [new MarketingEventsBriefResult()]) | [new MarketingEventsBriefResult()] | []
        new ListVO(fsUserId: 1, pageNum: 1, pageSize: 1, menuId: "id", keyword: "key")                  | ["id"] | new Result(errCode: 0, data: new PaasObjectRuleVO(objectAccessibleRule: "OBJECT_FILTER", filters: []))                | new com.facishare.marketing.api.result.PageResult(data: [new MarketingEventsBriefResult()]) | [new MarketingEventsBriefResult()] | []
    }

    @Unroll
    def "getDetail"() {
        given:
        marketingLiveDAO.getById(*_) >> entity
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> entity
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveStatisticsDAO.getByLiveId(*_) >> new MarketingLiveStatistics(status: 1)
        fileV2Manager.batchGetUrlByPath(*_) >> ["path": "path"]
        memberService.checkWxServiceUserIsMember(*_) >> new Result(data: "id")
        memberService.checkH5UserIsMember(*_) >> new Result(data: "id")
        shareObjectData.put(CrmMemberFieldEnum.NAME.getApiName(), "name")
        shareObjectData.put(CrmMemberFieldEnum.PHONE.getApiName(), "PHONE")
        crmV2Manager.getDetail(*_) >> shareObjectData
        fileV2Manager.getUrlByPath(*_) >> "path"
        shareObjectData.put("lock_status", lockStatus)
        crmV2Manager.getDetail(*_) >> shareObjectData
        objectEnrollJumpSettingDAO.getByMarketingEventId(*_) >> new ObjectEnrollJumpSettingEntity()
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity()
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        when:
        liveService.getDetail("ea", "id", role, identityCheckType, null, "appId", "openId")
        then:
        noExceptionThrown()
        where:
        role | identityCheckType | entity                                                                                                                                                                                                           | lockStatus
        1    | 1                 | null                                                                                                                                                                                                             | "1"
        2    | 1                 | null                                                                                                                                                                                                             | "1"
        2    | 1                 | new MarketingLiveEntity(corpId: 1, id: "id", startTime: new Date(), endTime: new Date(), tags: "[{}]", platform: 3, lecturePassword: "123")                                                                      | "1"
        1    | 2                 | new MarketingLiveEntity(corpId: 1, id: "id", startTime: DateUtil.plusDay(new Date(), 1), endTime: new Date(), tags: "[{}]", platform: 2, lecturePassword: "123")                                                 | "1"
        1    | 2                 | new MarketingLiveEntity(corpId: 1, id: "id", startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), 1), tags: "[{}]", platform: 2, lecturePassword: "123")                           | "0"
        1    | 2                 | new MarketingLiveEntity(corpId: 1, id: "id", startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), tags: "[{}]", platform: 2, lecturePassword: "123", showAcitivityList: true) | "0"
        2    | 2                 | new MarketingLiveEntity(corpId: 1, id: "id", startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1), tags: "[{}]", platform: 2, lecturePassword: "123", showAcitivityList: true) | "0"
    }

    @Unroll
    def "getParentDetail"() {
        given:
        marketingLiveDAO.getById(*_) >> entity
        marketingLiveDAO.getMarketingLiveByXiaoetongId(*_) >> parentEntity
        def spy = Spy(liveService)
        spy.getDetail(*_) >> null
        when:
        spy.getParentDetail("ea", "id", 1, null, null, "appId", "openId")
        then:
        noExceptionThrown()
        where:
        entity                    | parentEntity
        null                      | null
        new MarketingLiveEntity() | null
        new MarketingLiveEntity() | new MarketingLiveEntity()
    }

    @Unroll
    def "getLectureUrl"() {
        given:
        marketingLiveDAO.getById(*_) >> entity
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        vHallManager.getRoleUrl(*_) >> new VHallApiGetRoleUrlResult()
        polyvManager.getLiveDetail(*_) >> new PolyvLiveInnerData(code: 200, data: new PolyvLiveInnerData.LiveData())
        when:
        liveService.getLectureUrl("id", lecturePassword, type)
        then:
        noExceptionThrown()
        where:
        entity                                                      | lecturePassword | type
        null                                                        | "a"             | 1
        new MarketingLiveEntity(corpId: 1)                          | "a"             | 4
        new MarketingLiveEntity(corpId: 1)                          | "a"             | 7
        new MarketingLiveEntity(corpId: 1, lecturePassword: "pass") | "a"             | 8
        new MarketingLiveEntity(corpId: 1, lecturePassword: "pass") | "pass"          | 8
    }

    @Unroll
    def "getLiveBriefStatistics"() {
        given:
        liveManager.getLiveStatistics(*_) >> null
        when:
        liveService.getLiveBriefStatistics("ea", "id", [])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getLiveLeadDetail"() {
        given:
        customizeFormDataService.queryMultipleFormUserData(*_) >> formUserDataResult
        marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(*_) >> [new MarketingLiveViewLoginEntity(phone: "110")]
        metadataTagManager.getObjectDataIdAndTagNameListDatasByObjectDataIds(*_) >> [new ObjectDataIdAndTagNameListData(dataId: "id")]
        when:
        liveService.getLiveLeadDetail(arg)
        then:
        noExceptionThrown()
        where:
        arg                    | formUserDataResult
        new LiveLeadDetailVO() | null
        new LiveLeadDetailVO() | new Result(data: new PageResult(result: [new QueryFormUserDataResult()]))
        new LiveLeadDetailVO() | new Result(data: new PageResult(result: [new QueryFormUserDataResult(leadId: "id")]))
        new LiveLeadDetailVO() | new Result(data: new PageResult(result: [new QueryFormUserDataResult(leadId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110")), new QueryFormUserDataResult(leadId: "id2", submitContent: new CustomizeFormDataEnroll(phone: "110"))]))
    }

    @Unroll
    def "queryLeftFlow"() {
        given:
        liveCommonService.queryLeftFlow(*_) >> result
        when:
        liveService.queryLeftFlow(1)
        then:
        noExceptionThrown()
        where:
        result << [null, new com.facishare.training.common.result.Result(errorCode: 0)]
    }

    @Unroll
    def "getViewUrl"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLiveEntity
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> campaignMergeDataEntities
        crmV2Manager.getObjectData(*_) >> objectData
        marketingLiveStatisticsDAO.getByLiveId(*_) >> marketingLiveStatistics
        marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(*_) >> marketingLiveViewLoginEntityList
        marketingLiveViewLoginDAO.insert(*_) >> { printf "ddd" }
        crmLeadMarketingAccountAssociationService.associateCrmLead(*_) >> new Result(data: new AssociateCrmLeadModel.AssociateCrmLeadResult())
        userMarketingAccountService.batchAddTagsToUserMarketings(*_) >> null
        shareObjectData.put("approval_status", approvalStatus)
        when:
        liveService.getViewUrl(arg)
        then:
        noExceptionThrown()
        where:
        arg                                              | marketingLiveEntity                                                                                                                                                           | campaignMergeDataEntities                                                  | objectData      | approvalStatus | marketingLiveStatistics                   | marketingLiveViewLoginEntityList
        new GetLiveViewUrlVO()                           | null                                                                                                                                                                          | null                                                                       | null            | null           | null                                      | []
        new GetLiveViewUrlVO()                           | new MarketingLiveEntity(corpId: 1, status: 6)                                                                                                                                 | null                                                                       | null            | null           | null                                      | []
        new GetLiveViewUrlVO()                           | new MarketingLiveEntity(corpId: 1, status: 1)                                                                                                                                 | []                                                                         | null            | null           | null                                      | []
        new GetLiveViewUrlVO()                           | new MarketingLiveEntity(corpId: 1, status: 1)                                                                                                                                 | [new CampaignMergeDataEntity()]                                            | shareObjectData | "processing"   | null                                      | []
        new GetLiveViewUrlVO()                           | new MarketingLiveEntity(corpId: 1, status: 1, platform: 1, maxLiveCount: 1)                                                                                                   | [new CampaignMergeDataEntity()]                                            | shareObjectData | "aaa"          | new MarketingLiveStatistics(viewTimes: 2) | []
        new GetLiveViewUrlVO(phone: "110", name: "name") | new MarketingLiveEntity(corpId: 1, status: 1, platform: 1, maxLiveCount: 1)                                                                                                   | [new CampaignMergeDataEntity()]                                            | shareObjectData | "aaa"          | new MarketingLiveStatistics(viewTimes: 0) | [new MarketingLiveViewLoginEntity()]
        new GetLiveViewUrlVO(phone: "110", name: "name") | new MarketingLiveEntity(corpId: 1, status: 1, platform: 1, maxLiveCount: 1, tags: "[{}]")                                                                                     | [new CampaignMergeDataEntity(bindCrmObjectType: 0)]                        | shareObjectData | "aaa"          | new MarketingLiveStatistics(viewTimes: 0) | []
        new GetLiveViewUrlVO(phone: "110", name: "name") | new MarketingLiveEntity(corpId: 1, status: 1, platform: 2, maxLiveCount: 1, tags: "[{}]", otherPlatformUrl: "www.baidu.com?aa/", startTime: DateUtil.minusDay(new Date(), 1)) | [new CampaignMergeDataEntity(bindCrmObjectType: 0, bindCrmObjectId: "id")] | shareObjectData | "aaa"          | new MarketingLiveStatistics(viewTimes: 0) | []
    }

    @Unroll
    def "createRecordActionAndUpdateCampaign"() {
        given:
        browserUserRelationManager.getOrCreateBrowserUserIdByPhone(*_) >> Optional.of("aa")
        campaignMergeDataManager.updateCampaignMembersObj(*_) >> { printf "ddd" }
        actionManager.sendMarketingActivityActionToMq(*_) >> { printf "ddd" }
        when:
        liveService.createRecordActionAndUpdateCampaign(new GetLiveViewUrlVO(), "ea", entity, new CampaignMergeDataEntity())
        then:
        noExceptionThrown()
        where:
        entity << [new MarketingLiveEntity(startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), 1)),
                   new MarketingLiveEntity(startTime: DateUtil.plusDay(new Date(), 1), endTime: DateUtil.plusDay(new Date(), -1))]
    }

    @Unroll
    def "checkUserInMarketingLive"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLive
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> entity
        userMarketingAccountDAO.getById(*_) >> userMarketingAccount
        def spy = Spy(liveService)
        spy.getViewUrl(*_) >> result
        when:
        spy.checkUserInMarketingLive("id", "uid")
        then:
        noExceptionThrown()
        where:
        marketingLive                      | entity                                                               | userMarketingAccount                         | result
        null                               | null                                                                 | null                                         | null
        new MarketingLiveEntity(corpId: 1) | null                                                                 | null                                         | null
        new MarketingLiveEntity(corpId: 1) | new UserMarketingMiniappAccountRelationEntity(userMarketingId: "id") | null                                         | null
        new MarketingLiveEntity(corpId: 1) | new UserMarketingMiniappAccountRelationEntity(userMarketingId: "id") | new UserMarketingAccountEntity(phone: "110") | new Result(data: new GetViewUrlResult(code: -1))
        new MarketingLiveEntity(corpId: 1) | new UserMarketingMiniappAccountRelationEntity(userMarketingId: "id") | new UserMarketingAccountEntity(phone: "110") | new Result(data: new GetViewUrlResult(code: 0))
    }


    @Unroll
    def "exportLiveLead"() {
        given:
        customizeFormDataService.exportMultipleFormEnrollsData(*_) >> null
        when:
        liveService.exportLiveLead(new LiveLeadDetailVO())
        then:
        noExceptionThrown()
    }

    @Unroll
    def "handlerLiveMQ"() {
        given:
        marketingLiveDAO.getByLiveIdAndCorpId(*_) >> marketingLiveEntity
        marketingLiveStatisticsDAO.updateStatus(*_) >> 1
        marketingLiveDAO.updateStatusByLiveId(*_) >> { printf "dd" }
        liveCommonService.setDefaultRecord(*_) >> recordResult
        marketingLiveDAO.updateLiveRecord(*_) >> { printf "ddd" }
        when:
        liveService.handlerLiveMQ(new LiveMqTrainingMessage(status: 3, ei: 1))
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity                    | recordResult
        null                                   | null
        new MarketingLiveEntity(autoRecord: 1) | null
        new MarketingLiveEntity(autoRecord: 1) | new com.facishare.training.common.result.Result(errorCode: 0, data: 1)
    }

    @Unroll
    def "getViewCheckInfo"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLiveEntity
        when:
        liveService.getViewCheckInfo("id")
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity << [null, new MarketingLiveEntity()]
    }

    @Unroll
    def "getLectrureCheckInfo"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLiveEntity
        when:
        liveService.getLectrureCheckInfo("id")
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity << [null, new MarketingLiveEntity(lecturePassword: "ooo")]
    }

    @Unroll
    def "getLiveStatus"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLiveEntity
        marketingLiveStatisticsDAO.getByLiveId(*_) >> marketingLiveStatistics
        marketingLiveStatisticsDAO.getByXiaoetongLiveId(*_) >> marketingLiveStatistics2
        when:
        liveService.getLiveStatus("id")
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity                                                                                                                     | marketingLiveStatistics                | marketingLiveStatistics2
        null                                                                                                                                    | null                                   | []
        new MarketingLiveEntity(corpId: 1, platform: 2, startTime: DateUtil.plusDay(new Date(), 1), endTime: DateUtil.plusDay(new Date(), 1))   | null                                   | []
        new MarketingLiveEntity(corpId: 1, platform: 2, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), 1))  | null                                   | []
        new MarketingLiveEntity(corpId: 1, platform: 2, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1)) | null                                   | []
        new MarketingLiveEntity(corpId: 1, platform: 1, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1)) | null                                   | []
        new MarketingLiveEntity(corpId: 1, platform: 1, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1)) | new MarketingLiveStatistics(status: 1) | []
        new MarketingLiveEntity(corpId: 1, platform: 3, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1)) | new MarketingLiveStatistics(status: 1) | []
        new MarketingLiveEntity(corpId: 1, platform: 3, startTime: DateUtil.plusDay(new Date(), -1), endTime: DateUtil.plusDay(new Date(), -1)) | new MarketingLiveStatistics(status: 1) | [new MarketingLiveStatistics()]
    }

    @Unroll
    def "queryLiveEnrollList"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> marketingLiveEntity
        xiaoetongManager.isXiaoetongUpgradedLive(*_) >> true
        crmV2Manager.getObjIdsByRuleGroupJson(*_) >> ["id"]
        campaignMergeDataDAO.pageCampaignLiveData(*_) >> pageCampaignLiveDTOList
        campaignMergeDataDAO.countCampaignLiveData(*_) >> 1
        shareObjectData.put("_id", "id")
        conferenceManager.queryCampaignMembersObjByFilter(*_) >> [shareObjectData]
        conferenceManager.queryCampaignMembersObjBusinessOwnerMap(*_) >> ["id": "name"]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg()]
        fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(*_) >> [1: "name"]
        objectManager.getObjectName(*_) >> "name"
        memberManager.getLatestAccessibleMemberIdByCampaignIds(*_) >> ["id": "id"]
        campaignMergeDataManager.queryWxUserInfo(*_) >> ["openId": new CampaignMergeDataManager.WxUserData()]
        campaignMergeDataManager.getCampaignMembersObjByMaketingEventIds(*_) >> [shareObjectData]
        shareObjectData.put("enterpriserelation_id", "id")
        shareObjectData.put("name", "name")
        customizeFormDataManager.getCustomerListByEnterpriserelationIds(*_) >> [shareObjectData]
        shareObjectData.put("outer_uid", "uid")
        customizeFormDataManager.getEmployeeListByOuterUidIds(*_) >> [shareObjectData]
        crmMetadataManager.batchGetByIdsV3(*_) >> [shareObjectData]
        campaignMergeDataManager.countPayOrderNumber(*_) >> ["id": 1]
        when:
        liveService.queryLiveEnrollList(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                | marketingLiveEntity                             | pageCampaignLiveDTOList
        new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1) | null                                            | []
        new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1) | new MarketingLiveEntity(corpId: 1)              | []
        new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1) | new MarketingLiveEntity(corpId: 1)              | [new PageCampaignLiveDTO(outTenantId: "id", campaignMembersObjId: "id", spreadFsUid: 1, totalAmount: 1)]
        new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1) | new MarketingLiveEntity(corpId: 1)              | [new PageCampaignLiveDTO(outTenantId: "id", campaignMembersObjId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1)]
        new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1) | new MarketingLiveEntity(corpId: 1, platform: 1) | [new PageCampaignLiveDTO(outTenantId: "id", campaignMembersObjId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1)]
        new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1) | new MarketingLiveEntity(corpId: 1, platform: 1) | [new PageCampaignLiveDTO(outTenantId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1)]
    }

    @Unroll
    def "queryLiveEnrollListForSync"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> marketingLiveEntity
        xiaoetongManager.isXiaoetongUpgradedLive(*_) >> true
        campaignMergeDataDAO.pageCampaignLiveData(*_) >> pageCampaignLiveDTOList
        campaignMergeDataDAO.countCampaignLiveData(*_) >> 1
        when:
        liveService.queryLiveEnrollListForSync(new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1))
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity                  | pageCampaignLiveDTOList
        null                                 | []
        new MarketingLiveEntity()            | []
        new MarketingLiveEntity(platform: 1) | [new PageCampaignLiveDTO()]
    }

    @Unroll
    def "exportLiveEnrollListInner"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> marketingLiveEntity
        campaignMergeDataDAO.queryCampaignLiveData(*_) >> pageCampaignLiveDTOList
        marketingLiveViewLoginDAO.getBymarketingEventId(*_) >> [new MarketingLiveViewLoginEntity(phone: "110")]
        liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveId(*_) >> [new LiveUserStatusEntity(phone: "110")]
        shareObjectData.put("_id", "id")
        conferenceManager.queryCampaignMembersObjByFilter(*_) >> [shareObjectData]
        conferenceManager.queryCampaignMembersObjBusinessOwnerMap(*_) >> ["id": "id"]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg()]
        shareObjectData.put("enterpriserelation_id", "id")
        shareObjectData.put("name", "name")
        customizeFormDataManager.getCustomerListByEnterpriserelationIds(*_) >> [shareObjectData]
        shareObjectData.put("outer_uid", "uid")
        customizeFormDataManager.getEmployeeListByOuterUidIds(*_) >> [shareObjectData]
        objectManager.getObjectName(*_) >> "ddd"
        spreadChannelManager.queryChannelMapData(*_) >> ["channel": "channel"]
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(*_) >> new MemberAccessibleCampaignEntity()
        userMarketingAccountService.getUserMarketingAccountByMemberId(*_) >> new Result(data: "id")
        userMarketingAccountService.getUserMarketingAccountDetails(*_) >> new Result(data: new UserMarketingAccountDetailsResult())
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "ss"
        pushSessionManager.pushExcelToFileAssistant(*_) >> { printf "fff" }
        when:
        liveService.exportLiveEnrollListInner(new QueryLiveEnrollListVO(pageNum: 1, pageSize: 1))
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity                             | pageCampaignLiveDTOList
        null                                            | []
        new MarketingLiveEntity(corpId: 1, platform: 1) | [new PageCampaignLiveDTO(outTenantId: "id", campaignMembersObjId: "id", spreadFsUid: 1, totalAmount: 1)]
        new MarketingLiveEntity(corpId: 1, platform: 2) | [new PageCampaignLiveDTO(outTenantId: "id", campaignMembersObjId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1)]
        new MarketingLiveEntity(corpId: 1, platform: 2) | [new PageCampaignLiveDTO(outTenantId: "id", submitContent: new CustomizeFormDataEnroll(), campaignMembersObjId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1)]
        new MarketingLiveEntity(corpId: 1, platform: 3) | [new PageCampaignLiveDTO(phone: "110", submitContent: new CustomizeFormDataEnroll(), memberAccessibleCampaignId: "id", outTenantId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1)]
        new MarketingLiveEntity(corpId: 1, platform: 1) | [new PageCampaignLiveDTO(phone: "110", submitContent: new CustomizeFormDataEnroll(), memberAccessibleCampaignId: "id", outTenantId: "id", spreadFsUid: 1, totalAmount: 1, wxAppId: "appId", openId: "openId", outUid: "uid", bindCrmObjectId: "id", bindCrmObjectType: 1, addCampaignMember: true, saveCrmStatus: 1, viewStatus: 1, replayStatus: 1, interactiveCount: 1, viewTime: 1, replayTime: 1)]
    }

    @Unroll
    def "syncLiveStatistics"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLiveEntity
        liveManager.syncVhallLiveTryLock(*_) >> { printf "dd" }
        liveManager.syncXiaoetongLiveTryLock(*_) >> { printf "dd" }
        xiaoetongManager.syncXiaoetongLiveStatusByEa(*_) >> { printf "dd" }
        liveManager.syncLiveDataToCrm(*_) >> { printf "dd" }
        polyvManager.syncLiveDataById(*_) >> { printf "dd" }
        polyvManager.syncLiveStatusByEaAndChannelId(*_) >> { printf "dd" }
        liveManager.syncLiveDataToCrm(*_) >> { printf "dd" }
        liveManager.syncMuduLiveTryLock(*_) >> { printf "dd" }
        muduManager.syncMuduLiveStatus(*_) >> { printf "dd" }
        vHallManager.syncDataTryLock(*_) >> { printf "dd" }
        when:
        liveService.syncLiveStatistics("id", "ea")
        then:
        noExceptionThrown()
        where:
        marketingLiveEntity << [null, new MarketingLiveEntity(platform: 1), new MarketingLiveEntity(platform: 3), new MarketingLiveEntity(platform: 4), new MarketingLiveEntity(platform: 6), new MarketingLiveEntity(platform: 7)]
    }

    @Unroll
    def "upsertUserStatus"() {
        given:
        liveManager.upsertUserStatus(*_) >> { printf "dd" }
        when:
        liveService.upsertUserStatus(1, ["1"], null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "queryLiveUserByStatus"() {
        given:
        liveManager.queryLiveUserByStatus(*_) >> null
        when:
        liveService.upsertUserStatus(1, null, null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "syncLiveByTimer"() {
        given:
        liveManager.syncVhallLiveStatisticsData(*_) >> { printf "dd" }
        liveManager.syncXiaoetongLiveStatisticsData(*_) >> { printf "dd" }
        liveManager.syncPolyvLiveStatisticsData(*_) >> { printf "dd" }
        liveManager.syncMuduLiveData4Job(*_) >> { printf "dd" }
        liveManager.syncVHallLiveData4Job(*_) >> { printf "dd" }
        when:
        liveService.syncLiveByTimer()
        then:
        noExceptionThrown()
    }

    @Unroll
    def "xiaoetongLiveList"() {
        given:
        xiaoetongManager.getLiveList(*_) >> null
        when:
        liveService.xiaoetongLiveList(null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "polyvLiveList"() {
        given:
        xiaoetongManager.polyvLiveList(*_) >> null
        when:
        liveService.polyvLiveList(null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getMuduEventList"() {
        given:
        muduManager.getAccessToken(*_) >> "token"
        muduManager.getEventList(*_) >> result
        when:
        liveService.getMuduEventList(new ListMuduEventArg())
        then:
        noExceptionThrown()
        where:
        result << [null, new MuduApiGetEventListResult(total: 1, items: [])]
    }

    @Unroll
    def "getMuduLive"() {
        given:
        muduManager.getAccessToken(*_) >> "token"
        muduManager.getEventDetail(*_) >> result
        when:
        liveService.getMuduLive(new GetMuduLiveArg())
        then:
        noExceptionThrown()
        where:
        result << [null, new MuduApiGetEventListResult.EventInfo()]
    }

    @Unroll
    def "queryVHallLive"() {
        given:
        vHallManager.queryLive(*_) >> vHallApiQueryLiveResult
        when:
        liveService.queryVHallLive(new QueryThirdLiveArg(pageNum: 1, pageSize: 1))
        then:
        noExceptionThrown()
        where:
        vHallApiQueryLiveResult << [null, new VHallApiQueryLiveResult(list: [new VHallApiQueryLiveResult.Item()])]
    }

    @Unroll
    def "getVHallLive"() {
        given:
        vHallManager.getLive(*_) >> apiGetLiveResult
        when:
        liveService.getVHallLive(new GetThirdLiveArg())
        then:
        noExceptionThrown()
        where:
        apiGetLiveResult << [null, new VHallApiGetLiveResult()]
    }

    @Unroll
    def "bindXiaoketongAccount"() {
        given:
        xiaoetongManager.bindXiaoketongAccount(*_) >> null
        when:
        liveService.bindXiaoketongAccount(new BindXiaoetongAccountVO())
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getXiaoetongAccount"() {
        given:
        xiaoetongManager.getXiaoetongAccount(*_) >> null
        when:
        liveService.getXiaoetongAccount("ea")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "bindPolyvAccount"() {
        given:
        polyvManager.bindPolyvAccount(*_) >> null
        when:
        liveService.bindPolyvAccount("ea", "", "", "")
        then:
        noExceptionThrown()
    }


    @Unroll
    def "bindAccount"() {
        given:
        polyvManager.bindAccount(*_) >> null
        when:
        liveService.bindAccount("ea")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "bindChannelsAccount"() {
        given:
        polyvManager.bindAccount(*_) >> null
        when:
        liveService.bindChannelsAccount(new BindChannelsAccountVo())
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getChannelsAccount"() {
        given:
        channelsManager.getAccount(*_) >> null
        when:
        liveService.getChannelsAccount("ea", "id")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "bindMuduAccount"() {
        given:
        muduManager.bindMuduAccount(*_) >> null
        when:
        liveService.bindMuduAccount(new BindMuduAccountVO())
        then:
        noExceptionThrown()
    }


    @Unroll
    def "getMuduAccount"() {
        given:
        muduManager.getMuduAccount(*_) >> null
        when:
        liveService.getMuduAccount("ea")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "bindThirdAccount"() {
        given:
        thirdLiveAccountDAO.getByAppId(*_) >> thirdLiveAccountEntity
        thirdLiveAccountDAO.getByEa(*_) >> entity
        thirdLiveAccountDAO.update(*_) >> 1
        thirdLiveAccountDAO.insert(*_) >> 1
        when:
        liveService.bindThirdAccount(new BindThirdAccountArg(ea: "ea"))
        then:
        noExceptionThrown()
        where:
        thirdLiveAccountEntity               | entity
        new ThirdLiveAccountEntity(ea: "ea") | null
        new ThirdLiveAccountEntity(ea: "ea") | new ThirdLiveAccountEntity()
    }

    @Unroll
    def "getThirdAccount"() {
        given:
        thirdLiveAccountDAO.getByEa(*_) >> new ThirdLiveAccountEntity()
        when:
        liveService.getThirdAccount(new GetThirdAccountArg(ea: "ea"))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getAccountByMaterials"() {
        given:
        channelsManager.getAccount(*_) >> null
        objectManager.getObjectEa(*_) >> "ea"
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> new MarketingLiveEntity(associatedAccountId: "id")
        channelsManager.getAccountsByEa(*_) >> accounts
        when:
        liveService.getAccountByMaterials(vo)
        then:
        noExceptionThrown()
        where:
        vo                                          | accounts
        new LiveMaterialsVO(marketingEventId: "id") | new Result(data: [new ChannelsAccountResult()])
        new LiveMaterialsVO()                       | new Result(data: [new ChannelsAccountResult()])
        new LiveMaterialsVO()                       | null
        new LiveMaterialsVO(id: "id")               | null
    }

    @Unroll
    def "getDetailLive"() {
        given:
        marketingLiveDAO.getById(*_) >> marketingLive
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        fileV2Manager.getUrlByPath(*_) >> "cover"
        contentMarketingEventMaterialRelationDAO.listByMarketingEventIdAndObjectType(*_) >> [new ContentMarketingEventMaterialRelationEntity(objectId: "id")]
        hexagonSiteDAO.listByIds(*_) >> [new HexagonSiteEntity(name: "报名预约"), new HexagonSiteEntity(name: "视频号中转页面")]
        objectEnrollJumpSettingDAO.getByMarketingEventId(*_) >> new ObjectEnrollJumpSettingEntity()
        when:
        liveService.getDetailLive("id")
        then:
        noExceptionThrown()
        where:
        marketingLive << [null, new MarketingLiveEntity(corpId: 1, startTime: new Date(), endTime: new Date())]
    }

    @Unroll
    def "getMarketingLiveByXiaoetongId"() {
        given:
        xiaoetongManager.getMarketingLiveByXiaoetongId(*_) >> optional
        when:
        liveService.getMarketingLiveByXiaoetongId("id")
        then:
        noExceptionThrown()
        where:
        optional << [Optional.ofNullable(), Optional.of(new MarketingLiveEntity())]
    }

    @Unroll
    def "getMarketingLiveByPolyvId"() {
        given:
        polyvManager.getMarketingLiveByPolyvId(*_) >> optional
        when:
        liveService.getMarketingLiveByPolyvId("id")
        then:
        noExceptionThrown()
        where:
        optional << [Optional.ofNullable(), Optional.of(new MarketingLiveEntity())]
    }

    @Unroll
    def "checkRelate"() {
        given:
        marketingLiveDAO.getMarketingLiveByXiaoetongId(*_) >> new MarketingLiveEntity()
        when:
        liveService.checkRelate(new CheckThirdRelateArg(platform: platform))
        then:
        noExceptionThrown()
        where:
        platform << [1, 7]
    }

    @Unroll
    def "checkH5UserHaveSubmit"() {
        given:
        marketingLiveDAO.getById(*_) >> entity
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        memberManager.getH5LoginMemberId(*_) >> optionalMemberId
        shareObjectData.put("_id", "id")
        crmV2Manager.getDetail(*_) >> shareObjectData
        shareObjectData.put("approval_status", "processing")
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(*_) >> new MemberAccessibleCampaignEntity()
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndEventId(*_) >> customizeFormDataUserEntityList
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity(campaignMembersObjId: "id")
        when:
        liveService.checkH5UserHaveSubmit("id", "finger", null, true)
        then:
        noExceptionThrown()
        where:
        entity                             | optionalMemberId      | customizeFormDataUserEntityList
        null                               | Optional.of("id")     | []
        new MarketingLiveEntity(corpId: 1) | Optional.of("id")     | []
        new MarketingLiveEntity(corpId: 1) | Optional.ofNullable() | []
        new MarketingLiveEntity(corpId: 1) | Optional.ofNullable() | [new CustomizeFormDataUserEntity(submitContent: new CustomizeFormDataEnroll())]
    }

    @Unroll
    def "checkWxServiceUserHaveSubmit"() {
        given:
        marketingLiveDAO.getById(*_) >> entity
        eieaConverter.enterpriseIdToAccount(*_) >> "ea"
        memberManager.getWxOpenIdByMemberId(*_) >> optionalMemberId
        shareObjectData.put("_id", "id")
        crmV2Manager.getDetail(*_) >> shareObjectData
        shareObjectData.put("approval_status", "processing")
        memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(*_) >> new MemberAccessibleCampaignEntity()
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndEventId(*_) >> customizeFormDataUserEntityList
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> new CampaignMergeDataEntity(campaignMembersObjId: "id")
        when:
        liveService.checkWxServiceUserHaveSubmit("id", "wxAppId", "openId")
        then:
        noExceptionThrown()
        where:
        entity                             | optionalMemberId      | customizeFormDataUserEntityList
        null                               | Optional.of("id")     | []
        new MarketingLiveEntity(corpId: 1) | Optional.of("id")     | []
        new MarketingLiveEntity(corpId: 1) | Optional.ofNullable() | []
        new MarketingLiveEntity(corpId: 1) | Optional.ofNullable() | [new CustomizeFormDataUserEntity(submitContent: new CustomizeFormDataEnroll())]
    }
}