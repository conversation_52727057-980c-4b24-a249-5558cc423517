package com.facishare.marketing.provider.manager.qywx

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.ListQywxMarketingActivityEmployeeRankingArg
import com.facishare.marketing.api.arg.ListSopQywxMsgEmployeeRankingArg
import com.facishare.marketing.api.arg.MomentCustomerArg
import com.facishare.marketing.api.arg.OfficeMessageArg
import com.facishare.marketing.api.result.qywx.ListEmployeeQywxGroupSendDetailResult
import com.facishare.marketing.api.result.qywx.ListQywxMarketingActivityEmployeeRankingResult
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult
import com.facishare.marketing.api.result.qywx.customerGroup.QueryCustomerGroupListResult
import com.facishare.marketing.api.service.StatisticService
import com.facishare.marketing.api.vo.qywx.ListGroupSendMessageVO
import com.facishare.marketing.api.vo.qywx.QywxAttachmentsVO
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.MarketingNoticeSettingDAO
import com.facishare.marketing.provider.dao.QywxAttachmentsRelationDAO
import com.facishare.marketing.provider.dao.TriggerSnapshotDao
import com.facishare.marketing.provider.dao.TriggerTaskInstanceDao
import com.facishare.marketing.provider.dao.UserMarketingWxWorkExternalUserRelationDao
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.qywx.*
import com.facishare.marketing.provider.dto.ListEmployeeQywxGroupSendDetailDto
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import com.facishare.marketing.provider.dto.qywx.QueryUserGroupByMsgIdsDTO
import com.facishare.marketing.provider.dto.qywx.SendGroupMsgByStatusDTO
import com.facishare.marketing.provider.dto.qywx.SendGroupResultDTO
import com.facishare.marketing.provider.dto.qywx.SopQywxTaskResultDTO
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.ExternalConfig
import com.facishare.marketing.provider.entity.MarketingNoticeSettingEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity
import com.facishare.marketing.provider.entity.QywxAttachmentsRelationEntity
import com.facishare.marketing.provider.entity.TriggerSnapshotEntity
import com.facishare.marketing.provider.entity.data.QywxGroupSendMessageData
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.entity.qywx.*
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity
import com.facishare.marketing.provider.innerArg.qywx.AddMsgTemplateArg
import com.facishare.marketing.provider.innerArg.qywx.GetGroupMsgResultArg
import com.facishare.marketing.provider.innerArg.qywx.SendWelcomeMessageNewArg
import com.facishare.marketing.provider.innerResult.qywx.*
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import com.facishare.marketing.provider.remote.MarketingCrmManager
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager
import com.fxiaoke.common.http.spring.OkHttpSupport
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.google.gson.reflect.TypeToken
import spock.lang.Specification
import spock.lang.Unroll

class GroupSendMessageManagerSpec extends Specification {


    def groupSendMessageManager = new GroupSendMessageManager()

    def qywxManager = Mock(QywxManager)
    def httpManager = Mock(HttpManager)
    def redisManager = Mock(RedisManager)
    def qywxGroupSendTaskDAO = Mock(QywxGroupSendTaskDAO)
    def sendTaskDAO = Mock(QywxGroupSendTaskDAO)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qyweixinAccountBindManager = Mock(QyweixinAccountBindManager)
    def fileV2Manager = Mock(FileV2Manager)
    def httpSupport = Mock(OkHttpSupport)
    def miniappConfigDAO = Mock(QywxMiniappConfigDAO)
    def qywxMiniappConfigDAO = Mock(QywxMiniappConfigDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def sendResultDAO = Mock(QywxGroupSendResultDAO)
    def qywxGroupSendGroupResultDAO = Mock(QywxGroupSendGroupResultDAO)
    def photoManager = Mock(PhotoManager)
    def marketingUserGroupManager = Mock(MarketingUserGroupManager)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def customerGroupManager = Mock(CustomerGroupManager)
    def marketingCrmManager = Mock(MarketingCrmManager)
    def customizeFormClueManager = Mock(CustomizeFormClueManager)
    def objectManager = Mock(ObjectManager)
    def qywxUserManager = Mock(QywxUserManager)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def userMarketingWxWorkExternalUserRelationDao = Mock(UserMarketingWxWorkExternalUserRelationDao)
    def wechatAccountManager = Mock(WechatAccountManager)
    def marketingActivityManager = Mock(MarketingActivityManager)
    def qywxVirtualFsUserManager = Mock(QywxVirtualFsUserManager)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def enterpriseSpreadRecordManager = Mock(EnterpriseSpreadRecordManager)
    def marketingEventManager = Mock(MarketingEventManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def eieaConverter = Mock(EIEAConverter)
    def triggerTaskInstanceDao = Mock(TriggerTaskInstanceDao)
    def triggerSnapshotDao = Mock(TriggerSnapshotDao)
    def qyWxAddressBookDAO = Mock(QyWxAddressBookDAO)
    def qywxAddressBookManager = Mock(QywxAddressBookManager)
    def qywxEmployeeManager = Mock(QywxEmployeeManager)
    def qywxCustomerAppInfoDAO = Mock(QywxCustomerAppInfoDAO)
    def qywxMomentTaskDAO = Mock(QYWXMomentTaskDAO)
    def qywxMomentSendResultDao = Mock(QYWXMomentSendResultDaO)
    def wechatGroupObjDescribeManager = Mock(WechatGroupObjDescribeManager)
    def marketingNoticeSettingDAO = Mock(MarketingNoticeSettingDAO)
    def executeTaskDetailManager = Mock(ExecuteTaskDetailManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def virtualUserManager = Mock(VirtualUserManager)
    def marketingRecordActionSender = Mock(MarketingRecordActionSender)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def metadataTagManager = Mock(MetadataTagManager)
    def marketingActivityAuditManager = Mock(MarketingActivityAuditManager)
    def dataPermissionManager = Mock(DataPermissionManager)
    def enterpriseEditionManager = Mock(EnterpriseEditionManager)
    def qywxAttachmentsRelationDAO = Mock(QywxAttachmentsRelationDAO)
    def fsBindManager = Mock(FsBindManager)
    def outLinkMktParamManager = Mock(OutLinkMktParamManager)

    def setup() {
        groupSendMessageManager.qywxManager = qywxManager
        groupSendMessageManager.httpManager = httpManager
        groupSendMessageManager.redisManager = redisManager
        groupSendMessageManager.sendTaskDAO = sendTaskDAO
        groupSendMessageManager.qywxGroupSendTaskDAO = qywxGroupSendTaskDAO
        groupSendMessageManager.qywxMiniappConfigDAO = qywxMiniappConfigDAO
        groupSendMessageManager.agentConfigDAO = agentConfigDAO
        groupSendMessageManager.qyweixinAccountBindManager = qyweixinAccountBindManager
        groupSendMessageManager.fileV2Manager = fileV2Manager
        groupSendMessageManager.httpSupport = httpSupport
        groupSendMessageManager.miniappConfigDAO = miniappConfigDAO
        groupSendMessageManager.crmV2Manager = crmV2Manager
        groupSendMessageManager.sendResultDAO = sendResultDAO
        groupSendMessageManager.qywxGroupSendGroupResultDAO = qywxGroupSendGroupResultDAO
        groupSendMessageManager.photoManager = photoManager
        groupSendMessageManager.marketingUserGroupManager = marketingUserGroupManager
        groupSendMessageManager.wechatWorkExternalUserObjManager = wechatWorkExternalUserObjManager
        groupSendMessageManager.customerGroupManager = customerGroupManager
        groupSendMessageManager.marketingCrmManager = marketingCrmManager
        groupSendMessageManager.customizeFormClueManager = customizeFormClueManager
        groupSendMessageManager.objectManager = objectManager
        groupSendMessageManager.qywxUserManager = qywxUserManager
        groupSendMessageManager.userMarketingAccountManager = userMarketingAccountManager
        groupSendMessageManager.hexagonSiteDAO = hexagonSiteDAO
        groupSendMessageManager.userMarketingWxWorkExternalUserRelationDao = userMarketingWxWorkExternalUserRelationDao
        groupSendMessageManager.wechatAccountManager = wechatAccountManager
        groupSendMessageManager.marketingActivityManager = marketingActivityManager
        groupSendMessageManager.qywxVirtualFsUserManager = qywxVirtualFsUserManager
        groupSendMessageManager.fsAddressBookManager = fsAddressBookManager
        groupSendMessageManager.enterpriseSpreadRecordManager = enterpriseSpreadRecordManager
        groupSendMessageManager.marketingEventManager = marketingEventManager
        groupSendMessageManager.conferenceDAO = conferenceDAO
        groupSendMessageManager.marketingActivityRemoteManager = marketingActivityRemoteManager
        groupSendMessageManager.appVersionManager = appVersionManager
        groupSendMessageManager.eieaConverter = eieaConverter
        groupSendMessageManager.triggerTaskInstanceDao = triggerTaskInstanceDao
        groupSendMessageManager.triggerSnapshotDao = triggerSnapshotDao
        groupSendMessageManager.qyWxAddressBookDAO = qyWxAddressBookDAO
        groupSendMessageManager.qywxAddressBookManager = qywxAddressBookManager
        groupSendMessageManager.qywxEmployeeManager = qywxEmployeeManager
        groupSendMessageManager.qywxCustomerAppInfoDAO = qywxCustomerAppInfoDAO
        groupSendMessageManager.qywxMomentTaskDAO = qywxMomentTaskDAO
        groupSendMessageManager.qywxMomentSendResultDao = qywxMomentSendResultDao
        groupSendMessageManager.wechatGroupObjDescribeManager = wechatGroupObjDescribeManager
        groupSendMessageManager.marketingNoticeSettingDAO = marketingNoticeSettingDAO
        groupSendMessageManager.executeTaskDetailManager = executeTaskDetailManager
        groupSendMessageManager.crmMetadataManager = crmMetadataManager
        groupSendMessageManager.virtualUserManager = virtualUserManager
        groupSendMessageManager.marketingRecordActionSender = marketingRecordActionSender
        groupSendMessageManager.marketingActivityExternalConfigDao = marketingActivityExternalConfigDao
        groupSendMessageManager.metadataTagManager = metadataTagManager
        groupSendMessageManager.marketingActivityAuditManager = marketingActivityAuditManager
        groupSendMessageManager.dataPermissionManager = dataPermissionManager
        groupSendMessageManager.enterpriseEditionManager = enterpriseEditionManager
        groupSendMessageManager.qywxAttachmentsRelationDAO = qywxAttachmentsRelationDAO
        groupSendMessageManager.fsBindManager = fsBindManager
        groupSendMessageManager.outLinkMktParamManager = outLinkMktParamManager
    }

    @Unroll
    def "init"() {
        given:

        when:
        groupSendMessageManager.init()

        then:
        noExceptionThrown()
    }

    @Unroll
    def "addMsgTemplate"() {
        given:

        when:
        groupSendMessageManager.addMsgTemplate(accessToken, new AddMsgTemplateArg())

        then:
        noExceptionThrown()

        where:
        accessToken << [null, "1"]
    }

    @Unroll
    def "getGroupMsgResult"() {
        given:

        when:
        groupSendMessageManager.getGroupMsgResult(accessToken, new GetGroupMsgResultArg())

        then:
        noExceptionThrown()

        where:
        accessToken << [null, "1"]
    }

    @Unroll
    def "createGroupSendMsgTask"() {
        given:
        marketingActivityAuditManager.isNeedAudit(*_) >> true
        objectManager.convertNoticeContentTypeToObjectType(*_) >> objectType
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO()]
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]

        when:
        groupSendMessageManager.createGroupSendMsgTask(vo, "1", "1")

        then:
        noExceptionThrown()

        where:
        vo                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | objectType
        new QywxGroupSendMessageVO(sendRange: 4, type: 1, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 3, image: new QywxGroupSendMessageVO.Image())                                                                                                                                                                                                                                                                                                                                                                                   | 1
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 3, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()])                                                                                                                                                                                                                                                                                                     | 1
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 1, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link())                                                                                                                                                                                                                                                            | 1
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 2))                                                                                                                                                                                   | 1
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                                                                                                                                   | 13
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                                                                                                                                   | 6
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                                                                                                                                   | 4
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 26
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 13
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 6
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 4
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 1, link: new QywxAttachmentsVO.Link(url: "1", objectType: 1, materialId: "1"), miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1)) | 4
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 3, video: new QywxAttachmentsVO.Video(videoUrl: "1"), miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                          | 4
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 5, file: new QywxAttachmentsVO.File(fileUrl: "1"), miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                             | 4

    }

    @Unroll
    def "updateGroupSendMsgTask"() {
        given:
        marketingActivityAuditManager.isNeedAudit(*_) >> true
        objectManager.convertNoticeContentTypeToObjectType(*_) >> objectType
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO()]
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]
        sendTaskDAO.getByMarketingActivityId(*_) >> new QywxGroupSendTaskEntity()
        qywxAttachmentsRelationDAO.getDetailByTargetId(*_) >> attachmentsRelationEntities

        when:
        groupSendMessageManager.updateGroupSendMsgTask(vo, "1", "1")

        then:
        noExceptionThrown()

        where:
        vo                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | objectType | attachmentsRelationEntities
        new QywxGroupSendMessageVO(sendRange: 4, type: 1, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 3, image: new QywxGroupSendMessageVO.Image())                                                                                                                                                                                                                                                                                                                                                                                   | 1          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 3, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()])                                                                                                                                                                                                                                                                                                     | 1          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 1, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link())                                                                                                                                                                                                                                                            | 1          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 2))                                                                                                                                                                                   | 1          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                                                                                                                                   | 13         | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                                                                                                                                   | 6          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO()], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                                                                                                                                   | 4          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 26         | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 13         | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 6          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 4, miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                                                                             | 4          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 1, link: new QywxAttachmentsVO.Link(url: "1", objectType: 1, materialId: "1"), miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1)) | 4          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 3, video: new QywxAttachmentsVO.Video(videoUrl: "1"), miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                          | 4          | null
        new QywxGroupSendMessageVO(sendRange: 5, type: 2, text: new QywxGroupSendMessageVO.Text(), userIds: ["1"], departmentIds: [1], tagIds: [1], marketingUserGroupIds: ["1"], filters: [[1: "1"]], tagIdList: [new TagName()], qywxGroupList: ["1"], chatGroupFilters: [[1: "1"]], filterQywxGroupUser: 1, msgType: 4, image: new QywxGroupSendMessageVO.Image(materialId: "1", materialType: 1), qywxAttachmentsVO: [new QywxAttachmentsVO(attachmentType: 5, file: new QywxAttachmentsVO.File(fileUrl: "1"), miniprogram: new QywxAttachmentsVO.Miniprogram(miniProgramType: 1, materialId: "1"))], link: new QywxGroupSendMessageVO.Link(), miniprogram: new QywxGroupSendMessageVO.Miniprogram(miniProgramType: 1))                             | 4          | new QywxAttachmentsRelationEntity()

    }

    @Unroll
    def "groupSendMsgSchedule"() {
        given:
        sendTaskDAO.getNeedSendTaskByEa(*_) >> needSendTaskList
        marketingActivityRemoteManager.enterpriseStop(*_) >> enterpriseStop
        appVersionManager.getCurrentAppVersion(*_) >> "1"

        when:
        groupSendMessageManager.groupSendMsgSchedule("1")

        then:
        noExceptionThrown()

        where:
        needSendTaskList                | enterpriseStop
        null                            | false
        [new QywxGroupSendTaskEntity()] | true
        [new QywxGroupSendTaskEntity()] | false

    }

    @Unroll
    def "groupSendMsgById"() {
        given:
        if (exceptionFlag) {
            sendTaskDAO.queryById(*_) >> { throw new RuntimeException("e") }
        } else {
            sendTaskDAO.queryById(*_) >> groupSendTaskEntity
        }

        when:
        groupSendMessageManager.groupSendMsgById("1")

        then:
        noExceptionThrown()

        where:
        groupSendTaskEntity           | exceptionFlag
        null                          | false
        new QywxGroupSendTaskEntity() | false
        null                          | true

    }

    @Unroll
    def "handlerSendTask"() {
        given:
        marketingActivityAuditManager.checkAudtStatus(*_) >> isNormall
        agentConfigDAO.queryAgentByEa(*_) >> agentConfig
        sendTaskDAO.updateTaskStatusToRunningById(*_) >> updateResult
        qywxAttachmentsRelationDAO.getDetailByTargetId(*_) >> attachmentsRelation
        dataPermissionManager.getNewDataPermissionSetting(*_) >> isOpen
        wechatGroupObjDescribeManager.filterGroupId(*_) >> ["1"]
        qywxManager.getAccessToken(*_) >> "1"
        httpManager.executePostHttp(_, _, new TypeToken<AddMsgTemplateResult>() {
        }) >> addMsgResult
        qywxManager.handleQywxEmployeeUserId(*_) >> ["1"]
        qywxManager.builtNewAttachmentsArg(*_) >> attachmentList
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> objectDataInnerPage
        qywxManager.handleQywxOwnerUserId(*_) >> groupMsgSenderIds
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 1
        customerGroupManager.queryCustomerListNew(*_) >> new CustomerGroupListResult(errcode: 0, groupList: [new CustomerGroupListResult.GroupItem()])
        qywxGroupSendGroupResultDAO.batchInsertFlatResult(*_) >> { throw new RuntimeException("e") }

        when:
        groupSendMessageManager.handlerSendTask(groupSendTask)

        then:
        noExceptionThrown()

        where:
        groupSendTask                                                                                                                                                   | groupMsgSenderIds | isNormall | agentConfig                     | updateResult | attachmentsRelation                 | addMsgResult                         | attachmentList                              | isOpen | objectDataInnerPage
        null                                                                                                                                                            | []                | true      | null                            | false        | null                                | null                                 | []                                          | false  | null
        new QywxGroupSendTaskEntity()                                                                                                                                   | []                | true      | null                            | false        | null                                | null                                 | []                                          | false  | null
        new QywxGroupSendTaskEntity()                                                                                                                                   | []                | false     | null                            | false        | null                                | null                                 | []                                          | false  | null
        new QywxGroupSendTaskEntity()                                                                                                                                   | []                | true      | new QywxCorpAgentConfigEntity() | false        | null                                | null                                 | []                                          | false  | null
        new QywxGroupSendTaskEntity()                                                                                                                                   | []                | true      | new QywxCorpAgentConfigEntity() | true         | null                                | null                                 | []                                          | false  | null
        new QywxGroupSendTaskEntity(content: "1")                                                                                                                       | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | null                                 | []                                          | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"-999999\"]", qywxGroupList: "[\"1\"]")                                   | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | []                                          | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"-999999\"]", qywxGroupList: "[\"1\"]")                                   | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 0) | []                                          | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 0)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 0) | []                                          | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 0)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | []                                          | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 0)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | [new SendWelcomeMessageNewArg.Attachment()] | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | [new SendWelcomeMessageNewArg.Attachment()] | false  | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | [new SendWelcomeMessageNewArg.Attachment()] | true   | null
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | [new SendWelcomeMessageNewArg.Attachment()] | true   | new InnerPage(dataList: [new ObjectData("_id": 1, "qywx_user_id": "1", "external_user_id": "1"), new ObjectData("_id": 2, "qywx_user_id": "1", "external_user_id": "1")])
        new QywxGroupSendTaskEntity(content: "1", chatType: 1, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1)                           | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 0) | [new SendWelcomeMessageNewArg.Attachment()] | true   | new InnerPage(dataList: [new ObjectData("_id": 1, "qywx_user_id": "1", "external_user_id": "1"), new ObjectData("_id": 2, "qywx_user_id": "1", "external_user_id": "1")])
        new QywxGroupSendTaskEntity(content: "1", chatType: 2, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1, chatGroupFilters: "[{}]") | []                | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 0) | [new SendWelcomeMessageNewArg.Attachment()] | true   | new InnerPage(dataList: [new ObjectData("_id": 1, "qywx_user_id": "1", "external_user_id": "1"), new ObjectData("_id": 2, "qywx_user_id": "1", "external_user_id": "1")])
        new QywxGroupSendTaskEntity(content: "1", chatType: 2, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1, chatGroupFilters: "[{}]") | ["1"]             | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 1) | [new SendWelcomeMessageNewArg.Attachment()] | true   | new InnerPage(dataList: [new ObjectData("_id": 1, "qywx_user_id": "1", "external_user_id": "1"), new ObjectData("_id": 2, "qywx_user_id": "1", "external_user_id": "1")])
        new QywxGroupSendTaskEntity(content: "1", chatType: 2, allowSelect: false, userId: "[\"1\"]", qywxGroupList: "[\"1\"]", sendRange: 1, chatGroupFilters: "[{}]") | ["1"]             | true      | new QywxCorpAgentConfigEntity() | true         | new QywxAttachmentsRelationEntity() | new AddMsgTemplateResult(errcode: 0) | [new SendWelcomeMessageNewArg.Attachment()] | true   | new InnerPage(dataList: [new ObjectData("_id": 1, "qywx_user_id": "1", "external_user_id": "1"), new ObjectData("_id": 2, "qywx_user_id": "1", "external_user_id": "1")])

    }


    @Unroll
    def "builtOldAttachmentsArg"() {
        given:
        httpManager.uploadFile(*_) >> new UploadMediaResult(errcode: 0)

        when:
        groupSendMessageManager.builtOldAttachmentsArg(groupSendTask, new AddMsgTemplateArg(), "1")

        then:
        noExceptionThrown()

        where:
        groupSendTask << [new QywxGroupSendTaskEntity(content: "1"),
                          new QywxGroupSendTaskEntity(content: "1", msgType: 3, imagePath: "A_"),
                          new QywxGroupSendTaskEntity(content: "1", msgType: 3, imagePath: "C_"),
                          new QywxGroupSendTaskEntity(content: "1", msgType: 1, imagePath: "C_", linkDesc: "1"),
                          new QywxGroupSendTaskEntity(content: "1", msgType: 4, imagePath: "C_", linkDesc: "1", miniTitle: "1"),
                          new QywxGroupSendTaskEntity(content: "1", msgType: 4, imagePath: "C_", linkDesc: "1", miniTitle: "1", appId: "1"),
        ]
    }

    @Unroll
    def "getListTotalCount"() {
        given:
        crmMetadataManager.listV3(*_) >> result

        when:
        groupSendMessageManager.getListTotalCount("1", 1, new PaasQueryFilterArg(query: new PaasQueryArg(1, 1)))

        then:
        noExceptionThrown()

        where:
        result << [null, new InnerPage(totalCount: 1)]
    }

    @Unroll
    def "batchGetList"() {
        given:
        if (exceptionFlag) {
            crmMetadataManager.listV3(*_) >> { throw new RuntimeException("1") }
        } else {
            crmMetadataManager.listV3(*_) >> result
        }

        when:
        groupSendMessageManager.batchGetList("1", 1, 1, 2, new PaasQueryFilterArg(query: new PaasQueryArg(1, 1)))

        then:
        noExceptionThrown()

        where:
        result                         | exceptionFlag
        null                           | false
        new InnerPage(dataList: ["1"]) | false
        new InnerPage(dataList: ["1"]) | true
    }

    @Unroll
    def "getEmployeeExternalUserIds"() {
        given:

        when:
        groupSendMessageManager.getEmployeeExternalUserIds("1", ["1"])

        then:
        noExceptionThrown()
    }

    @Unroll
    def "getEmployeeExternalUserIdsV2"() {
        given:
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> new InnerPage(dataList: [new ObjectData("qywx_user_id": "1", "external_user_id": "1"), new ObjectData("qywx_user_id": "1", "external_user_id": "1")])
        crmV2Manager.listCrmObjectByFilterV3(*_) >> new InnerPage(dataList: [new ObjectData("_id": 1, "external_user_id": "1")])

        when:
        groupSendMessageManager.getEmployeeExternalUserIdsV2("1", ["1"])

        then:
        noExceptionThrown()

    }

    @Unroll
    def "getExternalUserIds"() {
        given:
        if (exceptionFlag) {
            marketingUserGroupManager.listWxWorkExternalUserIdsByMarketingUserGroupIds(*_) >> { throw new RuntimeException("1") }
        } else {
            marketingUserGroupManager.listWxWorkExternalUserIdsByMarketingUserGroupIds(*_) >> ["1"]
        }

        when:
        groupSendMessageManager.getExternalUserIds(new QywxGroupSendTaskEntity(sendRange: 2), [], [new TagName()], ["1"])

        then:
        noExceptionThrown()

        where:
        exceptionFlag << [true, false]

    }

    @Unroll
    def "getPaasQueryFilterArg"() {
        given:
        metadataTagManager.getTagIdsByTagNames(*_) >> new HashMap<TagName, String>(tag: "1")

        when:
        groupSendMessageManager.getPaasQueryFilterArg("1", sendRange, filters, [new TagName()])

        then:
        noExceptionThrown()

        where:
        filters                                                                                                                        | sendRange
        [new HashMap<String, Object>("fieldValues": ["2.5E8"], "valueType": new BigDecimal(1), "isCascade": true, "fieldName": "tag")] | 1
        [new HashMap<String, Object>("fieldValues": "2.5E8", "valueType": new BigDecimal(1), "isCascade": true, "fieldName": "tag")]   | 1
        [new HashMap<String, Object>("fieldValues": "2.5E8", "valueType": new BigDecimal(1), "isCascade": true, "fieldName": "tag")]   | 3
    }

    @Unroll
    def "deleteMoreResultRecord"() {
        given:
        sendResultDAO.getTaskMsgids(*_) >> msgids
        sendResultDAO.queryListByMsgid(*_) >> qywxGroupSendResultEntityList

        when:
        groupSendMessageManager.deleteMoreResultRecord()

        then:
        noExceptionThrown()

        where:
        msgids | qywxGroupSendResultEntityList
        null   | null
        ["1"]  | null
        ["1"]  | [new QywxGroupSendResultEntity(externalUserid: "1"), new QywxGroupSendResultEntity(externalUserid: "1")]
    }

    @Unroll
    def "getGroupMsgResultSchedule"() {
        given:
        executeTaskDetailManager.checkTaskAndAddIfNotExist(*_) >> taskAndAddIfNotExist
        sendTaskDAO.getNeedSendSingleResultTask() >> singleResultEntities
        //appVersionManager.getCurrentAppVersion(*_) >> currentAppVersion
        appVersionManager.getCurrentAppVersion(*_) >> "1"
        if (exceptionFlag) {
            marketingActivityRemoteManager.enterpriseStop(*_) >> { throw new RuntimeException("1") }
        } else {
            marketingActivityRemoteManager.enterpriseStop(*_) >> false
        }
        qywxGroupSendGroupResultDAO.getNeedSendGroupResultTask() >> [new SendGroupResultDTO()]


        when:
        groupSendMessageManager.getGroupMsgResultSchedule(scanAllTask)

        then:
        noExceptionThrown()

        where:
        taskAndAddIfNotExist | scanAllTask | singleResultEntities            | currentAppVersion | exceptionFlag
        true                 | false       | null                            | null              | false
        false                | true        | null                            | null              | false
        false                | false       | [new QywxGroupSendTaskEntity()] | null              | false
        false                | true        | [new QywxGroupSendTaskEntity()] | null              | false
        false                | true        | [new QywxGroupSendTaskEntity()] | "1"               | false
        false                | true        | [new QywxGroupSendTaskEntity()] | "1"               | true
    }

    @Unroll
    def "hanlerSendGroupTaskResultByPager"() {
        given:
        qywxGroupSendTaskDAO.queryById(*_) >> new QywxGroupSendTaskEntity()
        qywxGroupSendGroupResultDAO.batchInsertFlatResult(*_) >> { throw new RuntimeException("1") }

        when:
        groupSendMessageManager.hanlerSendGroupTaskResultByPager("1", "1", currentPage)

        then:
        noExceptionThrown()

        where:
        currentPage << [
                null,
                [new GroupMsgResult.Detail(status: 1)],
                [new GroupMsgResult.Detail(status: 0)],
                [new GroupMsgResult.Detail(status: -1)],
        ]
    }

    @Unroll
    def "handlerSendGroupTaskResult"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >>> [agentConfig, null]
        qywxManager.getAccessToken(*_) >> "1"
        httpManager.executePostHttp(_, _, new TypeToken<GroupMsgResult>() {
        }) >> result

        when:
        groupSendMessageManager.handlerSendGroupTaskResult(new SendGroupResultDTO(), "1")

        then:
        noExceptionThrown()

        where:
        agentConfig                     | result
        null                            | null
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 1)
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, detailList: [new GroupMsgResult.Detail()])
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0)
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, nextCursor: "1")
    }

    @Unroll
    def "handSopQywxGroupMsgTaskResult"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >>> [agentConfig, null]
        qywxManager.getAccessToken(*_) >> "1"
        httpManager.executePostHttp(_, _, new TypeToken<GroupMsgResult>() {
        }) >> result

        when:
        groupSendMessageManager.handSopQywxGroupMsgTaskResult("1", "1", "1")

        then:
        noExceptionThrown()

        where:
        agentConfig                     | result
        null                            | null
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 1)
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, detailList: [new GroupMsgResult.Detail()])
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0)
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, nextCursor: "1")
    }

    @Unroll
    def "handlerSopQywxGroupMsgTaskResultByPager"() {
        given:
        qywxGroupSendTaskDAO.queryById(*_) >> new QywxGroupSendTaskEntity()

        when:
        groupSendMessageManager.handlerSopQywxGroupMsgTaskResultByPager("1", currentPage)

        then:
        noExceptionThrown()

        where:
        currentPage << [
                null,
                [new GroupMsgResult.Detail(status: 1)],
                [new GroupMsgResult.Detail(status: 0)],
                [new GroupMsgResult.Detail(status: -1)],
        ]
    }

    @Unroll
    def "handlerSopQywxMsgTaskResult"() {
        given:
        if (exceptionFlag) {
            agentConfigDAO.queryAgentByEa(*_) >> { throw new RuntimeException("1") }
        } else {
            agentConfigDAO.queryAgentByEa(*_) >>> [agentConfig, null]
        }
        qywxManager.getAccessToken(*_) >> "1"
        httpManager.executePostHttp(_, _, new TypeToken<GroupMsgResult>() {
        }) >> result
        sendResultDAO.queryByMsgidAndexternalUserid(*_) >> qywxGroupSendResultEntity
        qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyId(*_) >> new QywxVirtualFsUserEntity(userId: 1)

        when:
        groupSendMessageManager.handlerSopQywxMsgTaskResult("1", "1")

        then:
        noExceptionThrown()

        where:
        agentConfig                     | result                                                                             | qywxGroupSendResultEntity                | exceptionFlag
        null                            | null                                                                               | null                                     | false
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 1)                                                     | null                                     | false
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0)                                                     | null                                     | false
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, nextCursor: "1")                                    | null                                     | false
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, detailList: [new GroupMsgResult.Detail(status: 1)]) | null                                     | false
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, detailList: [new GroupMsgResult.Detail(status: 1)]) | new QywxGroupSendResultEntity(status: 0) | false
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, detailList: [new GroupMsgResult.Detail(status: 1)]) | new QywxGroupSendResultEntity(status: 0) | true
    }

    @Unroll
    def "handleQywxSingleTaskResultByMsgId"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >>> [agentConfig, null]
        qywxManager.getAccessToken(*_) >> "1"
        httpManager.executePostHttp(_, _, new TypeToken<GroupMsgResult>() {
        }) >>> [result, null]

        when:
        groupSendMessageManager.handleQywxSingleTaskResultByMsgId("1", "1", "1", "1")

        then:
        noExceptionThrown()

        where:
        agentConfig                     | result
        null                            | null
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 1)
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, detailList: [new GroupMsgResult.Detail(status: 1)])
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0)
        new QywxCorpAgentConfigEntity() | new GroupMsgResult(errcode: 0, nextCursor: "1")
    }

    @Unroll
    def "handleQywxSingleTaskResultByPage"() {
        given:
        sendResultDAO.queryByMsgidAndexternalUseridList(*_) >> [new QywxGroupSendResultEntity(externalUserid: 1, status: 0)]

        when:
        groupSendMessageManager.handleQywxSingleTaskResultByPage("1", "1", "1", currentPage)

        then:
        noExceptionThrown()

        where:
        currentPage << [[new GroupMsgResult.Detail(externalUserId: 1, status: 1)]]

    }

    @Unroll
    def "sendUserMarketingMsg"() {
        given:
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> externalConfig
        qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyId(*_) >> new QywxVirtualFsUserEntity(userId: 1)

        when:
        groupSendMessageManager.sendUserMarketingMsg("1", "1", "1", "1")

        then:
        noExceptionThrown()

        where:
        externalConfig << [null, new MarketingActivityExternalConfigEntity()]

    }

    @Unroll
    def "handlerSendSingleTaskResult"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >> agentConfig

        when:
        groupSendMessageManager.handlerSendSingleTaskResult(new QywxGroupSendTaskEntity(msgid: "1,2"))

        then:
        noExceptionThrown()

        where:
        agentConfig << [null, new QywxCorpAgentConfigEntity()]

    }

    @Unroll
    def "listQywxMarketingActivityEmployeeRanking"() {
        given:
        sendTaskDAO.getByMarketingActivityId(*_) >> taskEntity
        fsBindManager.isQywxContactFsSetting(*_) >> isQywxContactFsSetting
        qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(*_) >> ["1"]
        sendResultDAO.querySendMsgByStatus(*_) >> groupMsgList
        sendResultDAO.queryDetailByPager(*_) >> [new QywxGroupSendResultEntity(status: 1, sendTime: 1, externalUserid: 1)]
        wechatWorkExternalUserObjManager.getObjectDataMap(*_) >> new HashMap<String, ObjectData>("1": new ObjectData())
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new Result<Map<String, String>>(errCode: 0, data: new HashMap("1": "1.1.1"))
        virtualUserManager.getVirtualUserByEaAndQyIds(*_) >> [new QywxVirtualFsUserEntity(userId: 1)]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>(1: new FsAddressBookManager.FSEmployeeMsg())
        qywxUserManager.getFsUserIdByQyWxInfo(*_) >> new HashMap()
        qywxGroupSendGroupResultDAO.queryGroupResultBySender(*_) >> sendGroupResultEntities
        redisManager.getQYWXCustomerGroup(*_) >> new QueryCustomerGroupListResult()


        when:
        groupSendMessageManager.listQywxMarketingActivityEmployeeRanking("1", new ListQywxMarketingActivityEmployeeRankingArg(departmentIdList: [1], pageNum: 1, pageSize: 10))

        then:
        noExceptionThrown()

        where:
        taskEntity                               | isQywxContactFsSetting | groupMsgList                    | sendGroupResultEntities
        null                                     | false                  | []                              | []
        new QywxGroupSendTaskEntity()            | false                  | []                              | []
        new QywxGroupSendTaskEntity(chatType: 1) | true                   | []                              | []
        new QywxGroupSendTaskEntity(chatType: 1) | true                   | [new SendGroupMsgByStatusDTO()] | []
        new QywxGroupSendTaskEntity(chatType: 2) | true                   | [new SendGroupMsgByStatusDTO()] | []
        new QywxGroupSendTaskEntity(chatType: 2) | true                   | [new SendGroupMsgByStatusDTO()] | [new QywxGroupSendGroupResultEntity(successCount: 2, sendGroupIds: "[\"1\"]", createTime: new Date())]

    }

    @Unroll
    def "fillDepartment"() {
        given:
        fsBindManager.isQywxContactFsSetting(*_) >> isQywxContactFsSetting
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>(1: new FsAddressBookManager.FSEmployeeMsg())
        qywxUserManager.getFsUserIdByQyWxInfo(*_) >> new HashMap("1": 1)
        qywxEmployeeManager.batchByQyUserIds(*_) >> [new QywxEmployeeResult()]

        when:
        groupSendMessageManager.fillDepartment("1", employeeRankingResultList, false)

        then:
        noExceptionThrown()

        where:
        employeeRankingResultList                                                 | isQywxContactFsSetting
        []                                                                        | false
        [new ListQywxMarketingActivityEmployeeRankingResult(employeeUserId: "1")] | true
        [new ListQywxMarketingActivityEmployeeRankingResult()]                    | false

    }

    @Unroll
    def "listEmployeeQywxGroupSendDetail"() {
        given:
        sendTaskDAO.getByMarketingActivityId(*_) >> taskEntity
        fsBindManager.isQywxContactFsSetting(*_) >> isOpen
        qywxUserManager.getQywxUserIdByQywxDepartment(*_) >> ["1"]
        qywxGroupSendGroupResultDAO.listEmployeeQywxGroupSendDetail(*_) >> dtoList
        sendResultDAO.queryQywxGroupSendDetailByEmployee(*_) >> dtoList2

        when:
        groupSendMessageManager.listEmployeeQywxGroupSendDetail("1", new ListQywxMarketingActivityEmployeeRankingArg(departmentIdList: [1], pageNum: 1, pageSize: 10))

        then:
        noExceptionThrown()

        where:
        taskEntity                               | isOpen | dtoList                                                                                   | dtoList2
        null                                     | false  | []                                                                                        | []
        new QywxGroupSendTaskEntity()            | true   | []                                                                                        | []
        new QywxGroupSendTaskEntity(chatType: 1) | false  | []                                                                                        | []
        new QywxGroupSendTaskEntity(chatType: 2) | false  | [new ListEmployeeQywxGroupSendDetailDto(successGroupCount: 1, groupSendTime: new Date())] | []
        new QywxGroupSendTaskEntity(chatType: 2) | false  | [new ListEmployeeQywxGroupSendDetailDto(successGroupCount: 0, groupSendTime: new Date())] | []
        new QywxGroupSendTaskEntity(chatType: 1) | false  | [new ListEmployeeQywxGroupSendDetailDto(successGroupCount: 0, groupSendTime: new Date())] | []
        new QywxGroupSendTaskEntity(chatType: 1) | false  | [new ListEmployeeQywxGroupSendDetailDto(successGroupCount: 0, groupSendTime: new Date())] | [new ListEmployeeQywxGroupSendDetailDto(successGroupCount: 0, groupSendTime: new Date())]

    }

    @Unroll
    def "fillNameAndDepartment"() {
        given:
        fsBindManager.isQywxContactFsSetting(*_) >> isQywxContactFsSetting
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>(1: new FsAddressBookManager.FSEmployeeMsg())
        qywxUserManager.getFsUserIdByQyWxInfo(*_) >> new HashMap("1": 1)
        qywxEmployeeManager.batchByQyUserIds(*_) >> [new QywxEmployeeResult()]

        when:
        groupSendMessageManager.fillNameAndDepartment("1", employeeGroupSendDetailResultList)

        then:
        noExceptionThrown()

        where:
        employeeGroupSendDetailResultList                            | isQywxContactFsSetting
        []                                                           | false
        [new ListEmployeeQywxGroupSendDetailResult(employeeId: "1")] | true
        [new ListEmployeeQywxGroupSendDetailResult()]                | false

    }

    @Unroll
    def "listSopQywxMsgEmployeeRanking"() {
        given:
        triggerSnapshotDao.getCurrentUseSnapshot(*_) >> new TriggerSnapshotEntity(triggerType: "repeat_timing")
        triggerTaskInstanceDao.listQywxMsgCustomer(*_) >> resultDTOS
        wechatWorkExternalUserObjManager.getObjectDataMap(*_) >> new HashMap<String, ObjectData>()
        qywxAddressBookManager.queryEaAndUserId(*_) >> qyWxAddressBookEntities

        when:
        groupSendMessageManager.listSopQywxMsgEmployeeRanking("1", new ListSopQywxMsgEmployeeRankingArg(queryTime: 1L, pageNum: 1, pageSize: 10))

        then:
        noExceptionThrown()

        where:
        resultDTOS                   | qyWxAddressBookEntities
        []                           | []
        [new SopQywxTaskResultDTO()] | []
        [new SopQywxTaskResultDTO()] | [new QyWxAddressBookEntity()]


    }

    @Unroll
    def "queryCachedGroupDetail"() {
        given:
        customerGroupManager.queryQywxGroupDetail(*_) >> groupDetailResult

        when:
        groupSendMessageManager.queryCachedGroupDetail("1", "!")

        then:
        noExceptionThrown()

        where:
        groupDetailResult << [new CustomerGroupDetailResult(qywxGroupChat: new CustomerGroupDetailResult.QywxGroupChat()), null]

    }

    @Unroll
    def "listGroupSendMessage"() {
        given:
        marketingCrmManager.listMarketingActivity(*_) >> pageMarketingActivityList
        marketingEventManager.listMarketingEventData(*_) >> [new com.facishare.marketing.api.data.MarketingEventData()]
        conferenceDAO.getActivityByEaAndMarketingEventIds(*_) >> [new ActivityEntity(marketingEventId: "1", id: "1")]
        sendTaskDAO.listGroupMessageByMarketingActivityIds(*_) >> sendTaskEntityList
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(externalConfig: new ExternalConfig(qywxGroupSendMessageVO: new QywxGroupSendMessageData(qywxGroupSendMessageType: 1)))
        customizeFormClueManager.batchCountClueNumByMarketingActivityIds(*_) >> new HashMap<String, Integer>("1": 1)
        fileV2Manager.batchGetUrlByPath(*_) >> new HashMap<String, String>("A_": "1")
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>(1: new FsAddressBookManager.FSEmployeeMsg())
        sendResultDAO.queryUserGroupByMsgIds(*_) >> userGroupByMsgIds
        qywxGroupSendGroupResultDAO.queryGroupResult(*_) >> [new QywxGroupSendGroupResultEntity()]

        when:
        groupSendMessageManager.listGroupSendMessage("1", 1, new ListGroupSendMessageVO(title: "1", pageNum: 1, pageSize: 10))

        then:
        noExceptionThrown()

        where:
        pageMarketingActivityList                                                 | sendTaskEntityList                                                                                                                                            | userGroupByMsgIds
        null                                                                      | []                                                                                                                                                            | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | []                                                                                                                                                            | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 0, sendType: 2, msgType: 4, createTime: new Date(), chatType: 1, msgid: ["1,2,3"])]                                      | [new QueryUserGroupByMsgIdsDTO(msgId: 2, status: 0, count: 1)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 0, sendType: 2, msgType: 4, createTime: new Date(), chatType: 2, msgid: ["1,2,3"])]                                      | [new QueryUserGroupByMsgIdsDTO(msgId: 2, status: 0, count: 1)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 0, sendType: 2, msgType: 4, createTime: new Date(), chatType: 1, msgid: ["1,2,3"])]                                      | [new QueryUserGroupByMsgIdsDTO(msgId: 2, status: 1, count: 1)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 0, sendType: 2, msgType: 4, createTime: new Date(), chatType: 1, msgid: ["1,2,3"], marketingActivityId: "1")]            | [new QueryUserGroupByMsgIdsDTO(msgId: 2, status: 2, count: 1)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 0, sendType: 2, msgType: 4, createTime: new Date(), chatType: 1, msgid: ["1,2"], marketingActivityId: "1", fsUserId: 1)] | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 1, sendType: 2, msgType: 4, createTime: new Date(), chatType: 1, msgid: ["1,2"])]                                        | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 1, sendType: 2, msgType: 3, createTime: new Date(), chatType: 1, msgid: ["1,2"], imagePath: "C_")]                       | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 1, sendType: 2, msgType: 3, createTime: new Date(), chatType: 1, msgid: ["1,2"], imagePath: "1")]                        | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 1, sendType: 2, msgType: 3, createTime: new Date(), chatType: 1, msgid: ["1,2"], imagePath: "1", fixedTime: 1L)]         | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 1, sendType: 2, msgType: 3, createTime: new Date(), chatType: 2, msgid: ["1,2"], imagePath: "1", fixedTime: 1L)]         | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]
        new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]) | [new QywxGroupSendTaskEntity(status: 1, sendType: 2, msgType: 3, createTime: new Date(), chatType: 1, msgid: ["1,2"], imagePath: "A_")]                       | [new QueryUserGroupByMsgIdsDTO(msgId: 2)]

    }

    @Unroll
    def "sendQywxSpreadMiniAppMessage"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >> qywxCorpAgentConfigEntity
        qywxMiniappConfigDAO.getByEa(*_) >> miniappConfigEntity
        qywxUserManager.getQyUserIdByFsUserInfo(*_) >> qywxUserIdMap
        groupSendMessageManager.groupMessageBatchNum = 1

        when:
        groupSendMessageManager.sendQywxSpreadMiniAppMessage("1", allUserIds, title, "1", new Date(), 0, "1", "1", true)

        then:
        noExceptionThrown()

        where:
        qywxCorpAgentConfigEntity       | allUserIds | miniappConfigEntity           | title | qywxUserIdMap
        null                            | null       | null                          | null  | null
        new QywxCorpAgentConfigEntity() | null       | null                          | null  | null
        new QywxCorpAgentConfigEntity() | [1]        | null                          | null  | null
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | null  | null
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | "1"   | null
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | "1"   | new HashMap<Integer, String>(1: "1")

    }

    @Unroll
    def "sendQywxAgentAppMessage"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >> qywxCorpAgentConfigEntity
        qywxMiniappConfigDAO.getByEa(*_) >> miniappConfigEntity
        qywxUserManager.getQyUserIdByFsUserInfo(*_) >> qywxUserIdMap
        groupSendMessageManager.groupMessageBatchNum = 1
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa(*_) >> appInfoEntities

        when:
        groupSendMessageManager.sendQywxAgentAppMessage("1", "1", allUserIds, title, "1", new Date(), 0, "1", "1", "1", true)

        then:
        noExceptionThrown()

        where:
        qywxCorpAgentConfigEntity       | allUserIds | miniappConfigEntity           | title | qywxUserIdMap                        | appInfoEntities
        null                            | null       | null                          | null  | null                                 | []
        new QywxCorpAgentConfigEntity() | null       | null                          | null  | null                                 | []
        new QywxCorpAgentConfigEntity() | [1]        | null                          | null  | null                                 | [new QywxCustomerAppInfoEntity()]
        new QywxCorpAgentConfigEntity() | []         | new QywxMiniappConfigEntity() | null  | null                                 | [new QywxCustomerAppInfoEntity()]
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | "1"   | null                                 | [new QywxCustomerAppInfoEntity()]
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | "1"   | new HashMap<Integer, String>(1: "1") | [new QywxCustomerAppInfoEntity()]

    }

    @Unroll
    def "sendQywxH5AgentMessage"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >> qywxCorpAgentConfigEntity
        qywxMiniappConfigDAO.getByEa(*_) >> miniappConfigEntity
        qywxUserManager.getQyUserIdByFsUserInfo(*_) >> qywxUserIdMap
        groupSendMessageManager.groupMessageBatchNum = 1
        qywxCustomerAppInfoDAO.selectByCorpIdAndEa(*_) >> appInfoEntities

        when:
        groupSendMessageManager.sendQywxH5AgentMessage("1", allUserIds, [new OfficeMessageArg.LabelWarp()], "1")

        then:
        noExceptionThrown()

        where:
        qywxCorpAgentConfigEntity       | allUserIds | miniappConfigEntity           | title | qywxUserIdMap                        | appInfoEntities
        null                            | null       | null                          | null  | null                                 | []
        new QywxCorpAgentConfigEntity() | null       | null                          | null  | null                                 | []
        new QywxCorpAgentConfigEntity() | [1]        | null                          | null  | null                                 | [new QywxCustomerAppInfoEntity()]
        new QywxCorpAgentConfigEntity() | []         | new QywxMiniappConfigEntity() | null  | null                                 | [new QywxCustomerAppInfoEntity()]
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | "1"   | null                                 | [new QywxCustomerAppInfoEntity()]
        new QywxCorpAgentConfigEntity() | [1]        | new QywxMiniappConfigEntity() | "1"   | new HashMap<Integer, String>(1: "1") | [new QywxCustomerAppInfoEntity()]

    }

    @Unroll
    def "sendSopQywxAgentH5Message"() {
        given:
        qywxManager.getAgentAccessToken(*_) >> "1"
        qywxManager.sendAgentMessage(*_) >> new SpreadQywxMiniappMessageResult()

        when:
        groupSendMessageManager.sendSopQywxAgentH5Message("1", [new OfficeMessageArg.LabelWarp()], "1", new QywxCustomerAppInfoEntity(agentId: "1"), ["1", "2"])

        then:
        noExceptionThrown()

    }

    @Unroll
    def "sendSpreadQywxAgentppMessage"() {
        given:
        qywxManager.getAgentAccessToken(*_) >> "1"
        qywxManager.sendAgentMessage(*_) >> new SpreadQywxMiniappMessageResult()

        when:
        groupSendMessageManager.sendSpreadQywxAgentppMessage("1", "1", "1", "1", new Date(), 1, "1", "1", new QywxCustomerAppInfoEntity(agentId: "1"), ["1", "1"], "1", multiple)

        then:
        noExceptionThrown()

        where:
        multiple << [true, false]

    }

    @Unroll
    def "sendSpreadQywxMiniappMessage"() {
        given:
        qywxManager.getAgentAccessToken(*_) >> "1"
        qywxManager.sendAgentMessage(*_) >> new SpreadQywxMiniappMessageResult()

        when:
        groupSendMessageManager.sendSpreadQywxMiniappMessage("1", title, description, new Date(), 1, "1", "1", new QywxMiniappConfigEntity(agentid: "1", appid: "wx9b9390b6c48a3c81"), ["1", "1"], multiple)

        then:
        noExceptionThrown()

        where:
        title                              | description                        | multiple
        "11111111111111111111111111111111" | "11111111111111111111111111111111" | true
        "1"                                | "1"                                | false
    }

    @Unroll
    def "cutStringByU8"() {
        given:

        when:
        groupSendMessageManager.cutStringByU8("1", 1)

        then:
        noExceptionThrown()

    }

    @Unroll
    def "cachedGroupDetail"() {
        given:

        when:
        groupSendMessageManager.cachedGroupDetail()

        then:
        noExceptionThrown()

    }

    @Unroll
    def "listMomentCustomer"() {
        given:
        qywxMomentTaskDAO.getByMarketingActivityId(*_) >> new QywxMomentTaskEntity()
        fsBindManager.isQywxContactFsSetting(*_) >> isQywxContactFsSetting
        qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(*_) >> ["1"]
        crmV2Manager.concurrentListCrmObjectByFilterV3(*_) >> new InnerPage<ObjectData>(dataList: [new ObjectData("external_user_id": "1", "_id": "1")])
        qywxUserManager.getFsUserIdByQyWxInfo(*_) >> new HashMap("1": 1)
        qywxMomentSendResultDao.getExternalUserIdAndcount(*_) >> externalUserIdAndcountList
        qywxMomentSendResultDao.getByMomentIdAndUserIdAndStatus(*_) >> [new QywxMomentSendResultEntity(), new QywxMomentSendResultEntity(externalUserId: "2", publishStatus: 1, createTime: new Date(), userId: "1")]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>(1: new FsAddressBookManager.FSEmployeeMsg())

        when:
        groupSendMessageManager.listMomentCustomer("ea", arg)

        then:
        noExceptionThrown()

        where:
        isQywxContactFsSetting | arg                                                                                                               | externalUserIdAndcountList
        false                  | new MomentCustomerArg(departmentIdList: [1])                                                                      | []
        true                   | new MomentCustomerArg(departmentIdList: [1], queryType: "USER_ASSOCIATED", userId: "1", pageNum: 1, pageSize: 10) | [new HashMap<String, Object>("externaluserid": "1", "count": 1L)]
        false                  | new MomentCustomerArg(departmentIdList: [1], queryType: "USER_ASSOCIATED", userId: "1", pageNum: 1, pageSize: 10) | [new HashMap<String, Object>("externaluserid": "1", "count": 1L)]
        true                   | new MomentCustomerArg(departmentIdList: [1], queryType: "USER_ASSOCIATED", userId: "1", pageNum: 1, pageSize: 10) | [new HashMap<String, Object>("externaluserid": "2", "count": 1L)]
        true                   | new MomentCustomerArg(departmentIdList: [1], queryType: "ALL_UNSEND", userId: "1", pageNum: 1, pageSize: 10)      | [new HashMap<String, Object>("externaluserid": "2", "count": 1L)]
        true                   | new MomentCustomerArg(departmentIdList: [1], queryType: "ALL_SEND", userId: "1", pageNum: 1, pageSize: 10)        | [new HashMap<String, Object>("externaluserid": "2", "count": 1L)]
        true                   | new MomentCustomerArg(departmentIdList: [1], queryType: "USER_SEND", userId: "1", pageNum: 1, pageSize: 10)       | [new HashMap<String, Object>("externaluserid": "2", "count": 1L)]
        true                   | new MomentCustomerArg(departmentIdList: [1], queryType: "ALL", userId: "1", pageNum: 1, pageSize: 10)             | [new HashMap<String, Object>("externaluserid": "2", "count": 1L)]

    }

    @Unroll
    def "getAllDepartment"() {
        given:
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxManager.queryDepartment(*_) >> new DepartmentListResult(departmentList: [new Department()])

        when:
        groupSendMessageManager.getAllDepartment("ea")

        then:
        noExceptionThrown()

    }

    @Unroll
    def "getQyWxAddressBookEntity"() {
        given:
        qywxAddressBookManager.queryEaAndUserId(*_) >> qyWxAddressBookEntities

        when:
        groupSendMessageManager.getQyWxAddressBookEntity(userIdList, "ea")

        then:
        noExceptionThrown()

        where:
        userIdList | qyWxAddressBookEntities
        []         | []
        ["1"]      | null
        ["1"]      | [new QyWxAddressBookEntity()]

    }

    @Unroll
    def "getAvatarFormCrmData"() {
        given:
        fileV2Manager.getUrlByPath(*_) >> "1"

        when:
        groupSendMessageManager.getAvatarFormCrmData(avatarMap, "ea")

        then:
        noExceptionThrown()

        where:
        avatarMap << [null, [new HashMap<String, Object>()]]

    }

    @Unroll
    def "openQywxH5Notice"() {
        given:
        marketingNoticeSettingDAO.queryByEa(*_) >> new MarketingNoticeSettingEntity(noticeType: "[\"qywx_h5\"]")

        when:
        groupSendMessageManager.openQywxH5Notice("ea")

        then:
        noExceptionThrown()

        where:
        avatarMap << [null, [new HashMap<String, Object>()]]

    }

    @Unroll
    def "checkChatGroupIdListByFilters"() {
        given:
        marketingNoticeSettingDAO.queryByEa(*_) >> new MarketingNoticeSettingEntity(noticeType: "[\"qywx_h5\"]")
        crmV2Manager.countCrmObjectByFilterV3(*_) >> totalRelationCount
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> objectDataInnerPage

        when:
        groupSendMessageManager.checkChatGroupIdListByFilters("ea", filters)

        then:
        noExceptionThrown()

        where:
        filters                                                                                                       | totalRelationCount | objectDataInnerPage
        null                                                                                                          | 0                  | null
        [new HashMap<String, Object>("fieldValues": [1.23E5], "valueType": 1, "isCascade": true, "fieldName": "tag")] | 0                  | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]   | 0                  | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]   | 1                  | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]   | 1                  | new InnerPage<ObjectData>(dataList: [new ObjectData("leader_id": "1", "chat_id": "1")])


    }


    @Unroll
    def "getChatGroupIdListByFilters"() {
        given:
        marketingNoticeSettingDAO.queryByEa(*_) >> new MarketingNoticeSettingEntity(noticeType: "[\"qywx_h5\"]")
        crmV2Manager.countCrmObjectByFilterV3(*_) >> totalRelationCount
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> objectDataInnerPage
        dataPermissionManager.getNewDataPermissionSetting(*_) >> true
        qywxManager.queryAllStaff(*_) >> staffInfoList
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(*_) >> qywxAccessibleDepartmentIds

        when:
        groupSendMessageManager.getChatGroupIdListByFilters("ea", filters, 1)

        then:
        noExceptionThrown()

        where:
        filters                                                                                                                       | totalRelationCount | objectDataInnerPage                                                                     | staffInfoList                           | qywxAccessibleDepartmentIds
        null                                                                                                                          | 0                  | null                                                                                    | null                                    | null
        [new HashMap<String, Object>("fieldValues": [1.23E5], "valueType": new BigDecimal(1), "isCascade": true, "fieldName": "tag")] | 0                  | null                                                                                    | null                                    | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]                   | 0                  | null                                                                                    | null                                    | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]                   | 1                  | null                                                                                    | null                                    | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": new BigDecimal(1), "isCascade": true, "fieldName": "tag")]   | 1                  | null                                                                                    | null                                    | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]                   | 1                  | new InnerPage<ObjectData>(dataList: [new ObjectData("leader_id": "1", "chat_id": "1")]) | null                                    | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]                   | 1                  | new InnerPage<ObjectData>(dataList: [new ObjectData("leader_id": "1", "chat_id": "1")]) | [new DepartmentStaffResult.StaffInfo()] | null
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]                   | 1                  | new InnerPage<ObjectData>(dataList: [new ObjectData("leader_id": "1", "chat_id": "1")]) | [new DepartmentStaffResult.StaffInfo(department: [1], userId: "1")] | [1]
        [new HashMap<String, Object>("fieldValues": 1.23E5, "valueType": 1, "isCascade": true, "fieldName": "tag")]                   | 0                  | new InnerPage<ObjectData>(dataList: [new ObjectData("leader_id": "1", "chat_id": "1")]) | [new DepartmentStaffResult.StaffInfo(department: [1], userId: "1")] | [1]


    }

}
