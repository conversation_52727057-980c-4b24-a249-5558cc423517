package com.facishare.marketing.provider.manager.mail


import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData
import com.facishare.marketing.api.result.UserMarketingActionResult
import com.facishare.marketing.api.vo.mail.AddApiUserVO
import com.facishare.marketing.api.vo.mail.AddTemplateVO
import com.facishare.marketing.api.vo.mail.BaseMailVO
import com.facishare.marketing.api.vo.mail.CreateWebHookVO
import com.facishare.marketing.api.vo.mail.DeleteMailLabelVO
import com.facishare.marketing.api.vo.mail.DeleteMailTemplateVO
import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO
import com.facishare.marketing.api.vo.mail.PageQueryMailLabelVO
import com.facishare.marketing.api.vo.mail.PageQueryTemplateVO
import com.facishare.marketing.api.vo.mail.QueryDomainListVO
import com.facishare.marketing.api.vo.mail.QueryEmailStatusVO
import com.facishare.marketing.api.vo.mail.QueryOpenAndClickListVO
import com.facishare.marketing.api.vo.mail.QueryTemplateDetailVO
import com.facishare.marketing.api.vo.mail.StatDayStatisticsVO
import com.facishare.marketing.api.vo.mail.UpdateMailLabelVO
import com.facishare.marketing.api.vo.mail.UpdateMailTemplateVO
import com.facishare.marketing.api.vo.mail.UpdateMainDomainVO
import com.facishare.marketing.common.typehandlers.value.MailSendExtraData
import com.facishare.marketing.provider.dao.ActivityEnrollDataDAO
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.MarketingUserGroupDao
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.mail.*
import com.facishare.marketing.provider.dto.MarketingUserWithEmail
import com.facishare.marketing.provider.dto.conference.ConferenceEnrollBaseInfoDTO
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.ClueManagementEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.mail.MailAccountEntity
import com.facishare.marketing.provider.entity.mail.MailAddressListEntity
import com.facishare.marketing.provider.entity.mail.MailAddressMapEntity
import com.facishare.marketing.provider.entity.mail.MailCommonTemplateEntity
import com.facishare.marketing.provider.entity.mail.MailLabelEntity
import com.facishare.marketing.provider.entity.mail.MailSendErrorAddressEntity
import com.facishare.marketing.provider.entity.mail.MailSendExtraDataEntity
import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity
import com.facishare.marketing.provider.entity.mail.MailTemplateEntity
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.innerArg.mail.SendEmailArg
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager
import com.facishare.marketing.provider.manager.mail.arg.MailStatisticsReq
import com.facishare.marketing.provider.manager.mail.arg.QueryApiUserListReq
import com.facishare.marketing.provider.manager.mail.arg.QueryWebHookArg
import com.facishare.marketing.provider.manager.mail.arg.UpdateWebHookArg
import com.facishare.marketing.provider.manager.mail.result.AccountInfoResp
import com.facishare.marketing.provider.manager.mail.result.AddAddressListMemberResp
import com.facishare.marketing.provider.manager.mail.result.AddAddressListResp
import com.facishare.marketing.provider.manager.mail.result.AddApiUserResp
import com.facishare.marketing.provider.manager.mail.result.AddMailDomainResp
import com.facishare.marketing.provider.manager.mail.result.AddMailLabelResp
import com.facishare.marketing.provider.manager.mail.result.AddMailTemplateResp
import com.facishare.marketing.provider.manager.mail.result.CheckMailDomainResp
import com.facishare.marketing.provider.manager.mail.result.DeleteMailLabelResp
import com.facishare.marketing.provider.manager.mail.result.DeleteMailTemplateResp
import com.facishare.marketing.provider.manager.mail.result.InternationalAccountInfoResp
import com.facishare.marketing.provider.manager.mail.result.MailBaseResp
import com.facishare.marketing.provider.manager.mail.result.QueryDomainListResp
import com.facishare.marketing.provider.manager.mail.result.QueryEmailStatusResp
import com.facishare.marketing.provider.manager.mail.result.QueryMailTemplateDetailResp
import com.facishare.marketing.provider.manager.mail.result.QueryOpenAndClickListResp
import com.facishare.marketing.provider.manager.mail.result.SendMailResp
import com.facishare.marketing.provider.manager.mail.result.StatDayStatisticsResp
import com.facishare.marketing.provider.manager.mail.result.UpdateMailLabelResp
import com.facishare.marketing.provider.manager.mail.result.UpdateMailTemplateResp
import com.facishare.marketing.provider.manager.mail.result.UpdateMainDomainResp
import com.facishare.marketing.provider.manager.mail.result.UpdateWebHookResp
import com.facishare.marketing.provider.manager.mail.result.WebHookResp
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo
import com.facishare.marketing.provider.util.MarketingJobUtil
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.google.common.collect.Lists
import okhttp3.MultipartBody
import org.apache.commons.lang.time.DateUtils
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class MailManagerSpec extends Specification {
    def mailLabelDAO = Mock(MailLabelDAO)
    def mailAccountDAO = Mock(MailAccountDAO)
    def mailSendTaskDAO = Mock(MailSendTaskDAO)
    def mailSendTaskResultDAO = Mock(MailSendTaskResultDAO)
    def mailTemplateDAO = Mock(MailTemplateDAO)
    def httpManager = Mock(HttpManager)
    def fileV2Manager = Mock(FileV2Manager)
    def marketingUserGroupManager = Mock(MarketingUserGroupManager)
    def mailSendReplyDAO = Mock(MailSendReplyDAO)
    def mailAddressListDAO = Mock(MailAddressListDAO)
    def marketingUserGroupDao = Mock(MarketingUserGroupDao)
    def crmV2Manager = Mock(CrmV2Manager)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def mailSendExtraDataDAO = Mock(MailSendExtraDataDAO)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def marketingActivityManager = Mock(MarketingActivityManager)
    def enterpriseSpreadRecordManager = Mock(EnterpriseSpreadRecordManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def eieaConverter = Mock(EIEAConverter)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def conferenceManager = Mock(ConferenceManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def mailAddressMapDAO = Mock(MailAddressMapDAO)
    def mailSendErrorAddressDAO = Mock(MailSendErrorAddressDAO)
    def mailFilterDetailDAO = Mock(MailFilterDetailDAO)
    def marketingActivityRemoteManager = Mock(MarketingActivityRemoteManager)
    def appVersionManager = Mock(AppVersionManager)
    def marketingActivityAuditManager = Mock(MarketingActivityAuditManager)
    def emailSendRecordDetailObjManager = Mock(EmailSendRecordDetailObjManager)
    def specialEaMailAccount = "fs"


    def mailManager = new MailManager(mailAccountDAO: mailAccountDAO,
            mailSendTaskDAO: mailSendTaskDAO,
            mailSendTaskResultDAO: mailSendTaskResultDAO,
            mailTemplateDAO: mailTemplateDAO,
            httpManager: httpManager,
            fileV2Manager: fileV2Manager,
            marketingUserGroupManager: marketingUserGroupManager,
            mailSendReplyDAO: mailSendReplyDAO,
            mailAddressListDAO: mailAddressListDAO,
            marketingUserGroupDao: marketingUserGroupDao,
            crmV2Manager: crmV2Manager,
            userMarketingAccountManager: userMarketingAccountManager,
            mailSendExtraDataDAO: mailSendExtraDataDAO,
            activityEnrollDataDAO: activityEnrollDataDAO,
            marketingActivityExternalConfigDao: marketingActivityExternalConfigDao,
            marketingActivityManager: marketingActivityManager,
            enterpriseSpreadRecordManager: enterpriseSpreadRecordManager,
            campaignMergeDataDAO: campaignMergeDataDAO,
            campaignMergeDataManager: campaignMergeDataManager,
            eieaConverter: eieaConverter,
            marketingLiveDAO: marketingLiveDAO,
            conferenceManager: conferenceManager,
            conferenceDAO: conferenceDAO,
            mailAddressMapDAO: mailAddressMapDAO,
            mailSendErrorAddressDAO: mailSendErrorAddressDAO,
            mailFilterDetailDAO: mailFilterDetailDAO,
            marketingActivityRemoteManager: marketingActivityRemoteManager,
            appVersionManager: appVersionManager,
            marketingActivityAuditManager: marketingActivityAuditManager,
            emailSendRecordDetailObjManager: emailSendRecordDetailObjManager,
            mailLabelDAO: mailLabelDAO,
            specialEaMailAccount: specialEaMailAccount)

    @Unroll
    def "createGroupSendMsgTask"() {
        given:
        marketingActivityAuditManager.isNeedAudit(*_) >> true
        fileV2Manager.changeAWarehouseTempToPermanentBybusiness(*_) >> path
        mailSendTaskDAO.insert(*_) >> { println "fff" }
        mailSendExtraDataDAO.insert(*_) >> { println "fff" }
        when:
        mailManager.createGroupSendMsgTask(arg, "eventId", "activiId", "11", 1)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                                                                                                                                                                                                                | path
        new MailServiceMarketingActivityVO(sendRange: 2, marketingUserGroupIds: ["1"], type: 2, fixedTime: System.currentTimeMillis(), templateId: "1", sendMailIds: ["a"], replyToIds: ["i"], attachments: [new MailServiceMarketingActivityVO.MailAttachment(attachmentPath: "TA_AA")])                  | null
        new MailServiceMarketingActivityVO(sendRange: 2, marketingUserGroupIds: ["1"], type: 2, fixedTime: System.currentTimeMillis(), templateId: "1", sendMailIds: ["a"], replyToIds: ["i"], attachments: [new MailServiceMarketingActivityVO.MailAttachment(attachmentPath: "TA_AA")])                  | "aaa"
        new MailServiceMarketingActivityVO(sendRange: 2, marketingUserGroupIds: ["1"], type: 1, fixedTime: System.currentTimeMillis(), templateId: "1", sendMailIds: ["a"], replyToIds: ["i"], attachments: [new MailServiceMarketingActivityVO.MailAttachment(attachmentPath: "TA_AA")])                  | "aaa"
        new MailServiceMarketingActivityVO(sendRange: 1, marketingUserGroupIds: ["1"], type: 1, fixedTime: System.currentTimeMillis(), templateId: "1", sendMailIds: ["a"], replyToIds: ["i"], attachments: [new MailServiceMarketingActivityVO.MailAttachment(attachmentPath: "TA_AA")])                  | "aaa"
        new MailServiceMarketingActivityVO(sendRange: 3, campaignIds: [], marketingUserGroupIds: ["1"], type: 1, fixedTime: System.currentTimeMillis(), templateId: "1", sendMailIds: ["a"], replyToIds: ["i"], attachments: [new MailServiceMarketingActivityVO.MailAttachment(attachmentPath: "TA_AA")]) | "aaa"
    }

    @Unroll
    def "saveExtraDataEntity"() {
        given:
        mailSendExtraDataDAO.insert(*_) >> { println "fff" }
        when:
        mailManager.saveExtraDataEntity(new MailServiceMarketingActivityVO(sendRange: sendRange), "eventId", new MailSendTaskEntity())
        then:
        noExceptionThrown()
        where:
        sendRange << [3, 1, 6, 8]

    }

    @Unroll
    def "getValidMails"() {
        given:
        when:
        mailManager.getValidMails(["<EMAIL>", "dddd"])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "addEmailLabel"() {
        given:
        httpManager.executePostHttpWithRequestBody(*_) >> response
        mailLabelDAO.insert(*_) >> { println "fff" }
        when:
        mailManager.addEmailLabel("1", "2", "3", "4")
        then:
        noExceptionThrown()
        where:
        response << [new MailBaseResp(result: false), new MailBaseResp(result: true, info: new AddMailLabelResp(data: new AddMailLabelResp.AddMainLabelData()))]

    }

    @Unroll
    def "pageQueryMailLabel"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailLabelDAO.pageQueryLabel(*_) >> [new MailLabelEntity(createTime: new Date(), updateTime: new Date())]
        when:
        mailManager.pageQueryMailLabel(new PageQueryMailLabelVO(pageSize: 1, pageNum: 1))
        then:
        noExceptionThrown()
        where:
        account << [null, new MailAccountEntity()]

    }

    @Unroll
    def "deleteMailLabel"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailLabelDAO.getById(*_) >> mailLabel
        httpManager.executePostHttp(*_) >> response
        mailLabelDAO.deleteById(*_) >> { println "fff" }
        when:
        mailManager.deleteMailLabel(new DeleteMailLabelVO())
        then:
        noExceptionThrown()
        where:
        account                 | mailLabel             | response
        null                    | null                  | new MailBaseResp(result: false)
        new MailAccountEntity() | null                  | new MailBaseResp(result: false)
        new MailAccountEntity() | new MailLabelEntity() | new MailBaseResp(result: false)
        new MailAccountEntity() | new MailLabelEntity() | new MailBaseResp(result: true, info: new DeleteMailLabelResp())

    }

    @Unroll
    def "updateMailLabel"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailLabelDAO.getById(*_) >> mailLabel
        httpManager.executePostHttp(*_) >> response
        mailLabelDAO.updateMailLabel(*_) >> { println "fff" }
        when:
        mailManager.updateMailLabel(new UpdateMailLabelVO())
        then:
        noExceptionThrown()
        where:
        account                 | mailLabel             | response
        null                    | null                  | new MailBaseResp(result: false)
        new MailAccountEntity() | null                  | new MailBaseResp(result: false)
        new MailAccountEntity() | new MailLabelEntity() | new MailBaseResp(result: false)
        new MailAccountEntity() | new MailLabelEntity() | new MailBaseResp(result: true, info: new UpdateMailLabelResp())

    }

    @Unroll
    def "buildEmailSendRequestBody"() {
        given:
        mailSendReplyDAO.getByIds(*_) >> [new MailSendReplyEntity(address: "d", name: "a")]
        fileV2Manager.downloadAFile(*_) >> []
        when:
        mailManager.buildEmailSendRequestBody(task, "1", "2", ["aa"], 1, "s", sendType)

        then:
        noExceptionThrown()
        where:
        task                                                                                                                                                                                              | sendType
        null                                                                                                                                                                                              | 1
        new MailSendTaskEntity(html: "h", subject: "s", senderIds: ["1"], replyIds: ["2"], ea: "a", id: "id", contentSummary: "a", templateInvokeName: "a", attachments: "[{\"attachmentName\":\"dd\"}]") | 0
        new MailSendTaskEntity(html: "h", subject: "s", senderIds: ["1"], replyIds: ["2"], ea: "a", id: "id", contentSummary: "a", templateInvokeName: "a", attachments: "[{\"attachmentName\":\"dd\"}]") | 1
        new MailSendTaskEntity(html: "h", subject: "s", senderIds: ["1"], replyIds: ["2"], ea: "a", id: "id", contentSummary: "a", templateInvokeName: "a", attachments: "[{\"attachmentName\":\"dd\"}]") | 2

    }


    @Unroll
    def "sendEmailTaskSchedule"() {
        given:
        marketingActivityExternalConfigDao.getMarketingActivityExternalConfigEntity(*_) >> null
        marketingActivityRemoteManager.enterpriseStop(*_) >> stop
        appVersionManager.getCurrentAppVersion(*_) >> "sss"
        mailSendTaskDAO.updateStatusAndCodeById(*_) >> 1
        when:
        mailManager.sendEmailTaskSchedule()
        then:
        noExceptionThrown()
        where:
        taskList                   | stop
        []                         | false
        [new MailSendTaskEntity()] | true
        [new MailSendTaskEntity()] | false

    }

    @Unroll
    def "getSendCloudApiHostHttpsUrl"() {
        given:
        appVersionManager.isInternationalProd() >> result
        when:
        mailManager.getSendCloudApiHostHttpsUrl()
        then:
        noExceptionThrown()
        where:
        result << [true, false]

    }

    @Unroll
    def "getSendCloudApiHostUrl"() {
        given:
        appVersionManager.isInternationalProd() >> result
        when:
        mailManager.getSendCloudApiHostUrl()
        then:
        noExceptionThrown()
        where:
        result << [true, false]

    }

    @Unroll
    def "cancelSend"() {
        given:
        mailSendTaskDAO.getById(*_) >> task
        mailSendTaskDAO.updateStatusById(*_) >> 1
        when:
        mailManager.cancelSend("aa", "ea")
        then:
        noExceptionThrown()
        where:
        task << [null, new MailSendTaskEntity(scheduleType: 1)]
    }

    @Unroll
    def "checkSendCancelable"() {
        given:
        mailSendTaskDAO.getById(*_) >> task
        when:
        mailManager.checkSendCancelable("aa")
        then:
        noExceptionThrown()
        where:
        task << [null, new MailSendTaskEntity(scheduleType: 2, sendStatus: 0), new MailSendTaskEntity(scheduleType: 1, sendStatus: 0)]
    }

    @Unroll
    def "buildUserActionResult"() {
        given:
        mailSendTaskDAO.getById(*_) >> task
        mailSendReplyDAO.getById(*_) >> new MailSendReplyEntity()
        when:
        mailManager.buildUserActionResult(actionType, new UserMarketingActionResult())
        then:
        noExceptionThrown()
        where:
        task                                         | actionType
        null                                         | 1500
        new MailSendTaskEntity()                     | 1501
        new MailSendTaskEntity()                     | 1502
        new MailSendTaskEntity(senderIds: "[\"a\"]") | 1503
        new MailSendTaskEntity()                     | 1507
        new MailSendTaskEntity()                     | 1504
    }

    @Unroll
    def "updateWebHook"() {
        given:
        appVersionManager.isInternationalProd() >> true
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "aa", apiKey: "dddd");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: new UpdateWebHookResp())
        when:
        mailManager.updateWebHook(arg)
        then:
        noExceptionThrown()
        where:
        arg << [new UpdateWebHookArg(), new UpdateWebHookArg(newCategoryName: "a", newEvent: "A", newUrl: "a", url: "s", categoryName: "a", event: "d", apiKey: "a", apiUser: "d")]
    }

    @Unroll
    def "webHookList"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "aa", apiKey: "dddd");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: new WebHookResp())
        when:
        mailManager.webHookList(new QueryWebHookArg(apiUser: "a", apiKey: "a", categoryName: "c", event: "e", url: "a"))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "removeWebHookDeliverEvent"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "aa", apiKey: "dddd");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: rep)
        when:
        mailManager.removeWebHookDeliverEvent("a")
        then:
        noExceptionThrown()
        where:
        rep << [null, new WebHookResp(dataList: [new WebHookResp.WebHookDetail(eventTypeMap: ["1": "1"]), new WebHookResp.WebHookDetail(eventTypeMap: ["2": "2"])])]
    }

    @Unroll
    def "canSendMail"() {
        given:
        specialEaMailAccount.contains(*_) >> result
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        appVersionManager.isInternationalProd() >> hws
        httpManager.executePostHttpWithRequestBody(*_) >> hwsRepon


        when:
        mailManager.canSendMail("a")
        then:
        noExceptionThrown()
        where:
        result | account                                          | hws   | hwsRepon
        true   | null                                             | true  | new MailBaseResp(result: true)
        false  | null                                             | true  | new MailBaseResp(result: true)
        false  | new MailAccountEntity(apiKey: "a", apiUser: "a") | true  | new MailBaseResp(result: false)
        false  | new MailAccountEntity(apiKey: "a", apiUser: "a") | true  | new MailBaseResp(result: true, info: new InternationalAccountInfoResp(restNum: 0, quota: 10, todayUsedQuota: 11, reputation: 3D))
        false  | new MailAccountEntity(apiKey: "a", apiUser: "a") | false | new MailBaseResp(result: true, info: new AccountInfoResp())
    }

    @Unroll
    def "addAddressMember"() {
        given:
        List<String> list = Lists.newArrayList()
        for (int i = 0; i < total; i++) {
            list.add(String.valueOf(i))
        }
        httpManager.executePostHttpWithRequestBody(*_) >> respon
        when:
        mailManager.addAddressMember("a", "a", "a", list)
        then:
        noExceptionThrown()
        where:
        total | respon
        0     | new MailBaseResp(result: false)
        1     | new MailBaseResp(result: false)
        1001  | new MailBaseResp(result: false)
        1     | new MailBaseResp(result: true, info: new AddAddressListMemberResp(count: 10, invalidAddressList: ["a"]))
        1001  | new MailBaseResp(result: true, info: new AddAddressListMemberResp(count: 10, invalidAddressList: ["a"]))
    }

    @Unroll
    def "addAddressList"() {
        given:
        httpManager.executeGetHttp(*_) >> respon
        mailAddressListDAO.insert(*_) >> insert
        when:
        mailManager.addAddressList("a", "a", "a", "d")
        then:
        noExceptionThrown()
        where:
        respon                                                                                                                        | insert
        null                                                                                                                          | 1
        new MailBaseResp(result: true, info: new AddAddressListResp(data: new AddAddressListResp.AddAddressListData(memberCount: 1))) | -1
        new MailBaseResp(result: true, info: new AddAddressListResp(data: new AddAddressListResp.AddAddressListData(memberCount: 1))) | 1
    }

    @Unroll
    def "statDayStatisticsToJob"() {
        given:
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: new StatDayStatisticsResp())
        mailSendTaskDAO.getLabelIdsByEa(*_) >> ["1"]
        def spy = Spy(mailManager)
        spy.queryOpenAndClickUserNumByLabelId(*_) >> [1: 1, 2: 2]
        spy.statDayStatisticsFromSendCloud(*_) >> new StatDayStatisticsResp(dataList: dataList)
        when:
        spy.statDayStatisticsToJob(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                                    | dataList
        new StatDayStatisticsVO()                                                                                              | []
        new StatDayStatisticsVO(startTime: DateUtils.addMonths(new Date(), -4).getTime(), endTime: System.currentTimeMillis()) | []
        new StatDayStatisticsVO(startTime: DateUtils.addMonths(new Date(), -4).getTime(), endTime: System.currentTimeMillis()) | [new StatDayStatisticsResp.StatDayDetail(clickNum: 1, deliveredNum: 1, openNum: 1)]
        new StatDayStatisticsVO(startTime: DateUtils.addMonths(new Date(), -1).getTime(), endTime: System.currentTimeMillis()) | []
        new StatDayStatisticsVO(startTime: DateUtils.addMonths(new Date(), -1).getTime(), endTime: System.currentTimeMillis()) | [new StatDayStatisticsResp.StatDayDetail(clickNum: 1, deliveredNum: 1, openNum: 1)]

    }

    @Unroll
    def "statDayStatisticsFromSendCloud"() {
        given:
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        when:
        mailManager.statDayStatisticsFromSendCloud(new StatDayStatisticsVO(apiUser: "a", apiKey: "a", aggregate: 1, startDate: "a", endDate: "1", labelIdList: ["a"]))
        then:
        noExceptionThrown()
        where:
        info << [null, new StatDayStatisticsResp()]

    }

    @Unroll
    def "queryAllOpenAndClickList"() {
        given:
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        when:
        mailManager.queryAllOpenAndClickList(new QueryOpenAndClickListVO(email: "s", trackType: 1, apiUser: "a", apiKey: "a", labelId: 1, startDate: "1", endDate: "2", start: 1, limit: 1))
        then:
        noExceptionThrown()
        where:
        info << [null, new QueryOpenAndClickListResp(total: 100, dataList: [new QueryOpenAndClickListResp.OpenAndClickInfo()])]

    }

    @Unroll
    def "queryOpenAndClickUserNumByLabelId"() {
        given:
        mailSendTaskDAO.getLabelIdsByEa(*_) >> labelId
        def spy = Spy(mailManager)
        spy.queryAllOpenAndClickList(*_) >> [new QueryOpenAndClickListResp.OpenAndClickInfo(trackType: 1, email: "1"), new QueryOpenAndClickListResp.OpenAndClickInfo(trackType: 2, email: "2")]
        when:
        spy.queryOpenAndClickUserNumByLabelId("ea", "1", searchAll, "1", "2")
        then:
        noExceptionThrown()
        where:
        searchAll | labelId
        null      | null
        true      | []
        //true      | [1]
        false     | [1]

    }


    @Unroll
    def "queryEmailStatus"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiKey: "a", apiUser: "d");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        when:
        mailManager.queryEmailStatus(new QueryEmailStatusVO(status: "a", subStatus: "2", emailIds: "s", email: "s", apiUser: "a", apiKey: "a", labelId: 1, startDate: "1", endDate: "2", start: 1, limit: 1))
        then:
        noExceptionThrown()
        where:
        info << [null, new QueryEmailStatusResp()]

    }

    @Unroll
    def "updateMainDomain"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiKey: "a", apiUser: "d");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        when:
        mailManager.updateMainDomain(new UpdateMainDomainVO(apiUser: "a", apiKey: "a", oldDomain: "aa", newDomain: "dd"))
        then:
        noExceptionThrown()
        where:
        info << [null, new UpdateMainDomainResp()]

    }

    @Unroll
    def "addApiUser"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiKey: "a", apiUser: "d");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        httpManager.transformUrlParams(*_) >> "ddddd"
        when:
        mailManager.addApiUser(new AddApiUserVO(apiUser: "a", apiKey: "a"))
        then:
        noExceptionThrown()
        where:
        info << [null, new AddApiUserResp()]

    }

    @Unroll
    def "updateTemplate"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiKey: "a", apiUser: "d");
        httpManager.executePostHttpWithRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        httpManager.transformUrlParams(*_) >> "ddddd"
        when:
        mailManager.updateTemplate(new UpdateMailTemplateVO(ea: "a", apiUser: "a", apiKey: "a", invokeName: "a", name: "name", html: "hh"))
        then:
        noExceptionThrown()
        where:
        info << [null, new UpdateMailTemplateResp()]

    }

    @Unroll
    def "deleteTemplate"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        httpManager.executePostHttpByRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        httpManager.transformUrlParams(*_) >> "ddddd"
        mailTemplateDAO.deleteTemplateById(*_) >> 1
        when:
        mailManager.deleteTemplate(new DeleteMailTemplateVO(ea: "a", id: "d"))
        then:
        noExceptionThrown()
        where:
        account                                          | info
        null                                             | null
        new MailAccountEntity(apiKey: "a", apiUser: "d") | null
        new MailAccountEntity(apiKey: "a", apiUser: "d") | new DeleteMailTemplateResp()

    }

    @Unroll
    def "queryCommonTemplateDetail"() {
        given:
        mailTemplateDAO.getCommonDetailById(*_) >> template

        when:
        mailManager.queryCommonTemplateDetail(new QueryTemplateDetailVO(id: "s"))
        then:
        noExceptionThrown()
        where:
        template << [null, new MailCommonTemplateEntity()]
    }


    @Unroll
    def "queryTemplateDetail"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        httpManager.executePostHttpByRequestBody(*_) >> new MailBaseResp(result: true, info: info)
        mailTemplateDAO.getDetailById(*_) >> new MailTemplateEntity()
        when:
        mailManager.queryTemplateDetail(new QueryTemplateDetailVO(id: "d"))
        then:
        noExceptionThrown()
        where:
        account                                          | info
        null                                             | null
        new MailAccountEntity(apiUser: "a", apiKey: "3") | null
        new MailAccountEntity(apiUser: "a", apiKey: "3") | new QueryMailTemplateDetailResp(data: new QueryMailTemplateDetailResp.TemplateData())
    }


    @Unroll
    def "pageQueryMailTemplate"() {
        given:
        mailTemplateDAO.pageList(*_) >> [new MailTemplateEntity(fsUserId: 1, type: 1, createTime: new Date(), updateTime: new Date())]
        when:
        mailManager.pageQueryMailTemplate(new PageQueryTemplateVO(pageNum: 1, pageSize: 1))
        then:
        noExceptionThrown()

    }

    @Unroll
    def "addMailTemplate"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailTemplateDAO.getTotalTemplateCount(*_) >> count
        httpManager.executePostHttpByRequestBody(*_) >> response
        mailTemplateDAO.insert(*_) >> { println "insert" }
        when:
        mailManager.addMailTemplate(new AddTemplateVO(templateName: "1", content: "1"))
        then:
        noExceptionThrown()
        where:
        account                                          | count | response
        null                                             | null  | null
        new MailAccountEntity(apiKey: "1", apiUser: "2") | 2000  | null
        new MailAccountEntity(apiKey: "1", apiUser: "2") | 1     | new MailBaseResp(result: false)
        new MailAccountEntity(apiKey: "1", apiUser: "2") | 1     | new MailBaseResp(result: true, info: new AddMailTemplateResp())
    }

    @Unroll
    def "sendMailByAddressList"() {
        given:
        def spy = Spy(mailManager)
        spy.buildEmailSendRequestBody(*_) >> requestBodyOptional
        mailSendTaskDAO.updateStatusById(*_) >> 1
        httpManager.executePostHttpByRequestBody(*_) >> response
        mailSendTaskDAO.updateStatusAndCodeById(*_) >> 1
        mailSendTaskDAO.updateStatusById(*_) >> 11
        mailSendTaskResultDAO.insert(*_) >> { println("33") }
        mailSendTaskDAO.setLabelIdById(*_) >> 1
        enterpriseSpreadRecordManager.upsertList(*_) >> { println "ff" }
        when:
        spy.sendMailByAddressList(new MailSendTaskEntity(), new MailAccountEntity(apiKey: "1", apiUser: "d"), "", [""], 1, ["a"])
        then:
        noExceptionThrown()
        where:
        requestBodyOptional                                                              | response
        Optional.empty()                                                                 | null
        Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp(statusCode: 1, result: false)
        Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp(result: true)
        Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp(result: true, info: new SendMailResp(maillistTaskId: []))
        Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp(result: true, info: new SendMailResp(maillistTaskId: [1]))
    }

    @Unroll
    def "distinctAndReturnOriginAddressList"() {
        given:
        when:
        mailManager.distinctAndReturnOriginAddressList(arg)
        then:
        noExceptionThrown()
        where:
        arg << [[], ["a", "a", "b"]]
    }

    @Shared
    String toUserStr = "0;1;2;3;4;5;6;7;8;9;10;11;12;13;14;15;16;17;18;19;20;21;22;23;24;25;26;27;28;29;30;31;32;33;34;35;36;37;38;39;40;41;42;43;44;45;46;47;48;49;50;51;52;53;54;55;56;57;58;59;60;61;62;63;64;65;66;67;68;69;70;71;72;73;74;75;76;77;78;79;80;81;82;83;84;85;86;87;88;89;90;91;92;93;94;95;96;97;98;99;100;101";

    @Unroll
    def "sendMailByXsmtpapi"() {
        given:
        println(String.join(";", toUserStr))
        def spy = Spy(mailManager)
        spy.buildEmailSendRequestBody(*_) >> requestBodyOptional
        httpManager.executePostHttpByRequestBody(*_) >> response
        mailSendTaskDAO.updateStatusById(*_) >> 1
        mailSendTaskResultDAO.batchInsert(*_) >> { println "dd" }
        when:
        spy.sendMailByXsmtpapi(new MailSendTaskEntity(toUser: toUser), "a", "a", "")
        then:
        noExceptionThrown()
        where:
        toUser    | requestBodyOptional                                                              | response
        null      | null                                                                             | null
        toUserStr | Optional.empty()                                                                 | null
        toUserStr | Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp()
        toUserStr | Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp(result: true, info: new SendMailResp(emailIdList: []))
        toUserStr | Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build()) | new MailBaseResp(result: true, info: new SendMailResp(emailIdList: ["d"]))

    }

    @Unroll
    def "sendMarketingActivityEmail"() {
        given:
        mailSendExtraDataDAO.getExtraDataByTaskId(*_) >> exterEntity
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> campaign
        campaignMergeDataManager.getUserEmailByCampaignId(*_) >> userMailDataMap
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailSendTaskDAO.updateStatusById(*_) >> 1
        httpManager.executePostHttpWithRequestBody(*_) >>> [new MailBaseResp(info: new AddMailLabelResp(data: new AddMailLabelResp.AddMainLabelData(labelId: 1, labelName: "a")), result: true)]
        mailSendTaskDAO.updateTotalSendCountById(*_) >> 1
        crmV2Manager.getDetail(*_) >> new ObjectData()
        def spy = Spy(mailManager)
        spy.buildEmailSendRequestBody(*_) >> Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build())
        mailSendTaskResultDAO.insert(*_) >> { println "s" }
        mailSendTaskDAO.setLabelIdById(*_) >> 1
        mailSendErrorAddressDAO.queryAllErrorAddressByEa(*_) >> []
        mailAddressMapDAO.insertMailAddressMap(*_) >> { printf "aa" }
        when:
        spy.sendMarketingActivityEmail(task)
        then:
        noExceptionThrown()
        where:
        task                                                                         | exterEntity                                                                       | campaign                      | userMailDataMap | account
        null                                                                         | null                                                                              | null                          | null            | null
        new MailSendTaskEntity()                                                     | null                                                                              | null                          | null            | null
        new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | null                          | null            | null
        new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | null            | null
        new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | ["a": "A"]      | null
        new MailSendTaskEntity(html: "{市场活动名称} {活动开始时间} {活动结束时间}") | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | ["a": "A"]      | new MailAccountEntity(apiUser: "a", apiKey: "d")
    }

    @Unroll
    def "sendLiveEnrollEmail"() {
        given:
        mailSendExtraDataDAO.getExtraDataByTaskId(*_) >> exterEntity
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> campaign
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> liveEntity
        campaignMergeDataManager.getUserEmailByCampaignId(*_) >> userMailDataMap
        mailSendTaskDAO.updateTotalSendCountById(*_) >> 1
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailSendTaskDAO.updateStatusById(*_) >> 1
        httpManager.executePostHttpWithRequestBody(*_) >>> [new MailBaseResp(info: new AddMailLabelResp(data: new AddMailLabelResp.AddMainLabelData(labelId: 1, labelName: "a")), result: true)]
        mailSendTaskDAO.updateTotalSendCountById(*_) >> 1
        crmV2Manager.getDetail(*_) >> new ObjectData()
        def spy = Spy(mailManager)
        spy.buildEmailSendRequestBody(*_) >> Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build())
        mailSendTaskResultDAO.insert(*_) >> { println "s" }
        mailSendTaskDAO.setLabelIdById(*_) >> 1
        mailSendErrorAddressDAO.queryAllErrorAddressByEa(*_) >> []
        mailAddressMapDAO.insertMailAddressMap(*_) >> { printf "aa" }
        when:
        spy.sendLiveEnrollEmail(task, [])
        then:
        noExceptionThrown()
        where:
        liveEntity                | task                                                                         | exterEntity                                                                       | campaign                      | userMailDataMap | account
        null                      | null                                                                         | null                                                                              | null                          | null            | null
        new MarketingLiveEntity() | null                                                                         | null                                                                              | null                          | null            | null
        new MarketingLiveEntity() | new MailSendTaskEntity()                                                     | null                                                                              | null                          | null            | null
        new MarketingLiveEntity() | new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | null                          | null            | null
        new MarketingLiveEntity() | new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | null            | null
        new MarketingLiveEntity() | new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | ["a": "A"]      | null
        new MarketingLiveEntity() | new MailSendTaskEntity(html: "{市场活动名称} {活动开始时间} {活动结束时间}") | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | ["a": "A"]      | new MailAccountEntity(apiUser: "a", apiKey: "d")
    }

    @Unroll
    def "sendConferenceEmail"() {
        given:
        mailSendExtraDataDAO.getExtraDataByTaskId(*_) >> exterEntity
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> campaign
        eieaConverter.enterpriseAccountToId(*_) >> 1
        campaignMergeDataManager.getUserEmailByCampaignId(*_) >> userMailDataMap
        mailSendTaskDAO.updateTotalSendCountById(*_) >> 1
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailSendTaskDAO.updateStatusById(*_) >> 1
        httpManager.executePostHttpWithRequestBody(*_) >>> [new MailBaseResp(info: new AddMailLabelResp(data: new AddMailLabelResp.AddMainLabelData(labelId: 1, labelName: "a")), result: true)]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        def spy = Spy(mailManager)
        spy.buildEmailSendRequestBody(*_) >> Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build())
        mailSendTaskResultDAO.insert(*_) >> { println "s" }
        mailSendTaskDAO.setLabelIdById(*_) >> 1
        mailSendErrorAddressDAO.queryAllErrorAddressByEa(*_) >> []
        mailAddressMapDAO.insertMailAddressMap(*_) >> { printf "aa" }
        activityEnrollDataDAO.queryConferenceEnrollBaseInfoByIds(*_) >> enrollList
        campaignMergeDataManager.campaignIdToActivityEnrollIdMap(*_) >> ["a": "a"]
        when:
        spy.sendConferenceEmail(task, [])
        then:
        noExceptionThrown()
        where:
        enrollList                                                                                                     | task                                                                         | exterEntity                                                                       | campaign                      | userMailDataMap | account
        []                                                                                                             | null                                                                         | null                                                                              | null                          | null            | null
        []                                                                                                             | new MailSendTaskEntity()                                                     | null                                                                              | null                          | null            | null
        [new ConferenceEnrollBaseInfoDTO(activityEnrollId: "a", startTimeStamp: new Date(), endTimeStamp: new Date())] | new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | null                          | null            | null
        [new ConferenceEnrollBaseInfoDTO(activityEnrollId: "a", startTimeStamp: new Date(), endTimeStamp: new Date())] | new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | null            | null
        [new ConferenceEnrollBaseInfoDTO(activityEnrollId: "a", startTimeStamp: new Date(), endTimeStamp: new Date())] | new MailSendTaskEntity()                                                     | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | ["a": "A"]      | null
        [new ConferenceEnrollBaseInfoDTO(activityEnrollId: "a", startTimeStamp: new Date(), endTimeStamp: new Date())] | new MailSendTaskEntity(html: "{市场活动名称} {活动开始时间} {活动结束时间}") | new MailSendExtraDataEntity(extraData: new MailSendExtraData(campaignIds: ["a"])) | new CampaignMergeDataEntity() | ["a": "A"]      | new MailAccountEntity(apiUser: "a", apiKey: "d")
    }

    @Unroll
    def "filterSendErrorAddress"() {
        given:
        mailSendErrorAddressDAO.queryAllErrorAddressByEa(*_) >> list
        mailFilterDetailDAO.insertMailFilterDetail(*_) >> { printf "dd" }
        when:
        mailManager.filterSendErrorAddress("ea", "ta", "aa")
        then:
        noExceptionThrown()
        where:
        list << [[], [new MailSendErrorAddressEntity(address: "aa")], [new MailSendErrorAddressEntity(address: "bb")]]
    }

    @Unroll
    def "filterSendErrorAddressV2"() {
        given:
        mailSendErrorAddressDAO.queryAllErrorAddressByEa(*_) >> list
        mailFilterDetailDAO.insertMailFilterDetail(*_) >> { printf "dd" }
        when:
        mailManager.filterSendErrorAddress("ea", "ta", ["aa"])
        then:
        noExceptionThrown()
        where:
        list << [[], [new MailSendErrorAddressEntity(address: "aa")], [new MailSendErrorAddressEntity(address: "bb")]]
    }

    @Unroll
    def "replaceCustomFields"() {
        given:
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo(fieldProperty: 2, fieldCaption: "a", fieldName: "d"), new CrmUserDefineFieldVo(fieldProperty: 2, fieldCaption: "aa", fieldName: "dd")]
        crmV2Manager.getObjectDataEnTextVal(*_) >> ["a": "b"]
        when:
        mailManager.replaceCustomFields("ea", "ta", "aa")
        then:
        noExceptionThrown()

    }

    @Unroll
    def "findHolders"() {
        given:
        when:
        mailManager.findHolders("aa\$\${ee.tt}ffff\$\${qq.tt}fff\$\${fff}\$\$")
        then:
        noExceptionThrown()

    }

    @Unroll
    def "buildValue"() {
        given:
        crmV2Manager.getObjectDataEnTextVal(*_) >> map
        String[] arrStr = ["Ananas", "Banana", "Kiwi"]
        when:
        mailManager.buildValue("ea", "a", "a", arrStr, objectId)
        then:
        noExceptionThrown()
        where:
        objectId | map
        null     | null
        "a"      | ["a": "a"]
        "a"      | ["b": "a"]

    }

    @Unroll
    def "handlerSendTask"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> account
        marketingActivityExternalConfigDao.getMarketingActivityExternalConfigEntityWithMarketingEventId(*_) >> new MarketingActivityExternalConfigEntity()
        marketingActivityAuditManager.checkAudtStatus(*_) >> isNormal
        mailSendTaskDAO.updateStatusByIdAndBeforeStatus(*_) >> updateNum
        mailSendTaskDAO.updateStatusById(*_) >> 1
        marketingUserGroupManager.listEmailByMarketingUserGroupIds(*_) >> marketingUserWithEmailSet
        httpManager.executeGetHttp(*_) >> new MailBaseResp(result: true, info: new AddAddressListResp(data: new AddAddressListResp.AddAddressListData(memberCount: 1, address: "sss")))
        enterpriseSpreadRecordManager.filterList(*_) >> ["<EMAIL>"]
        mailSendErrorAddressDAO.queryAllErrorAddressByEa(*_) >> []
        mailSendTaskDAO.updateTotalSendCountById(*_) >> 1
        httpManager.executePostHttpWithRequestBody(*_) >>> [new MailBaseResp(result: true, info: new AddAddressListMemberResp(count: 1)), new MailBaseResp(info: new AddMailLabelResp(data: new AddMailLabelResp.AddMainLabelData(labelId: 1, labelName: "a")), result: true), new MailBaseResp(result: true)]
        mailAddressListDAO.updateMemberCount(*_) >> 1
        mailAddressListDAO.insert(*_) >> 1
        userMarketingAccountManager.getBaseInfosByIds(*_) >> ["ss": new UserMarketingAccountData(crmLeadInfos: [new UserMarketingAccountData.CrmObjectIdName(id: "aa")], crmWxWorkExternalInfos: [new UserMarketingAccountData.CrmObjectIdName(id: "aa")], crmCustomerInfos: [new UserMarketingAccountData.CrmObjectIdName(id: "aa")], crmContactInfos: [new UserMarketingAccountData.CrmObjectIdName(id: "aa")], crmWxUserInfos: [new UserMarketingAccountData.CrmObjectIdName(id: "aa")])]
        def spy = Spy(mailManager)
        spy.buildEmailSendRequestBody(*_) >> Optional.of(new MultipartBody.Builder().addFormDataPart("apiUser", "1").build())
        mailSendTaskResultDAO.insert(*_) >> { printf "dd" }
        mailSendTaskDAO.setLabelIdById(*_) >> 1
        crmV2Manager.getObjectDataEnTextVal(*_) >> new ObjectData()
        when:
        spy.handlerSendTask(new MailSendTaskEntity(mailType: 23, sendRange: sendRange, html: "aa\$\${LeadsObj.name}ffff\$\${AccountObj.name}fff\$\${ContactObj.name}\$\$aa\$\${WechatFanObj.name}aa\$\${WechatWorkExternalUserObj.name}", marketingGroupUserIds: "[\"a\"]"))
        then:
        noExceptionThrown()
        where:
        sendRange | account                                          | isNormal | updateNum | marketingUserWithEmailSet
        2         | null                                             | false    | 1         | null
        2         | new MailAccountEntity(apiUser: "a", apiKey: "d") | false    | 1         | null
        2         | new MailAccountEntity(apiUser: "a", apiKey: "d") | true     | 0         | null
        2         | new MailAccountEntity(apiUser: "a", apiKey: "d") | true     | 1         | [new MarketingUserWithEmail("ss", "<EMAIL>")]
        3         | new MailAccountEntity(apiUser: "a", apiKey: "d") | true     | 1         | [new MarketingUserWithEmail("ss", "<EMAIL>")]
        6         | new MailAccountEntity(apiUser: "a", apiKey: "d") | true     | 1         | [new MarketingUserWithEmail("ss", "<EMAIL>")]
        8         | new MailAccountEntity(apiUser: "a", apiKey: "d") | true     | 1         | [new MarketingUserWithEmail("ss", "<EMAIL>")]
        5         | new MailAccountEntity(apiUser: "a", apiKey: "d") | true     | 1         | [new MarketingUserWithEmail("ss", "<EMAIL>")]

    }

    @Unroll
    def "sendEmailWithOutSaveDetail"() {
        given:

        mailAccountDAO.getAccountByEaAndType(*_) >> account
        mailAddressListDAO.insert(*_) >> 1
        httpManager.executeGetHttp(*_) >> new MailBaseResp(result: true, info: new AddAddressListResp(data: new AddAddressListResp.AddAddressListData(memberCount: 1, address: "sss")))
        httpManager.executePostHttpWithRequestBody(*_) >>> [new MailBaseResp(result: true, info: new AddMailLabelResp(data: new AddMailLabelResp.AddMainLabelData(labelId: 1)))]
        mailLabelDAO.insert(*_) >> { println "ddd" }
        when:
        mailManager.sendEmailWithOutSaveDetail(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                       | account
        new SendEmailArg()                                                                        | null
        new SendEmailArg(ea: "a", accountType: 1, senderIds: ["a"], title: "a", mailList: ["a"],) | null
        new SendEmailArg(ea: "a", accountType: 1, senderIds: ["a"], title: "a", mailList: ["a"],) | new MailAccountEntity(apiKey: "a", apiUser: "b")


    }

    @Unroll
    def "getValidMail"() {
        given:
        when:
        mailManager.getValidMail("1111")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getSendAddressByLowerCase"() {
        given:
        mailAddressMapDAO.queryEmailMapByLowerCase(*_) >> addresslist
        when:
        mailManager.getSendAddressByLowerCase(argList)
        then:
        noExceptionThrown()
        where:
        argList | addresslist
        []      | []
        ["aa"]  | []
        ["aa"]  | [new MailAddressMapEntity(sendEmailAddressLowerCase: "a", sendEmailAddress: "A")]

    }

    @Unroll
    def "pageCommonMailTemplate"() {
        given:
        mailTemplateDAO.pageCommonTemplate(*_) >> list
        when:
        mailManager.pageCommonMailTemplate(new PageQueryTemplateVO(pageNum: 1, pageSize: 1))
        then:
        noExceptionThrown()
        where:
        list << [[], [new MailCommonTemplateEntity(type: 1, createTime: new Date())]]

    }

    @Unroll
    def "queryApiUserList"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "a", apiKey: "b")
        httpManager.executePostHttp(*_) >> result
        when:
        mailManager.queryApiUserList(new BaseMailVO(emailType: 1, cType: 1, domain: "a"))
        then:
        noExceptionThrown()
        where:
        result << [null, new MailBaseResp(result: true, info: new QueryApiUserListReq())]

    }

    @Unroll
    def "addMailDomain"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "a", apiKey: "b")
        httpManager.executePostHttp(*_) >> result
        when:
        mailManager.addMailDomain(new BaseMailVO(emailType: 1, cType: 1, domain: "a"))
        then:
        noExceptionThrown()
        where:
        result << [null, new MailBaseResp(result: true, info: new AddMailDomainResp())]

    }

    @Unroll
    def "checkMailDomain"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "a", apiKey: "b")
        httpManager.executePostHttp(*_) >> result
        when:
        mailManager.checkMailDomain(new BaseMailVO(emailType: 1, cType: 1, domain: "a"))
        then:
        noExceptionThrown()
        where:
        result << [null, new MailBaseResp(result: true, info: new CheckMailDomainResp(dataList: [new CheckMailDomainResp.CheckMailResult()]))]

    }

    @Unroll
    def "queryDomainList"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "a", apiKey: "b")
        httpManager.executePostHttp(*_) >> result
        when:
        mailManager.queryDomainList(new QueryDomainListVO(type: 1, domain: "a", verify: "v"))
        then:
        noExceptionThrown()
        where:
        result << [null, new MailBaseResp(result: true, info: new QueryDomainListResp(dataList: [new AddMailDomainResp.DomainInfo()]))]

    }

    @Unroll
    def "createWebHook"() {
        given:
        mailAccountDAO.getAccountByEaAndType(*_) >> new MailAccountEntity(apiUser: "a", apiKey: "b")
        httpManager.executePostHttp(*_) >> result
        when:
        mailManager.createWebHook(new CreateWebHookVO(type: type, ea: "ea", url: "w", categoryName: "n", event: "ee"))
        then:
        noExceptionThrown()
        where:
        type | result
        0    | null
        0    | new MailBaseResp(result: true, info: new WebHookResp())
        1    | new MailBaseResp(result: true, info: new WebHookResp())
        99   | new MailBaseResp(result: true, info: new WebHookResp())
    }

    @Unroll
    def "getMarketingGroupUserInfo"() {
        given:
        marketingUserGroupDao.batchGet(*_) >> groupList
        when:
        mailManager.getMarketingGroupUserInfo("ea", groupId)
        then:
        noExceptionThrown()
        where:
        groupId   | groupList
        null      | null
        "[]"      | null
        "[\"a\"]" | null
        "[\"a\"]" | [new MarketingUserGroupEntity(id: "a", userNumber: 1)]
    }

    @Unroll
    def "getMarketingGroupUserMap"() {
        given:
        marketingUserGroupDao.batchGet(*_) >> [new MarketingUserGroupEntity(id: "a", userNumber: 1)]
        when:
        mailManager.getMarketingGroupUserMap("ea", ["a"])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getMarketingActivityUserByEmail"() {
        given:
        def objectData = new ObjectData()
        objectData.put("email", "qq")
        objectData.put("_id", "id")
        crmV2Manager.listCrmObjectByFilterV3(*_) >>> [new InnerPage(dataList: [objectData]), new InnerPage(dataList: [objectData]), new InnerPage(dataList: [objectData])]
        userMarketingAccountManager.associateAncGetObjectIdToMarketingUserIdMap(*_) >> ["id": "b"]
        userMarketingAccountManager.getBaseInfosByIdsV2(*_) >> ["b": new UserMarketingAccountData()]
        when:
        mailManager.getMarketingActivityUserByEmail(["qq", "ww", "ee"], "ea", 1)
        then:
        noExceptionThrown()
    }
}