package com.facishare.marketing.provider.service

import com.facishare.mankeep.api.outService.result.article.CrawlerArticleContentResult
import com.facishare.mankeep.api.outService.service.OutArticleService
import com.facishare.mankeep.api.outService.service.OutCoverService
import com.facishare.mankeep.api.outService.service.OutQRCodeService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.AddCustomArticlesResult
import com.facishare.marketing.api.arg.AddArticleArg
import com.facishare.marketing.api.arg.AddCustomArticlesArg
import com.facishare.marketing.api.arg.CancelMaterialTopArg
import com.facishare.marketing.api.arg.DeleteArticleArg
import com.facishare.marketing.api.arg.DeleteMaterialArg
import com.facishare.marketing.api.arg.ListArticleArg
import com.facishare.marketing.api.arg.PhotoCutOffset
import com.facishare.marketing.api.arg.PreviewArticleArg
import com.facishare.marketing.api.arg.QueryArticleDetailArg
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg
import com.facishare.marketing.api.arg.TopMaterialArg
import com.facishare.marketing.api.arg.UpdateArticleArg
import com.facishare.marketing.api.arg.UpdateWebCrawlerArticleArg
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg
import com.facishare.marketing.api.result.AddWebCrawlerArticleResult
import com.facishare.marketing.api.result.ArticleListResult
import com.facishare.marketing.api.result.EditObjectGroupResult
import com.facishare.marketing.api.result.InitWebCrawlerArticleResult
import com.facishare.marketing.api.result.ListObjectGroupResult
import com.facishare.marketing.api.result.MaterialTagResult
import com.facishare.marketing.api.result.ObjectSloganResult
import com.facishare.marketing.api.result.PreviewArticleResult
import com.facishare.marketing.api.result.QueryArticleDetailResult
import com.facishare.marketing.api.service.ObjectSloganRelationService
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult
import com.facishare.marketing.common.enums.ArticleStatusEnum
import com.facishare.marketing.common.enums.BindObjectType
import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.ButtonStyle
import com.facishare.marketing.common.typehandlers.value.FormHeadSetting
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.common.typehandlers.value.TagNameList
import com.facishare.marketing.provider.dao.ArticleDAO
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataObjectDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO
import com.facishare.marketing.provider.dao.MaterialRelationDao
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO
import com.facishare.marketing.provider.dao.ProductDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.manager.ArticleDAOManager
import com.facishare.marketing.provider.dao.manager.MaterialRelationDaoManager
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao
import com.facishare.marketing.provider.dto.ArticleEntityDTO
import com.facishare.marketing.provider.dto.CustomizeFormUserDataCountByObjectIdDTO
import com.facishare.marketing.provider.dto.ObjectStatisticData
import com.facishare.marketing.provider.entity.ActivityBindObjectEntity
import com.facishare.marketing.provider.entity.ArticleEntity
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataObjectEntity
import com.facishare.marketing.provider.entity.MaterialRelationEntity
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.ObjectTagEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.ProductEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity
import com.facishare.marketing.provider.manager.CustomizeFormDataManager
import com.facishare.marketing.provider.manager.FileManager
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.MaterialTagManager
import com.facishare.marketing.provider.manager.ObjectGroupManager
import com.facishare.marketing.provider.manager.ObjectTagManager
import com.facishare.marketing.provider.manager.PhotoManager
import com.facishare.marketing.provider.manager.PictureManager
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager
import com.facishare.marketing.provider.manager.image.ImageCreator
import com.facishare.marketing.provider.manager.image.ImageDrawer
import com.facishare.marketing.provider.manager.image.ImageDrawerTypeEnum
import com.facishare.marketing.provider.manager.image.material.CardExchangeCover
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager
import com.facishare.marketing.provider.manager.qr.QRCodeManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.util.statistics.util.ArticleStatisticsUtil
import com.facishare.marketing.statistic.outapi.result.ActionDurationTimeAvgByObjectResult
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.google.gson.Gson
import spock.lang.*

class ArticleServiceImplTest extends Specification {

    def articleServiceImpl = new ArticleServiceImpl()


    def articleDAO = Mock(ArticleDAO)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def customizeFormDataObjectDAO = Mock(CustomizeFormDataObjectDAO)
    def fileManager = Mock(FileManager)
    def pictureManager = Mock(PictureManager)
    def articleStatisticsUtil = Mock(ArticleStatisticsUtil)
    def outCoverService = Mock(OutCoverService)
    def imageCreator = Mock(ImageCreator)
    def photoManager = Mock(PhotoManager)
    def materialTagManager = Mock(MaterialTagManager)
    def fileV2Manager = Mock(FileV2Manager)
    def outQRCodeService = Mock(OutQRCodeService)
    def redisManager = Mock(RedisManager)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def outArticleService = Mock(OutArticleService)
    def marketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def objectTagManager = Mock(ObjectTagManager)
    def articleDAOManager = Mock(ArticleDAOManager)
    def qrCodeManager = Mock(QRCodeManager)
    def hexagonSiteManager = Mock(HexagonSiteManager)
    def hexagonSiteObjectDAO = Mock(HexagonSiteObjectDAO)
    def marketingMaterialInstanceLogManager = Mock(MarketingMaterialInstanceLogManager)
    def objectGroupManager = Mock(ObjectGroupManager)
    def objectGroupDAO = Mock(ObjectGroupDAO)
    def objectGroupRelationDAO = Mock(ObjectGroupRelationDAO)
    def objectTopManager = Mock(ObjectTopManager)
    def objectGroupRelationVisibleManager = Mock(ObjectGroupRelationVisibleManager)
    def productDAO = Mock(ProductDAO)
    def mktContentMgmtLogObjManager = Mock(MktContentMgmtLogObjManager)
    def materialRelationDao = Mock(MaterialRelationDao)
    def materialRelationDaoManager = Mock(MaterialRelationDaoManager)
    def userMarketingStatisticService = Mock(UserMarketingStatisticService)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def marketingObjectAmountStatisticDao = Mock(MarketingObjectAmountStatisticDao)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def objectSloganRelationService = Mock(ObjectSloganRelationService)
    def host = "ceshi112"

    def setup() {
        //   articleServiceImpl.gs = gs
        articleServiceImpl.articleDAO = articleDAO
        articleServiceImpl.customizeFormDataDAO = customizeFormDataDAO
        articleServiceImpl.customizeFormDataObjectDAO = customizeFormDataObjectDAO
        articleServiceImpl.fileManager = fileManager
        articleServiceImpl.pictureManager = pictureManager
        articleServiceImpl.articleStatisticsUtil = articleStatisticsUtil
        articleServiceImpl.outCoverService = outCoverService
        articleServiceImpl.imageCreator = imageCreator
        articleServiceImpl.photoManager = photoManager
        articleServiceImpl.materialTagManager = materialTagManager
        articleServiceImpl.fileV2Manager = fileV2Manager
        articleServiceImpl.outQRCodeService = outQRCodeService
        articleServiceImpl.redisManager = redisManager
        articleServiceImpl.customizeFormDataManager = customizeFormDataManager
        articleServiceImpl.outArticleService = outArticleService
        articleServiceImpl.marketingEventMaterialRelationDAO = marketingEventMaterialRelationDAO
        articleServiceImpl.objectTagManager = objectTagManager
        articleServiceImpl.articleDAOManager = articleDAOManager
        articleServiceImpl.qrCodeManager = qrCodeManager
        articleServiceImpl.hexagonSiteManager = hexagonSiteManager
        articleServiceImpl.hexagonSiteObjectDAO = hexagonSiteObjectDAO
        articleServiceImpl.marketingMaterialInstanceLogManager = marketingMaterialInstanceLogManager
        articleServiceImpl.objectGroupManager = objectGroupManager
        articleServiceImpl.objectGroupDAO = objectGroupDAO
        articleServiceImpl.objectGroupRelationDAO = objectGroupRelationDAO
        articleServiceImpl.objectTopManager = objectTopManager
        articleServiceImpl.objectGroupRelationVisibleManager = objectGroupRelationVisibleManager
        articleServiceImpl.productDAO = productDAO
        articleServiceImpl.mktContentMgmtLogObjManager = mktContentMgmtLogObjManager
        articleServiceImpl.materialRelationDao = materialRelationDao
        articleServiceImpl.materialRelationDaoManager = materialRelationDaoManager
        articleServiceImpl.userMarketingStatisticService = userMarketingStatisticService
        articleServiceImpl.customizeFormDataUserDAO = customizeFormDataUserDAO
        articleServiceImpl.marketingObjectAmountStatisticDao = marketingObjectAmountStatisticDao
        articleServiceImpl.contentMarketingEventMaterialRelationDAO = contentMarketingEventMaterialRelationDAO
        articleServiceImpl.crmV2Manager = crmV2Manager
        articleServiceImpl.objectSloganRelationService = objectSloganRelationService
    }


    def "getValuesTest"() {

        when:
        String result = ArticleServiceImpl.getValues(["result"])
        then:
        result == "result"
    }


    def "initWebCrawlerArticleTest"() {
        given:
        articleDAO.queryArticleByEaAndUrl(*_) >> queryArticleByEaAndUrlMock
        articleDAO.getBindObject(*_) >> new ActivityBindObjectEntity()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(title: "title", name: "name"))
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> getObjectBindingFormMock
        photoManager.querySinglePhoto(*_) >> new PhotoEntity(path: "path1", url: "url1", thumbnailUrl: "thumbnailUrl", thumbnailPath: "thumbnailPath")
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity()
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        fileV2Manager.downloadAFile(*_) >> null
        outArticleService.crawlerArticleContent(*_) >> crawlerArticleContentMock
        objectTagManager.queryObjectTag(*_) >> new ObjectTagEntity()
        hexagonSiteManager.getBindHexagonSiteByObject(*_) >> new HexagonSiteEntity()
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> getObjectBindingHexagonSiteMock
        productDAO.queryProductDetail(*_) >> new ProductEntity()
        materialRelationDao.queryMaterialRelationByObjectId(*_) >> new MaterialRelationEntity(sharePosterAPath: "sharePosterAPath")

        when:
        Result<InitWebCrawlerArticleResult> result = articleServiceImpl.initWebCrawlerArticle("ea", 0, "url")
        then:
        result == resultMock
        where:
        queryArticleByEaAndUrlMock                     | getObjectBindingHexagonSiteMock | getObjectBindingFormMock            | crawlerArticleContentMock                                 | resultMock
        null                                           | null                            | null                                | new ModelResult<CrawlerArticleContentResult>()            | Result.newError(SHErrorCode.SYSTEM_ERROR)
        null                                           | null                            | null                                | ModelResult.newSuccess(new CrawlerArticleContentResult()) | Result.newSuccess(new InitWebCrawlerArticleResult(url: "url", createSourceType: 2))
        new ArticleEntity(id: "id")                    | null                            | new CustomizeFormDataObjectEntity() | new ModelResult<CrawlerArticleContentResult>()            | Result.newSuccess(new InitWebCrawlerArticleResult(id: "id", sharePosterUrl: "getUrlByPathResponse", thumbApath: "thumbnailPath", photoApath: "path1", photoUrl: "url1", thumbUrl: "thumbnailUrl", bindObjectType: 16, formData: new QueryArticleDetailResult.FormData(formTitle: "title", formName: "name"), sharePosterAPath: "sharePosterAPath"))
        new ArticleEntity(id: "id")                    | new HexagonSiteObjectEntity()   | null                                | new ModelResult<CrawlerArticleContentResult>()            | Result.newSuccess(new InitWebCrawlerArticleResult(id: "id", bindObjectType: 26, sharePosterAPath: "sharePosterAPath", sharePosterUrl: "getUrlByPathResponse", thumbApath: "thumbnailPath", photoApath: "path1", photoUrl: "url1", thumbUrl: "thumbnailUrl", hexagonSiteData: new InitWebCrawlerArticleResult.HexagonSiteData()))
        new ArticleEntity(id: "id")                    | null                            | null                                | new ModelResult<CrawlerArticleContentResult>()            | Result.newSuccess(new InitWebCrawlerArticleResult(id: "id", bindObjectType: 4, sharePosterAPath: "sharePosterAPath", sharePosterUrl: "getUrlByPathResponse", thumbApath: "thumbnailPath", photoApath: "path1", photoUrl: "url1", thumbUrl: "thumbnailUrl"))
        new ArticleEntity(id: "id", articlePath: "aa") | null                            | null                                | new ModelResult<CrawlerArticleContentResult>()            | Result.newSuccess(new InitWebCrawlerArticleResult(id: "id", webPage: "", bindObjectType: 4, sharePosterAPath: "sharePosterAPath", sharePosterUrl: "getUrlByPathResponse", thumbApath: "thumbnailPath", photoApath: "path1", photoUrl: "url1", thumbUrl: "thumbnailUrl"))

    }


    def "addWebCrawlerArticleTest"() {
        given:
        articleDAO.queryArticleByEaAndUrl(*_) >> queryArticleByEaAndUrlMock
        articleDAO.getById(*_) >> getByIdMock
        articleDAO.updateArticleParsedContentPath(*_) >> 0
        articleDAO.queryArticleCountByName(*_) >> queryArticleCountByNameMock
        articleDAO.queryArticleByEaAndUrl(*_) >> new ArticleEntity()
        articleDAO.getById(*_) >> new ArticleEntity()
        articleDAO.updateArticleParsedContentPath(*_) >> 0
        articleDAO.queryArticleCountByName(*_) >> 0
        articleDAO.getBindObject(*_) >> new ActivityBindObjectEntity()
        articleDAO.bindObject(*_) >> 0
        articleDAO.unBindObject(*_) >> 0
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 0
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity()
        fileManager.uploadFileToAWarehouse(*_) >> "uploadFileToAWarehouseResponse"
        fileManager.uploadTmpToAWarehouse(*_) >> "uploadTmpToAWarehouseResponse"
        outCoverService.createLuckyMoneyIconCoverAsync(*_) >> new ModelResult(0, "errMsg", "data")
        imageCreator.getImageDrawer(*_) >> new ImageDrawer() {
            @Override
            String draw(Map<String, Object> params) {
                return null
            }
        }
        imageCreator.getImageDrawer(*_) >> null
        photoManager.savePhotoByAapath(*_) >> true
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        photoManager.addOrUpdatePhotoByCutOffset(*_) >> true
        fileV2Manager.parseArticleContent(*_) >> "parseArticleContentResponse"
        fileV2Manager.uploadToApath(*_) >> "uploadToApathResponse"
        fileV2Manager.getApathByTApath(*_) >> getApathByTApathMock
        fileV2Manager.deleteFilesByApath(*_) >> true
        outArticleService.crawlerArticleContent(*_) >> new ModelResult<CrawlerArticleContentResult>(0, "errMsg", new CrawlerArticleContentResult())
        objectTagManager.addOrUpdateObjectTag(*_) >> true
        articleDAOManager.addArticle(*_) >> addArticleMock
        articleDAOManager.updateArticle(*_) >> true
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> new HexagonSiteObjectEntity()
        hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(*_) >> 0
        def spy = Spy(articleServiceImpl)
        spy.setArticleEntity(*_) >> setArticleEntityMock
        spy.getArticlePath(*_) >> getArticlePathMock
        when:
        Result<AddWebCrawlerArticleResult> result = spy.addWebCrawlerArticle("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | queryArticleByEaAndUrlMock | queryArticleCountByNameMock | setArticleEntityMock        | getApathByTApathMock                                                                         | getArticlePathMock | addArticleMock | getByIdMock                              | resultMock
        new AddArticleArg(url: "url")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | new ArticleEntity()        | 0                           | null                        | null                                                                                         | null               | true           | null                                     | Result.newError(SHErrorCode.ARTICLE_EXIST)
        new AddArticleArg(url: "url")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | null                       | 1                           | null                        | null                                                                                         | null               | true           | null                                     | Result.newError(SHErrorCode.ARTICLE_NAME_EXIST)
        new AddArticleArg(url: "url")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | null                       | 0                           | null                        | null                                                                                         | null               | true           | null                                     | Result.newError(SHErrorCode.WEB_CRAWLER_FAIL)
        new AddArticleArg(url: "url", content: "content")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | null                       | 0                           | null                        | null                                                                                         | null               | true           | null                                     | Result.newError(SHErrorCode.ADD_NO_COVER)
        new AddArticleArg(url: "url", content: "content", coverTapath: "tapth")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | null                       | 0                           | null                        | null                                                                                         | null               | true           | null                                     | Result.newError(SHErrorCode.ADD_FAIL)
        new AddArticleArg(url: "url", content: "content", coverTapath: "tapth")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | null                       | 0                           | null                        | null                                                                                         | "articlePath"      | true           | null                                     | Result.newError(SHErrorCode.ADD_FAIL)
        new AddArticleArg(url: "url", content: "content", coverTapath: "TA_")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | null                       | 0                           | null                        | null                                                                                         | "articlePath"      | true           | null                                     | Result.newError(SHErrorCode.ADD_FAIL)
        new AddArticleArg(url: "url", content: "content", coverTapath: "TA_")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | null                       | 0                           | null                        | new FileV2Manager.FileManagerPicResult(urlAPath: "urlapath", thumbUrlApath: "thumbUrlApath") | "articlePath"      | true           | null                                     | Result.newError(SHErrorCode.ADD_FAIL)
        new AddArticleArg(url: "url", content: "content", coverTapath: "A_")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | null                       | 0                           | null                        | new FileV2Manager.FileManagerPicResult(urlAPath: "urlapath", thumbUrlApath: "thumbUrlApath") | "articlePath"      | true           | null                                     | Result.newError(SHErrorCode.ADD_FAIL)
        new AddArticleArg(url: "url", sharePosterAPath: "path1")                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | null                       | 0                           | null                        | new ArticleEntity(id: "id")                                                                  | "articlePath"      | false          | null                                     | Result.newError(SHErrorCode.WEB_CRAWLER_FAIL)
        new AddArticleArg(url: "url", sharePosterAPath: "path1", bindObjectType: BindObjectType.CUSTOMIZE_FORM.getType())                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | null                       | 0                           | new ArticleEntity(id: "id") | new ArticleEntity(id: "id")                                                                  | "articlePath"      | true           | null                                     | Result.newSuccess(new AddWebCrawlerArticleResult(id: "id"))
        new AddArticleArg(url: "url", sharePosterAPath: "path1", bindObjectType: BindObjectType.HEXAGON_SITE.getType())                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | null                       | 0                           | new ArticleEntity(id: "id") | new ArticleEntity(id: "id")                                                                  | "articlePath"      | true           | null                                     | Result.newSuccess(new AddWebCrawlerArticleResult(id: "id"))
        new AddArticleArg(url: "url", sharePosterAPath: "path1", bindObjectType: BindObjectType.PRODUCT.getType(), originalImageAPath: "path2", cutOffsetList: [new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())])                                  | null                       | 0                           | new ArticleEntity(id: "id") | new ArticleEntity(id: "id")                                                                  | "articlePath"      | true           | null                                     | Result.newSuccess(new AddWebCrawlerArticleResult(id: "id"))
        new AddArticleArg(id: "id", title: "a", url: "url", sharePosterAPath: "path1", bindObjectType: BindObjectType.PRODUCT.getType(), originalImageAPath: "path2", cutOffsetList: [new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())])            | null                       | 1                           | new ArticleEntity(id: "id") | new ArticleEntity(id: "id")                                                                  | "articlePath"      | true           | new ArticleEntity(id: "id1", title: "b") | Result.newError(SHErrorCode.ARTICLE_NAME_EXIST)
        new AddArticleArg(id: "id", status: 2, title: "a", url: "url", sharePosterAPath: "path1", bindObjectType: BindObjectType.PRODUCT.getType(), originalImageAPath: "path2", cutOffsetList: [new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", "left": "0", "width": "0", "height": "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | null                       | 1                           | new ArticleEntity(id: "id") | new ArticleEntity(id: "id")                                                                  | "articlePath"      | true           | new ArticleEntity(id: "id1", title: "a") | Result.newSuccess(new AddWebCrawlerArticleResult(id: "id1"))
    }


    def "updateArticlePhotoTest"() {
        given:
        articleDAO.updateArticlePhoto(*_) >> true
        imageCreator.getImageDrawer(*_) >> null
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        fileV2Manager.getApathByTApath(*_) >> new FileV2Manager.FileManagerPicResult()
        fileV2Manager.changeCWarehouseTempToPermanent(*_) >> "changeCWarehouseTempToPermanentResponse"
        fileV2Manager.getUrlByCPath(*_) >> "getUrlByCPathResponse"

        when:
        Result result = articleServiceImpl.updateArticlePhoto("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                                    | getApathByTApathMock                                                     | changeCWarehouseTempToPermanentMock | resultMock
        new UpdateArticleArg(photoUrl: "TA_X") | null                                                                     | null                                | Result.newError(SHErrorCode.SYSTEM_ERROR)
        new UpdateArticleArg(photoUrl: "TA_X") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | null                                | Result.newSuccess()
        new UpdateArticleArg(photoUrl: "TC_X") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | null                                | Result.newError(SHErrorCode.SYSTEM_ERROR)
        new UpdateArticleArg(photoUrl: "TC_X") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | "C_a"                               | Result.newSuccess()

    }


    def "deleteArticleTest"() {
        given:
        articleDAO.queryArticleDetail(*_) >> new ArticleEntity()
        articleDAO.queryArticleDetail(*_) >> queryArticleDetailMock
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(*_) >> 0
        articleDAOManager.deleteArticle(*_) >> true
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0
        contentMarketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(*_) >> 0

        when:
        Result result = articleServiceImpl.deleteArticle(arg1, 0, arg2)
        then:
        result == resultMock
        where:
        arg1 | arg2                   | queryArticleDetailMock       | resultMock
        null | new DeleteArticleArg() | new ArticleEntity(status: 1) | Result.newError(SHErrorCode.USER_NOT_BIND_EA);
        "88146" | new DeleteArticleArg() | new ArticleEntity(status: 1) | Result.newError(SHErrorCode.DEL_ARTICLE_FAIL_TIP);
        "88146" | new DeleteArticleArg() | new ArticleEntity(status: 2) | Result.newSuccess()
    }


    def "listArticlesTest"() {
        given:
        articleDAO.getAccessiblePage(*_) >> [new ArticleEntityDTO()]
        articleDAO.createByMePage(*_) >> [new ArticleEntityDTO()]
        articleDAO.noGroupPage(*_) >> [new ArticleEntityDTO()]
        pictureManager.checkArticleImg(*_) >> "checkArticleImgResponse"
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity()
        photoManager.resetArticlePhotoUrlByDTO(*_) >> [new ArticleEntityDTO()]
        materialTagManager.buildTagName(*_) >> ["buildTagNameResponse": ["buildTagNameResponse"]]
        fileV2Manager.processImageSizes(*_) >> ["processImageSizesResponse": 1l]
        marketingEventMaterialRelationDAO.getContentMarketingByEaAndObjectTypeAndObjectIds(*_) >> [new ContentMarketingEventMaterialRelationEntity()]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> [new ObjectGroupEntity()]
        userMarketingStatisticService.listActionDurationTimeAvgListByObject(*_) >> new com.facishare.marketing.statistic.common.result.Result<List<ActionDurationTimeAvgByObjectResult>>()
        customizeFormDataUserDAO.queryObjectClueCount(*_) >> [new CustomizeFormUserDataCountByObjectIdDTO()]
        marketingObjectAmountStatisticDao.listStatisticData(*_) >> [new ObjectStatisticData()]
        contentMarketingEventMaterialRelationDAO.getContentMarketingByEaAndObjectTypeAndObjectIds(*_) >> [new ContentMarketingEventMaterialRelationEntity()]
        crmV2Manager.getList(*_) >> new Page<ObjectData>()
        when:
        Result<ArticleListResult> result = articleServiceImpl.listArticles("fsEa", 0, arg)
        then:
        result == resultMock
        where:
        arg                                                                                                | createByMePageMock                                                        | noGroupPageMock | getAccessiblePageMock | getAccessibleGroupMock             | resultMock
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: DefaultObjectGroupEnum.CREATED_BY_ME.getId()) | null                                                                      | null            | null                  | null                               | Result.newSuccess(new ArticleListResult(articleDetailResults: []))
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: DefaultObjectGroupEnum.NO_GROUP.getId())      | null                                                                      | null            | null                  | null                               | Result.newSuccess(new ArticleListResult(articleDetailResults: []))
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: DefaultObjectGroupEnum.ALL.getId())           | null                                                                      | null            | null                  | [new ObjectGroupEntity(id: "111")] | Result.newSuccess(new ArticleListResult(articleDetailResults: []))
        new ListArticleArg(pageNum: 1, pageSize: 1)                                                        | null                                                                      | null            | null                  | [new ObjectGroupEntity(id: "111")] | Result.newSuccess(new ArticleListResult(articleDetailResults: []))

        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: "111")                                        | null                                                                      | null            | null                  | [new ObjectGroupEntity(id: "111")] | Result.newSuccess(new ArticleListResult(articleDetailResults: []))
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: "111")                                        | null                                                                      | null            | null                  | [new ObjectGroupEntity(id: "222")] | Result.newSuccess(new ArticleListResult(articleDetailResults: []))
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: DefaultObjectGroupEnum.CREATED_BY_ME.getId()) | null                                                                      | null            | null                  | null                               | Result.newSuccess(new ArticleListResult(articleDetailResults: []))
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: DefaultObjectGroupEnum.CREATED_BY_ME.getId()) | [new ArticleEntityDTO(id: "aaa")]                                         | null            | [null]                | null                               | Result.newSuccess(new ArticleListResult(articleDetailResults: [new QueryArticleDetailResult(id: "aaa", photoUrl: "checkArticleImgResponse", photoThumbnailUrl: "checkArticleImgResponse", actionDurationTimeAvg: "1秒", marketingEventCount: 0, accessCount: 2, leadCount: 1, objectLookUpCount: 2, materialTags: [new MaterialTagResult(name: "buildTagNameResponse")]), new QueryArticleDetailResult(id: "bbb", photoUrl: "checkArticleImgResponse", photoThumbnailUrl: "photoThumbnailUrl", "photoThumbnailAPath": "photoThumbnailAPath", title: "title", summary: "summary", belong: 2, marketingEventCount: 0, accessCount: 0, leadCount: 0, objectLookUpCount: 0, actionDurationTimeAvg: "0秒")]))
        new ListArticleArg(pageNum: 1, pageSize: 1, groupId: DefaultObjectGroupEnum.CREATED_BY_ME.getId()) | [new ArticleEntityDTO(id: "aaa", photoThumbnailUrl: "photoThumbnailUrl")] | null            | [null]                | null                               | Result.newSuccess(new ArticleListResult(articleDetailResults: [new QueryArticleDetailResult(id: "aaa", photoUrl: "checkArticleImgResponse", photoThumbnailUrl: "checkArticleImgResponse", actionDurationTimeAvg: "1秒", marketingEventCount: 0, accessCount: 2, leadCount: 1, objectLookUpCount: 2, materialTags: [new MaterialTagResult(name: "buildTagNameResponse")]), new QueryArticleDetailResult(id: "bbb", photoUrl: "checkArticleImgResponse", photoThumbnailUrl: "photoThumbnailUrl", "photoThumbnailAPath": "photoThumbnailAPath", title: "title", summary: "summary", belong: 2, marketingEventCount: 0, accessCount: 0, leadCount: 0, objectLookUpCount: 0, actionDurationTimeAvg: "0秒")]))
    }


    def "updateArticleStatusTest"() {
        given:
        articleDAO.updateArticleStatus(*_) >> updateArticleStatusMock

        when:
        Result result = articleServiceImpl.updateArticleStatus("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                                                                                | updateArticleStatusMock | resultMock
        new UpdateArticleArg(id: "111", status: 1)                                         | 0                       | Result.newError(SHErrorCode.UPDATE_FAIL)
        new UpdateArticleArg(id: "111", status: ArticleStatusEnum.DELETE.getStatus())      | 1                       | Result.newSuccess()
        new UpdateArticleArg(id: "111", status: ArticleStatusEnum.STOP_USING.getStatus())  | 1                       | Result.newSuccess()
        new UpdateArticleArg(id: "111", status: ArticleStatusEnum.START_USING.getStatus()) | 1                       | Result.newSuccess()
    }


    def "addCustomArticlesTest"() {
        given:
        articleDAO.getById(*_) >> new ArticleEntity()
        articleDAO.updateArticleParsedContentPath(*_) >> 0
        articleDAO.queryArticleCountByName(*_) >> 0
        articleDAO.getById(*_) >> new ArticleEntity(title: "titleb")
        articleDAO.updateArticleParsedContentPath(*_) >> 0
        articleDAO.queryArticleCountByName(*_) >> queryArticleCountByNameMock
        articleDAO.getBindObject(*_) >> new ActivityBindObjectEntity()
        articleDAO.bindObject(*_) >> 0
        articleDAO.unBindObject(*_) >> 0
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 0
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity()
        fileManager.uploadFileToAWarehouse(*_) >> "uploadFileToAWarehouseResponse"
        fileManager.uploadTmpToAWarehouse(*_) >> "uploadTmpToAWarehouseResponse"
        imageCreator.getImageDrawer(*_) >> null
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >>> addOrUpdatePhotoByPhotoTargetTypeMock
        photoManager.addOrUpdatePhotoByCutOffset(*_) >> true
        fileV2Manager.parseArticleContent(*_) >> "parseArticleContentResponse"
        fileV2Manager.uploadToApath(*_) >> "uploadToApathResponse"
        fileV2Manager.getApathByTApath(*_) >> getApathByTApathMock
        fileV2Manager.deleteFilesByApath(*_) >> true
        objectTagManager.addOrUpdateObjectTag(*_) >> true
        articleDAOManager.addArticle(*_) >> addArticleMock
        articleDAOManager.updateArticle(*_) >> updateArticleMock
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> new HexagonSiteObjectEntity()
        hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(*_) >> 0
        imageCreator.getImageDrawer(*_) >> new ImageDrawer() {
            @Override
            String draw(Map<String, Object> params) {
                return "draw";
            }
        }
        def spy = Spy(articleServiceImpl)
        spy.getArticlePath(*_) >> getArticlePathMock

        when:
        Result<AddCustomArticlesResult> result = spy.addCustomArticles(arg)
        then:
        result.getErrCode() == resultMock
        where:
        arg                                   | queryArticleCountByNameMock | getApathByTApathMock | getArticlePathMock | addOrUpdatePhotoByPhotoTargetTypeMock | addArticleMock | updateArticleMock | resultMock
        new AddCustomArticlesArg(title: "aa") | 1                           | null                 | null               | [true, true]                          | true           | false             | Result.newError(SHErrorCode.ARTICLE_NAME_EXIST).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "TA_A") | 0 | null | null | [true, true] | true | false | Result.newError(SHErrorCode.ADD_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "N_") | 0 | null | new FileV2Manager.FileManagerPicResult() | [true, true] | true | false | Result.newError(SHErrorCode.PARAMS_ERROR).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_") | 0 | null | new FileV2Manager.FileManagerPicResult() | [false, false] | true | false | Result.newError(SHErrorCode.ADD_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_") | 0 | null | new FileV2Manager.FileManagerPicResult() | [true, false] | true | false | Result.newError(SHErrorCode.ADD_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_") | 0 | null | null | [true, true] | true | false | Result.newError(SHErrorCode.ADD_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_") | 0 | null | "content" | [true, true] | false | false | Result.newError(SHErrorCode.ADD_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_", formId: "formid", bindObjectType: BindObjectType.CUSTOMIZE_FORM.getType(), tagNameList: TagNameList.convert([new TagName(firstTagName: "tag1", secondTagName: "tag2")]), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | 0 | null | "content" | [true, true] | true | false | Result.newSuccess().getErrCode()
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_", formId: "formid", bindObjectType: BindObjectType.PRODUCT.getType(), tagNameList: TagNameList.convert([new TagName(firstTagName: "tag1", secondTagName: "tag2")]), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | 0 | null | "content" | [true, true] | true | false | Result.newSuccess().getErrCode()
        new AddCustomArticlesArg(title: "bb", coverTapath: "A_", formId: "formid", bindObjectType: BindObjectType.HEXAGON_SITE.getType(), tagNameList: TagNameList.convert([new TagName(firstTagName: "tag1", secondTagName: "tag2")]), originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | 0 | null | "content" | [true, true] | true | false | Result.newSuccess().getErrCode()
        new AddCustomArticlesArg(title: "aa", id: "id1") | 1 | null | null | [true, true] | true | false | Result.newError(SHErrorCode.ARTICLE_NAME_EXIST).getErrCode();
        new AddCustomArticlesArg(title: "aa", id: "id1", coverTapath: "TA_A") | 0 | null | null | [true, true] | true | false | Result.newError(SHErrorCode.UPDATE_COVER_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "aa", id: "id1", coverTapath: "A_") | 0 | null | null | [false, false] | true | false | Result.newError(SHErrorCode.UPDATE_COVER_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "aa", id: "id1", coverTapath: "A_", updateContent: true) | 0 | null | null | [true, false] | true | false | Result.newError(SHErrorCode.UPDATE_COVER_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "aa", id: "id1", coverTapath: "A_", updateContent: true) | 0 | null | null | [true, true] | true | false | Result.newError(SHErrorCode.UPDATE_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "aa", id: "id1", coverTapath: "A_", updateContent: true, status: 2) | 0 | null | "content" | [true, true] | true | false | Result.newError(SHErrorCode.UPDATE_FAIL).getErrCode();
        new AddCustomArticlesArg(title: "aa", id: "id1", bindObjectType: BindObjectType.CUSTOMIZE_FORM.getType(), coverTapath: "A_", updateContent: true, status: 2, originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | 0 | null | "content" | [true, true] | true | true | Result.newSuccess().getErrCode()
        new AddCustomArticlesArg(title: "aa", id: "id1", bindObjectType: BindObjectType.HEXAGON_SITE.getType(), coverTapath: "A_", updateContent: true, status: 2, originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | 0 | null | "content" | [true, true] | true | true | Result.newSuccess().getErrCode()
        new AddCustomArticlesArg(title: "aa", id: "id1", bindObjectType: BindObjectType.PRODUCT.getType(), coverTapath: "A_", updateContent: true, status: 2, originalImageAPath: "path", cutOffsetList: [new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()), new PhotoCutOffset(top: "0", left: "0", width: "0", height: "0", photoTargetType: PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType())]) | 0 | null | "content" | [true, true] | true | true | Result.newSuccess().getErrCode()

    }


    def "queryArticleDetailTest"() {
        given:
        articleDAO.getById(*_) >> getByIdMock
        articleDAO.getBindObject(*_) >> new ActivityBindObjectEntity()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name", title: "title"))
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> getObjectBindingFormMock
        photoManager.querySinglePhoto(*_) >> querySinglePhotoMock
        photoManager.querySingleCpathPhoto(*_) >>> new PhotoEntity()
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        fileV2Manager.downloadAFile(*_) >> null
        objectTagManager.queryObjectTag(*_) >> new ObjectTagEntity()
        hexagonSiteManager.getBindHexagonSiteByObject(*_) >> new HexagonSiteEntity()
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> getObjectBindingHexagonSiteMock
        productDAO.queryProductDetail(*_) >> new ProductEntity()
        materialRelationDao.queryMaterialRelationByObjectId(*_) >> new MaterialRelationEntity(sharePosterAPath: "sharePosterAPath")
        objectSloganRelationService.getSlogan(*_) >> new Result<ObjectSloganResult>(0, "errMsg", new ObjectSloganResult())
        when:
        Result<QueryArticleDetailResult> result = articleServiceImpl.queryArticleDetail(arg)
        then:
        result.getErrCode() == resultMock
        where:
        arg                                         | getByIdMock                                                                          | querySinglePhotoMock | getObjectBindingFormMock                            | getObjectBindingHexagonSiteMock | resultMock
        new QueryArticleDetailArg(articleId: "111") | null                                                                                 | null                 | null                                                | null                            | Result.newError(SHErrorCode.NO_DATA).getErrCode()
        new QueryArticleDetailArg(articleId: "111") | new ArticleEntity()                                                                  | null                 | null                                                | null                            | Result.newError(SHErrorCode.NO_DATA).getErrCode()
        new QueryArticleDetailArg(articleId: "111") | new ArticleEntity(summary: "summary", title: "title")                                | new PhotoEntity()    | null                                                | null                            | Result.newError(SHErrorCode.NO_DATA).getErrCode()
        new QueryArticleDetailArg(articleId: "111") | new ArticleEntity(id: "id", summary: "summary", title: "title", articlePath: "path") | new PhotoEntity()    | new CustomizeFormDataObjectEntity(formId: "formId") | null                            | Result.newSuccess(new QueryArticleDetailResult(id: "id", title: "title", summary: "summary", articlePath: "path", formData: new QueryArticleDetailResult.FormData(formId: "formId", formTitle: "title", formName: "name"), bindObjectType: 16, sharePosterAPath: "sharePosterAPath", sharePosterUrl: "getUrlByPathResponse")).getErrCode()
        new QueryArticleDetailArg(articleId: "111") | new ArticleEntity(id: "id", summary: "summary", title: "title", articlePath: "path") | new PhotoEntity()    | null                                                | null                            | Result.newSuccess(new QueryArticleDetailResult(id: "id", title: "title", summary: "summary", articlePath: "path", formData: new QueryArticleDetailResult.FormData(formId: "formId", formTitle: "title", formName: "name"), bindObjectType: 16, sharePosterAPath: "sharePosterAPath", sharePosterUrl: "getUrlByPathResponse")).getErrCode()
        new QueryArticleDetailArg(articleId: "111") | new ArticleEntity(id: "id", summary: "summary", title: "title", articlePath: "path") | new PhotoEntity()    | null                                                | new HexagonSiteObjectEntity()   | Result.newSuccess(new QueryArticleDetailResult(id: "id", title: "title", summary: "summary", articlePath: "path", formData: new QueryArticleDetailResult.FormData(formId: "formId", formTitle: "title", formName: "name"), bindObjectType: 16, sharePosterAPath: "sharePosterAPath", sharePosterUrl: "getUrlByPathResponse")).getErrCode()

    }


    def "previewArticleTest"() {
        given:
        fileV2Manager.uploadToTApath(*_) >> uploadToTApathMock
        redisManager.getPreviewArticleTempValue(*_) >> getPreviewArticleTempValueMock
        qrCodeManager.createQRCode(*_) >> createQRCodeMock

        when:
        Result<PreviewArticleResult> result = articleServiceImpl.previewArticle(arg)
        then:
        result.getErrCode() == resultMock
        where:
        arg                                                                                                                                                                                                              | uploadToTApathMock | createQRCodeMock                       | getPreviewArticleTempValueMock | resultMock
        new PreviewArticleArg(content: "content")                                                                                                                                                                        | null               | null                                   | null                           | Result.newError(SHErrorCode.ADD_FAIL).getErrCode()
        new PreviewArticleArg(content: "content")                                                                                                                                                                        | "path"             | null                                   | null                           | Result.newError(SHErrorCode.PREVIEW_ARTICLE_CARD_QRURL_CREATE_FAIL).getErrCode()
        new PreviewArticleArg(content: "content", title: "title", creator: "creator")                                                                                                                                    | "path"             | new QRCodeManager.CreateQRCodeResult() | null                           | Result.newSuccess().getErrCode()
        new PreviewArticleArg(previewArticleId: "id", content: "content", title: "title")                                                                                                                                | "path"             | new QRCodeManager.CreateQRCodeResult() | null                           | Result.newError(SHErrorCode.NO_DATA).getErrCode()
        new PreviewArticleArg(previewArticleId: "id", content: "content", title: "title", creator: "creator", formStyleType: 2, formButtonName: "formButtonName", buttonStyle: new ButtonStyle(), isUpdateContent: true) | "path"             | new QRCodeManager.CreateQRCodeResult() | "{}"                           | Result.newSuccess().getErrCode()
        new PreviewArticleArg(previewArticleId: "id", content: "content", title: "title", creator: "creator", formStyleType: 2, formButtonName: "formButtonName", buttonStyle: new ButtonStyle(), isUpdateContent: true) | null | new QRCodeManager.CreateQRCodeResult() | "{}" | Result.newError(SHErrorCode.UPDATE_FAIL).getErrCode()
    }


    def "updateWebCrawlerArticleTest"() {
        given:
        articleDAO.updateArticlePhoto(*_) >> true
        articleDAO.updateArticleSummary(*_) >> 0
        articleDAO.getBindObject(*_) >> new ActivityBindObjectEntity()
        articleDAO.bindObject(*_) >> 0
        articleDAO.unBindObject(*_) >> 0
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 0
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity()
        imageCreator.getImageDrawer(*_) >> new ImageDrawer() {
            @Override
            String draw(Map<String, Object> params) {
                return "draw";
            }
        }
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        fileV2Manager.getApathByTApath(*_) >> getApathByTApathMock
        imageCreator.getImageDrawer(*_) >> null
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> new HexagonSiteObjectEntity()
        hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(*_) >> 0

        when:
        Result result = articleServiceImpl.updateWebCrawlerArticle(arg)
        then:
        result == resultMock
        where:
        arg                                                                                                                              | getApathByTApathMock                                                     | resultMock
        new UpdateWebCrawlerArticleArg(id: "id", photoUrl: "TA_A")                                                                       | null                                                                     | Result.newError(SHErrorCode.SYSTEM_ERROR)
        new UpdateWebCrawlerArticleArg(id: "id", photoUrl: "TA_A", bindObjectType: BindObjectType.CUSTOMIZE_FORM.getType())              | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | Result.newSuccess()
        new UpdateWebCrawlerArticleArg(id: "id", photoUrl: "TA_A", bindObjectType: BindObjectType.HEXAGON_SITE.getType())                | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | Result.newSuccess()
        new UpdateWebCrawlerArticleArg(id: "id", photoUrl: "TA_A", bindObjectType: BindObjectType.PRODUCT.getType(), summary: "summary") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | Result.newSuccess()
    }


    def "addOrUpdateArticlePhotoTest"() {
        given:
        imageCreator.getImageDrawer(*_) >> null
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        fileV2Manager.getApathByTApath(*_) >> new FileV2Manager.FileManagerPicResult()
        imageCreator.getImageDrawer(*_) >> new ImageDrawer() {
            @Override
            String draw(Map<String, Object> params) {
                return "draw";
            }
        }
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >>> addOrUpdatePhotoByPhotoTargetTypeMock
        fileV2Manager.getApathByTApath(*_) >> getApathByTApathMock

        when:
        ArticleEntity result = articleServiceImpl.addOrUpdateArticlePhoto(arg, new ArticleEntity(fsEa: "ea", fsUserId: 1000))
        then:
        result == resultMock
        where:
        arg                                    | getApathByTApathMock                                                     | addOrUpdatePhotoByPhotoTargetTypeMock | resultMock
        new AddArticleArg(coverTapath: "TA_A") | null                                                                     | [true, true]                          | null
        new AddArticleArg(coverTapath: "TA_A") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | [false, true]                         | null
        new AddArticleArg(coverTapath: "TA_A") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | [true, false]                         | null
        new AddArticleArg(coverTapath: "TA_A") | new FileV2Manager.FileManagerPicResult(url: "url", thumbUrl: "thumbUrl") | [true, true]                          | new ArticleEntity(fsEa: "ea", fsUserId: 1000)
    }

    @Unroll
    def "setArticleEntityTest"() {
        given:
        fileManager.uploadFileToAWarehouse(*_) >> "uploadFileToAWarehouseResponse"
        fileManager.uploadTmpToAWarehouse(*_) >> "uploadTmpToAWarehouseResponse"
        photoManager.savePhotoByAapath(*_) >> true
        outArticleService.crawlerArticleContent(*_) >> new ModelResult<CrawlerArticleContentResult>(0, "errMsg", new CrawlerArticleContentResult(webPage: "ddd"))
        when:
        ArticleEntity result = articleServiceImpl.setArticleEntity(arg)
        then:
        resultMock == (result != null)
        where:
        arg                           | crawlerArticleContentMock                                                                               | resultMock
        new AddArticleArg(url: "url", status: 1) | new ModelResult<CrawlerArticleContentResult>()                                                          | false
        new AddArticleArg(url: "url") | new ModelResult<CrawlerArticleContentResult>(data: new CrawlerArticleContentResult(webPage: "webpage")) | true
    }


    def "getArticlePathTest"() {
        given:
        fileManager.uploadFileToAWarehouse(*_) >> "uploadFileToAWarehouseResponse"
        fileManager.uploadTmpToAWarehouse(*_) >> "uploadTmpToAWarehouseResponse"

        when:
        String result = articleServiceImpl.getArticlePath("content", arg)
        then:
        result == resultMock
        where:
        arg   | resultMock
        false | "uploadFileToAWarehouseResponse"
        true  | "uploadTmpToAWarehouseResponse"
    }


    def "editArticleGroupTest"() {
        given:
        objectGroupManager.editGroup(*_) >> new Result<EditObjectGroupResult>(0, "errMsg", new EditObjectGroupResult())
        objectGroupManager.isAppAdmin(*_) >> isAppAdminMock

        when:
        Result<EditObjectGroupResult> result = articleServiceImpl.editArticleGroup("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                                  | isAppAdminMock | resultMock
        new EditObjectGroupArg(name: "全部")  | true           | Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME)
        new EditObjectGroupArg(name: "11")  | true            | new Result<EditObjectGroupResult>(0, "errMsg", new EditObjectGroupResult())
        new EditObjectGroupArg(name: "11")  | false           | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
    }


    def "deleteArticleGroupTest"() {
        given:
        objectGroupManager.deleteGroup(*_) >> Result.newSuccess()
        objectGroupManager.isAppAdmin(*_) >> isAppAdminMock

        when:
        Result<Void> result = articleServiceImpl.deleteArticleGroup("ea", 0, arg)
        then:
        result == resultMock
        where:
        arg                        | isAppAdminMock | resultMock
        new DeleteObjectGroupArg() | true           | Result.newSuccess()
        new DeleteObjectGroupArg() | false          | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
    }


    def "setArticleGroupTest"() {
        given:
        articleDAO.getByIds(*_) >> getByIdsMock
        objectGroupDAO.getById(*_) >> getByIdMock
        articleDAO.getByIds(*_) >> [new ArticleEntity()]
        objectGroupDAO.getById(*_) >> new ObjectGroupEntity()
        objectGroupRelationDAO.batchInsert(*_) >> 0
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0

        when:
        Result<Void> result = articleServiceImpl.setArticleGroup("ea", 0, new SetObjectGroupArg(objectIdList: ["id1", "id2"]))
        then:
        result == resultMock
        where:
        getByIdsMock | getByIdMock | resultMock
        null         | null        | Result.newError(SHErrorCode.ARTICLE_NOT_EXIST);
        [new ArticleEntity()] | null | Result.newError(SHErrorCode.PART_COUPON_TEMPLATE_NOT_FOUND)
        [new ArticleEntity(), new ArticleEntity()] | null | Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST)
        [new ArticleEntity(), new ArticleEntity()] | new ObjectGroupEntity() | Result.newSuccess()
    }


    def "deleteArticleBatchTest"() {
        given:
        articleDAO.getByIds(*_) >> [new ArticleEntity()]
        articleDAO.getByIds(*_) >> getByIdsMock
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(*_) >> 0
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0
        contentMarketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(*_) >> 0

        when:
        Result<Void> result = articleServiceImpl.deleteArticleBatch(arg, 1000, new DeleteMaterialArg())
        then:
        result == resultMock
        where:
        arg  | getByIdsMock                                   | resultMock
        null | null                                           | new Result<>(SHErrorCode.ARTICLE_NOT_EXIST)
        null | [new ArticleEntity(status: 1)]                 | new Result<>(SHErrorCode.DEL_ARTICLE_FAIL_TIP)
        null | [new ArticleEntity(status: 2)]                 | new Result<>(SHErrorCode.USER_NOT_BIND_EA)
        "ea" | [new ArticleEntity(status: 2, title: "title")] | Result.newSuccess()
    }


    def "topArticleTest"() {
        given:
        articleDAO.getById(*_) >> new ArticleEntity()
        articleDAO.getById(*_) >> getByIdMock

        when:
        Result<Void> result = articleServiceImpl.topArticle("ea", 0, new TopMaterialArg())
        then:
        result == new Result<Void>(0, "errMsg", null)
        result == resultMock
        where:
        getByIdMock | resultMock
        null        | Result.newError(SHErrorCode.ARTICLE_NOT_EXIST);
        new ArticleEntity() | Result.newSuccess();
    }


    def "cancelTopArticleTest"() {

        when:
        Result<Void> result = articleServiceImpl.cancelTopArticle("ea", 0, new CancelMaterialTopArg())
        then:
        result == new Result<Void>(0, "errMsg", null)
        result == Result.newSuccess()
    }


    def "addArticleGroupRoleTest"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> true
        objectGroupManager.isAppAdmin(*_) >> isAppAdminMock

        when:
        Result<Void> result = articleServiceImpl.addArticleGroupRole("ea", 0, new SaveObjectGroupVisibleArg())
        then:
        result == new Result<Void>(0, "errMsg", null)
        result == resultMock
        where:
        isAppAdminMock | resultMock
        true           | Result.newSuccess()
        false          | Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
    }


    def "listArticleGroupTest"() {
        given:
        articleDAO.queryCountByUnGrouped(*_) >> 0
        articleDAO.queryCountCreateByMe(*_) >> 0
        articleDAO.queryAccessibleCount(*_) >> 0
        articleDAO.queryUnGroupAndCreateByMeCount(*_) >> 0
        objectGroupManager.getShowGroup(*_) >> new ObjectGroupListResult()

        when:
        Result<ObjectGroupListResult> result = articleServiceImpl.listArticleGroup("ea", 1000, new ListGroupArg(useType: 0))
        then:
        result == resultMock
        where:
        getShowGroupMock                                                                             | resultMock
        new ObjectGroupListResult(objectGroupList: [new ListObjectGroupResult(groupId: "groupid1")]) | Result.newSuccess(new ObjectGroupListResult(objectGroupList: [new ListObjectGroupResult(groupId: -1, groupName: "全部", objectCount: 0, system: true, roleNameList: ["全部"]), new ListObjectGroupResult(groupId: -2, groupName: "我创建的", objectCount: 0, system: true, roleNameList: ["全部"]), new ListObjectGroupResult(groupId: -3, groupName: "未分组", objectCount: 0, system: true, roleNameList: ["全部"]), new ListObjectGroupResult(groupId: "groupid1", objectCount: 0, system: false)]))
        new ObjectGroupListResult(objectGroupList: [])                                               | Result.newSuccess(new ObjectGroupListResult(objectGroupList: [new ListObjectGroupResult(groupId: -1, groupName: "全部", objectCount: 0, system: true, roleNameList: ["全部"]), new ListObjectGroupResult(groupId: -2, groupName: "我创建的", objectCount: 0, system: true, roleNameList: ["全部"]), new ListObjectGroupResult(groupId: -3, groupName: "未分组", objectCount: 0, system: true, roleNameList: ["全部"])]))
    }


    def "listArticleGroup4OuterTest"() {
        given:
        objectGroupManager.getShowGroup4Outer(*_) >> new ObjectGroupListResult()

        when:
        Result<ObjectGroupListResult> result = articleServiceImpl.listArticleGroup4Outer("upstreamEA", "outTenantId", "outUserId", new ListGroupArg())
        then:
        result == new Result<ObjectGroupListResult>(0, "errMsg", new ObjectGroupListResult())
        result.getErrCode() == new Result<ObjectGroupListResult>(0, "errMsg", new ObjectGroupListResult()).getErrCode()
    }


    def "getGroupRoleTest"() {
        given:
        objectGroupRelationVisibleManager.getRoleRelationByGroupId(*_) >> [new ObjectGroupRoleRelationEntity()]

        when:
        Result<List<String>> result = articleServiceImpl.getGroupRole("groupId")
        then:
        result == new Result<List<String>>(0, "errMsg", ["data"])
        result.getErrCode() == new Result<List<String>>(0, "errMsg", ["data"]).getErrCode()
    }

}