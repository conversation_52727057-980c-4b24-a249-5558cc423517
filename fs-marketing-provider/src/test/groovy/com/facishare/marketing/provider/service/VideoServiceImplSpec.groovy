package com.facishare.marketing.provider.service


import com.facishare.mankeep.api.service.KmVideoService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.arg.CancelMaterialTopArg
import com.facishare.marketing.api.arg.DeleteMaterialArg
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg
import com.facishare.marketing.api.arg.TopMaterialArg
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg
import com.facishare.marketing.api.arg.video.*
import com.facishare.marketing.api.result.EditObjectGroupResult
import com.facishare.marketing.api.result.ListObjectGroupResult
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO
import com.facishare.marketing.provider.dao.VideoDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO
import com.facishare.marketing.provider.dto.video.VideoEntityDTO
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.VideoEntity
import com.facishare.marketing.provider.innerResult.qywx.UploadMediaResult
import com.facishare.marketing.provider.manager.FsAddressBookManager
import com.facishare.marketing.provider.manager.MaterialTagManager
import com.facishare.marketing.provider.manager.ObjectGroupManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.marketing.provider.manager.qywx.MomentManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import spock.lang.Specification
import spock.lang.Unroll

class VideoServiceImplSpec extends Specification {

    def videoServiceImpl = new VideoServiceImpl()

    def videoDAO = Mock(VideoDAO)
    def kmVideoService = Mock(KmVideoService)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def momentManager = Mock(MomentManager)
    def httpManager = Mock(HttpManager)
    def materialTagManager = Mock(MaterialTagManager)
    def qywxManager = Mock(QywxManager)
    def qywxMiniappConfigDAO = Mock(QywxMiniappConfigDAO)
    def wechatAccountManager = Mock(WechatAccountManager)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def objectGroupManager = Mock(ObjectGroupManager)
    def objectGroupDAO = Mock(ObjectGroupDAO)
    def objectGroupRelationDAO = Mock(ObjectGroupRelationDAO)
    def objectTopManager = Mock(ObjectTopManager)
    def objectGroupRelationVisibleManager = Mock(ObjectGroupRelationVisibleManager)
    def mktContentMgmtLogObjManager = Mock(MktContentMgmtLogObjManager)
    def marketingMaterialInstanceLogManager = Mock(MarketingMaterialInstanceLogManager)
    def appMenuTemplateService = Mock(AppMenuTemplateService)

    def setup() {
        videoServiceImpl.videoDAO = videoDAO
        videoServiceImpl.kmVideoService = kmVideoService
        videoServiceImpl.fsAddressBookManager = fsAddressBookManager
        videoServiceImpl.momentManager = momentManager
        videoServiceImpl.httpManager = httpManager
        videoServiceImpl.materialTagManager = materialTagManager
        videoServiceImpl.qywxManager = qywxManager
        videoServiceImpl.miniappConfigDAO = qywxMiniappConfigDAO
        videoServiceImpl.wechatAccountManager = wechatAccountManager
        videoServiceImpl.agentConfigDAO = agentConfigDAO
        videoServiceImpl.objectGroupManager = objectGroupManager
        videoServiceImpl.objectGroupDAO = objectGroupDAO
        videoServiceImpl.objectGroupRelationDAO = objectGroupRelationDAO
        videoServiceImpl.objectTopManager = objectTopManager
        videoServiceImpl.objectGroupRelationVisibleManager = objectGroupRelationVisibleManager
        videoServiceImpl.mktContentMgmtLogObjManager = mktContentMgmtLogObjManager
        videoServiceImpl.marketingMaterialInstanceLogManager = marketingMaterialInstanceLogManager
        videoServiceImpl.appMenuTemplateService = appMenuTemplateService
    }

    @Unroll
    def "addVideoUrlTest"() {
        given:
        videoDAO.addVideo(*_) >> 1
        marketingMaterialInstanceLogManager.sendPersistLog(*_) >> {}
        AddVideoUrlArg arg = new AddVideoUrlArg()
        arg.setTargetType(0)

        when:
        Result<String> result = videoServiceImpl.addVideoUrl(arg)

        then:
        result.isSuccess()

    }

    @Unroll
    def "queryVideoPlayUrlByIdTest"() {
        given:
        videoDAO.getById(_) >> videoEntity
        when:
        videoServiceImpl.queryVideoPlayUrlById("")
        then:
        noExceptionThrown()
        where:
        videoEntity << [null, new VideoEntity()]
    }

    @Unroll
    def "uploadToQVideo"() {
        given:
        kmVideoService.addTransVideo(_) >> modelResult
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(*_) >> {}
        videoDAO.getByEaAndToken(*_) >> new VideoEntity()

        VideoUploadArg arg = new VideoUploadArg()
        arg.setTargetType(0)

        when:
        videoServiceImpl.uploadToQVideo(arg)
        then:
        noExceptionThrown()
        where:
        modelResult << [null, new ModelResult(errCode: 0)]

    }

    @Unroll
    def "uploadCancled"() {
        given:
        videoDAO.getById(_) >> videoEntity
        kmVideoService.uploadCancled(_) >> modelResult

        VideoCancledArg arg = new VideoCancledArg()

        when:
        videoServiceImpl.uploadCancled(arg)
        then:
        noExceptionThrown()
        where:
        videoEntity       | modelResult
        null              | null
        new VideoEntity() | null
        new VideoEntity() | new ModelResult(errCode: 0)

    }

    @Unroll
    def "deleteVideo"() {
        given:
        videoDAO.getById(_) >> videoEntity
        kmVideoService.deleteVideo(_) >> modelResult
        objectTopManager.deleteByObjectIdAndObjectType(*_) >> {}
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 1
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(*_) >> {}
        DeleteVideoArg arg = new DeleteVideoArg()

        when:
        videoServiceImpl.deleteVideo(arg)
        then:
        noExceptionThrown()
        where:
        videoEntity       | modelResult
        null              | null
        new VideoEntity() | null
        new VideoEntity() | new ModelResult(errCode: 0)
    }

    @Unroll
    def "queryList"() {
        given:
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> objectGroupEntityList
        objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(*_) >> new ArrayList<String>()
        videoDAO.getAccessiblePage(*_) >> videoEntityList
        materialTagManager.buildTagName(*_) >> materialTagMap
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> fsEmployeeMsgMap
        appMenuTemplateService.getMenuTagRule(*_) >> appMenuTagResult
        objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(*_) >> [new ObjectGroupEntity(id: "id")]
        appMenuTemplateService.needStrictCheckGroup(*_) >> true
        when:
        videoServiceImpl.queryList(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                  | videoEntityList                               | objectGroupEntityList           | materialTagMap                                           | fsEmployeeMsgMap                                                                                            | appMenuTagResult
        new ListVideosArg(pageNum: 0, pageSize: 0, userId: 1)                | []                                            | [new ObjectGroupEntity()]       | new HashMap<String, List<String>>()                      | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>()                                                  | new Result(errCode: 0, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))
        new ListVideosArg(pageNum: 0, pageSize: 0, userId: 1)                | [new VideoEntityDTO(uploadUid: "0", id: "0")] | [new ObjectGroupEntity()]       | new HashMap<String, List<String>>() << ["0": ["0", "1"]] | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>() << [0: new FsAddressBookManager.FSEmployeeMsg()] | new Result(errCode: -1, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "-3", userId: 1)   | []                                            | [new ObjectGroupEntity()]       | new HashMap<String, List<String>>()                      | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>()                                                  | new Result(errCode: -1, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "-2", userId: 1)   | []                                            | [new ObjectGroupEntity()]       | new HashMap<String, List<String>>()                      | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>()                                                  | new Result(errCode: -1, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "-4", userId: 1)   | []                                            | [new ObjectGroupEntity(id: -4)] | new HashMap<String, List<String>>()                      | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>()                                                  | new Result(errCode: -1, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "-4", userId: 1)   | []                                            | [new ObjectGroupEntity()]       | new HashMap<String, List<String>>()                      | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>()                                                  | new Result(errCode: -1, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "id", userId: 1) | []                                            | [new ObjectGroupEntity()]       | new HashMap<String, List<String>>()                      | new HashMap<Integer, FsAddressBookManager.FSEmployeeMsg>()                                                  | new Result(errCode: -1, data: new AppMenuTagVO(tagOperator: 1, tagIdList: ["Id"]))

    }

    @Unroll
    def "queryList4Outer"() {
        given:
        objectGroupRelationVisibleManager.getAccessibleGroup4Outer(*_) >> [new ObjectGroupEntity(id: "0")]
        objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(*_) >> []
        videoDAO.getAccessiblePage4Outer(*_) >> videoEntityList
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [0: new FsAddressBookManager.FSEmployeeMsg()]

        when:
        videoServiceImpl.queryList4Outer(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                       | videoEntityList
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "0")  | []
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "1")  | []
        new ListVideosArg(pageNum: 0, pageSize: 0, groupId: "-1") | [new VideoEntityDTO(uploadUid: "0")]
    }

    @Unroll
    def "uploadToQywxMomentVideo"() {
        given:
        if (id == null) {
            momentManager.uploadAttachment(*_) >> { throw new RuntimeException("upload failed") }
        } else {
            momentManager.uploadAttachment(*_) >> id
        }

        when:
        def result = videoServiceImpl.uploadToQywxMomentVideo("", new byte[1024], "")

        then:
        result.isSuccess()

        where:
        id << [null, "0"]
    }

    @Unroll
    def "uploadToQywxWelcomeVideo"() {
        given:
        if (flag == 1) {
            httpManager.uploadFile(*_) >> { throw new RuntimeException("upload failed") }
        } else {
            httpManager.uploadFile(*_) >> mediaResult
        }

        when:
        def result = videoServiceImpl.uploadToQywxWelcomeVideo("", new byte[0], "")

        then:
        result != null

        where:
        mediaResult                                   | flag
        null                                          | 0
        new UploadMediaResult(errcode: 0, mediaId: 0) | 0
        null                                          | 1
    }

    @Unroll
    def "editVideoGroup"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdmin
        objectGroupManager.editGroup(*_) >> new Result<EditObjectGroupResult>()

        when:
        def result = videoServiceImpl.editVideoGroup("", 0, arg)

        then:
        result != null

        where:
        arg                                  | isAppAdmin
        new EditObjectGroupArg()             | false
        new EditObjectGroupArg(name: "全部") | true
        new EditObjectGroupArg()             | true

    }

    @Unroll
    def "deleteVideoGroup"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdmin
        objectGroupManager.deleteGroup(*_) >> new Result()

        when:
        def result = videoServiceImpl.deleteVideoGroup("", 0, new DeleteObjectGroupArg())

        then:
        result != null

        where:
        isAppAdmin << [false, true]
    }

    @Unroll
    def "setVideoGroup"() {
        given:
        videoDAO.getByIdList(*_) >> videoEntityList
        objectGroupDAO.getById(*_) >> objectGroupEntity

        when:
        def result = videoServiceImpl.setVideoGroup("", 0, arg)

        then:
        result != null

        where:
        arg                                             | videoEntityList     | objectGroupEntity
        new SetObjectGroupArg()                         | null                | new ObjectGroupEntity()
        new SetObjectGroupArg(objectIdList: ["0", "1"]) | [new VideoEntity()] | new ObjectGroupEntity()
        new SetObjectGroupArg(objectIdList: ["0"])      | [new VideoEntity()] | null
        new SetObjectGroupArg(objectIdList: ["0"])      | [new VideoEntity()] | new ObjectGroupEntity()
    }

    @Unroll
    def "deleteVideoBatch"() {
        given:
        videoDAO.getByIdList(*_) >> videoEntityList
        kmVideoService.deleteVideo(*_) >> modelResult

        when:
        def result = videoServiceImpl.deleteVideoBatch("", 0, new DeleteMaterialArg())

        then:
        result != null

        where:
        videoEntityList                  | modelResult
        []                               | null
        [new VideoEntity(videoName: "")] | null
        [new VideoEntity(id: "0")]       | new ModelResult<Void>(errCode: 0)
    }

    @Unroll
    def "topVideo"() {
        given:
        videoDAO.getById(*_) >> entity

        when:
        def result = videoServiceImpl.topVideo("", 0, new TopMaterialArg())

        then:
        result != null

        where:
        entity << [null, new VideoEntity()]
    }

    @Unroll
    def "cancelTopVideo"() {
        when:
        def reslut = videoServiceImpl.cancelTopVideo("", 0, new CancelMaterialTopArg())

        then:
        reslut.isSuccess()
    }

    @Unroll
    def "addVideoGroupRole"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> isAppAdmin

        when:
        def result = videoServiceImpl.addVideoGroupRole("", 0, new SaveObjectGroupVisibleArg())

        then:
        result != null

        where:
        isAppAdmin << [false, true]
    }

    @Unroll
    def "listVideoGroup"() {
        given:
        objectGroupManager.getShowGroup(*_) >> customizeGroupListVO

        when:
        def result = videoServiceImpl.listVideoGroup("", 0, new ListGroupArg(useType: 0))

        then:
        result.isSuccess()

        where:
        customizeGroupListVO << [new ObjectGroupListResult(objectGroupList: []), new ObjectGroupListResult(objectGroupList: [new ListObjectGroupResult(groupId: "0")])]
    }

    @Unroll
    def "listVideoGroup4Outer"() {
        given:
        objectGroupManager.getShowGroup4Outer(*_) >> new ObjectGroupListResult()

        when:
        def result = videoServiceImpl.listVideoGroup4Outer("", "", "", new ListGroupArg())

        then:
        result.isSuccess()

    }

    @Unroll
    def "getGroupRole"() {
        given:
        objectGroupRelationVisibleManager.getRoleRelationByGroupId(*_) >> []

        when:
        def result = videoServiceImpl.getGroupRole("")

        then:
        result.isSuccess()

    }

    @Unroll
    def "updateVideo"() {
        given:
        videoDAO.getById(*_) >> videoEntity

        when:
        def result = videoServiceImpl.updateVideo("", new UpdateVideoArg())

        then:
        result != null

        where:
        videoEntity << [null, new VideoEntity()]
    }

}