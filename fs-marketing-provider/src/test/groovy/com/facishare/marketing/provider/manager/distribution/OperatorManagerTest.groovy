package com.facishare.marketing.provider.manager.distribution

import com.facishare.mankeep.api.outService.result.qrCode.CreateQRCodeResult
import com.facishare.mankeep.api.outService.service.OutQRCodeService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.arg.PageOperatorArg
import com.facishare.marketing.api.result.OperatorResult
import com.facishare.marketing.api.result.PageResult
import com.facishare.marketing.api.result.distribution.OperatorQRCodeResult
import com.facishare.marketing.api.result.distribution.QueryDistributorByOperatorResult
import com.facishare.marketing.api.result.distribution.QueryOperatorClueResult
import com.facishare.marketing.api.result.distribution.QueryOperatorInfoResult
import com.facishare.marketing.api.result.distribution.QueryPlanListResult
import com.facishare.marketing.api.service.SettingService
import com.facishare.marketing.api.vo.QueryDistributorByOperatorVO
import com.facishare.marketing.api.vo.QueryOperatorClueVO
import com.facishare.marketing.api.vo.QueryOperatorInfoVO
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.CardDAO
import com.facishare.marketing.provider.dao.DistributePlanDao
import com.facishare.marketing.provider.dao.OperatorDao
import com.facishare.marketing.provider.dao.distribution.ClueDAO
import com.facishare.marketing.provider.dao.distribution.DistributorApplicationDAO
import com.facishare.marketing.provider.dao.distribution.DistributorDao
import com.facishare.marketing.provider.dao.distribution.OperatorDistributorDAO
import com.facishare.marketing.provider.dto.OperatorDistributorEntityCount
import com.facishare.marketing.provider.dto.distribution.DistributorClueCountDTO
import com.facishare.marketing.provider.dto.distribution.OperatorPlanDTO
import com.facishare.marketing.provider.dto.distribution.QueryDistributorByOperatorDTO
import com.facishare.marketing.provider.entity.CardEntity
import com.facishare.marketing.provider.entity.Operator
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.distribution.ClueEntity
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity
import com.facishare.marketing.provider.entity.distribution.DistributorApplicationEntity
import com.facishare.marketing.provider.entity.distribution.DistributorBaseInfoResult
import com.facishare.marketing.provider.entity.distribution.DistributorEntity
import com.facishare.marketing.provider.entity.distribution.OperatorDistributorEntity
import com.facishare.marketing.provider.entity.distribution.RecruitCountEntity
import com.facishare.marketing.provider.manager.AuthManager
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.FsAddressBookManager
import com.facishare.marketing.provider.manager.PhotoManager
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService
import spock.lang.*

/**
 * Test for OperatorManager
 * <AUTHOR>
 * @date 2024/7/4 16:31
 */
class OperatorManagerTest extends Specification {

    def operatorManager = new OperatorManager()

    def authManager = Mock(AuthManager)
    def operatorDao = Mock(OperatorDao)
    def clueDAO = Mock(ClueDAO)
    def outQRCodeService = Mock(OutQRCodeService)
    def operatorDistributorDAO = Mock(OperatorDistributorDAO)
    def openFsUserAppViewService = Mock(OpenFsUserAppViewService)
    def distributePlanDao = Mock(DistributePlanDao)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def fileV2Manager = Mock(FileV2Manager)
    def photoManager = Mock(PhotoManager)
    def operatorDistributorDao = Mock(OperatorDistributorDAO)
    def distributorApplicationDAO = Mock(DistributorApplicationDAO)
    def distributorDao = Mock(DistributorDao)
    def cardDAO = Mock(CardDAO)
    def distributorManager = Mock(DistributorManager)
    def settingService = Mock(SettingService)

    def setup() {
        operatorManager.authManager = authManager
        operatorManager.operatorDao = operatorDao
        operatorManager.clueDAO = clueDAO
        operatorManager.outQRCodeService = outQRCodeService
        operatorManager.operatorDistributorDAO = operatorDistributorDAO
        operatorManager.openFsUserAppViewService = openFsUserAppViewService
        operatorManager.distributePlanDao = distributePlanDao
        operatorManager.fsAddressBookManager = fsAddressBookManager
        operatorManager.fileV2Manager = fileV2Manager
        operatorManager.photoManager = photoManager
        operatorManager.operatorDistributorDao = operatorDistributorDao
        operatorManager.distributorApplicationDAO = distributorApplicationDAO
        operatorManager.distributorDao = distributorDao
        operatorManager.cardDAO = cardDAO
        operatorManager.distributorManager = distributorManager
        operatorManager.settingService = settingService
    }

    @Unroll
    def "addOperatorByFsUserIdsTest"() {
        given:
        operatorDao.getOperatorByFsUidAndPlanId(*_) >> new Operator()
        operatorDao.getOperatorBatchedByFsUidAndFsEa(*_) >> [new Operator()]
        operatorDao.updateQrUrl(*_) >> true
        operatorDao.batchInsert(*_) >> 0
        outQRCodeService.createQRCode(*_) >> new ModelResult<CreateQRCodeResult>(0, "errMsg", new CreateQRCodeResult())
        distributePlanDao.getDistributePlanByPlanId(*_) >> new DistributePlanEntity()
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [(0): new FsAddressBookManager.FSEmployeeMsg()]
        fileV2Manager.getApathByUrl(*_) >> "getApathByUrlResponse"
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        settingService.addUserRoles(*_) >> new Result<Void>(0, "errMsg", null)

        expect:
        operatorManager.addOperatorByFsUserIds(ea, currentUserId, fsUserIds, planId) == expectedResult

        where:
        currentUserId | planId   | ea   | fsUserIds || expectedResult
        0             | "planId" | "ea" | [0]       || new Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "deleteByOperatorIdsTest"() {
        given:
        authManager.getAppAdmins(*_) >> [0]
        operatorDao.getById(*_) >> new Operator()
        operatorDistributorDAO.queryDistributorEntityCountByOperator(*_) >> null
        settingService.deleteUserRoleByUser(*_) >> new Result<Void>(0, "errMsg", null)

        expect:
        operatorManager.deleteByOperatorIds(ea, currentUserId, operatorId) == expectedResult

        where:
        currentUserId | ea   | operatorId   || expectedResult
        0             | "ea" | "operatorId" || new Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "pageOperatorsTest"() {
        given:
        operatorDao.queryEntityByPager(*_) >> [new Operator(type: 1,id: "00")]
        operatorDistributorDAO.queryDistributorEntityCountByOperator(*_) >> [new OperatorDistributorEntityCount(operatorId: "00",distributorCount: 10)]
        distributePlanDao.queryDistributePlanByIds(*_) >> [new DistributePlanEntity()]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [(0): new FsAddressBookManager.FSEmployeeMsg()]

        expect:
        operatorManager.pageOperators(vo) == expectedResult

        where:
        vo                    || expectedResult
        new PageOperatorArg(pageSize: 10,pageNum: 1) || new Result<PageResult<OperatorResult>>(0, "errMsg", new PageResult<OperatorResult>(0, [new OperatorResult()]))
    }

    @Unroll
    def "isOperatorTest"() {
        given:
        operatorDao.getOperatorByFsUidAndFsEa(*_) >> [new Operator()]

        expect:
        operatorManager.isOperator(ea, userId) == expectedResult

        where:
        ea   | userId || expectedResult
        "ea" | 0      || new Result<Boolean>(0, "errMsg", Boolean.TRUE)
    }

    @Unroll
    def "getQRCodeTest"() {
        given:
        operatorDao.getOperatorByFsUidAndPlanId(*_) >> new Operator()
        operatorDao.updateQrUrl(*_) >> false
        outQRCodeService.createQRCode(*_) >> new ModelResult<CreateQRCodeResult>(0, "errMsg", new CreateQRCodeResult(qrCodeUrl: "qrCodeUrl"))
        distributePlanDao.getDistributePlanByPlanId(*_) >> new DistributePlanEntity()
        fileV2Manager.getApathByUrl(*_) >> "getApathByUrlResponse"
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true

        expect:
        operatorManager.getQRCode(ea, planId, fsUserId) == expectedResult

        where:
        fsUserId | planId   | ea   || expectedResult
        0        | "planId" | "ea" || new Result<OperatorQRCodeResult>(0, "errMsg", new OperatorQRCodeResult())
    }

    @Unroll
    def "queryOperatorInfoTest"() {
        given:
        operatorDao.queryOperatorPlanDtoById(*_) >> new OperatorPlanDTO()
        clueDAO.queryClueCountByDistributorIds(*_) >> 0
        outQRCodeService.createQRCode(*_) >> new ModelResult<CreateQRCodeResult>(0, "errMsg", new CreateQRCodeResult())
        operatorDistributorDAO.getDistributorIdsByOperatorId(*_) >> [new OperatorDistributorEntity()]
        distributePlanDao.getDistributePlanByPlanId(*_) >> new DistributePlanEntity()
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]
        photoManager.savePhotoByAapath(*_) >> true
        operatorDistributorDao.getDistributorIdsByOperatorId(*_) >> [new OperatorDistributorEntity()]

        expect:
        operatorManager.queryOperatorInfo(operatorId, planId) == expectedResult

        where:
        planId   | operatorId   || expectedResult
        "planId" | "operatorId" || new Result<QueryOperatorInfoResult>(0, "errMsg", new QueryOperatorInfoResult())
    }

    @Unroll
    def "queryDistributorByOperatorTest"() {
        given:
        operatorDao.queryDistributorByOperator(*_) >> [new QueryDistributorByOperatorDTO(recruitId: "zzz",createTime: new Date(),distributorId:"getDistributorFormDataMapResponse" )]
        clueDAO.queryClueCountGroupByDistributors(*_) >> [new DistributorClueCountDTO(clueCount: 0)]
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        distributorApplicationDAO.getLatestInfoById(*_) >> new DistributorApplicationEntity()
        distributorDao.queryDistributorById(*_) >> new DistributorEntity()
        distributorDao.getRecruitCounts(*_) >> [new RecruitCountEntity()]
        cardDAO.queryCardInfoByUid(*_) >> new CardEntity()
        distributorManager.getDistributorFormDataMap(*_) >> ["getDistributorFormDataMapResponse": new DistributorBaseInfoResult()]

        expect:
        operatorManager.queryDistributorByOperator(vo) == expectedResult

        where:
        vo                                 || expectedResult
        new QueryDistributorByOperatorVO(pageNum: 1,pageSize: 10) || new Result<PageResult<QueryDistributorByOperatorResult>>(0, "errMsg", new PageResult<QueryDistributorByOperatorResult>(0, [new QueryDistributorByOperatorResult()]))
    }

    @Unroll
    def "queryOperatorClueTest"() {
        given:
        operatorDao.queryOperatorClue(*_) >> [new ClueEntity(createTime: new Date())]

        expect:
        operatorManager.queryOperatorClue(vo) == expectedResult

        where:
        vo                        || expectedResult
        new QueryOperatorClueVO(pageNum: 1,pageSize: 10) || new Result<PageResult<QueryOperatorClueResult>>(0, "errMsg", new PageResult<QueryOperatorClueResult>(0, [new QueryOperatorClueResult()]))
    }

    @Unroll
    def "queryPlanListTest"() {
        given:
        operatorDao.queryPlanList(*_) >> [new QueryPlanListResult()]

        expect:
        operatorManager.queryPlanList(ea, userId) == expectedResult

        where:
        ea   | userId || expectedResult
        "ea" | 0      || new Result<List<QueryPlanListResult>>(0, "errMsg", [new QueryPlanListResult()])
    }

    @Unroll
    def "queryOperatorInfoTest1"() {
        given:
        operatorDao.getById(*_) >> new Operator()
        operatorDao.queryOperatorPlanDtoById(*_) >> new OperatorPlanDTO()
        operatorDao.queryOperatorPlanDtoByFsUserId(*_) >> new OperatorPlanDTO()
        clueDAO.queryClueCountByDistributorIds(*_) >> 0
        outQRCodeService.createQRCode(*_) >> new ModelResult<CreateQRCodeResult>(0, "errMsg", new CreateQRCodeResult())
        operatorDistributorDAO.getDistributorIdsByOperatorId(*_) >> [new OperatorDistributorEntity()]
        distributePlanDao.getDistributePlanByPlanId(*_) >> new DistributePlanEntity()
        photoManager.queryPhoto(*_) >> null >> [new PhotoEntity(url: "testUrl")]
        photoManager.savePhotoByAapath(*_) >> true
        operatorDistributorDao.getDistributorIdsByOperatorId(*_) >> [new OperatorDistributorEntity()]

        expect:
        operatorManager.queryOperatorInfo(vo) == expectedResult

        where:
        vo                        || expectedResult
        new QueryOperatorInfoVO(planId: "planId",operatorId: "operatorId") || new Result<QueryOperatorInfoResult>(0, "errMsg", new QueryOperatorInfoResult())
    }

}