package com.facishare.marketing.provider.manager.usermarketingaccount

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.common.enums.ChannelEnum
import com.facishare.marketing.common.enums.CrmWechatFanFieldEnum
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum
import com.facishare.marketing.common.typehandlers.value.CustomizeObjectMappings
import com.facishare.marketing.common.util.UUIDUtil
import com.facishare.marketing.provider.dao.MarketingUserGroupCustomizeObjectMappingDao
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmAccountAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmContactAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmCustomizeObjectRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmLeadAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmMemberRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmWxUserAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmWxWorkExternalUserRelationDao
import com.facishare.marketing.provider.dao.UserMarketingMergeLogsDao
import com.facishare.marketing.provider.dao.UserMarketingMiniappAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingWxServiceAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingWxWorkExternalUserRelationDao
import com.facishare.marketing.provider.dto.UserMarketingMiniappAccountRelationDTO
import com.facishare.marketing.provider.entity.MarketingUserGroupCustomizeObjectMappingEntity
import com.facishare.marketing.provider.entity.landing.LandingObjCustomizeUserRelation
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity
import com.facishare.marketing.provider.innerArg.AssociationArg
import com.facishare.marketing.provider.innerResult.AssociationResult
import com.facishare.marketing.provider.manager.MarketingStatLogPersistorManger
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.SettingManager
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager
import com.facishare.marketing.provider.manager.landing.LandingObjCustomizeUserRelationManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager
import com.facishare.marketing.provider.mq.sender.UserMarketingAccountMergeSender
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.common.result.Result
import com.google.common.collect.Lists
import spock.lang.*

class UserMarketingAccountAssociationManagerTest extends Specification {

    def userMarketingAccountAssociationManager = new UserMarketingAccountAssociationManager()
    def userMarketingAccountRelationManager = Mock(UserMarketingAccountRelationManager)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def userMarketingAccountMergeSender = Mock(UserMarketingAccountMergeSender)
    def userMarketingMergeLogsDao = Mock(UserMarketingMergeLogsDao)
    def marketingStatLogPersistorManger = Mock(MarketingStatLogPersistorManger)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def eieaConverter = Mock(EIEAConverter)
    def crmV2Manager = Mock(CrmV2Manager)
    def userMarketingCrmLeadAccountRelationDao = Mock(UserMarketingCrmLeadAccountRelationDao)
    def userMarketingCrmWxUserAccountRelationDao = Mock(UserMarketingCrmWxUserAccountRelationDao)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingWxServiceAccountRelationDao = Mock(UserMarketingWxServiceAccountRelationDao)
    def userMarketingCrmContactAccountRelationDao = Mock(UserMarketingCrmContactAccountRelationDao)
    def userMarketingCrmAccountAccountRelationDao = Mock(UserMarketingCrmAccountAccountRelationDao)
    def userMarketingCrmWxWorkExternalUserRelationDao = Mock(UserMarketingCrmWxWorkExternalUserRelationDao)
    def marketingUserGroupCustomizeObjectMappingDao = Mock(MarketingUserGroupCustomizeObjectMappingDao)
    def userMarketingWxWorkExternalUserRelationDao = Mock(UserMarketingWxWorkExternalUserRelationDao)
    def userMarketingCrmMemberRelationDao = Mock(UserMarketingCrmMemberRelationDao)
    def userMarketingCrmCustomizeObjectRelationDao = Mock(UserMarketingCrmCustomizeObjectRelationDao)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def redisManager = Mock(RedisManager)
    def settingManager = Mock(SettingManager)
    def qyweixinAccountBindManager = Mock(QyweixinAccountBindManager)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def qywxManager = Mock(QywxManager)
    def marketingPromotionSourceObjManager = Mock(MarketingPromotionSourceObjManager)
    def landingObjCustomizeUserRelationManager = Mock(LandingObjCustomizeUserRelationManager)

    def setup() {
        userMarketingAccountAssociationManager.userMarketingAccountRelationManager = userMarketingAccountRelationManager
        userMarketingAccountAssociationManager.userMarketingAccountDAO = userMarketingAccountDAO
        userMarketingAccountAssociationManager.userMarketingAccountMergeSender = userMarketingAccountMergeSender
        userMarketingAccountAssociationManager.userMarketingMergeLogsDao = userMarketingMergeLogsDao
        userMarketingAccountAssociationManager.marketingStatLogPersistorManger = marketingStatLogPersistorManger
        userMarketingAccountAssociationManager.crmMetadataManager = crmMetadataManager
        userMarketingAccountAssociationManager.eieaConverter = eieaConverter
        userMarketingAccountAssociationManager.crmV2Manager = crmV2Manager
        userMarketingAccountAssociationManager.userMarketingCrmLeadAccountRelationDao = userMarketingCrmLeadAccountRelationDao
        userMarketingAccountAssociationManager.userMarketingCrmWxUserAccountRelationDao = userMarketingCrmWxUserAccountRelationDao
        userMarketingAccountAssociationManager.userMarketingMiniappAccountRelationDao = userMarketingMiniappAccountRelationDao
        userMarketingAccountAssociationManager.userMarketingWxServiceAccountRelationDao = userMarketingWxServiceAccountRelationDao
        userMarketingAccountAssociationManager.userMarketingCrmContactAccountRelationDao = userMarketingCrmContactAccountRelationDao
        userMarketingAccountAssociationManager.userMarketingCrmAccountAccountRelationDao = userMarketingCrmAccountAccountRelationDao
        userMarketingAccountAssociationManager.userMarketingCrmWxWorkExternalUserRelationDao = userMarketingCrmWxWorkExternalUserRelationDao
        userMarketingAccountAssociationManager.userMarketingWxWorkExternalUserRelationDao = userMarketingWxWorkExternalUserRelationDao
        userMarketingAccountAssociationManager.userMarketingCrmMemberRelationDao = userMarketingCrmMemberRelationDao
        userMarketingAccountAssociationManager.marketingUserGroupCustomizeObjectMappingDao = marketingUserGroupCustomizeObjectMappingDao
        userMarketingAccountAssociationManager.wechatWorkExternalUserObjManager = wechatWorkExternalUserObjManager
        userMarketingAccountAssociationManager.redisManager = redisManager
        userMarketingAccountAssociationManager.settingManager = settingManager
        userMarketingAccountAssociationManager.qyweixinAccountBindManager = qyweixinAccountBindManager
        userMarketingAccountAssociationManager.userMarketingAccountManager = userMarketingAccountManager
        userMarketingAccountAssociationManager.qywxManager = qywxManager
        userMarketingAccountAssociationManager.marketingPromotionSourceObjManager = marketingPromotionSourceObjManager
        userMarketingAccountAssociationManager.landingObjCustomizeUserRelationManager = landingObjCustomizeUserRelationManager
    }


    def "associateAfterLockTest"() {
        given:
        crmMetadataManager.getById(*_) >> getByIdMock
        userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(*_) >> isExistsByEaAndKeyPropertiesMock
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >>> getByEaAndKeyPropertiesMock
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> isExcludeApiNameByChannelMock
        userMarketingAccountDAO.getByTenantIdAndPhone(*_) >>> getByTenantIdAndPhoneMock
        userMarketingAccountDAO.insert(*_) >> insertMock
        userMarketingAccountDAO.getById(*_) >> userMarketingAccountDAOgetByIdMock
        userMarketingAccountRelationManager.delete(*_) >> null
        def spy = Spy(userMarketingAccountAssociationManager)
        spy.associate(*_) >> associateMock
        spy.merge(*_) >> null
        spy.mergeMarketingUserByUnionId(*_) >> null
        spy.doAssociateAccount(*_) >> null
        spy.sendUserMarketingAccountToShence(*_) >> null
        redisManager.lock(*_) >>> redisLockMock
        when:
        AssociationResult result = spy.associateAfterLock(associationArg)
        then:
        result == resultMock
        where:
        associationArg                                                                                                                                                      | resultMock                   | isExistsByEaAndKeyPropertiesMock | getByEaAndKeyPropertiesMock                                                                | isExcludeApiNameByChannelMock | getByIdMock                                                             | associateMock                | redisLockMock       | getByTenantIdAndPhoneMock                                                              | insertMock | userMarketingAccountDAOgetByIdMock
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146", additionalAssociationId: "222") | new AssociationResult("111") | true                             | [new AssociationResult("111"), new AssociationResult("111"), new AssociationResult("111")] | true                          | new ObjectData()                                                        | new AssociationResult("111") | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                                 | new AssociationResult("111") | true                             | [new AssociationResult(), new AssociationResult("111"), new AssociationResult("111")]      | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                                 | new AssociationResult()      | true                             | [new AssociationResult("111"), new AssociationResult(), new AssociationResult()]           | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                                 | new AssociationResult("111") | false                            | [new AssociationResult("111"), new AssociationResult("111"), new AssociationResult("111")] | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                   | null                         | false                            | [new AssociationResult("111"), new AssociationResult("111"), new AssociationResult("111")] | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | null                         | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                   | new AssociationResult("111") | false                            | [new AssociationResult("111"), new AssociationResult("111"), new AssociationResult("111")] | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                   | new AssociationResult("111") | false                            | [new AssociationResult(), new AssociationResult(), new AssociationResult()]                | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [true, true, true]  | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                                    | new AssociationResult()      | false                            | [new AssociationResult(), new AssociationResult(), new AssociationResult()]                | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | new UserMarketingAccountEntity()                                                       | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                                    | new AssociationResult()      | false                            | [new AssociationResult(), new AssociationResult(), new AssociationResult()]                | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | new UserMarketingAccountEntity(id: "aaa")                                              | 0          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, phone: "***********", associationId: "111", wxAppId: "wxAppId", ea: "88146")                                    | new AssociationResult()      | false                            | [new AssociationResult(), new AssociationResult(), new AssociationResult()]                | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [null, new UserMarketingAccountEntity(id: "aaa")]                                      | 1          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, associationId: "111", wxAppId: "wxAppId", ea: "88146")                                                          | new AssociationResult()      | false                            | [new AssociationResult(), new AssociationResult(), new AssociationResult()]                | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [null, new UserMarketingAccountEntity(id: "aaa")]                                      | 1          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, associationId: "111", wxAppId: "wxAppId", ea: "88146")                                                          | new AssociationResult("111") | false                            | [new AssociationResult("111"), new AssociationResult(), new AssociationResult()]           | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [null, new UserMarketingAccountEntity(id: "aaa")]                                      | 1          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, associationId: "111", wxAppId: "wxAppId", ea: "88146", phone: "***********")                                    | new AssociationResult()      | false                            | [new AssociationResult("111"), new AssociationResult(), new AssociationResult()]           | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [null, new UserMarketingAccountEntity(id: "aaa")]                                      | 1          | null
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, associationId: "111", wxAppId: "wxAppId", ea: "88146", phone: "***********")                                    | new AssociationResult()      | false                            | [new AssociationResult("111"), new AssociationResult(), new AssociationResult()]           | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [null, new UserMarketingAccountEntity(id: "aaa")]                                      | 1          | new UserMarketingAccountEntity(phone: "***********")
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, associationId: "111", wxAppId: "wxAppId", ea: "88146", phone: "***********")                                    | new AssociationResult()      | false                            | [new AssociationResult("111"), new AssociationResult(), new AssociationResult()]           | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [new UserMarketingAccountEntity(id: "aaa"), new UserMarketingAccountEntity(id: "aaa")] | 1          | new UserMarketingAccountEntity()
        new AssociationArg(type: ChannelEnum.CRM_LEAD.type, associationId: "111", wxAppId: "wxAppId", phone: "***********")                                                 | null                         | false                            | [new AssociationResult("111"), new AssociationResult(), new AssociationResult()]           | true                          | new ObjectData(id: "aaa", wx_app_id: "wxAppId", wx_open_id: "wxOpenId") | new AssociationResult("111") | [false, true, true] | [new UserMarketingAccountEntity(id: "aaa"), new UserMarketingAccountEntity(id: "aaa")] | 1          | new UserMarketingAccountEntity()

    }

    def "associateTest"() {
        given:
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >>> getByEaAndKeyPropertiesMock
        userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(*_) >> Boolean.TRUE
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> isExcludeApiNameByChannelMock
        userMarketingAccountDAO.insert(*_) >> 0
        userMarketingAccountDAO.updatePhone(*_) >> 0
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity()
        userMarketingAccountDAO.batchGet(*_) >> [new UserMarketingAccountEntity()]
        userMarketingAccountDAO.getByTenantIdAndPhone(*_) >> new UserMarketingAccountEntity()
        crmMetadataManager.getById(*_) >> new ObjectData()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getWechatFanByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getWxExternalUserByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.isExistFiled(*_) >> true
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["listByCrmWxUserIdsResponse"]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationByUnionId(*_) >> [new UserMarketingMiniappAccountRelationDTO()]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(*_) >> new UserMarketingMiniappAccountRelationDTO()
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["listByCrmWxWorkExternalUserIdsResponse"]
        marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(*_) >> new MarketingUserGroupCustomizeObjectMappingEntity()
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> new Result<Page<ObjectData>>()
        redisManager.lock(*_) >> redisManagerLockMock
        redisManager.unLock(*_) >> false
        qywxManager.getWxUnionIdByExternalUserId(*_) >> "getWxUnionIdByExternalUserIdResponse"
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]
        def spy = Spy(userMarketingAccountAssociationManager)
        spy.associateAfterLock(*_) >> associateAfterLockMock

        when:
        AssociationResult result = spy.associate(arg)
        then:
        result == resultMock
        where:
        arg                                                                                                                        | isExcludeApiNameByChannelMock | getByEaAndKeyPropertiesMock          | resultMock                   | redisManagerLockMock | associateAfterLockMock
        new AssociationArg(type: ChannelEnum.BROWSER_USER.type, associationId: "222")                                              | true                          | [null, null]                         | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.BROWSER_USER.type, associationId: "222")                                              | true                          | [new AssociationResult("111"), null] | new AssociationResult("111") | true                 | null
        new AssociationArg(type: ChannelEnum.BROWSER_USER.type, associationId: "222")                                              | false                         | [new AssociationResult("111"), null] | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "009", associationId: "111", ea: "88146")                        | false                         | [new AssociationResult("111"), null] | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.WX_SERVICE.type, phone: "009", associationId: "111", ea: "88146", wxAppId: "wxAppId") | false                         | [new AssociationResult("111"), null] | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.type, phone: "009", associationId: "111", ea: "88146")          | false                         | [new AssociationResult("111"), null] | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.CUSTOMIZE_OBJECT.type, phone: "009", associationId: "111", ea: "88146")               | false                         | [new AssociationResult("111"), null] | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.WX_SERVICE.type, phone: "009", associationId: "111", ea: "88146", wxAppId: "wxAppId") | false                         | [new AssociationResult("111"), null] | null                         | true                 | null
        new AssociationArg(type: ChannelEnum.WX_SERVICE.type, phone: "009", associationId: "111", ea: "88146", wxAppId: "wxAppId") | false                         | [new AssociationResult("111"), null] | new AssociationResult("111") | false                | null

    }


    def "bindTest"() {
        given:
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >> new AssociationResult("userMarketingAccountId")
        userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(*_) >> isExistsByEaAndKeyPropertiesMock
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> isExcludeApiNameByChannelMock
        userMarketingAccountDAO.insert(*_) >> 0
        userMarketingAccountDAO.updatePhone(*_) >> 0
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity()
        userMarketingAccountDAO.batchGet(*_) >> [new UserMarketingAccountEntity()]
        userMarketingAccountDAO.getByTenantIdAndPhone(*_) >> new UserMarketingAccountEntity()
        crmMetadataManager.getById(*_) >> new ObjectData()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getWechatFanByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getWxExternalUserByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.isExistFiled(*_) >> true
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["listByCrmWxUserIdsResponse"]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationByUnionId(*_) >> [new UserMarketingMiniappAccountRelationDTO()]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(*_) >> new UserMarketingMiniappAccountRelationDTO()
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["listByCrmWxWorkExternalUserIdsResponse"]
        marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(*_) >> new MarketingUserGroupCustomizeObjectMappingEntity()
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> new Result<Page<ObjectData>>()
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        qywxManager.getWxUnionIdByExternalUserId(*_) >> "getWxUnionIdByExternalUserIdResponse"
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]
        def spy = Spy(userMarketingAccountAssociationManager)
        spy.associate(*_) >>> associateMock
        when:
        String result = spy.bind(targetArg, sourceArg)
        then:
        result == resultMock
        where:
        targetArg                                                                                                                                  | sourceArg                                                                                                     | resultMock                | associateMock                                                                                       | isExistsByEaAndKeyPropertiesMock | isExcludeApiNameByChannelMock
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", wxAppId: "wxAppId")                         | null                                                                                                          | null                      | [null, null]                                                                                        | true                             | false
        new AssociationArg(type: ChannelEnum.WX_SERVICE.type, phone: "***********", associationId: "111")                                          | null                                                                                                          | null                      | [null, null]                                                                                        | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | null                      | [null, null]                                                                                        | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111")                | null                      | [null, null]                                                                                        | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | null                      | [new AssociationResult("userMarketingAccountId"), null]                                             | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | null                      | [new AssociationResult("userMarketingAccountId"), null]                                             | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | "userMarketingAccountId"  | [new AssociationResult("userMarketingAccountId"), new AssociationResult("userMarketingAccountId")]  | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | "userMarketingAccountId1" | [new AssociationResult("userMarketingAccountId1"), new AssociationResult("userMarketingAccountId")] | true                             | false
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01")                              | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | "userMarketingAccountId1" | [new AssociationResult("userMarketingAccountId1"), new AssociationResult("userMarketingAccountId")] | false                            | true
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********00000000000000000000000000000", associationId: "111", ea: "shfia01") | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | "userMarketingAccountId1" | [new AssociationResult("userMarketingAccountId1"), new AssociationResult("userMarketingAccountId")] | false                            | true
        new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "009", associationId: "111", ea: "fs")                                           | new AssociationArg(type: ChannelEnum.MINIAPP.type, phone: "***********", associationId: "111", ea: "shfia01") | "userMarketingAccountId1" | [new AssociationResult("userMarketingAccountId1"), new AssociationResult("userMarketingAccountId")] | false                            | true

    }


    def "doBindLeadAndQywxExternalUserTest"() {
        given:
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >> new AssociationResult("userMarketingAccountId")
        userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(*_) >> Boolean.TRUE
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> true
        userMarketingAccountDAO.insert(*_) >> 0
        userMarketingAccountDAO.updatePhone(*_) >> 0
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity()
        userMarketingAccountDAO.batchGet(*_) >> [new UserMarketingAccountEntity()]
        userMarketingAccountDAO.getByTenantIdAndPhone(*_) >> new UserMarketingAccountEntity()
        crmMetadataManager.getById(*_) >> new ObjectData()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getWechatFanByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getWxExternalUserByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.getDetailIgnoreError(*_) >>> getDetailIgnoreErrorMock
        crmV2Manager.isExistFiled(*_) >> true
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["listByCrmWxUserIdsResponse"]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationByUnionId(*_) >> [new UserMarketingMiniappAccountRelationDTO()]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(*_) >> new UserMarketingMiniappAccountRelationDTO()
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["listByCrmWxWorkExternalUserIdsResponse"]
        marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(*_) >> new MarketingUserGroupCustomizeObjectMappingEntity()
        wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(*_) >> 0
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> new Result<Page<ObjectData>>()
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        qyweixinAccountBindManager.qywxExternalUserIsvIdToAgentId(*_) >> qywxExternalUserIsvIdToAgentIdMock
        userMarketingAccountManager.getPhoneFromObjectData(*_) >> "getPhoneFromObjectDataResponse"
        qywxManager.getWxUnionIdByExternalUserId(*_) >> "getWxUnionIdByExternalUserIdResponse"
        qywxManager.convertToNewExternalUserId(*_) >> convertToNewExternalUserIdMock
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]
        when:
        userMarketingAccountAssociationManager.doBindLeadAndQywxExternalUser("fs", "crmApiName", objectData)
        then:
        noExceptionThrown() // todo - validate something
        where:
        objectData                                                  | getDetailIgnoreErrorMock                                                                 | qywxExternalUserIsvIdToAgentIdMock                             | convertToNewExternalUserIdMock
        new ObjectData(id: "id1", wechat_friend_id: "111")          | [null, new ObjectData(), null]                                                           | null                                                           | null
        new ObjectData(id: "id1", wechat_friend_id: "111")          | [new ObjectData(external_user_id: "222"), null, null]                                    | null                                                           | null
        new ObjectData(id: "id1", wechat_friend_id: "111")          | [new ObjectData(external_user_id: "222"), new ObjectData(external_user_id: "222"), null] | null                                                           | null
        new ObjectData(id: "id1")                                   | [new ObjectData(external_user_id: "222"), new ObjectData(external_user_id: "222"), null] | null                                                           | null
        new ObjectData(id: "id1", enterprise_wechat_user_id: "111") | null                                                                                     | com.facishare.marketing.common.result.Result.newSuccess("abc") | null
        new ObjectData(id: "id1", enterprise_wechat_user_id: "111") | null                                                                                     | com.facishare.marketing.common.result.Result.newSuccess("abc") | new HashMap<String, Object>(abc: "abc")

    }


    def "doBindUserMarketingByCrmObjectTest"() {
        given:
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >> new AssociationResult("userMarketingAccountId")
        userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(*_) >> Boolean.TRUE
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> true
        userMarketingAccountDAO.insert(*_) >> 0
        userMarketingAccountDAO.updatePhone(*_) >> 0
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity()
        userMarketingAccountDAO.batchGet(*_) >> [new UserMarketingAccountEntity()]
        userMarketingAccountDAO.getByTenantIdAndPhone(*_) >> new UserMarketingAccountEntity()
        crmMetadataManager.getById(*_) >> new ObjectData()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getWechatFanByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getWxExternalUserByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.getDetailIgnoreError(*_) >> getDetailIgnoreErrorMock
        crmV2Manager.isExistFiled(*_) >> true
        userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadIdForMq(*_) >> getByEaAndCrmLeadIdForMqMock
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["listByCrmWxUserIdsResponse"]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationByUnionId(*_) >> [new UserMarketingMiniappAccountRelationDTO()]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(*_) >> new UserMarketingMiniappAccountRelationDTO()
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["listByCrmWxWorkExternalUserIdsResponse"]
        marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(*_) >> new MarketingUserGroupCustomizeObjectMappingEntity()
        wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(*_) >> 0
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>>(code: 1)
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        userMarketingAccountManager.getPhoneFromObjectData(*_) >> "getPhoneFromObjectDataResponse"
        qywxManager.getWxUnionIdByExternalUserId(*_) >> "getWxUnionIdByExternalUserIdResponse"
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]

        when:
        userMarketingAccountAssociationManager.doBindUserMarketingByCrmObject("88146", crmApiName, objectData)
        then:
        noExceptionThrown() // todo - validate something
        where:
        crmApiName                                               | objectData                                                           | getByEaAndCrmLeadIdForMqMock                    | getDetailIgnoreErrorMock
        CrmObjectApiNameEnum.CUSTOMER.getName()                  | new ObjectData()                                                     | null                                            | null
        CrmObjectApiNameEnum.CUSTOMER.getName()                  | new ObjectData()                                                     | new UserMarketingCrmLeadAccountRelationEntity() | null
        CrmObjectApiNameEnum.CONTACT.getName()                   | new ObjectData()                                                     | new UserMarketingCrmLeadAccountRelationEntity() | null
        CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName() | new ObjectData()                                                     | new UserMarketingCrmLeadAccountRelationEntity() | null
        CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName() | new ObjectData(contact_id: "111", external_user_id: "222")           | null                                            | null
        CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName() | new ObjectData(contact_id: "111", external_user_id: "222")           | null                                            | new ObjectData(contact_id: "111", external_user_id: "222")
        CrmObjectApiNameEnum.LEADS_TRANSFER_LOG_OBJ.getName()    | new ObjectData(account_id: "111", lead_id: "222")                    | null                                            | null
        CrmObjectApiNameEnum.LEADS_TRANSFER_LOG_OBJ.getName()    | new ObjectData(account_id: "111", lead_id: "222", contact_id: "333") | new UserMarketingCrmLeadAccountRelationEntity() | new ObjectData(account_id: "111", lead_id: "222", "contact_id": "333")
    }


    def "bindUserMarketingByQywxExternalUserTest"() {
        given:
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >> new AssociationResult("userMarketingAccountId")
        userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(*_) >> Boolean.TRUE
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> isExcludeApiNameByChannelMock
        userMarketingAccountDAO.insert(*_) >> 0
        userMarketingAccountDAO.updatePhone(*_) >> 0
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity()
        userMarketingAccountDAO.batchGet(*_) >> [new UserMarketingAccountEntity()]
        userMarketingAccountDAO.getByTenantIdAndPhone(*_) >> new UserMarketingAccountEntity()
        crmMetadataManager.getById(*_) >> new ObjectData()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        crmV2Manager.getWechatFanByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getWxExternalUserByUnionId(*_) >> [new ObjectData()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.getDetailIgnoreError(*_) >> getDetailIgnoreErrorMock
        crmV2Manager.isExistFiled(*_) >> true
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["listByCrmWxUserIdsResponse"]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationByUnionId(*_) >> [new UserMarketingMiniappAccountRelationDTO()]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(*_) >> new UserMarketingMiniappAccountRelationDTO()
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["listByCrmWxWorkExternalUserIdsResponse"]
        marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(*_) >> new MarketingUserGroupCustomizeObjectMappingEntity()
        wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(*_) >> 0
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> listObjectDataByIdsLimitedMock
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        userMarketingAccountManager.getPhoneFromObjectData(*_) >> "***********"
        qywxManager.getWxUnionIdByExternalUserId(*_) >> "getWxUnionIdByExternalUserIdResponse"
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]
        def spy = Spy(userMarketingAccountAssociationManager)
        spy.associate(*_) >> associateMock
        spy.bind(*_) >> null

        when:
        spy.bindUserMarketingByQywxExternalUser("88146", "1", apiname, "111")
        then:
        noExceptionThrown() // todo - validate something
        where:
        apiname                         | getDetailIgnoreErrorMock | listObjectDataByIdsLimitedMock                                                                                                                                   | isExcludeApiNameByChannelMock | associateMock
        ChannelEnum.CRM_LEAD.apiName    | null                     | null                                                                                                                                                             | null                          | null
        ChannelEnum.CRM_LEAD.apiName    | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList()))                                                                         | false                         | null
        ChannelEnum.CRM_LEAD.apiName    | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | false                         | null
        ChannelEnum.CRM_LEAD.apiName    | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | true                          | new AssociationResult(userMarketingAccountId: "1111")
        ChannelEnum.CRM_CONTACT.apiName | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | true                          | new AssociationResult(userMarketingAccountId: "1111")
        ChannelEnum.CRM_LEAD.apiName    | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | true                          | new AssociationResult(userMarketingAccountId: "1111")
        ChannelEnum.CRM_CONTACT.apiName | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | true                          | new AssociationResult(userMarketingAccountId: "1111")
        ChannelEnum.CRM_ACCOUNT.apiName | new ObjectData()         | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | true                          | new AssociationResult(userMarketingAccountId: "1111")

    }


    def "getFirstPhoneTest"() {
        when:
        String result = UserMarketingAccountAssociationManager.getFirstPhone(phone)
        then:
        result == resultMock
        where:
        phone     | resultMock
        null      | null
        "111;222" | "111"
    }


    def "mergeMarketingUserByUnionIdTest"() {
        given:
        userMarketingAccountRelationManager.getByEaAndKeyProperties(*_) >> getByEaAndKeyPropertiesMock
        userMarketingAccountDAO.getById(*_) >> getByIdMock
        userMarketingAccountDAO.batchGet(*_) >> [new UserMarketingAccountEntity(id: "aaa")]
        crmMetadataManager.getById(*_) >> crmMetadataManagerGetByIdMock
        eieaConverter.enterpriseAccountToId(*_) >> 88146
        crmV2Manager.getWechatFanByUnionId(*_) >> [new ObjectData(id: "abc")]
        crmV2Manager.getWxExternalUserByUnionId(*_) >> [new ObjectData(id: "aaa")]
        crmV2Manager.isExistFiled(*_) >> true
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["aaa"]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationByUnionId(*_) >> [new UserMarketingMiniappAccountRelationDTO(userMarketingId: "111")]
        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(*_) >> queryUserMarketingMiniappAccountRelationUnionIdByUidMock
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["listByCrmWxWorkExternalUserIdsResponse"]
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> listObjectDataByIdsLimitedMock
        qywxManager.getWxUnionIdByExternalUserId(*_) >> getWxUnionIdByExternalUserIdResponse
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]
        qywxManager.dealCrmWxUserUnionId(*_) >> null;

        when:
        userMarketingAccountAssociationManager.mergeMarketingUserByUnionId(associationArg)
        then:
        noExceptionThrown() // todo - validate something
        where:
        associationArg                                                   | getByEaAndKeyPropertiesMock                           | getByIdMock                               | crmMetadataManagerGetByIdMock           | queryUserMarketingMiniappAccountRelationUnionIdByUidMock | listObjectDataByIdsLimitedMock                                                                                                                                   | getWxUnionIdByExternalUserIdResponse
        null                                                             | null                                                  | null                                      | null                                    | null                                                     | null                                                                                                                                                             | null
        null                                                             | new AssociationResult(userMarketingAccountId: "1111") | null                                      | null                                    | null                                                     | null                                                                                                                                                             | null
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.type)           | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity()          | null                                    | null                                                     | null                                                                                                                                                             | null
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.type)           | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | null                                                     | null                                                                                                                                                             | null
        new AssociationArg(type: ChannelEnum.MINIAPP.type)               | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | null                                                     | null                                                                                                                                                             | null
        new AssociationArg(type: ChannelEnum.MINIAPP.type)               | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | new UserMarketingMiniappAccountRelationDTO()             | null                                                                                                                                                             | null
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.type) | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | new UserMarketingMiniappAccountRelationDTO()             | new Result<Page<ObjectData>>()                                                                                                                                   | null
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.type) | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | new UserMarketingMiniappAccountRelationDTO()             | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | null
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.type) | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | new UserMarketingMiniappAccountRelationDTO()             | new Result<Page<ObjectData>>(data: [new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")])                                                     | "111"
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.type) | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | new UserMarketingMiniappAccountRelationDTO()             | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", wx_union_id: "111", external_user_id: "222")]))) | "111"
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.type) | new AssociationResult(userMarketingAccountId: "1111") | new UserMarketingAccountEntity(id: "aaa") | new ObjectData(wx_union_id: "xiaowang") | new UserMarketingMiniappAccountRelationDTO()             | new Result<Page<ObjectData>>(data: new Page<ObjectData>(dataList: Lists.newArrayList([new ObjectData(id: "abc", external_user_id: "222")])))                     | "111"
    }

    @Shared
    def marketingUserGroupCustomizeObjectMappingEntity = new MarketingUserGroupCustomizeObjectMappingEntity(fieldDataCommonMapping: new CustomizeObjectMappings())

    def "doAssociateAccountTest"() {
        given:
        userMarketingAccountRelationManager.isExcludeApiNameByChannel(*_) >> isExcludeApiNameByChannelMock
        crmV2Manager.getDetail(*_) >> new ObjectData()
        marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(*_) >> MarketingUserGroupCustomizeObjectMappingMock
        marketingUserGroupCustomizeObjectMappingEntity.getFieldDataCommonMapping().add(new CustomizeObjectMappings.CustomizeObjectMapping(mankeepFieldApiName: "姓名", crmFieldName: "name", mankeepFieldName: "姓名", crmFieldApiName: "name"))
        marketingUserGroupCustomizeObjectMappingEntity.getFieldDataCommonMapping().add(new CustomizeObjectMappings.CustomizeObjectMapping(mankeepFieldApiName: "邮箱", crmFieldName: "mail", mankeepFieldName: "邮箱", crmFieldApiName: "mail"))

        when:
        userMarketingAccountAssociationManager.doAssociateAccount(associationArg, marketingUserId)
        then:
        noExceptionThrown() // todo - validate something
        where:
        associationArg                                                           | marketingUserId | isExcludeApiNameByChannelMock | MarketingUserGroupCustomizeObjectMappingMock
        new AssociationArg(ea: "88146")                                          | "111"           | true                          | null
        new AssociationArg(ea: "88146", type: ChannelEnum.WX_SERVICE.type)       | "111"           | false                         | null
        new AssociationArg(ea: "88146", type: ChannelEnum.CUSTOMIZE_OBJECT.type) | "111"           | false                         | marketingUserGroupCustomizeObjectMappingEntity
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_LEAD.type)         | "111"           | false                         | marketingUserGroupCustomizeObjectMappingEntity
    }


    def "sendUserMarketingAccountToShenceTest"() {
        when:
        userMarketingAccountAssociationManager.sendUserMarketingAccountToShence("ea", "userMarketingAccountId")
        then:
        noExceptionThrown() // todo - validate something

    }

    def "updateLandingObjByUserMarketingTest"() {
        given:
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation(id: "111")]
        marketingPromotionSourceObjManager.updateUserMarketingIdByMergeEvent(*_) >> null
        when:
        userMarketingAccountAssociationManager.updateLandingObjByUserMarketing("88146", "oldUserMarketingAccountId", "newUserMarketingAccountId")
        then:
        noExceptionThrown()

    }

    def "mergeTest"() {
        given:
        landingObjCustomizeUserRelationManager.getByUserMarketingId(*_) >> [new LandingObjCustomizeUserRelation()]
        //   qywxCiphertextEa.stream(*_) >> null
        userMarketingAccountRelationManager.changeUserMarketingId(*_) >> null
        userMarketingAccountDAO.deleteById(*_) >> null
        userMarketingAccountMergeSender.send(*_) >> null
        userMarketingMergeLogsDao.insert(*_) >> null
        landingObjCustomizeUserRelationManager.updateUserMarketingIdByIdList(*_) >> null

        when:
        userMarketingAccountAssociationManager.merge(ea, oldUserMarketingAccountId, newUserMarketingAccountId, null)

        then:
        noExceptionThrown() // todo - validate something

        where:
        ea      | oldUserMarketingAccountId | newUserMarketingAccountId
        "88146" | "aaa"                     | "aaa"
        "88146" | "aaa"                     | "bbb"
    }

}