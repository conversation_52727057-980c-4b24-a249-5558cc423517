package com.facishare.marketing.provider.service

import com.facishare.converter.EIEAConverter
import com.facishare.mankeep.api.outService.service.OuterPhoneService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.arg.AccountIsApplyForKISArg
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult
import com.facishare.marketing.api.result.account.GetFsUserInfoResult
import com.facishare.marketing.api.result.account.GetQywxBaseInfoFromWxResult
import com.facishare.marketing.api.result.account.QywxEmployeeBindWxUserResult
import com.facishare.marketing.api.result.qywx.QywxBaseInfoResult
import com.facishare.marketing.api.result.qywx.ResetAppUserDataResult
import com.facishare.marketing.api.service.sms.SendService
import com.facishare.marketing.common.enums.qywx.FsEnterpriseBindTypeEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.provider.dao.AccountDAO
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO
import com.facishare.marketing.provider.dao.FSBindDAO
import com.facishare.marketing.provider.dao.ProductDAO
import com.facishare.marketing.provider.dao.UserDAO
import com.facishare.marketing.provider.dao.distribution.DistributionPlanDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxVirtualFsUserDAO
import com.facishare.marketing.provider.dao.qywx.ResetDataStatusDAO
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao
import com.facishare.marketing.provider.entity.AccountEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity
import com.facishare.marketing.provider.entity.FSBindEntity
import com.facishare.marketing.provider.entity.ProductEntity
import com.facishare.marketing.provider.entity.ResetDataStatusEntity
import com.facishare.marketing.provider.entity.UserEntity
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult
import com.facishare.marketing.provider.manager.FsAddressBookManager
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.miniappLogin.WxMiniappLoginManager
import com.facishare.marketing.provider.manager.qywx.QywxBindAppUserManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.QywxUserManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult
import com.facishare.organization.adapter.api.service.EmployeeService
import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult
import com.facishare.uc.api.model.fscore.EnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService
import spock.lang.*

class AccountServiceImplTest extends Specification {

    def accountServiceImpl = new AccountServiceImpl()

    def accountDAO = Mock(AccountDAO)
    def fsBindDAO = Mock(FSBindDAO)
    def productDAO = Mock(ProductDAO)
    def qywxMiniappConfigDAO = Mock(QywxMiniappConfigDAO)
    def employeeService = Mock(EmployeeService)
    def eieaConverter = Mock(EIEAConverter)
    def enterpriseEditionService = Mock(EnterpriseEditionService)
    def redisManager = Mock(RedisManager)
    def qywxUserManager = Mock(QywxUserManager)
    def wechatAccountManager = Mock(WechatAccountManager)
    def qywxVirtualFsUserDAO = Mock(QywxVirtualFsUserDAO)
    def userDAO = Mock(UserDAO)
    def qywxBindAppUserManager = Mock(QywxBindAppUserManager)
    def resetDataStatusDAO = Mock(ResetDataStatusDAO)
    def appVersionManager = Mock(AppVersionManager)
    def wechatAccountConfigDao = Mock(WechatAccountConfigDao)
    def qywxManager = Mock(QywxManager)
    def qywxCorpAgentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def sendService = Mock(SendService)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def distributePlanDAO = Mock(DistributionPlanDAO)
    def outerPhoneService = Mock(OuterPhoneService)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def userMarketingAccountRelationManager = Mock(UserMarketingAccountRelationManager)
    def qyweixinAccountBindManager = Mock(QyweixinAccountBindManager)
    def objectManager = Mock(ObjectManager)
    def outerServiceWechatService = Mock(OuterServiceWechatService)
    def crmV2Manager = Mock(CrmV2Manager)
    def wxMiniappLoginManager = Mock(WxMiniappLoginManager)
    def publicEmployeeService = Mock(PublicEmployeeService)

    def setup() {
        accountServiceImpl.accountDAO = accountDAO
        accountServiceImpl.fsBindDAO = fsBindDAO
        accountServiceImpl.productDAO = productDAO
        accountServiceImpl.qywxMiniappConfigDAO = qywxMiniappConfigDAO
        accountServiceImpl.employeeService = employeeService
        accountServiceImpl.eieaConverter = eieaConverter
        accountServiceImpl.enterpriseEditionService = enterpriseEditionService
        accountServiceImpl.redisManager = redisManager
        accountServiceImpl.qywxUserManager = qywxUserManager
        accountServiceImpl.wechatAccountManager = wechatAccountManager
        accountServiceImpl.qywxVirtualFsUserDAO = qywxVirtualFsUserDAO
        accountServiceImpl.userDAO = userDAO
        accountServiceImpl.qywxBindAppUserManager = qywxBindAppUserManager
        accountServiceImpl.resetDataStatusDAO = resetDataStatusDAO
        accountServiceImpl.appVersionManager = appVersionManager
        accountServiceImpl.wechatAccountConfigDao = wechatAccountConfigDao
        accountServiceImpl.qywxManager = qywxManager
        accountServiceImpl.qywxCorpAgentConfigDAO = qywxCorpAgentConfigDAO
        accountServiceImpl.sendService = sendService
        accountServiceImpl.hexagonSiteDAO = hexagonSiteDAO
        accountServiceImpl.customizeFormDataDAO = customizeFormDataDAO
        accountServiceImpl.distributePlanDAO = distributePlanDAO
        accountServiceImpl.outerPhoneService = outerPhoneService
        accountServiceImpl.fsAddressBookManager = fsAddressBookManager
        accountServiceImpl.userMarketingAccountRelationManager = userMarketingAccountRelationManager
        accountServiceImpl.qyweixinAccountBindManager = qyweixinAccountBindManager
        accountServiceImpl.objectManager = objectManager
        accountServiceImpl.outerServiceWechatService = outerServiceWechatService
        accountServiceImpl.crmV2Manager = crmV2Manager
        accountServiceImpl.wxMiniappLoginManager = wxMiniappLoginManager
        accountServiceImpl.qywxCrmAppid = "qywxCrmAppid"
        accountServiceImpl.publicEmployeeService = publicEmployeeService
        accountServiceImpl.partnerAppId = "partnerAppId"
    }


    def "isApplyForKISTest"() {
        given:
        accountDAO.queryAccountByUid(*_) >> new AccountEntity("uid", null, null)
        fsBindDAO.queryFSBindByUid(*_) >> queryFSBindByUidMock
        fsBindDAO.queryByUserAndAppId(*_) >> new FSBindEntity()
        qywxMiniappConfigDAO.getByEa(*_) >> new QywxMiniappConfigEntity()
        employeeService.getEmployeeDto(*_) >> getEmployeeDtoMock
        enterpriseEditionService.getEnterpriseData(*_) >> getEnterpriseDataMock
        qywxUserManager.getVirtualUserByQywxInfo(*_) >> new QywxVirtualFsUserEntity("id", "ea", 0, "corpId", "qyUserId", new GregorianCalendar(2024, Calendar.AUGUST, 30, 10, 44).getTime(), 0, new GregorianCalendar(2024, Calendar.AUGUST, 30, 10, 44).getTime())
        qywxUserManager.getQywxUserInfoByPhone(*_) >> new DepartmentStaffResult.StaffInfo()
        qywxUserManager.getAccountByPhone(*_) >> getAccountByPhoneMock
        wechatAccountManager.getNotEmptyWxAppIdByEa(*_) >> "getNotEmptyWxAppIdByEaResponse"
        qywxVirtualFsUserDAO.insert(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByUserIdAndEa(*_) >> new QywxVirtualFsUserEntity("id", "ea", 0, "corpId", "qyUserId", new GregorianCalendar(2024, Calendar.AUGUST, 30, 10, 44).getTime(), 0, new GregorianCalendar(2024, Calendar.AUGUST, 30, 10, 44).getTime())
        userDAO.queryByCorpIdAndQYUserIdAndAppid(*_) >> new UserEntity()
        qywxManager.fsEnterpriseBindType(*_) >> fsEnterpriseBindTypeMock
        fsAddressBookManager.mustUseFxiaokeAddressBook(*_) >> true
        fsAddressBookManager.getFxEmployeeByUserId(*_) >> new EmployeeDto()
        eieaConverter.enterpriseAccountToId(*_) >> 88146
        def spy = Spy(accountServiceImpl)
        spy.applyForKisNotBindQywx(*_) >> Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 1, uid: "uid"))
        spy.applyForKisQywxEa(*_) >> Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 1, uid: "uid"))
        when:
        Result<AccountIsApplyForKISResult> result = spy.isApplyForKIS(arg1)
        then:
        result.getErrCode() == resultMock
        where:
        arg1                                                     | getEmployeeDtoMock                                                         | fsEnterpriseBindTypeMock                               | getAccountByPhoneMock         | queryFSBindByUidMock                                             | getEnterpriseDataMock                                             | resultMock
        null                                                     | null                                                                       | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO | null                          | null                                                             | null                                                              | new Result<>(SHErrorCode.PARAMS_ERROR).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: null) | null                                                                       | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO | null                          | null                                                             | null                                                              | new Result<>(SHErrorCode.PARAMS_ERROR).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | null                                                                       | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO | null                          | null                                                             | null                                                              | new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult()                                                 | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO | null                          | null                                                             | null                                                              | new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO | null                          | null                                                             | null                                                              | Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 1, uid: "uid")).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.BIND_QYWX_AND_MINIAPPPRO      | null                          | null                                                             | null                                                              | Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 1, uid: "uid")).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | null                          | null                                                             | null                                                              | Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 0, uid: "uid")).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity(uid: "uid") | null                                                             | null                                                              | Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 0, uid: "uid")).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity(uid: "uid") | null                                                             | null                                                              | Result.newSuccess(new AccountIsApplyForKISResult(phone: "***********", status: 0, uid: "uid")).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity(uid: "uid") | new FSBindEntity(fsEa: "88146", fsCorpId: 88146, fsUserId: 1000) | null                                                              | new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity(uid: "uid") | new FSBindEntity(fsEa: "88146", fsCorpId: 88146, fsUserId: 1000) | new GetEnterpriseDataResult()                                     | new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND).getErrCode()
        new AccountIsApplyForKISArg(ea: "88146", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity(uid: "uid") | new FSBindEntity(fsEa: "88146", fsCorpId: 88146, fsUserId: 1000) | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData()) | Result.newSuccess().getErrCode()
        new AccountIsApplyForKISArg(ea: "88000", fsUserId: 1000) | new GetEmployeeDtoResult(employee: new EmployeeDto(mobile: "***********")) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity(uid: "uid") | new FSBindEntity(fsEa: "88146", fsCorpId: 88146, fsUserId: 1000) | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData()) | Result.newSuccess().getErrCode()
    }

    def "applyForKisQywxEa"() {
        given:
        fsAddressBookManager.mustUseFxiaokeAddressBook(*_) >> mustUseFxiaokeAddressBookMock
        qywxVirtualFsUserDAO.queryQyUserIdByUserIdAndEa(*_) >> queryQyUserIdByUserIdAndEaMock
        qywxUserManager.getQywxUserInfoByPhone(*_) >> getQywxUserInfoByPhoneMock
        qywxUserManager.getVirtualUserByQywxInfo(*_) >> getVirtualUserByQywxInfoMock
        userDAO.queryByCorpIdAndQYUserIdAndAppid(*_) >> queryByCorpIdAndQYUserIdAndAppidMock
        accountDAO.queryAccountByUid(*_) >> queryAccountByUidMock
        fsBindDAO.queryFSBindByUid(*_) >> queryFSBindByUidMock
        enterpriseEditionService.getEnterpriseData(*_) >> getEnterpriseDataMock
        def spy = Spy(accountServiceImpl)
        spy.applyForKisQywxSpecialEnterprise(*_) >> Result.newSuccess();
        when:
        Result<AccountIsApplyForKISResult> result = spy.applyForKisQywxEa("88146", "88146", "appid", new EmployeeDto(), new AccountIsApplyForKISResult(), 88146)
        then:
        result.getErrCode() == resultMock
        where:
        mustUseFxiaokeAddressBookMock | queryQyUserIdByUserIdAndEaMock                | getQywxUserInfoByPhoneMock                        | getVirtualUserByQywxInfoMock  | queryByCorpIdAndQYUserIdAndAppidMock | queryAccountByUidMock | queryFSBindByUidMock | getEnterpriseDataMock | resultMock
        true                          | null                                          | null                                              | null                          | null                                 | null                  | null                 | null                  | Result.newSuccess().getErrCode()
        false                         | null                                          | null                                              | null                          | null                                 | null                  | null                 | null                  | Result.newSuccess().getErrCode()
        false                         | null                                          | new DepartmentStaffResult.StaffInfo(userId: 1000) | null                          | null                                 | null                  | null                 | null                  | Result.newSuccess().getErrCode()
        false                         | new QywxVirtualFsUserEntity(qyUserId: "1001") | new DepartmentStaffResult.StaffInfo(userId: 1000) | new QywxVirtualFsUserEntity() | null                                 | null                  | null                 | null                  | Result.newSuccess().getErrCode()

    }

    def "applyForKisNotBindQywx"() {

    }

    def "isApplyForQyWxKISTest"() {
        given:
        accountDAO.queryAccountByUid(*_) >> new AccountEntity(null, null, "phone")
        fsBindDAO.queryFSBindByUid(*_) >> new FSBindEntity()
        fsBindDAO.insert(*_) >> 0
        fsBindDAO.update(*_) >> 0
        eieaConverter.enterpriseAccountToId(*_) >> 0

        when:
        Result<AccountIsApplyForKISResult> result = accountServiceImpl.isApplyForQyWxKIS("ea", 0, "uid", "appId")
        then:
        result == new Result<AccountIsApplyForKISResult>(0, "errMsg", new AccountIsApplyForKISResult())
    }


    def "getFsUserInfoTest"() {
        given:
        employeeService.getEmployeeDto(*_) >> new GetEmployeeDtoResult(new EmployeeDto())
        eieaConverter.enterpriseAccountToId(*_) >> 0
        qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(*_) >> "queryQyUserIdByVirtualInfoResponse"
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> new Result<Map<String, String>>(0, "errMsg", ["data": "data"])

        when:
        Result<GetFsUserInfoResult> result = accountServiceImpl.getFsUserInfo("ea", 0, "uid")
        then:
        result == new Result<GetFsUserInfoResult>(0, "errMsg", new GetFsUserInfoResult())
    }


    def "sendSMCodeTest"() {
        given:
        productDAO.queryProductDetail(*_) >> new ProductEntity()
        sendService.sendVerificationCode(*_) >> new Result<Void>(0, "errMsg", null)
        hexagonSiteDAO.getById(*_) >> new HexagonSiteEntity()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()
        distributePlanDAO.getDistributePlanById(*_) >> new DistributePlanEntity()

        when:
        Result result = accountServiceImpl.sendSMCode("phone", 0, "objectId")
        then:
        result == new Result(0, "errMsg", "data")
    }


    def "checkSMCodeTest"() {
        given:
        outerPhoneService.checkVerifyNormalSMCode(*_) >> new ModelResult(0, "errMsg", "data")

        when:
        Result result = accountServiceImpl.checkSMCode("phone", "verifyCode")
        then:
        result == new Result(0, "errMsg", null)
    }


    def "bindToWxWorkExternalUserTest"() {
        given:
        userMarketingAccountRelationManager.bindMiniappUserAndWxWorkExternalUser(*_) >> null

        when:
        Result<String> result = accountServiceImpl.bindToWxWorkExternalUser("ea", "uid", "wxWorkExternalUserId")
        then:
        result == new Result<String>(0, "errMsg", "data")
    }


    def "resetAppUserDataTest"() {
        given:
        qywxBindAppUserManager.bindAppUser(*_) >> new Result(0, "errMsg", "data")
        resetDataStatusDAO.getResetDataStatusDataByUser(*_) >> new ResetDataStatusEntity()
        resetDataStatusDAO.insert(*_) >> 0
        resetDataStatusDAO.deleteDataById(*_) >> 0

        when:
        Result<ResetAppUserDataResult> result = accountServiceImpl.resetAppUserData("ea", 0)
        then:
        result == new Result<ResetAppUserDataResult>(0, "errMsg", new ResetAppUserDataResult())
    }


    def "getQywxBaseInfoFromWxTest"() {
        given:
        fsBindDAO.queryFSBindByUid(*_) >> new FSBindEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()

        when:
        Result<GetQywxBaseInfoFromWxResult> result = accountServiceImpl.getQywxBaseInfoFromWx("targetUid")
        then:
        result == new Result<GetQywxBaseInfoFromWxResult>(0, "errMsg", new GetQywxBaseInfoFromWxResult())
    }


    def "getApplyInfoKeyForWxTest"() {
        given:
        fsBindDAO.queryByFsEaAndUserId(*_) >> "queryByFsEaAndUserIdResponse"
        eieaConverter.enterpriseAccountToId(*_) >> 0
        userDAO.queryByUid(*_) >> new UserEntity()

        when:
        Result<String> result = accountServiceImpl.getApplyInfoKeyForWx("ea", 0)
        then:
        result == new Result<String>(0, "errMsg", "data")
    }


    def "getDownstreamEmployeeInfoTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 0
        publicEmployeeService.getDownstreamEmployeeInfo(*_) >> new RestResult<GetDownstreamEmployeeInfoResult>()

        when:
        Result<com.facishare.marketing.api.result.account.GetDownstreamEmployeeInfoResult> result = accountServiceImpl.getDownstreamEmployeeInfo("erUpstreamEa", "erOuterTenantId", "erOuterUid")
        then:
        result == new Result<com.facishare.marketing.api.result.account.GetDownstreamEmployeeInfoResult>(0, "errMsg", new com.facishare.marketing.api.result.account.GetDownstreamEmployeeInfoResult())
    }


    def "queryQywxH5UserBindWxUserInfoTest"() {
        given:
        userDAO.queryByCorpIdAndQYUserIdAndAppid(*_) >> new UserEntity()

        when:
        Result<QywxEmployeeBindWxUserResult> result = accountServiceImpl.queryQywxH5UserBindWxUserInfo("ea", 0, "qywxCorpId", "qywxUserId", "appId")
        then:
        result == new Result<QywxEmployeeBindWxUserResult>(0, "errMsg", new QywxEmployeeBindWxUserResult())
    }


    def "queryEmployeeByOfficeAccountOpenIdTest"() {
        given:
        fsBindDAO.queryFSBindByUid(*_) >> new FSBindEntity()
        userDAO.queryByUnionId(*_) >> new UserEntity()
        outerServiceWechatService.transWxAppIdToFsEa(*_) >> new com.facishare.wechat.proxy.common.result.ModelResult<String>(0, "errorMessage", "result")
        crmV2Manager.getWechatFanObjByOpenId(*_) >> new ObjectData(0, 0f)

        when:
        Result<GetFsUserInfoResult> result = accountServiceImpl.queryEmployeeByOfficeAccountOpenId("officeAccountAppId", "officeAccountOpenId")
        then:
        result == new Result<GetFsUserInfoResult>(0, "errMsg", new GetFsUserInfoResult())
    }


    def "setApplyRedisDataTest"() {

        when:
        String result = accountServiceImpl.setApplyRedisData("ea", 0, 0, "phone", "qywxCorpId", "qywxUserId")
        then:
        result == "replaceMeWithExpectedResult"
    }


    def "qywxBaseInfoTest"() {
        given:
        qywxMiniappConfigDAO.getByEa(*_) >> new QywxMiniappConfigEntity()
        wechatAccountManager.getNotEmptyWxAppIdByEa(*_) >> "getNotEmptyWxAppIdByEaResponse"
        appVersionManager.getCurrentAppVersion(*_) >> "getCurrentAppVersionResponse"
        wechatAccountConfigDao.getByWxAppId(*_) >> new WechatAccountConfigEntity()

        when:
        Result<QywxBaseInfoResult> result = accountServiceImpl.qywxBaseInfo("ea", "appId")
        then:
        result == new Result<QywxBaseInfoResult>(0, "errMsg", new QywxBaseInfoResult())
    }

}