package com.facishare.marketing.provider.service

import com.facishare.converter.EIEAConverter
import com.facishare.mankeep.api.outService.service.OuterPhoneService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.marketing.api.arg.AccountIsApplyForKISArg
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult
import com.facishare.marketing.api.service.sms.SendService
import com.facishare.marketing.common.enums.qywx.FsEnterpriseBindTypeEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.distribution.DistributionPlanDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO
import com.facishare.marketing.provider.dao.qywx.ResetDataStatusDAO
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao
import com.facishare.marketing.provider.entity.AccountEntity
import com.facishare.marketing.provider.entity.FSBindEntity
import com.facishare.marketing.provider.entity.ResetDataStatusEntity
import com.facishare.marketing.provider.entity.UserEntity
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult
import com.facishare.marketing.provider.manager.FsAddressBookManager
import com.facishare.marketing.provider.manager.FsBindManager
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.miniappLogin.WxMiniappLoginManager
import com.facishare.marketing.provider.manager.qywx.QywxBindAppUserManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.QywxUserManager
import com.facishare.marketing.provider.manager.user.UserManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult
import com.facishare.organization.adapter.api.service.EmployeeService
import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.organization.api.model.type.EmployeeEntityStatus
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult
import com.facishare.uc.api.model.fscore.EnterpriseData
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService
import spock.lang.Specification
import spock.lang.Unroll

class AccountServiceSpec extends Specification {

    // 模拟依赖项
    def accountDAO = Mock(AccountDAO)
    def fsBindManager = Mock(FsBindManager)
    def productDAO = Mock(ProductDAO)
    def qywxMiniappConfigDAO = Mock(QywxMiniappConfigDAO)
    def employeeService = Mock(EmployeeService)
    def eieaConverter = Mock(EIEAConverter)
    def enterpriseEditionService = Mock(EnterpriseEditionService)
    def redisManager = Mock(RedisManager)
    def qywxUserManager = Mock(QywxUserManager)
    def wechatAccountManager = Mock(WechatAccountManager)
    def qywxVirtualFsUserManager = Mock(QywxVirtualFsUserManager)
    def userManager = Mock(UserManager)
    def qywxBindAppUserManager = Mock(QywxBindAppUserManager)
    def resetDataStatusDAO = Mock(ResetDataStatusDAO)
    def appVersionManager = Mock(AppVersionManager)
    def wechatAccountConfigDao = Mock(WechatAccountConfigDao)
    def qywxManager = Mock(QywxManager)
    def qywxCorpAgentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def sendService = Mock(SendService)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def distributePlanDAO = Mock(DistributionPlanDAO)
    def outerPhoneService = Mock(OuterPhoneService)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def userMarketingAccountRelationManager = Mock(UserMarketingAccountRelationManager)
    def qyweixinAccountBindManager = Mock(QyweixinAccountBindManager)
    def objectManager = Mock(ObjectManager)
    def wxMiniappLoginManager = Mock(WxMiniappLoginManager)
    def publicEmployeeService = Mock(PublicEmployeeService)


    // 待测系统
    def accountService = new AccountServiceImpl(accountDAO: accountDAO,
            fsBindManager: fsBindManager,
            productDAO: productDAO,
            qywxMiniappConfigDAO: qywxMiniappConfigDAO,
            employeeService: employeeService,
            eieaConverter: eieaConverter,
            enterpriseEditionService: enterpriseEditionService,
            redisManager: redisManager,
            qywxUserManager: qywxUserManager,
            wechatAccountManager: wechatAccountManager,
            qywxVirtualFsUserManager: qywxVirtualFsUserManager,
            userManager: userManager,
            qywxBindAppUserManager: qywxBindAppUserManager,
            resetDataStatusDAO: resetDataStatusDAO,
            appVersionManager: appVersionManager,
            wechatAccountConfigDao: wechatAccountConfigDao,
            qywxManager: qywxManager,
            qywxCorpAgentConfigDAO: qywxCorpAgentConfigDAO,
            sendService: sendService,
            hexagonSiteDAO: hexagonSiteDAO,
            customizeFormDataDAO: customizeFormDataDAO,
            distributePlanDAO: distributePlanDAO,
            outerPhoneService: outerPhoneService,
            fsAddressBookManager: fsAddressBookManager,
            userMarketingAccountRelationManager: userMarketingAccountRelationManager,
            qyweixinAccountBindManager: qyweixinAccountBindManager,
            objectManager: objectManager,
            wxMiniappLoginManager: wxMiniappLoginManager,
            publicEmployeeService: publicEmployeeService,
            qywxCrmAppid: "",
            partnerAppId: "",)


    @Unroll
    def "isApplyForKIS"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> ei
        employeeService.getEmployeeDto(*_) >> employeeDtoResult
        qywxManager.fsEnterpriseBindType(*_) >> fsEnterpriseBindTypeEnum
        redisManager.setKISApplyInfoToRedis(*_) >> { printf "dd" }
        qywxMiniappConfigDAO.getByEa(*_) >> new QywxMiniappConfigEntity()
        qywxUserManager.getAccountByPhone(*_) >> accountEntity
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        enterpriseEditionService.getEnterpriseData(*_) >> enterpriseDataResult
        when:
        accountService.isApplyForKIS(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                   | ei | employeeDtoResult                                     | fsEnterpriseBindTypeEnum                               | accountEntity       | fsBindEntity                                            | enterpriseDataResult
        null                                                  | 1  | null                                                  | null                                                   | null                | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: null) | 1  | null                                                  | null                                                   | null                | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | null                                                  | null                                                   | null                | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | null                                                  | null                                                   | null                | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult()                            | null                                                   | null                | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | null                | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity() | null                                                    | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity() | new FSBindEntity(fsEa: "ea", fsUserId: 1, fsCorpId: 1)  | null
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity() | new FSBindEntity(fsEa: "ea", fsUserId: 1, fsCorpId: 1)  | new GetEnterpriseDataResult()
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity() | new FSBindEntity(fsEa: "ea", fsUserId: 1, fsCorpId: 1)  | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPP    | new AccountEntity() | new FSBindEntity(fsEa: "ea2", fsUserId: 1, fsCorpId: 1) | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.BIND_QYWX_AND_MINIAPPPRO      | new AccountEntity() | new FSBindEntity(fsEa: "ea2", fsUserId: 1, fsCorpId: 1) | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
        new AccountIsApplyForKISArg(ea: "ea", fsUserId: 1)    | 1  | new GetEmployeeDtoResult(employee: new EmployeeDto()) | FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO | new AccountEntity() | new FSBindEntity(fsEa: "ea2", fsUserId: 1, fsCorpId: 1) | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
    }

    @Unroll
    def "isApplyForQyWxKIS"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        accountDAO.queryAccountByUid(*_) >> accountEntity
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        fsBindManager.insert(*_) >> 1
        fsBindManager.update(*_) >> 1
        when:
        accountService.isApplyForQyWxKIS(ea, userId, uid, "appId")
        then:
        noExceptionThrown()
        where:
        ea   | userId | uid   | accountEntity       | fsBindEntity
        null | null   | null  | null                | null
        null | null   | "uid" | null                | null
        "ea" | 1      | "uid" | null                | null
        "ea" | 1      | "uid" | new AccountEntity() | new FSBindEntity(fsUserId: *********)
        "ea" | 1      | "uid" | new AccountEntity() | null
        "ea" | 1      | "uid" | new AccountEntity() | new FSBindEntity(fsUserId: 1, fsEa: "ea", fsCorpId: 2)
    }

    @Unroll
    def "getFsUserInfo"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(*_) >> qywxUserId
        qyweixinAccountBindManager.outAccountToFsAccountBatch(*_) >> outAccountToFsAccountBatchResult
        qywxUserManager.qywxUserBindFsUserFromVirtualUser(*_) >> { printf "ddd" }
        employeeService.getEmployeeDto(*_) >> employeeDtoResult

        when:
        accountService.getFsUserInfo(ea, userId, uid)
        then:
        noExceptionThrown()
        where:
        ea   | userId    | uid  | qywxUserId   | outAccountToFsAccountBatchResult                            | employeeDtoResult
        null | null      | null | null         | null                                                        | null
        "ea" | ********* | null | null         | null                                                        | null
        "ea" | ********* | null | "qywxUserId" | new Result(errCode: -1)                                     | null
        "ea" | ********* | null | "qywxUserId" | new Result(errCode: 0, data: ["qywxUserId1": "qywxUserId"]) | null
        "ea" | ********* | null | "qywxUserId" | new Result(errCode: 0, data: ["qywxUserId": "ea.1"])        | null
        "ea" | 1         | null | "qywxUserId" | new Result(errCode: 0, data: ["qywxUserId": "ea.1"])        | null
        "ea" | 1         | null | "qywxUserId" | new Result(errCode: 0, data: ["qywxUserId": "ea.1"])        | new GetEmployeeDtoResult()
        "ea" | 1         | null | "qywxUserId" | new Result(errCode: 0, data: ["qywxUserId": "ea.1"])        | new GetEmployeeDtoResult(employee: new EmployeeDto())
    }

    @Unroll
    def "sendSMCode"() {
        given:
        hexagonSiteDAO.getById(*_) >> null
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> null
        distributePlanDAO.getDistributePlanById(*_) >> null
        productDAO.queryProductDetail(*_) >> null
        sendService.sendVerificationCode(*_) >> null
        when:
        accountService.sendSMCode("110", objectType, "id")
        then:
        noExceptionThrown()
        where:
        objectType << [26, 16, 20, 4]
    }

    @Unroll
    def "checkSMCode"() {
        given:
        outerPhoneService.checkVerifyNormalSMCode(*_) >> modelResult
        when:
        accountService.checkSMCode("110", "110")
        then:
        noExceptionThrown()
        where:
        modelResult << [new ModelResult(errCode: 0), new ModelResult(errCode: -1)]
    }

    @Unroll
    def "bindToWxWorkExternalUser"() {
        given:
        userMarketingAccountRelationManager.bindMiniappUserAndWxWorkExternalUser(*_) >> Optional.of("aa")
        when:
        accountService.bindToWxWorkExternalUser("ea", "uid", "id")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "resetAppUserData"() {
        given:
        resetDataStatusDAO.getResetDataStatusDataByUser(*_) >> resetDataStatusEntity
        resetDataStatusDAO.insert(*_) >> 1
        resetDataStatusDAO.deleteDataById(*_) >> 1
        qywxBindAppUserManager.bindAppUser(*_) >> result
        when:
        accountService.resetAppUserData("ea", 1)
        then:
        noExceptionThrown()
        where:
        resetDataStatusEntity       | result
        null                        | new com.facishare.marketing.common.result.Result(errCode: 0)
        null                        | new com.facishare.marketing.common.result.Result(errCode: -1)
        new ResetDataStatusEntity() | new com.facishare.marketing.common.result.Result(errCode: -1)
    }

    @Unroll
    def "getQywxBaseInfoFromWx"() {
        given:
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> qywxCorpAgentConfigEntity
        when:
        accountService.getQywxBaseInfoFromWx("uid")
        then:
        noExceptionThrown()
        where:
        fsBindEntity       | qywxCorpAgentConfigEntity
        null               | null
        new FSBindEntity() | null
        new FSBindEntity() | new QywxCorpAgentConfigEntity()
    }

    @Unroll
    def "getApplyInfoKeyForWx"() {
        given:
        fsBindManager.queryByFsEaAndUserId(*_) >> "uid"
        userManager.queryByUid(*_) >> userEntity
        eieaConverter.enterpriseAccountToId(*_) >> 1
        redisManager.setKISApplyInfoToRedis(*_) >> { printf "ddd" }
        when:
        accountService.getApplyInfoKeyForWx("ea", 1)
        then:
        noExceptionThrown()
        where:
        userEntity << [null, new UserEntity()]
    }

    @Unroll
    def "getDownstreamEmployeeInfo"() {
        given:
        publicEmployeeService.getDownstreamEmployeeInfo(*_) >> downstreamEmployeeInfo
        when:
        accountService.getDownstreamEmployeeInfo("ea", "1", "1")
        then:
        noExceptionThrown()
        where:
        downstreamEmployeeInfo << [new RestResult(errCode: -1), new RestResult(errCode: 0, data: new GetDownstreamEmployeeInfoResult())]
    }

    @Unroll
    def "applyForKisQywxEa"() {
        given:
        fsAddressBookManager.mustUseFxiaokeAddressBook(*_) >> mustUseFxiaokeAddressBook
        redisManager.setKISApplyInfoToRedis(*_) >> { printf "ddd" }
        qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(*_) >> qywxVirtualFsUserEntity
        qywxUserManager.getVirtualUserByQywxInfo(*_) >> qywxVirtualFsUserEntity2
        userManager.queryByCorpIdAndQYUserIdAndAppid(*_) >> userEntity
        accountDAO.queryAccountByUid(*_) >> accountEntity
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        enterpriseEditionService.getEnterpriseData(*_) >> enterpriseDataResult
        qywxUserManager.getQywxUserInfoByPhone(*_) >> staffInfo

        when:
        accountService.applyForKisQywxEa("ea", "1", "1", new EmployeeDto(), new AccountIsApplyForKISResult(), 1)
        then:
        noExceptionThrown()
        where:
        mustUseFxiaokeAddressBook | qywxVirtualFsUserEntity       | staffInfo                                      | qywxVirtualFsUserEntity2                       | userEntity       | accountEntity       | fsBindEntity                                                   | enterpriseDataResult
        true                      | null                          | null                                           | null                                           | null             | null                | null                                                           | null
        false                     | null                          | null                                           | null                                           | null             | null                | null                                                           | null
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | null                                           | null             | null                | null                                                           | null
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | null             | null                | null                                                           | null
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | null                | null                                                           | null
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | new AccountEntity() | null                                                           | null
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | new AccountEntity() | new FSBindEntity(fsCorpId: 1)                                  | null
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | new AccountEntity() | new FSBindEntity(fsCorpId: 1)                                  | new GetEnterpriseDataResult()
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | new AccountEntity() | new FSBindEntity(fsEa: "ea2", fsCorpId: 1)                     | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity(userId: *********) | new UserEntity() | new AccountEntity() | new FSBindEntity(fsEa: "ea", fsUserId: *********, fsCorpId: 1) | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
        false                     | null                          | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | new AccountEntity() | new FSBindEntity(fsEa: "ea", fsUserId: 1000, fsCorpId: 1)      | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
        false                     | new QywxVirtualFsUserEntity() | new DepartmentStaffResult.StaffInfo(userId: 1) | new QywxVirtualFsUserEntity()                  | new UserEntity() | new AccountEntity() | new FSBindEntity(fsEa: "ea", fsUserId: 1000, fsCorpId: 1)      | new GetEnterpriseDataResult(enterpriseData: new EnterpriseData())
    }

    @Unroll
    def "applyForKisQywxSpecialEnterprise"() {
        given:
        qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(*_) >> qywxVirtualFsUserEntity
        qywxUserManager.getQywxUserInfoByPhone(*_) >> staffInfo
        qywxUserManager.getVirtualUserByQywxInfo(*_) >> qywxVirtualFsUserEntity2
        redisManager.setKISApplyInfoToRedis(*_) >> { printf "ddd" }
        qywxUserManager.getAccountByPhone(*_) >> accountEntity
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity

        when:
        accountService.applyForKisQywxSpecialEnterprise("ea", "1", new EmployeeDto(), new AccountIsApplyForKISResult(), 1)
        then:
        noExceptionThrown()
        where:
        qywxVirtualFsUserEntity | staffInfo                                         | qywxVirtualFsUserEntity2                       | accountEntity       | fsBindEntity
        null                    | null                                              | null                                           | null                | null
        null                    | new DepartmentStaffResult.StaffInfo(userId: 1000) | null                                           | null                | null
        null                    | new DepartmentStaffResult.StaffInfo(userId: 1000) | new QywxVirtualFsUserEntity()                  | null                | null
        null                    | new DepartmentStaffResult.StaffInfo(userId: 1000) | new QywxVirtualFsUserEntity()                  | new AccountEntity() | null
        null                    | new DepartmentStaffResult.StaffInfo(userId: 1000) | new QywxVirtualFsUserEntity()                  | new AccountEntity() | new FSBindEntity(fsEa: "ea2")
        null                    | new DepartmentStaffResult.StaffInfo(userId: 1000) | new QywxVirtualFsUserEntity(userId: *********) | new AccountEntity() | new FSBindEntity(fsEa: "ea")
        null                    | new DepartmentStaffResult.StaffInfo(userId: 1000) | new QywxVirtualFsUserEntity(userId: 1000)      | new AccountEntity() | new FSBindEntity(fsEa: "ea")
    }

    @Unroll
    def "applyForKisNotBindQywx"() {
        given:
        qywxUserManager.getAccountByPhone(*_) >> accountEntity
        fsBindManager.queryFSBindByUid(*_) >> fsBindEntity
        fsAddressBookManager.getFxEmployeeByUserId(*_) >> employeeDto
        wxMiniappLoginManager.deleteBindFsByUid(*_) >> { printf "ddd" }
        when:
        accountService.applyForKisNotBindQywx("ea", new EmployeeDto(mobile: mobile), new AccountIsApplyForKISResult(), "key")
        then:
        noExceptionThrown()
        where:
        mobile | accountEntity       | fsBindEntity                                       | employeeDto
        null   | null                | null                                               | null
        "110"  | null                | null                                               | null
        "110"  | new AccountEntity() | null                                               | null
        "110"  | new AccountEntity() | new FSBindEntity(fsUserId: 1000)                   | new EmployeeDto(status: EmployeeEntityStatus.STOP)
        "110"  | new AccountEntity() | new FSBindEntity(fsUserId: *********, fsEa: "ea2") | new EmployeeDto(status: EmployeeEntityStatus.STOP)
        "110"  | new AccountEntity() | new FSBindEntity(fsUserId: *********, fsEa: "ea")  | new EmployeeDto(status: EmployeeEntityStatus.STOP)
    }

    @Unroll
    def "applyForKisNotBindQywxWithoutPhone"() {
        given:
        wechatAccountManager.getNotEmptyWxAppIdByEa(*_) >> "111"
        fsBindManager.queryByUserAndAppId(*_) >> fsBindEntity

        when:
        accountService.applyForKisNotBindQywxWithoutPhone("ea", new EmployeeDto(), new AccountIsApplyForKISResult(), "key")
        then:
        noExceptionThrown()
        where:
        fsBindEntity << [null, new FSBindEntity()]
    }

    @Unroll
    def "qywxBaseInfo"() {
        given:
        qywxMiniappConfigDAO.getByEa(*_) >> qywxMiniappConfigEntity
        wechatAccountConfigDao.getByWxAppId(*_) >> null
        appVersionManager.getCurrentAppVersion(*_) >> "app"
        when:
        accountService.qywxBaseInfo("ea", "appId")
        then:
        noExceptionThrown()
        where:
        qywxMiniappConfigEntity << [null, new QywxMiniappConfigEntity(appid: "appId"), new QywxMiniappConfigEntity(appid: "wxdf2d0fe00d61af56"), new QywxMiniappConfigEntity(appid: "appId2")]
    }
}
