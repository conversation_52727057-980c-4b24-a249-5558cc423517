package com.facishare.marketing.provider.service.qywx


import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.CancelMaterialTopArg
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg
import com.facishare.marketing.api.arg.TopMaterialArg
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg
import com.facishare.marketing.api.result.EditObjectGroupResult
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult
import com.facishare.marketing.api.vo.qywx.*
import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum
import com.facishare.marketing.common.enums.qywx.MiniProgramTypeEnum
import com.facishare.marketing.common.enums.qywx.QywxAttachmentTypeEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO
import com.facishare.marketing.provider.dao.QywxAttachmentsRelationDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxWelcomeMsgDAO
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import com.facishare.marketing.provider.dto.qywx.QywxWelcomeMsgDTO
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.QywxAttachmentsRelationEntity
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxWelcomeMsgEntity
import com.facishare.marketing.provider.innerResult.qywx.Department
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.FsBindManager
import com.facishare.marketing.provider.manager.ObjectGroupManager
import com.facishare.marketing.provider.manager.PhotoManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.QywxUserManager
import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult
import com.facishare.organization.api.service.DepartmentProviderService
import com.facishare.organization.api.service.EmployeeProviderService
import com.github.mybatis.pagination.Page
import spock.lang.Specification
import spock.lang.Unroll

class QywxWelcomeMsgServiceImplSpec extends Specification {

    def qywxWelcomeMsgDAO = Mock(QywxWelcomeMsgDAO)
    def qywxManager = Mock(QywxManager)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qywxUserManager = Mock(QywxUserManager)
    def employeeProviderService = Mock(EmployeeProviderService)
    def eieaConverter = Mock(EIEAConverter)
    def objectGroupManager = Mock(ObjectGroupManager)
    def objectGroupDAO = Mock(ObjectGroupDAO)
    def objectGroupRelationDAO = Mock(ObjectGroupRelationDAO)
    def objectTopManager = Mock(ObjectTopManager)
    def objectGroupRelationVisibleManager = Mock(ObjectGroupRelationVisibleManager)
    def dataPermissionManager = Mock(DataPermissionManager)
    def fsBindManager = Mock(FsBindManager)
    def departmentProviderService = Mock(DepartmentProviderService)
    def qywxAttachmentsRelationDAO = Mock(QywxAttachmentsRelationDAO)
    def objectManager = Mock(ObjectManager)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def photoManager = Mock(PhotoManager)
    def fileV2Manager = Mock(FileV2Manager)

    def qywxWelcomeMsgServiceImpl = new QywxWelcomeMsgServiceImpl(
            qywxWelcomeMsgDAO: qywxWelcomeMsgDAO,
            qywxManager: qywxManager,
            agentConfigDAO: agentConfigDAO,
            qywxUserManager: qywxUserManager,
            employeeProviderService: employeeProviderService,
            eieaConverter: eieaConverter,
            objectGroupManager: objectGroupManager,
            objectGroupDAO: objectGroupDAO,
            objectGroupRelationDAO: objectGroupRelationDAO,
            objectTopManager: objectTopManager,
            objectGroupRelationVisibleManager: objectGroupRelationVisibleManager,
            dataPermissionManager: dataPermissionManager,
            fsBindManager: fsBindManager,
            departmentProviderService: departmentProviderService,
            qywxAttachmentsRelationDAO: qywxAttachmentsRelationDAO,
            objectManager: objectManager,
            hexagonSiteDAO: hexagonSiteDAO,
            photoManager: photoManager,
            fileV2Manager: fileV2Manager
    )

    @Unroll
    def "saveWelcomeMsg - 测试各种场景 - #scenario"() {
        given:
        def vo = createQywxWelcomeMsgVO()
        vo.userIdList = userIdList
        vo.departmentIds = departmentIds
        vo.tagIds = tagIds
        vo.qywxAttachmentsVO = attachments

        dataPermissionManager.getNewDataPermissionSetting(_) >> isNewPermissionOpen
        qywxManager.batchGetEmployeeByTags(_, _) >> tagEmployeeIds
        qywxUserManager.getQywxUserIdByQywxDepartment(_, _) >> deptEmployeeIds
        qywxManager.handleQywxEmployeeUserId(_, _, _, _) >> handleEmployeeIds
        dataPermissionManager.filterUserAccessibleQywxDeptIds(_, _, _) >> accessibleDeptIds
        objectManager.convertNoticeContentTypeToObjectType(_) >> objectType
        photoManager.queryPhoto(_, _) >> photoEntities
        hexagonSiteDAO.getCoverBySiteIds(_) >> hexagonCovers
        qywxAttachmentsRelationDAO.insert(_) >> 1
        qywxWelcomeMsgDAO.saveWelcomeMsg(_) >> 1
        objectGroupManager.setGroup(_, _, _, _, _) >> {}

        when:
        def result = qywxWelcomeMsgServiceImpl.saveWelcomeMsg(vo)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario                  | isNewPermissionOpen | userIdList | departmentIds | tagIds | attachments                                                         | tagEmployeeIds | deptEmployeeIds | handleEmployeeIds | accessibleDeptIds | objectType                            | photoEntities         | hexagonCovers         | expectSuccess
        "新权限开启-有用户ID"     | true                | ["user1"]  | [1001]        | [1]    | []                                                                  | []             | []              | ["user1"]         | [1]               | 0                                     | []                    | []                    | true
        "新权限开启-无用户"       | true                | []         | []            | []     | []                                                                  | []             | []              | []                | []                | 0                                     | []                    | []                    | false
        "旧权限-有用户ID"         | false               | ["user1"]  | []            | []     | []                                                                  | []             | []              | []                | []                | 0                                     | []                    | []                    | true
        "旧权限-有部门"           | false               | []         | [1001]        | []     | []                                                                  | []             | ["user2"]       | []                | []                | 0                                     | []                    | []                    | true
        "旧权限-有标签"           | false               | []         | []            | [1]    | []                                                                  | ["user3"]      | []              | []                | []                | 0                                     | []                    | []                    | true
        "有小程序附件-活动类型"   | false               | ["user1"]  | []            | []     | createMiniprogramAttachments(ObjectTypeEnum.ACTIVITY.getType())     | []             | []              | []                | []                | ObjectTypeEnum.ACTIVITY.getType()     | [createPhotoEntity()] | []                    | true
        "有小程序附件-文章类型"   | false               | ["user1"]  | []            | []     | createMiniprogramAttachments(ObjectTypeEnum.ARTICLE.getType())      | []             | []              | []                | []                | ObjectTypeEnum.ARTICLE.getType()      | [createPhotoEntity()] | []                    | true
        "有小程序附件-产品类型"   | false               | ["user1"]  | []            | []     | createMiniprogramAttachments(ObjectTypeEnum.PRODUCT.getType())      | []             | []              | []                | []                | ObjectTypeEnum.PRODUCT.getType()      | [createPhotoEntity()] | []                    | true
        "有小程序附件-六边形站点" | false               | ["user1"]  | []            | []     | createMiniprogramAttachments(ObjectTypeEnum.HEXAGON_SITE.getType()) | []             | []              | []                | []                | ObjectTypeEnum.HEXAGON_SITE.getType() | []                    | [createHexagonSite()] | true
    }

    @Unroll
    def "queryMsgList - 测试各种查询场景 - #scenario"() {
        given:
        def vo = createQueryMsgListVO()
        vo.groupId = groupId
        vo.userId = userId

        def page = new Page(vo.pageNum, vo.pageSize, true)
        page.totalNum = totalCount

        objectGroupRelationVisibleManager.getAccessibleGroup(_, _, _) >> accessibleGroups
        qywxWelcomeMsgDAO.getAccessiblePage(_, _) >> msgList
        qywxWelcomeMsgDAO.getCreateByMePage(_, _) >> msgList
        qywxWelcomeMsgDAO.noGroupPage(_, _) >> msgList
        objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(_, _, _, _) >> subGroupIds
        agentConfigDAO.queryAgentByEa(_) >> agentConfig
        qywxAttachmentsRelationDAO.getDetailByTargetIds(_, _) >> attachmentRelations
        qywxManager.getAccessToken(_) >> (agentConfig ? "accessToken" : null)
        qywxManager.getStaffDetail(_, _, _, _) >> staffDetail
        qywxManager.queryDepartment(_) >> departmentListResult
        qywxManager.queryEmployeeInfo(_) >> employeeTags
        employeeProviderService.batchGetEmployeeDto(_) >> batchEmployeeResult
        eieaConverter.enterpriseAccountToId(_) >> 1

        // 添加缺失的mock方法
        qywxWelcomeMsgDAO.queryAccessibleCount(_, _, _) >> totalCount
        qywxWelcomeMsgDAO.queryCountCreateByMe(_, _) >> totalCount
        qywxWelcomeMsgDAO.queryCountByUnGrouped(_) >> totalCount

        when:
        def result = qywxWelcomeMsgServiceImpl.queryMsgList(vo)

        then:
        noExceptionThrown()
        where:
        scenario          | groupId                                      | userId | accessibleGroups                           | msgList                     | subGroupIds    | agentConfig         | attachmentRelations          | staffDetail         | departmentListResult         | employeeTags | batchEmployeeResult         | totalCount | expectSuccess | expectedSize
        "查询全部分组"    | DefaultObjectGroupEnum.ALL.getId()           | 1      | [createObjectGroupEntity()]                | [createQywxWelcomeMsgDTO()] | []             | createAgentConfig() | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 1          | true          | 1
        "查询我创建的"    | DefaultObjectGroupEnum.CREATED_BY_ME.getId() | 1      | []                                         | [createQywxWelcomeMsgDTO()] | []             | createAgentConfig() | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 1          | true          | 1
        "查询未分组"      | DefaultObjectGroupEnum.NO_GROUP.getId()      | 1      | []                                         | [createQywxWelcomeMsgDTO()] | []             | createAgentConfig() | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 1          | true          | 1
        "查询指定分组"    | "customGroupId"                              | 1      | [createObjectGroupEntity("customGroupId")] | [createQywxWelcomeMsgDTO()] | ["subGroupId"] | createAgentConfig() | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 1          | true          | 1
        "无权限访问分组"  | "customGroupId"                              | 1      | []                                         | []                          | []             | createAgentConfig() | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 0          | true          | 0
        "Agent配置不存在" | DefaultObjectGroupEnum.ALL.getId()           | 1      | []                                         | []                          | []             | null                | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 0          | false         | 0
        "空消息列表"      | DefaultObjectGroupEnum.ALL.getId()           | 1      | []                                         | []                          | []             | createAgentConfig() | []                           | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 0          | true          | 0
        "有附件关系"      | DefaultObjectGroupEnum.ALL.getId()           | 1      | []                                         | [createQywxWelcomeMsgDTO()] | []             | createAgentConfig() | [createAttachmentRelation()] | createStaffDetail() | createDepartmentListResult() | []           | createBatchEmployeeResult() | 1          | true          | 1
    }

    @Unroll
    def "updateWelcomeMsg - 测试更新场景 - #scenario"() {
        given:
        def vo = createUpdateWelcomeMsgVO()
        vo.userIdList = userIdList
        vo.departmentIds = departmentIds
        vo.tagIds = tagIds
        vo.qywxAttachmentsVO = attachments

        def existingEntity = scenario.contains("不存在") ? null : createQywxWelcomeMsgEntity()

        qywxWelcomeMsgDAO.queryWelcomeMsgDetail(_) >> existingEntity
        dataPermissionManager.getNewDataPermissionSetting(_) >> isNewPermissionOpen
        qywxManager.batchGetEmployeeByTags(_, _) >> tagEmployeeIds
        qywxUserManager.getQywxUserIdByQywxDepartment(_, _) >> deptEmployeeIds
        qywxManager.handleQywxEmployeeUserId(_, _, _, _) >> handleEmployeeIds
        dataPermissionManager.filterUserAccessibleQywxDeptIds(_, _, _) >> accessibleDeptIds
        objectManager.convertNoticeContentTypeToObjectType(_) >> objectType
        photoManager.queryPhoto(_, _) >> photoEntities
        hexagonSiteDAO.getCoverBySiteIds(_) >> hexagonCovers
        qywxAttachmentsRelationDAO.getDetailByTargetId(_, _) >> existingAttachment
        qywxAttachmentsRelationDAO.insert(_) >> 1
        qywxAttachmentsRelationDAO.updateByTargetId(_, _, _) >> 1
        qywxWelcomeMsgDAO.updateQywxWelcomeMsgById(_) >> 1

        when:
        def result = qywxWelcomeMsgServiceImpl.updateWelcomeMsg(vo)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario          | isNewPermissionOpen | userIdList | departmentIds | tagIds | attachments                                                     | tagEmployeeIds | deptEmployeeIds | handleEmployeeIds | accessibleDeptIds | objectType                        | photoEntities         | hexagonCovers | existingAttachment         | expectSuccess
        "消息不存在"      | false               | ["user1"]  | []            | []     | []                                                              | []             | []              | []                | []                | 0                                 | []                    | []            | null                       | false
        "新权限-正常更新" | true                | ["user1"]  | [1001]        | [1]    | []                                                              | []             | []              | ["user1"]         | [1]               | 0                                 | []                    | []            | null                       | true
        "旧权限-正常更新" | false               | ["user1"]  | [1001]        | [1]    | []                                                              | ["user2"]      | []              | []                | []                | 0                                 | []                    | []            | null                       | true
        "无用户权限"      | false               | []         | []            | []     | []                                                              | []             | []              | []                | []                | 0                                 | []                    | []            | null                       | false
        "有附件-新建关系" | false               | ["user1"]  | []            | []     | createMiniprogramAttachments(ObjectTypeEnum.ACTIVITY.getType()) | []             | []              | []                | []                | ObjectTypeEnum.ACTIVITY.getType() | [createPhotoEntity()] | []            | null                       | true
        "有附件-更新关系" | false               | ["user1"]  | []            | []     | createMiniprogramAttachments(ObjectTypeEnum.ACTIVITY.getType()) | []             | []              | []                | []                | ObjectTypeEnum.ACTIVITY.getType() | [createPhotoEntity()] | []            | createAttachmentRelation() | true
    }

    @Unroll
    def "updateWelcomeMsg - 测试附件处理场景 - #scenario"() {
        given:
        def vo = createUpdateWelcomeMsgVO()
        vo.qywxAttachmentsVO = attachments

        def existingEntity = createQywxWelcomeMsgEntity()

        qywxWelcomeMsgDAO.queryWelcomeMsgDetail(_) >> existingEntity
        dataPermissionManager.getNewDataPermissionSetting(_) >> false
        qywxManager.batchGetEmployeeByTags(_, _) >> []
        qywxUserManager.getQywxUserIdByQywxDepartment(_, _) >> []
        qywxManager.handleQywxEmployeeUserId(_, _, _, _) >> ["user1"]
        dataPermissionManager.filterUserAccessibleQywxDeptIds(_, _, _) >> []
        objectManager.convertNoticeContentTypeToObjectType(_) >> objectType
        photoManager.queryPhoto(_, _) >> photoEntities
        hexagonSiteDAO.getCoverBySiteIds(_) >> hexagonCovers
        qywxAttachmentsRelationDAO.getDetailByTargetId(_, _) >> existingAttachment
        qywxAttachmentsRelationDAO.insert(_) >> 1
        qywxAttachmentsRelationDAO.updateByTargetId(_, _, _) >> 1
        qywxWelcomeMsgDAO.updateQywxWelcomeMsgById(_) >> 1
        photoManager.queryPhoto(*_) >> [new PhotoEntity(path: "nnn")]
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO(sharePicH5Apath: "path")]
        when:
        def result = qywxWelcomeMsgServiceImpl.updateWelcomeMsg(vo)

        then:
        noExceptionThrown()

        where:
        scenario                  | attachments                                                                            | objectType                            | photoEntities         | hexagonCovers         | existingAttachment         | expectSuccess
        "有小程序附件-活动类型"   | createMiniprogramAttachmentsWithEmptyPic(QywxAttachmentTypeEnum.MINIPROGRAM.getType()) | ObjectTypeEnum.ACTIVITY.getType()     | [createPhotoEntity()] | []                    | null                       | true
        "有小程序附件-活动类型"   | createMiniprogramAttachmentsWithEmptyPic(QywxAttachmentTypeEnum.MINIPROGRAM.getType()) | ObjectTypeEnum.ARTICLE.getType()      | [createPhotoEntity()] | []                    | null                       | true
        "有小程序附件-活动类型"   | createMiniprogramAttachmentsWithEmptyPic(ObjectTypeEnum.ACTIVITY.getType())            | ObjectTypeEnum.PRODUCT.getType()      | [createPhotoEntity()] | []                    | null                       | true
        "有小程序附件-活动类型"   | createMiniprogramAttachmentsWithEmptyPic(ObjectTypeEnum.ACTIVITY.getType())            | ObjectTypeEnum.HEXAGON_SITE.getType() | [createPhotoEntity()] | []                    | null                       | true
        "有小程序附件-文章类型"   | createMiniprogramAttachmentsWithEmptyPic(ObjectTypeEnum.ARTICLE.getType())             | ObjectTypeEnum.ARTICLE.getType()      | [createPhotoEntity()] | []                    | null                       | true
        "有小程序附件-产品类型"   | createMiniprogramAttachmentsWithEmptyPic(ObjectTypeEnum.PRODUCT.getType())             | ObjectTypeEnum.PRODUCT.getType()      | [createPhotoEntity()] | []                    | null                       | true
        "有小程序附件-六边形站点" | createMiniprogramAttachmentsWithEmptyPic(ObjectTypeEnum.HEXAGON_SITE.getType())        | ObjectTypeEnum.HEXAGON_SITE.getType() | []                    | [createHexagonSite()] | null                       | true
        "有小程序附件-无封面图片" | createMiniprogramAttachmentsWithEmptyPic(ObjectTypeEnum.ACTIVITY.getType())            | ObjectTypeEnum.ACTIVITY.getType()     | []                    | []                    | null                       | true
        "空附件关系-有附件"       | createMiniprogramAttachments(ObjectTypeEnum.ACTIVITY.getType())                        | ObjectTypeEnum.ACTIVITY.getType()     | [createPhotoEntity()] | []                    | null                       | true
        "空附件关系-无附件"       | []                                                                                     | 0                                     | []                    | []                    | null                       | true
        "已存在附件关系-更新"     | createMiniprogramAttachments(ObjectTypeEnum.ACTIVITY.getType())                        | ObjectTypeEnum.ACTIVITY.getType()     | [createPhotoEntity()] | []                    | createAttachmentRelation() | true
        "已存在附件关系-清空"     | []                                                                                     | 0                                     | []                    | []                    | createAttachmentRelation() | true
    }

    @Unroll
    def "updateWelcomeMsg - 测试权限系统分支 - #scenario"() {
        given:
        def vo = createUpdateWelcomeMsgVO()
        vo.userIdList = userIdList
        vo.departmentIds = departmentIds
        vo.tagIds = tagIds

        def existingEntity = createQywxWelcomeMsgEntity()

        qywxWelcomeMsgDAO.queryWelcomeMsgDetail(_) >> existingEntity
        dataPermissionManager.getNewDataPermissionSetting(_) >> isNewPermissionOpen
        qywxManager.batchGetEmployeeByTags(_, _) >> tagEmployeeIds
        qywxUserManager.getQywxUserIdByQywxDepartment(_, _) >> deptEmployeeIds
        qywxManager.handleQywxEmployeeUserId(_, _, _, _) >> handleEmployeeIds
        dataPermissionManager.filterUserAccessibleQywxDeptIds(_, _, _) >> accessibleDeptIds
        qywxAttachmentsRelationDAO.getDetailByTargetId(_, _) >> null
        qywxAttachmentsRelationDAO.insert(_) >> 1
        qywxWelcomeMsgDAO.updateQywxWelcomeMsgById(_) >> 1

        when:
        def result = qywxWelcomeMsgServiceImpl.updateWelcomeMsg(vo)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario              | isNewPermissionOpen | userIdList | departmentIds | tagIds | tagEmployeeIds | deptEmployeeIds | handleEmployeeIds | accessibleDeptIds | expectSuccess
        "新权限-有用户ID"     | true                | ["user1"]  | [1001]        | [1]    | []             | []              | ["user1"]         | [1001]            | true
        "新权限-有部门无权限" | true                | []         | [1001]        | []     | []             | []              | []                | []                | false
        "旧权限-有用户ID"     | false               | ["user1"]  | []            | []     | []             | []              | []                | []                | true
        "旧权限-有部门ID"     | false               | []         | [1001]        | []     | []             | ["user2"]       | []                | []                | true
        "旧权限-有标签ID"     | false               | []         | []            | [1]    | ["user3"]      | []              | []                | []                | true
        "旧权限-混合权限"     | false               | ["user1"]  | [1001]        | [1]    | ["user3"]      | ["user2"]       | []                | []                | true
        "旧权限-无任何权限"   | false               | []         | []            | []     | []             | []              | []                | []                | false
    }

    @Unroll
    def "deleteWelcomeMsg - 测试删除场景 - #scenario"() {
        given:
        def vo = createDeleteWelcomeMsgVO()
        def existingEntity = scenario.contains("不存在") ? null : createQywxWelcomeMsgEntity()

        qywxWelcomeMsgDAO.queryWelcomeMsgDetail(_) >> existingEntity
        qywxWelcomeMsgDAO.deleteWelcomeMsg(_) >> 1
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(_, _, _) >> 1
        objectTopManager.deleteByObjectIdAndObjectType(_, _) >> {}

        when:
        def result = qywxWelcomeMsgServiceImpl.deleteWelcomeMsg(vo)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario     | expectSuccess
        "消息不存在" | false
        "正常删除"   | true
    }

    @Unroll
    def "editQywxWelcomeMsgGroup - 测试编辑分组场景 - #scenario"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createEditObjectGroupArg()
        arg.name = groupName

        objectGroupManager.isAppAdmin(_, _) >> isAppAdmin
        objectGroupManager.editGroup(_, _, _, _, _) >> Result.newSuccess(new EditObjectGroupResult())

        when:
        def result = qywxWelcomeMsgServiceImpl.editQywxWelcomeMsgGroup(ea, fsUserId, arg)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario       | isAppAdmin | groupName                            | expectSuccess
        "非应用管理员" | false      | "新分组名"                           | false
        "使用默认名称" | true       | DefaultObjectGroupEnum.ALL.getName() | false
        "正常编辑"     | true       | "新分组名"                           | true
    }

    @Unroll
    def "deleteQywxWelcomeMsgGroup - 测试删除分组场景 - #scenario"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createDeleteObjectGroupArg()

        objectGroupManager.isAppAdmin(_, _) >> isAppAdmin
        objectGroupManager.deleteGroup(_, _, _, _) >> Result.newSuccess()

        when:
        def result = qywxWelcomeMsgServiceImpl.deleteQywxWelcomeMsgGroup(ea, fsUserId, arg)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario       | isAppAdmin | expectSuccess
        "非应用管理员" | false      | false
        "正常删除"     | true       | true
    }

    @Unroll
    def "setQywxWelcomeMsgGroup - 测试设置分组场景 - #scenario"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createSetObjectGroupArg()
        arg.objectIdList = objectIds

        def entities = scenario.contains("不存在") ? [] :
                scenario.contains("部分") ? [createQywxWelcomeMsgEntity()] :
                        [createQywxWelcomeMsgEntity(), createQywxWelcomeMsgEntity()]
        def groupEntity = scenario.contains("分组不存在") ? null : createObjectGroupEntity()

        qywxWelcomeMsgDAO.getByIds(_) >> entities
        objectGroupDAO.getById(_, _) >> groupEntity
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(_, _, _) >> 1
        objectGroupRelationDAO.batchInsert(_) >> 1

        when:
        def result = qywxWelcomeMsgServiceImpl.setQywxWelcomeMsgGroup(ea, fsUserId, arg)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario         | objectIds      | expectSuccess
        "消息不存在"     | ["id1", "id2"] | false
        "部分消息不存在" | ["id1", "id2"] | false
        "分组不存在"     | ["id1", "id2"] | false
        "正常设置"       | ["id1", "id2"] | true
    }

    @Unroll
    def "topQywxWelcomeMsg - 测试置顶场景 - #scenario"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createTopMaterialArg()

        def entity = scenario.contains("不存在") ? null : createQywxWelcomeMsgEntity()

        qywxWelcomeMsgDAO.getById(_) >> entity
        objectTopManager.insert(_, _) >> {}

        when:
        def result = qywxWelcomeMsgServiceImpl.topQywxWelcomeMsg(ea, fsUserId, arg)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario     | expectSuccess
        "消息不存在" | false
        "正常置顶"   | true
    }

    @Unroll
    def "cancelTopQywxWelcomeMsg - 测试取消置顶"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createCancelMaterialTopArg()

        objectTopManager.cancelTop(_, _, _, _) >> {}

        when:
        def result = qywxWelcomeMsgServiceImpl.cancelTopQywxWelcomeMsg(ea, fsUserId, arg)

        then:
        result.isSuccess()
    }

    @Unroll
    def "addQywxWelcomeMsgGroupRole - 测试添加分组角色场景 - #scenario"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createSaveObjectGroupVisibleArg()

        objectGroupManager.isAppAdmin(_, _) >> isAppAdmin
        objectGroupRelationVisibleManager.saveGroupRelation(_, _, _) >> {}

        when:
        def result = qywxWelcomeMsgServiceImpl.addQywxWelcomeMsgGroupRole(ea, fsUserId, arg)

        then:
        result.isSuccess() == expectSuccess

        where:
        scenario       | isAppAdmin | expectSuccess
        "非应用管理员" | false      | false
        "正常添加"     | true       | true
    }

    @Unroll
    def "listQywxWelcomeMsgGroup - 测试列表分组场景 - #scenario"() {
        given:
        def ea = "testEa"
        def fsUserId = 1
        def arg = createListGroupArg()
        arg.useType = useType

        def customGroups = createObjectGroupListResult()

        objectGroupManager.getShowGroup(_, _, _, _, _) >> customGroups
        qywxWelcomeMsgDAO.queryUnGroupAndCreateByMeCount(_, _) >> 5
        qywxWelcomeMsgDAO.queryAccessibleCount(_, _, _) >> 10
        qywxWelcomeMsgDAO.queryCountCreateByMe(_, _) >> 3
        qywxWelcomeMsgDAO.queryCountByUnGrouped(_) >> 2

        when:
        def result = qywxWelcomeMsgServiceImpl.listQywxWelcomeMsgGroup(ea, fsUserId, arg)

        then:
        result.isSuccess()
        if (useType == 0) {
            result.data.objectGroupList.size() >= 3  // 至少包含系统默认分组
        }

        where:
        scenario           | useType
        "获取使用类型分组" | 0
        "获取其他类型分组" | 1
        "空使用类型"       | null
    }

    def "getQywxWelcomeMsgGroupRole - 测试获取分组角色"() {
        given:
        def groupId = "testGroupId"
        def roleRelations = [createObjectGroupRoleRelation()]

        objectGroupRelationVisibleManager.getRoleRelationByGroupId(_) >> roleRelations

        when:
        def result = qywxWelcomeMsgServiceImpl.getQywxWelcomeMsgGroupRole(groupId)

        then:
        result.isSuccess()
        result.data.size() == 1
    }

    @Unroll
    def "queryMsgList - 测试参数验证 - #scenario"() {
        given:
        def vo = new QueryMsgListVO()
        vo.ea = ea
        vo.pageSize = pageSize
        vo.pageNum = pageNum
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> []
        when:
        def result = qywxWelcomeMsgServiceImpl.queryMsgList(vo)

        then:
        noExceptionThrown()

        where:
        scenario       | ea   | pageSize | pageNum
        "EA为空"       | "ea" | 10       | 1
        "EA为空字符串" | "ea" | 10       | 1
    }

    // Helper methods for creating test data
    private createQywxWelcomeMsgVO() {
        def vo = new QywxWelcomeMsgVO()
        vo.ea = "testEa"
        vo.operator = 1
        vo.welcomeMsgName = "测试标题"
        vo.welcomeMsgContent = "测试内容"
        vo.groupId = "testGroupId"
        vo.userIdList = []
        vo.departmentIds = []
        vo.tagIds = []
        vo.qywxAttachmentsVO = []
        return vo
    }

    private createQueryMsgListVO() {
        def vo = new QueryMsgListVO()
        vo.ea = "testEa"
        vo.userId = 1
        vo.pageNum = 1
        vo.pageSize = 10
        vo.groupId = DefaultObjectGroupEnum.ALL.getId()
        return vo
    }

    private createUpdateWelcomeMsgVO() {
        def vo = new UpdateWelcomeMsgVO()
        vo.id = "testId"
        vo.ea = "testEa"
        vo.operator = 1
        vo.welcomeMsgName = "更新标题"
        vo.welcomeMsgContent = "更新内容"
        vo.userIdList = ["ididd"]
        vo.departmentIds = []
        vo.tagIds = []
        vo.qywxAttachmentsVO = []
        return vo
    }

    private createDeleteWelcomeMsgVO() {
        def vo = new DeleteWelcomeMsgVO()
        vo.id = "testId"
        return vo
    }

    private createMiniprogramAttachments(int materialType) {
        def attachment = new QywxAttachmentsVO()
        attachment.attachmentType = QywxAttachmentTypeEnum.MINIPROGRAM.getType()
        def miniprogram = new QywxAttachmentsVO.Miniprogram()
        miniprogram.miniProgramType = MiniProgramTypeEnum.CONTENT.getType()
        miniprogram.materialType = materialType
        miniprogram.materialId = "materialId123"
        miniprogram.picPath = "path/to/pic"
        attachment.miniprogram = miniprogram
        return [attachment]
    }

    private createMiniprogramAttachmentsWithEmptyPic(int materialType) {
        def attachment = new QywxAttachmentsVO()
        attachment.attachmentType = QywxAttachmentTypeEnum.MINIPROGRAM.getType()
        def miniprogram = new QywxAttachmentsVO.Miniprogram()
        miniprogram.miniProgramType = MiniProgramTypeEnum.CONTENT.getType()
        miniprogram.materialType = materialType
        miniprogram.materialId = "materialId123"
        miniprogram.picPath = null  // 空封面路径，触发封面处理逻辑
        attachment.miniprogram = miniprogram
        return [attachment]
    }

    private createPhotoEntity() {
        def entity = new PhotoEntity()
        entity.path = "test/path/image.jpg"
        return entity
    }

    private createHexagonSite() {
        def dto = new HexagonSiteListDTO()
        dto.sharePicH5Apath = "test/path/cover.jpg"
        return dto
    }

    private createQywxWelcomeMsgDTO() {
        def dto = new QywxWelcomeMsgDTO()
        dto.id = "testId"
        dto.ea = "testEa"
        dto.operator = 1
        dto.welcomeMsgName = "测试标题"
        dto.welcomeMsgContent = "测试内容"
        dto.userIdList = '["user1"]'
        dto.departmentId = '[1001]'
        dto.tagId = '[1]'
        dto.contentType = 0
        dto.createTime = new Date()
        dto.updateTime = new Date()
        return dto
    }

    private createQywxWelcomeMsgEntity() {
        def entity = new QywxWelcomeMsgEntity()
        entity.id = "testId"
        entity.ea = "testEa"
        entity.operator = 1
        entity.welcomeMsgName = "测试标题"
        entity.welcomeMsgContent = "测试内容"
        entity.userIdList = '["user1"]'
        entity.departmentId = '[1001]'
        entity.tagId = '[1]'
        entity.contentType = 0
        entity.createTime = new Date()
        entity.updateTime = new Date()
        return entity
    }

    private createObjectGroupEntity(String id = "testGroupId") {
        def entity = new ObjectGroupEntity()
        entity.id = id
        entity.name = "测试分组"
        entity.ea = "testEa"
        return entity
    }

    private createAgentConfig() {
        def entity = new QywxCorpAgentConfigEntity()
        entity.ea = "testEa"
        entity.corpid = "testCorpId"
        return entity
    }

    private createAttachmentRelation() {
        def entity = new QywxAttachmentsRelationEntity()
        entity.targetId = "testId"
        entity.attachments = '[]'
        return entity
    }

    private createStaffDetail() {
        def detail = new StaffDetailResult()
        detail.name = "员工姓名"
        return detail
    }

    private createDepartmentListResult() {
        def result = new DepartmentListResult()
        def dept = new Department()
        dept.id = 1
        dept.name = "测试部门"
        result.departmentList = [dept]
        return result
    }

    private createBatchEmployeeResult() {
        def result = new BatchGetEmployeeDtoResult()
        def employee = new EmployeeDto()
        employee.employeeId = 1
        employee.name = "员工姓名"
        result.employeeDtos = [employee]
        return result
    }

    private createEditObjectGroupArg() {
        def arg = new EditObjectGroupArg()
        arg.id = "testGroupId"
        arg.name = "新分组名"
        return arg
    }

    private createDeleteObjectGroupArg() {
        def arg = new DeleteObjectGroupArg()
        arg.id = "testGroupId"
        return arg
    }

    private createSetObjectGroupArg() {
        def arg = new SetObjectGroupArg()
        arg.groupId = "testGroupId"
        arg.objectIdList = ["id1", "id2"]
        return arg
    }

    private createTopMaterialArg() {
        def arg = new TopMaterialArg()
        arg.objectId = "testId"
        return arg
    }

    private createCancelMaterialTopArg() {
        def arg = new CancelMaterialTopArg()
        arg.objectId = "testId"
        return arg
    }

    private createSaveObjectGroupVisibleArg() {
        def arg = new SaveObjectGroupVisibleArg()
        arg.groupId = "testGroupId"
        arg.roleIdList = ["roleId1"]
        return arg
    }

    private createListGroupArg() {
        def arg = new ListGroupArg()
        arg.useType = 0
        return arg
    }

    private createObjectGroupListResult() {
        def result = new ObjectGroupListResult()
        result.sortVersion = 1L
        result.objectGroupList = []
        return result
    }

    private createObjectGroupRoleRelation() {
        def entity = new ObjectGroupRoleRelationEntity()
        entity.roleId = "roleId1"
        return entity
    }

    private createUpdateWelcomeMsgVOWithId(String id) {
        def vo = createUpdateWelcomeMsgVO()
        vo.id = id
        return vo
    }

    private createDeleteWelcomeMsgVOWithId(String id) {
        def vo = new DeleteWelcomeMsgVO()
        vo.id = id
        return vo
    }
} 