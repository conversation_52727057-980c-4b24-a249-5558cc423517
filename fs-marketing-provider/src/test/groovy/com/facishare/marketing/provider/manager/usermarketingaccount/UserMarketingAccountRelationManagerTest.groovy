package com.facishare.marketing.provider.manager.usermarketingaccount

import com.facishare.marketing.api.result.marketinguser.MarketingUserExcludeApinameResult
import com.facishare.marketing.common.enums.ChannelEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.provider.dao.MarketingUserGroupToUserRelationDao
import com.facishare.marketing.provider.dao.TriggerInstanceDao
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmAccountAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmContactAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmCustomizeObjectRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmLeadAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmMemberRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmWxUserAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingCrmWxWorkExternalUserRelationDao
import com.facishare.marketing.provider.dao.UserMarketingMiniappAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingWxServiceAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingWxWorkExternalUserRelationDao
import com.facishare.marketing.provider.dao.marketingUserGroup.UserMarketingExcludeObjectDAO
import com.facishare.marketing.provider.entity.MarketingUserGroupToUserRelationEntity
import com.facishare.marketing.provider.entity.UserMarketingExcludeObjectEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmAccountAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmContactAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmMemberRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmWxUserAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmWxWorkExternalUserRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCustomizeObjectRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity
import com.facishare.marketing.provider.innerArg.AssociationArg
import com.facishare.marketing.provider.innerData.ObjectIdAndDescribeData
import com.facishare.marketing.provider.innerResult.AssociationResult
import com.google.common.collect.Lists
import org.apache.zookeeper.Op
import spock.lang.*

class UserMarketingAccountRelationManagerTest extends Specification {

    def userMarketingAccountRelationManager = new UserMarketingAccountRelationManager()

    def userMarketingCrmLeadAccountRelationDao = Mock(UserMarketingCrmLeadAccountRelationDao)
    def userMarketingCrmWxUserAccountRelationDao = Mock(UserMarketingCrmWxUserAccountRelationDao)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingExcludeObjectDAO = Mock(UserMarketingExcludeObjectDAO)
    def userMarketingWxServiceAccountRelationDao = Mock(UserMarketingWxServiceAccountRelationDao)
    def userMarketingCrmContactAccountRelationDao = Mock(UserMarketingCrmContactAccountRelationDao)
    def userMarketingCrmAccountAccountRelationDao = Mock(UserMarketingCrmAccountAccountRelationDao)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def userMarketingCrmWxWorkExternalUserRelationDao = Mock(UserMarketingCrmWxWorkExternalUserRelationDao)
    def userMarketingWxWorkExternalUserRelationDao = Mock(UserMarketingWxWorkExternalUserRelationDao)
    def userMarketingCrmMemberRelationDao = Mock(UserMarketingCrmMemberRelationDao)
    def marketingUserGroupToUserRelationDao = Mock(MarketingUserGroupToUserRelationDao)
    def userMarketingAccountAssociationManager = Mock(UserMarketingAccountAssociationManager)
    def userMarketingCrmCustomizeObjectRelationDao = Mock(UserMarketingCrmCustomizeObjectRelationDao)
    def triggerInstanceDao = Mock(TriggerInstanceDao)

    def setup() {
        userMarketingAccountRelationManager.userMarketingCrmLeadAccountRelationDao = userMarketingCrmLeadAccountRelationDao
        userMarketingAccountRelationManager.userMarketingCrmWxUserAccountRelationDao = userMarketingCrmWxUserAccountRelationDao
        userMarketingAccountRelationManager.userMarketingMiniappAccountRelationDao = userMarketingMiniappAccountRelationDao
        userMarketingAccountRelationManager.userMarketingExcludeObjectDAO = userMarketingExcludeObjectDAO
        userMarketingAccountRelationManager.userMarketingWxServiceAccountRelationDao = userMarketingWxServiceAccountRelationDao
        userMarketingAccountRelationManager.userMarketingCrmContactAccountRelationDao = userMarketingCrmContactAccountRelationDao
        userMarketingAccountRelationManager.userMarketingCrmAccountAccountRelationDao = userMarketingCrmAccountAccountRelationDao
        userMarketingAccountRelationManager.userMarketingBrowserUserRelationDao = userMarketingBrowserUserRelationDao
        userMarketingAccountRelationManager.userMarketingAccountDAO = userMarketingAccountDAO
        userMarketingAccountRelationManager.userMarketingCrmWxWorkExternalUserRelationDao = userMarketingCrmWxWorkExternalUserRelationDao
        userMarketingAccountRelationManager.userMarketingWxWorkExternalUserRelationDao = userMarketingWxWorkExternalUserRelationDao
        userMarketingAccountRelationManager.userMarketingCrmMemberRelationDao = userMarketingCrmMemberRelationDao
        userMarketingAccountRelationManager.marketingUserGroupToUserRelationDao = marketingUserGroupToUserRelationDao
        userMarketingAccountRelationManager.userMarketingAccountAssociationManager = userMarketingAccountAssociationManager
        userMarketingAccountRelationManager.userMarketingCrmCustomizeObjectRelationDao = userMarketingCrmCustomizeObjectRelationDao
        userMarketingAccountRelationManager.triggerInstanceDao = triggerInstanceDao
        //    userMarketingAccountRelationManager.USER_MARKETING_RELATION_EXCLUDE_OBJECT_STATUS = USER_MARKETING_RELATION_EXCLUDE_OBJECT_STATUS
    }


    def "bindBrowserUserAndLeadTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindBrowserUserAndLead("ea", "browserUserId", "crmLeadId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindBrowserUserAndCustomerTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"
        userMarketingAccountAssociationManager.associate(*_) >> associateMock

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindBrowserUserAndCustomer("ea", browserUserId, "customerId", "phone")
        then:
        result == resultMock
        where:
        browserUserId | associateMock                                        | resultMock
        null          | null                                                 | Optional.empty()
        null          | new AssociationResult(userMarketingAccountId: "111") | Optional.of("111")
        "**********"  | null                                                 | Optional.of("bindResponse")
    }


    def "bindBrowserUserAndContactTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"
        userMarketingAccountAssociationManager.associate(*_) >> associateMock

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindBrowserUserAndContact("ea", browserUserId, "contactId", "phone")
        then:
        result == resultMock
        where:
        browserUserId | associateMock                                        | resultMock
        null          | null                                                 | Optional.empty()
        null          | new AssociationResult(userMarketingAccountId: "111") | Optional.of("111")
        "**********"  | null                                                 | Optional.of("bindResponse")
    }


    def "bindLoginIdentityAndCrmObjectTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindLoginIdentityAndCrmObject(ea, new AssociationArg(), ChannelEnum.MINIAPP, "crmObjectId", "phone")
        then:
        result == resultMock
        where:
        ea      | resultMock
        null    | Optional.empty()
        "88146" | Optional.of("bindResponse")
    }


    def "bindWxUserAndLeadTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindWxUserAndLead("ea", "wxAppId", "wxOpenId", "crmLeadId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindWxUserAndCustomerTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindWxUserAndCustomer("ea", "wxAppId", "wxOpenId", "customerId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindWxUserAndContactTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> bindResponse

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindWxUserAndContact("ea", "wxAppId", "wxOpenId", "contactId", "phone")
        then:
        result == resultMock
        where:
        bindResponse | resultMock
        "111"        | Optional.of("111")
    }


    def "bindMiniappUserAndLeadTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindMiniappUserAndLead("ea", "uid", "leadId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindMiniappUserAndMemberTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindMiniappUserAndMember("ea", "uid", "memberId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindMiniappUserAndCustomerTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindMiniappUserAndCustomer("ea", "uid", "customerId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindMiniappUserAndWxWorkExternalUserTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindMiniappUserAndWxWorkExternalUser("ea", "uid", "externalUserId")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindWxUserAndBrowserUserTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindWxUserAndBrowserUser("ea", "wxAppId", "wxOpenId", "browserUserId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindQywxExternalUserAndBrowserUserTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindQywxExternalUserAndBrowserUser("ea", "qywxCrmObjectId", "browserUserId", "externUserId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindMiniappUserAndContactTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindMiniappUserAndContact("ea", "uid", "crmContactId", "phone")
        then:
        result == Optional.of("bindResponse")
    }


    def "bindMinappUserAndWxUserTest"() {
        given:
        userMarketingAccountAssociationManager.bind(*_) >> "bindResponse"

        when:
        Optional<String> result = userMarketingAccountRelationManager.bindMinappUserAndWxUser("ea", "uid", "crmWxUserId", "miniAppPhone", "crmWxUserPhone")
        then:
        result == Optional.of("bindResponse")
    }


    def "getUserMarketingAccountIdByCrmObjectIdTest"() {
        given:
        def spy = Spy(userMarketingAccountRelationManager)
        spy.getByEaAndKeyProperties(*_) >> associationResultMock
        when:
        Optional<String> result = spy.getUserMarketingAccountIdByCrmObjectId("ea", ChannelEnum.CRM_LEAD.apiName, "222")
        then:
        result == resultMock
        where:
        associationResultMock                                | resultMock
        new AssociationResult(userMarketingAccountId: "111") | Optional.of("111")
    }


    def "getByEaAndKeyPropertiesTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadId(*_) >> new UserMarketingCrmLeadAccountRelationEntity(crmLeadId: "111", userMarketingId: "222", ea: "88146")
        userMarketingCrmWxUserAccountRelationDao.getByEaAndCrmWxUserId(*_) >> new UserMarketingCrmWxUserAccountRelationEntity(crmWxUserId: "111", userMarketingId: "222", ea: "88146")
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity(uid: "111", userMarketingId: "222", ea: "88146")
        userMarketingWxServiceAccountRelationDao.getByEaAndWxAppIdAndWxOpenId(*_) >> new UserMarketingWxServiceAccountRelationEntity(userMarketingId: "222", ea: "88146", wxAppId: "aa", wxOpenId: "bb")
        userMarketingCrmContactAccountRelationDao.getByEaAndCrmContactId(*_) >> new UserMarketingCrmContactAccountRelationEntity(crmContactId: "111", userMarketingId: "222", ea: "88146")
        userMarketingCrmAccountAccountRelationDao.getByEaAndCrmAccountId(*_) >> new UserMarketingCrmAccountAccountRelationEntity(crmAccountId: "111", userMarketingId: "222", ea: "88146")
        userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(*_) >> new UserMarketingBrowserUserRelationEntity(browserUserId: "111", userMarketingId: "222", ea: "88146")
        userMarketingCrmWxWorkExternalUserRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmWxWorkExternalUserRelationEntity(crmWxWorkExternalUserObjectId: "111", userMarketingId: "222", ea: "88146")
        userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(*_) >> new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: "111", userMarketingId: "222", ea: "88146")
        userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmMemberRelationEntity(crmMemberObjectId: "111", userMarketingId: "222", ea: "88146")
        userMarketingCrmCustomizeObjectRelationDao.getByEaAndCustomizeObjectId(*_) >> new UserMarketingCustomizeObjectRelationEntity(objectId: "111", userMarketingId: "222", ea: "88146")

        when:
        AssociationResult result = userMarketingAccountRelationManager.getByEaAndKeyProperties(associationArg)
        then:
        result == resultMock
        where:
        associationArg                                                            | resultMock
        new AssociationArg(type: ChannelEnum.MINIAPP.getType())                   | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.WX_SERVICE.getType())                | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CRM_CONTACT.getType())               | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CRM_ACCOUNT.getType())               | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CRM_LEAD.getType())                  | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.getType())               | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.BROWSER_USER.getType())              | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.getType())     | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType()) | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CRM_MEMBER.getType())                | new AssociationResult(userMarketingAccountId: "222")
        new AssociationArg(type: ChannelEnum.CUSTOMIZE_OBJECT.getType())          | new AssociationResult(userMarketingAccountId: "222")
    }


    def "isExistsByEaAndKeyPropertiesTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadId(*_) >> new UserMarketingCrmLeadAccountRelationEntity(crmLeadId: "111", userMarketingId: "222")
        userMarketingCrmWxUserAccountRelationDao.getByEaAndCrmWxUserId(*_) >> new UserMarketingCrmWxUserAccountRelationEntity(crmWxUserId: "111", userMarketingId: "222")
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity(uid: "111", userMarketingId: "222")
        userMarketingWxServiceAccountRelationDao.getByEaAndWxAppIdAndWxOpenId(*_) >> new UserMarketingWxServiceAccountRelationEntity(wxAppId: "111", userMarketingId: "222")
        userMarketingCrmContactAccountRelationDao.getByEaAndCrmContactId(*_) >> new UserMarketingCrmContactAccountRelationEntity(crmContactId: "111", userMarketingId: "222")
        userMarketingCrmAccountAccountRelationDao.getByEaAndCrmAccountId(*_) >> new UserMarketingCrmAccountAccountRelationEntity(crmAccountId: "111", userMarketingId: "222")
        userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(*_) >> new UserMarketingBrowserUserRelationEntity(browserUserId: "111", userMarketingId: "222")
        userMarketingCrmWxWorkExternalUserRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmWxWorkExternalUserRelationEntity(crmWxWorkExternalUserObjectId: "111", userMarketingId: "222")
        userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(*_) >> new UserMarketingWxWorkExternalUserRelationEntity(wxWorkExternalUserId: "111", userMarketingId: "222")
        userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmMemberRelationEntity(crmMemberObjectId: "111", userMarketingId: "222")

        when:
        Boolean result = userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(associationArg)
        then:
        result == resultMock
        where:
        associationArg                                                                          | resultMock
        new AssociationArg(type: ChannelEnum.MINIAPP.getType())                                 | true
        new AssociationArg(type: ChannelEnum.WX_SERVICE.getType())                              | true
        new AssociationArg(type: ChannelEnum.CRM_CONTACT.getType())                             | true
        new AssociationArg(type: ChannelEnum.CRM_LEAD.getType())                                | true
        new AssociationArg(type: ChannelEnum.CRM_ACCOUNT.getType())                             | true
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.getType())                             | true
        new AssociationArg(type: ChannelEnum.CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID.getType()) | true
        new AssociationArg(type: ChannelEnum.BROWSER_USER.getType())                            | true
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.getType())                   | true
        new AssociationArg(type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType())               | true
        new AssociationArg(type: ChannelEnum.CRM_MEMBER.getType())                              | true
    }


    def "doGetUserMarketingCrmMemberRelationTest"() {
        given:
        userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmMemberRelationEntity()

        when:
        UserMarketingCrmMemberRelationEntity result = userMarketingAccountRelationManager.doGetUserMarketingCrmMemberRelation(new AssociationArg())
        then:
        result == new UserMarketingCrmMemberRelationEntity()
    }


    def "getPhoneByEaAndKeyPropertiesTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadId(*_) >> new UserMarketingCrmLeadAccountRelationEntity()
        userMarketingCrmWxUserAccountRelationDao.getByEaAndCrmWxUserId(*_) >> new UserMarketingCrmWxUserAccountRelationEntity()
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity(uid: "111", userMarketingId: "222")
        userMarketingWxServiceAccountRelationDao.getByEaAndWxAppIdAndWxOpenId(*_) >> new UserMarketingWxServiceAccountRelationEntity()
        userMarketingCrmContactAccountRelationDao.getByEaAndCrmContactId(*_) >> new UserMarketingCrmContactAccountRelationEntity()
        userMarketingCrmAccountAccountRelationDao.getByEaAndCrmAccountId(*_) >> new UserMarketingCrmAccountAccountRelationEntity()
        userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(*_) >> new UserMarketingBrowserUserRelationEntity()
        userMarketingAccountDAO.getById(*_) >> new UserMarketingAccountEntity(phone: "123")
        userMarketingCrmWxWorkExternalUserRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmWxWorkExternalUserRelationEntity()
        userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(*_) >> new UserMarketingWxWorkExternalUserRelationEntity()
        userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmMemberRelationEntity()

        when:
        String result = userMarketingAccountRelationManager.getPhoneByEaAndKeyProperties(associationArg)
        then:
        result == resultMock
        where:
        associationArg                                                                          | resultMock
        new AssociationArg(type: ChannelEnum.MINIAPP.getType())                                 | "123"
        new AssociationArg(type: ChannelEnum.WX_SERVICE.getType())                              | "123"
        new AssociationArg(type: ChannelEnum.CRM_CONTACT.getType())                             | "123"
        new AssociationArg(type: ChannelEnum.CRM_LEAD.getType())                                | "123"
        new AssociationArg(type: ChannelEnum.CRM_ACCOUNT.getType())                             | "123"
        new AssociationArg(type: ChannelEnum.CRM_WX_USER.getType())                             | "123"
        new AssociationArg(type: ChannelEnum.CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID.getType()) | null
        new AssociationArg(type: ChannelEnum.BROWSER_USER.getType())                            | "123"
        new AssociationArg(type: ChannelEnum.WX_WORK_EXTERNAL_USER.getType())                   | "123"
        new AssociationArg(type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType())               | "123"
        new AssociationArg(type: ChannelEnum.CRM_MEMBER.getType())                              | "123"
    }


    def "changeUserMarketingIdTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0
        userMarketingCrmWxUserAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0
        userMarketingBrowserUserRelationDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0
        userMarketingCrmWxWorkExternalUserRelationDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0
        userMarketingWxWorkExternalUserRelationDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0
        userMarketingCrmMemberRelationDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0
        marketingUserGroupToUserRelationDao.listAllByMarketingUserId(*_) >> [new MarketingUserGroupToUserRelationEntity()]
        marketingUserGroupToUserRelationDao.batchInsertEntitiesIgnore(*_) >> 0
        marketingUserGroupToUserRelationDao.deleteByMarketingUserId(*_) >> 0
        triggerInstanceDao.updateOldUserMarketingIdToNewUserMarketingId(*_) >> 0

        when:
        userMarketingAccountRelationManager.changeUserMarketingId("88136", oldUserMarketingId, newUserMarketingId)
        then:
        noExceptionThrown() // todo - validate something
        where:
        oldUserMarketingId | newUserMarketingId
        null               | "222"
        "222"              | "222"
        "222"              | "223"

    }


    def "deleteTest"() {
        given:
        userMarketingBrowserUserRelationDao.deleteByEaAndBrowserUserId(*_) >> 0
        userMarketingCrmWxWorkExternalUserRelationDao.deleteByEaAndCrmObjectId(*_) >> 0
        userMarketingWxWorkExternalUserRelationDao.deleteByEaAndWxWorkExternalUserId(*_) >> 0
        userMarketingCrmMemberRelationDao.deleteByEaAndCrmObjectId(*_) >> 0
        userMarketingCrmCustomizeObjectRelationDao.deleteByEaAndCrmObjectId(*_) >> 0
        when:
        userMarketingAccountRelationManager.delete(new AssociationArg(type: type))
        then:
        noExceptionThrown() // todo - validate something
        where:
        type << [ChannelEnum.MINIAPP.type, ChannelEnum.WX_SERVICE.type, ChannelEnum.CRM_CONTACT.type, ChannelEnum.CRM_LEAD.type, ChannelEnum.CRM_ACCOUNT.type, ChannelEnum.CRM_WX_USER.type, ChannelEnum.BROWSER_USER.type, ChannelEnum.WX_WORK_EXTERNAL_USER.type, ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.type, ChannelEnum.CRM_MEMBER.type, ChannelEnum.CUSTOMIZE_OBJECT.type]

    }


    def "insertTest"() {
        given:
        userMarketingMiniappAccountRelationDao.insert(*_) >> null
        userMarketingWxServiceAccountRelationDao.insert(*_) >> null
        userMarketingCrmContactAccountRelationDao.insert(*_) >> 1
        userMarketingCrmLeadAccountRelationDao.insert(*_) >> 1
        userMarketingCrmAccountAccountRelationDao.insert(*_) >> 1
        userMarketingCrmWxUserAccountRelationDao.insert(*_) >> 1
        userMarketingBrowserUserRelationDao.insertIgnore(*_) >> 1;
        userMarketingWxWorkExternalUserRelationDao.insertIgnore(*_) >> 1
        userMarketingCrmWxWorkExternalUserRelationDao.insertIgnore(*_) >> 1
        userMarketingCrmMemberRelationDao.insertIgnore(*_) >> 1
        userMarketingCrmCustomizeObjectRelationDao.insert(*_) >> 1

        when:
        userMarketingAccountRelationManager.insert(associationArg, userMarketingId)
        then:
        noExceptionThrown()

        where:
        associationArg                                                                                                               | userMarketingId
        new AssociationArg(ea: "88146", type: ChannelEnum.MINIAPP.type, associationId: "111")                                        | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.WX_SERVICE.type, associationId: "111")                                     | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_CONTACT.type, associationId: "111", userName: "a", email: "<EMAIL>") | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_LEAD.type, associationId: "111")                                       | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_ACCOUNT.type, associationId: "111")                                    | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_WX_USER.type, associationId: "111")                                    | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.BROWSER_USER.type, associationId: "111")                                   | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.WX_WORK_EXTERNAL_USER.type, associationId: "111")                          | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.type, associationId: "111")                      | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CRM_MEMBER.type, associationId: "111")                                     | ""
        new AssociationArg(ea: "88146", type: ChannelEnum.CUSTOMIZE_OBJECT.type, objectApiName: "LeadObj", associationId: "111")     | ""
    }


    def "listByUserMarketingIdsTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmLeadAccountRelationEntity(ea: 88146, crmLeadId: "111")]
        userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxUserAccountRelationEntity(ea: 88146, crmWxUserId: "111")]
        userMarketingMiniappAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingMiniappAccountRelationEntity(ea: 88146, uid: "111")]
        userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmContactAccountRelationEntity(ea: 88146, crmContactId: "111")]
        userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmAccountAccountRelationEntity(ea: 88146, crmAccountId: "111")]
        userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxWorkExternalUserRelationEntity(ea: 88146, crmWxWorkExternalUserObjectId: "111")]
        userMarketingCrmMemberRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmMemberRelationEntity(ea: 88146, crmMemberObjectId: "111")]

        when:
        List<String> result = userMarketingAccountRelationManager.listByUserMarketingIds("88146", type, userMarketingAccountIds)
        then:
        result == resultMock
        where:
        type                                            | userMarketingAccountIds   | resultMock
        null                                            | null                      | null
        ChannelEnum.CRM_LEAD.getType()                  | Lists.newArrayList("abc") | Lists.newArrayList("111")
        ChannelEnum.CRM_ACCOUNT.getType()               | Lists.newArrayList("abc") | Lists.newArrayList("111")
        ChannelEnum.CRM_CONTACT.getType()               | Lists.newArrayList("abc") | Lists.newArrayList("111")
        ChannelEnum.CRM_WX_USER.getType()               | Lists.newArrayList("abc") | Lists.newArrayList("111")
        ChannelEnum.MINIAPP.getType()                   | Lists.newArrayList("abc") | Lists.newArrayList("111")
        ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType() | Lists.newArrayList("abc") | Lists.newArrayList("111")
        ChannelEnum.CRM_MEMBER.getType()                | Lists.newArrayList("abc") | Lists.newArrayList("111")
    }


    def "getRelationIdMapByMarketingUserIdsTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmLeadAccountRelationEntity(ea: "88146", userMarketingId: "1", crmLeadId: "1")]
        userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxUserAccountRelationEntity(ea: "88146", userMarketingId: "12", crmWxUserId: "12")]
        userMarketingMiniappAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingMiniappAccountRelationEntity(ea: "88146", userMarketingId: "13", uid: "13")]
        userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmContactAccountRelationEntity(ea: "88146", userMarketingId: "14", crmContactId: "14")]
        userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmAccountAccountRelationEntity(ea: "88146", userMarketingId: "15", crmAccountId: "15")]
        userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxWorkExternalUserRelationEntity(ea: "88146", userMarketingId: "16", crmWxWorkExternalUserObjectId: "16")]
        userMarketingCrmMemberRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmMemberRelationEntity(ea: "88146", userMarketingId: "17", crmMemberObjectId: "17")]
        when:
        Map<String, String> result = userMarketingAccountRelationManager.getRelationIdMapByMarketingUserIds("88146", type, userMarketingAccountIds)
        then:
        result == resultMock
        where:
        type                                            | userMarketingAccountIds  | resultMock
        null                                            | null                     | new HashMap<>()
        ChannelEnum.CRM_LEAD.getType()                  | Lists.newArrayList("1")  | ["1": "1"]
        ChannelEnum.CRM_WX_USER.getType()               | Lists.newArrayList("12") | ["12": "12"]
        ChannelEnum.MINIAPP.getType()                   | Lists.newArrayList("13") | ["13": "13"]
        ChannelEnum.CRM_CONTACT.getType()               | Lists.newArrayList("14") | ["14": "14"]
        ChannelEnum.CRM_ACCOUNT.getType()               | Lists.newArrayList("15") | ["15": "15"]
        ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType() | Lists.newArrayList("16") | ["16": "16"]
        ChannelEnum.CRM_MEMBER.getType()                | Lists.newArrayList("17") | ["17": "17"]
        100                                             | Lists.newArrayList()     | [:]
    }


    def "listDataIdsByUserMarketingAccountIdsTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmLeadAccountRelationEntity(ea: "88146", crmLeadId: "1")]
        userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxUserAccountRelationEntity(ea: "88146", crmWxUserId: "1")]
        userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmContactAccountRelationEntity(ea: "88146", crmContactId: "1")]
        userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmAccountAccountRelationEntity(ea: "88146", crmAccountId: "1")]
        userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxWorkExternalUserRelationEntity(ea: "88146", crmWxWorkExternalUserObjectId: "1")]
        userMarketingCrmMemberRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmMemberRelationEntity(ea: "88146", crmMemberObjectId: "1")]
        userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCustomizeObjectRelationEntity(ea: "88146", objectId: "1")]

        when:
        List<String> result = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds("88146", type, userMarketingAccountIds)
        then:
        result == resultMock
        where:
        type                                            | userMarketingAccountIds | resultMock
        null                                            | null                    | null
        ChannelEnum.CRM_LEAD.getType()                  | Lists.newArrayList("1") | ["1"]
        ChannelEnum.CRM_ACCOUNT.getType()               | Lists.newArrayList("1") | ["1"]
        ChannelEnum.CRM_CONTACT.getType()               | Lists.newArrayList("1") | ["1"]
        ChannelEnum.CRM_WX_USER.getType()               | Lists.newArrayList("1") | ["1"]
        ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType() | Lists.newArrayList("1") | ["1"]
        ChannelEnum.CRM_MEMBER.getType()                | Lists.newArrayList("1") | ["1"]
        ChannelEnum.CUSTOMIZE_OBJECT.getType()          | Lists.newArrayList("1") | ["1"]
    }


    def "listUserMarketingAccountIdsByDataIdsTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.listByLeadIds(*_) >> ["1"]
        userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(*_) >> ["1"]
        userMarketingMiniappAccountRelationDao.listByUids(*_) >> ["1"]
        userMarketingCrmContactAccountRelationDao.listByContactIds(*_) >> ["1"]
        userMarketingCrmAccountAccountRelationDao.listByAccountIds(*_) >> ["1"]
        userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(*_) >> ["1"]

        when:
        List<String> result = userMarketingAccountRelationManager.listUserMarketingAccountIdsByDataIds("88146", type, dataIds)
        then:
        result == resultMock
        where:
        type                                            | dataIds | resultMock
        null                                            | null    | []
        ChannelEnum.CRM_LEAD.getType()                  | ["1"]   | ["1"]
        ChannelEnum.CRM_ACCOUNT.getType()               | ["1"]   | ["1"]
        ChannelEnum.CRM_CONTACT.getType()               | ["1"]   | ["1"]
        ChannelEnum.CRM_WX_USER.getType()               | ["1"]   | ["1"]
        ChannelEnum.MINIAPP.getType()                   | ["1"]   | ["1"]
        ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType() | ["1"]   | ["1"]
    }

    def "listDescribeApiNameByUserMarketingIdTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(*_) >> crmLeadAccountListMarketingIdsMock
        userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(*_) >> crmWxUserAccountListMarketingIdsMock
        userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(*_) >> crmContactAccountListMarketingIdsMock
        userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(*_) >> crmAccountAccountListMarketingIdsMock
        userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> crmWxWorkExternalUserListMarketingMock
        userMarketingCrmMemberRelationDao.listByUserMarketingIds(*_) >> crmMemberListMarketingMock
        userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(*_) >> customizeObjectListMarketingMock
        when:
        List<String> result = userMarketingAccountRelationManager.listDescribeApiNameByUserMarketingId("88146", "111")
        then:
        result == resultMock
        where:
        crmLeadAccountListMarketingIdsMock                        | crmAccountAccountListMarketingIdsMock                        | crmContactAccountListMarketingIdsMock                        | crmWxUserAccountListMarketingIdsMock                             | crmWxWorkExternalUserListMarketingMock                        | crmMemberListMarketingMock                           | customizeObjectListMarketingMock                                         | resultMock
        [new UserMarketingCrmLeadAccountRelationEntity(id: "11")] | [new UserMarketingCrmAccountAccountRelationEntity(id: "22")] | [new UserMarketingCrmContactAccountRelationEntity(id: "33")] | [new UserMarketingCrmWxUserAccountRelationEntity(id: "44")] | [new UserMarketingCrmWxWorkExternalUserRelationEntity(id: "55")] | [new UserMarketingCrmMemberRelationEntity(id: "66")] | [new UserMarketingCustomizeObjectRelationEntity(objectApiName: "01x_c")] | [new ObjectIdAndDescribeData("11", "LeadsObj"), new ObjectIdAndDescribeData("22", "AccountObj"), new ObjectIdAndDescribeData("33", "ContactObj"), new ObjectIdAndDescribeData("44", "WechatFanObj"), new ObjectIdAndDescribeData("55", "WechatWorkExternalUserObj"),new ObjectIdAndDescribeData("66", "MemberObj"),new ObjectIdAndDescribeData(null, "01x_c")]
    }

    def "isRelateCrmObjectTest"() {
        given:
        userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmLeadAccountRelationEntity()]
        userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmWxUserAccountRelationEntity()]
        userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmContactAccountRelationEntity()]
        userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmAccountAccountRelationEntity()]
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity()]
        userMarketingCrmMemberRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingCrmMemberRelationEntity()]

        when:
        Boolean result = userMarketingAccountRelationManager.isRelateCrmObject("88146", "1", needCheckApiName)
        then:
        result == resultMock
        where:
        needCheckApiName                                                       | resultMock
        Lists.newArrayList()                                                   | false
        Lists.newArrayList(ChannelEnum.CRM_LEAD.getApiName())                  | true
        Lists.newArrayList(ChannelEnum.CRM_ACCOUNT.getApiName())               | true
        Lists.newArrayList(ChannelEnum.CRM_CONTACT.getApiName())               | true
        Lists.newArrayList(ChannelEnum.CRM_WX_USER.getApiName())               | true
        Lists.newArrayList(ChannelEnum.CRM_MEMBER.getApiName())                | true
        Lists.newArrayList(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getApiName()) | true
    }


    def "getByEaAndCrmAccountIdTest1"() {
        given:
        userMarketingCrmAccountAccountRelationDao.getByEaAndCrmAccountId(*_) >> new UserMarketingCrmAccountAccountRelationEntity()

        when:
        UserMarketingCrmAccountAccountRelationEntity result = userMarketingAccountRelationManager.getByEaAndCrmAccountId("ea", "crmAccountId")
        then:
        result == new UserMarketingCrmAccountAccountRelationEntity()
    }


    def "getByEaAndCrmAccountIdTest"() {
        given:
        userMarketingCrmAccountAccountRelationDao.listUserMarketingCrmAccountAccountRelationEntityByAccountIds(*_) >> [new UserMarketingCrmAccountAccountRelationEntity(ea: "88146", userMarketingId: "1", crmAccountId: "2")]

        when:
        List<UserMarketingCrmAccountAccountRelationEntity> result = userMarketingAccountRelationManager.getByEaAndCrmAccountId("ea", ["crmAccountIds"])
        then:
        result == [new UserMarketingCrmAccountAccountRelationEntity(ea: "88146", userMarketingId: "1", crmAccountId: "2")]
    }


    def "getUserMarketingByOuterUserIdentifyIdTest"() {
        given:
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity(userMarketingId: "a")
        userMarketingWxServiceAccountRelationDao.getByEaAndWxOpenId(*_) >> new UserMarketingWxServiceAccountRelationEntity(userMarketingId: "a")
        userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(*_) >> new UserMarketingBrowserUserRelationEntity(userMarketingId: "a")
        userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(*_) >> new UserMarketingWxWorkExternalUserRelationEntity(userMarketingId: "a")
        userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmMemberRelationEntity(userMarketingId: "a")

        when:
        String result = userMarketingAccountRelationManager.getUserMarketingByOuterUserIdentifyId("ea", "1", type)
        then:
        result == resultMock
        where:
        type                                        | resultMock
        ChannelEnum.BROWSER_USER.getType()          | "a"
        ChannelEnum.MINIAPP.getType()               | "a"
        ChannelEnum.WX_SERVICE.getType()            | "a"
        ChannelEnum.CRM_MEMBER.getType()            | "a"
        ChannelEnum.WX_WORK_EXTERNAL_USER.getType() | "a"
        100                                         | null

    }


    def "getUserMarketingAccountIdByMemberIdTest"() {
        given:
        userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(*_) >> new UserMarketingCrmMemberRelationEntity(crmMemberObjectId: "11")

        when:
        UserMarketingCrmMemberRelationEntity result = userMarketingAccountRelationManager.getUserMarketingAccountIdByMemberId("ea", "11")
        then:
        result == new UserMarketingCrmMemberRelationEntity(crmMemberObjectId: "11")
    }


    def "getByEaAndUserMarketingIdTest"() {
        given:
        userMarketingCrmMemberRelationDao.getByEaAndUserMarketingId(*_) >> new UserMarketingCrmMemberRelationEntity(userMarketingId: "1", crmMemberObjectId: "1")
        userMarketingCrmMemberRelationDao.getByUserMarketingId(*_) >> new UserMarketingCrmMemberRelationEntity(userMarketingId: "2", crmMemberObjectId: "2")

        when:
        UserMarketingCrmMemberRelationEntity result = userMarketingAccountRelationManager.getByEaAndUserMarketingId(ea, "11")
        then:
        result == resultMock
        where:
        ea      | resultMock
        null    | new UserMarketingCrmMemberRelationEntity(userMarketingId: "2", crmMemberObjectId: "2")
        "88146" | new UserMarketingCrmMemberRelationEntity(userMarketingId: "1", crmMemberObjectId: "1")

    }


    def "getExcludeApiNameListTest"() {
        given:
        userMarketingExcludeObjectDAO.getByEa(*_) >> getByEaMock

        when:
        List<MarketingUserExcludeApinameResult> result = userMarketingAccountRelationManager.getExcludeApiNameList(ea)
        then:
        result == resultMock
        where:
        ea      | resultMock                                                                                            | getByEaMock
        null    | []                                                                                                    | null
        "88146" | [new MarketingUserExcludeApinameResult(ea: "88146", objectApiName: "AccountObj", objectName: "客户")] | [new UserMarketingExcludeObjectEntity(ea: "88146", objectApiName: "AccountObj", objectName: "客户")]
    }


    def "setExcludeApiNameTest"() {
        given:
        userMarketingExcludeObjectDAO.insert(*_) >> 0
        userMarketingExcludeObjectDAO.deleteByObjectApiName(*_) >> 0
        userMarketingExcludeObjectDAO.getByObjectApiName(*_) >> getByObjectApiNameMock

        when:
        Result result = userMarketingAccountRelationManager.setExcludeApiName("88146", 0, "AccountObj", "客户", status)
        then:
        result == resultMock
        where:
        getByObjectApiNameMock                                                                             | resultMock          | status
        null                                                                                               | Result.newSuccess() | 1
        new UserMarketingExcludeObjectEntity(ea: "88146", objectName: "客户", objectApiName: "AccountObj") | Result.newSuccess() | 0
    }


    def "isExcludeApiNameTest"() {
        given:
        userMarketingExcludeObjectDAO.getByObjectApiName(*_) >> new UserMarketingExcludeObjectEntity()

        when:
        boolean result = userMarketingAccountRelationManager.isExcludeApiName("88146", objectApiName)
        then:
        result == resultMock
        where:
        objectApiName | resultMock
        null          | true
    }


    def "isExcludeApiNameByChannelTest"() {
        given:
        userMarketingExcludeObjectDAO.getByObjectApiName(*_) >> userMarketingExcludeObjectEntityMock

        when:
        boolean result = userMarketingAccountRelationManager.isExcludeApiNameByChannel("ea", channel)
        then:
        result == resultMock
        where:
        channel                           | resultMock | userMarketingExcludeObjectEntityMock
        ChannelEnum.CRM_ACCOUNT.getType() | false      | null
        ChannelEnum.CRM_ACCOUNT.getType() | true       | new UserMarketingExcludeObjectEntity()
        ChannelEnum.CRM_LEAD.getType()    | false      | new UserMarketingExcludeObjectEntity()
    }

}