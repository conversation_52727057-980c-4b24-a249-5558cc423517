package com.facishare.marketing.provider.manager.advertise

import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO
import com.facishare.marketing.api.vo.baidu.QueryAccountInfoVO
import com.facishare.marketing.provider.advertiser.adAccount.TencentAdResult
import com.facishare.marketing.provider.advertiser.tencent.GetTencentAdAccountFundsInfoData
import com.facishare.marketing.provider.advertiser.tencent.PageInfo
import com.facishare.marketing.provider.advertiser.tencent.TencentAdGroupDataResult
import com.facishare.marketing.provider.advertiser.tencent.TencentAdGroupResult
import com.facishare.marketing.provider.advertiser.tencent.TencentCampaignResult
import com.facishare.marketing.provider.advertiser.tencent.TencentLeadsResult
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsMappingDataDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDataDAO
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentCampaignDAO
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO
import com.facishare.marketing.provider.dao.baidu.BaiduDataStatusDAO
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupDataEntity
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentCampaignEntity
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.entity.baidu.CampaignDataOverviewDTOEntity
import com.facishare.marketing.provider.manager.MarketingStatLogPersistorManger
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager
import com.facishare.marketing.provider.manager.baidu.AccountApiManager
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager
import com.facishare.marketing.provider.manager.baidu.CampaignApiManager
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager
import com.facishare.marketing.provider.manager.crmobjectcreator.AdvertisingDetailsObjManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.result.ActionAddResult
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class TencentAdMarketingManagerSpec extends Specification {

    def accountApiManager = Mock(AccountApiManager)
    def redisManager = Mock(RedisManager)
    def adAccountManager = Mock(AdAccountManager)
    def refreshDataManager = Mock(RefreshDataManager)
    def campaignApiManager = Mock(CampaignApiManager)
    def tencentCampaignDAO = Mock(TencentCampaignDAO)
    def tencentAdGroupDAO = Mock(TencentAdGroupDAO)
    def adTokenManager = Mock(AdTokenManager)
    def adObjectFieldMappingDAO = Mock(AdObjectFieldMappingDAO)
    def baiduDataStatusDAO = Mock(BaiduDataStatusDAO)
    def tencentAdGroupDataDAO = Mock(TencentAdGroupDataDAO)
    def campaignDataManager = Mock(CampaignDataManager)
    def adLeadsMappingDataDAO = Mock(AdLeadsMappingDataDAO)
    def marketingStatLogPersistorManger = Mock(MarketingStatLogPersistorManger)
    def baiduAdMarketingManager = Mock(BaiduAdMarketingManager)
    def advertisingDetailsObjManager = Mock(AdvertisingDetailsObjManager)
    def adCommonManager = Mock(AdCommonManager)
    def campaignList = "{\"Q1投放产品\":[\"纷享营销通\"],\"Q2投放产品\":[\"纷享服务通\"],\"Q3投放产品\":[\"纷享代理通\"],\"朋友圈推广\":[\"线索转化-2022年春季\",\"提升销售效率的CRM工具\",\"实时数据分析的CRM平台\"], \"营销自动化\":[\"营销自动化工具全攻略\"]}"

    def tencentAdMarketingManager = new TencentAdMarketingManager(
            "accountApiManager": accountApiManager,
            "redisManager": redisManager,
            "adAccountManager": adAccountManager,
            "refreshDataManager": refreshDataManager,
            "campaignApiManager": campaignApiManager,
            "tencentCampaignDAO": tencentCampaignDAO,
            "tencentAdGroupDAO": tencentAdGroupDAO,
            "adTokenManager": adTokenManager,
            "adObjectFieldMappingDAO": adObjectFieldMappingDAO,
            "baiduDataStatusDAO": baiduDataStatusDAO,
            "tencentAdGroupDataDAO": tencentAdGroupDataDAO,
            "campaignDataManager": campaignDataManager,
            "adLeadsMappingDataDAO": adLeadsMappingDataDAO,
            "marketingStatLogPersistorManger": marketingStatLogPersistorManger,
            "baiduAdMarketingManager": baiduAdMarketingManager,
            "advertisingDetailsObjManager": advertisingDetailsObjManager,
            "adCommonManager": adCommonManager,
            "campaignList": campaignList
    )

    @Unroll
    def "queryAccountInfo"() {
        given:
        adAccountManager.queryAccountByEaAndSource(*_) >> adAccountList
        baiduDataStatusDAO.queryRefreshStatus(*_) >> null
        when:
        tencentAdMarketingManager.queryAccountInfo(new QueryAccountInfoVO(ea: "ea", source: "source"))
        then:
        noExceptionThrown()
        where:
        adAccountList << [null, [new AdAccountEntity()]]
    }

    @Unroll
    def "isValidAccount"() {
        given:
        adTokenManager.getTencentAccessToken(*_) >> token
        accountApiManager.getTencentAdAccountFundsInfo(*_) >> new TencentAdResult(code: code)
        when:
        tencentAdMarketingManager.isValidAccount(new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        token                | code
        Optional.empty()     | 0
        Optional.of("token") | -1
        Optional.of("token") | 0
    }

    @Unroll
    def "getDataOverview"() {
        given:
        tencentAdGroupDataDAO.getTencentDataOverviewByCampaignName(*_) >> new CampaignDataOverviewDTOEntity()
        tencentAdGroupDataDAO.getTencentDataOverview(*_) >> null
        tencentAdGroupDAO.queryMarketingEventIds(*_) >> []
        when:
        tencentAdMarketingManager.getDataOverview(arg)
        then:
        noExceptionThrown()
        where:
        arg << [new GetDataOverviewVO(ea: "ea", source: "source", campaignName: "campaignName"), new GetDataOverviewVO(ea: "ea", source: "source")]
    }

    @Unroll
    def "refreshAccountInfo"() {
        given:
        adTokenManager.getTencentAccessToken(*_) >> token
        accountApiManager.getTencentAdAccountFundsInfo(*_) >> fundResult
        adAccountManager.updateBalanceAndCostById(*_) >> { println "adAccountManager.updateBalanceAndCostById" }
        when:
        tencentAdMarketingManager.refreshAccountInfo(new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        token                | fundResult
        Optional.empty()     | new TencentAdResult(code: 0)
        Optional.of("token") | new TencentAdResult(code: -1, data: new GetTencentAdAccountFundsInfoData(list: [new GetTencentAdAccountFundsInfoData.TencentAdAccountFundsInfo()]))
        Optional.of("token") | new TencentAdResult(code: 0, data: new GetTencentAdAccountFundsInfoData(list: [new GetTencentAdAccountFundsInfoData.TencentAdAccountFundsInfo(balance: 112D)]))
    }

    @Unroll
    def "refreshCampaignInfo"() {
        given:
        def spy = Spy(TencentAdMarketingManager)
        spy.refreshTencentCampaign(*_) >> true
        when:
        spy.refreshCampaignInfo(new AdAccountEntity())
        then:
        noExceptionThrown()
    }

    @Unroll
    def "refreshCampaignData"() {
        given:
        when:
        tencentAdMarketingManager.refreshCampaignData(new AdAccountEntity(), 5)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "refreshMarketingEventLeads"() {
        given:
        when:
        tencentAdMarketingManager.refreshMarketingEventLeads(new AdAccountEntity(), 5, [])
        then:
        noExceptionThrown()
    }

    @Unroll
    def "refreshAllData"() {
        given:
        adCommonManager.isPurchaseAdLicense(*_) >> adLicense
        adTokenManager.getTencentAccessToken(*_) >> token
        accountApiManager.getTencentAdAccountFundsInfo(*_) >> fundResult
        adAccountManager.updateAdAccountStatusById(*_) >> { println "adAccountManager.updateAdAccountStatusById" }
        tencentAdMarketingManager.refreshTencentCampaign(*_) >> true
        tencentAdMarketingManager.refreshTencentAdGroupData(*_) >> { println "spy.refreshTencentAdGroupData" }
        tencentAdMarketingManager.syncTencentCampaignToMarketingEventObj(*_) >> { println "spy.syncTencentCampaignToMarketingEventObj" }
        tencentAdMarketingManager.syncTencentAdGroupToSubMarketingEventObj(*_) >> { println "spy.syncTencentAdGroupToSubMarketingEventObj" }
        tencentAdMarketingManager.syncTencentClueDataToCrm(*_) >> { println "spy.syncTencentClueDataToCrm" }
        adAccountManager.queryEnableAccountById(*_) >> adAccount
        when:
        tencentAdMarketingManager.refreshAllData("88146", "ddd", "腾讯")
        then:
        noExceptionThrown()
        where:
        adAccount             | adLicense | token                | fundResult
        null                  | false     | Optional.empty()     | new TencentAdResult(code: 0)
        new AdAccountEntity() | true      | Optional.of("token") | new TencentAdResult(code: -1, data: new GetTencentAdAccountFundsInfoData(list: [new GetTencentAdAccountFundsInfoData.TencentAdAccountFundsInfo()]))
        new AdAccountEntity() | true      | Optional.of("token") | new TencentAdResult(code: 0, data: new GetTencentAdAccountFundsInfoData(list: [new GetTencentAdAccountFundsInfoData.TencentAdAccountFundsInfo(balance: 112D)]))

    }

    @Unroll
    def "refreshTencentAdGroupData"() {
        given:
        adTokenManager.getTencentAccessToken(*_) >> token
        campaignApiManager.getTencentAdGroupData(*_) >> adGroupResult
        tencentAdGroupDataDAO.queryTencentAdGroupDataList(*_) >> existAdGroupData
        tencentAdGroupDataDAO.batchUpdateTencentAdGroupData(*_) >> { println "tencentAdGroupDataDAO.batchUpdateTencentAdGroupData" }
        tencentAdGroupDataDAO.batchAddTencentAdGroupData(*_) >> 1
        tencentAdGroupDAO.queryTencentAdGroupList(*_) >> [new TencentAdGroupEntity(adgroupId: 1L, subMarketingEventId: "hhhh")]
        advertisingDetailsObjManager.tryUpdateOrCreateObj(*_) >> { println "advertisingDetailsObjManager.tryUpdateOrCreateObj" }
        tencentAdGroupDAO.scanByAdAccountId(*_) >>> [tencentAdGroupEntities1, tencentAdGroupEntities2]
        when:
        tencentAdMarketingManager.refreshTencentAdGroupData("88146", new AdAccountEntity(), "", "", "")
        then:
        noExceptionThrown()
        where:
        tencentAdGroupEntities1                                                                                            | tencentAdGroupEntities2 | token                | adGroupResult                                                                                                                                                                                         | existAdGroupData
        [new TencentAdGroupEntity(adgroupId: 1L, campaignId: 1L), new TencentAdGroupEntity(adgroupId: 1L, campaignId: 0L)] | []                      | Optional.empty()     | new TencentAdResult(code: 0)                                                                                                                                                                          | null
        [new TencentAdGroupEntity(adgroupId: 1L, campaignId: 1L), new TencentAdGroupEntity(adgroupId: 1L, campaignId: 0L)] | []                      | Optional.of("token") | new TencentAdResult(code: -1, data: new TencentAdGroupDataResult(list: [new TencentAdGroupDataResult.TencentAdGroupData()]))                                                                          | null
        [new TencentAdGroupEntity(adgroupId: 1L, campaignId: 1L), new TencentAdGroupEntity(adgroupId: 1L, campaignId: 0L)] | []                      | Optional.of("token") | new TencentAdResult(code: 0, data: new TencentAdGroupDataResult(list: [new TencentAdGroupDataResult.TencentAdGroupData(date: "2024-05-04")], page_info: new PageInfo(total_page: 1)))                 | null
        [new TencentAdGroupEntity(adgroupId: 1L, campaignId: 1L), new TencentAdGroupEntity(adgroupId: 1L, campaignId: 0L)] | []                      | Optional.of("token") | new TencentAdResult(code: 0, data: new TencentAdGroupDataResult(list: [new TencentAdGroupDataResult.TencentAdGroupData(date: "2024-05-06", adgroup_id: 1L)], page_info: new PageInfo(total_page: 1))) | [new TencentAdGroupDataEntity(adgroupId: 1L, reportTime: new Date())]

    }

    @Unroll
    def "reindexAdvertisingDetailsObj"() {
        given:
        tencentAdGroupDataDAO.countByEa(*_) >> 1
        tencentAdGroupDataDAO.scanById(*_) >> exsitList
        tencentAdGroupDAO.queryTencentAdGroupList(*_) >> [new TencentAdGroupEntity(adgroupId: 1L, subMarketingEventId: "hhhh")]
        advertisingDetailsObjManager.tryUpdateOrCreateObj(*_) >> { println "advertisingDetailsObjManager.tryUpdateOrCreateObj" }
        when:
        tencentAdMarketingManager.reindexAdvertisingDetailsObj("88146")
        then:
        noExceptionThrown()
        where:
        exsitList << [null, [new TencentAdGroupDataEntity()], [new TencentAdGroupDataEntity(adAccountId: "ttttt", reportTime: new Date())]]
    }

    @Shared
    def funcMap = new HashMap<>()

    @Unroll
    def "syncTencentClueDataToCrm"() {
        given:
        adTokenManager.getTencentAccessToken(*_) >> token
        campaignApiManager.getTencentLeads(*_) >> leadResult
        adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(*_) >> mapping
        tencentAdGroupDAO.queryTencentAdGroup(*_) >> adGroup
        refreshDataManager.syncLeadCallFunc(*_) >> funcResult
        refreshDataManager.syncClueDataToCrmObj(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: 0, data: new ActionAddResult(objectData: new ObjectData()))
        marketingStatLogPersistorManger.sendLeadData(*_) >> { println "marketingStatLogPersistorManger.sendLeadData" }
        baiduAdMarketingManager.syncCampaignMember(*_) >> { println "baiduAdMarketingManager.syncCampaignMember" }
        funcMap.put("functionResult", new HashMap<String, Object>())
        when:
        tencentAdMarketingManager.syncTencentClueDataToCrm("88146", new AdAccountEntity(), "ttt", System.currentTimeMillis(), System.currentTimeMillis())
        then:
        noExceptionThrown()
        where:
        token                | leadResult                                                                                                                                                                                                                                                    | mapping                          | adGroup | funcResult
        Optional.empty()     | null                                                                                                                                                                                                                                                          | null                             | null    | null
        Optional.of("token") | null                                                                                                                                                                                                                                                          | null                             | null    | null
        Optional.of("token") | new TencentAdResult<TencentLeadsResult>(code: 0, data: new TencentLeadsResult(leads_info: [new TencentLeadsResult.TencentLeads()], page_info: new PageInfo(total_page: 1)))                                                                                   | null                             | null    | null
        Optional.of("token") | new TencentAdResult<TencentLeadsResult>(code: 0, data: new TencentLeadsResult(leads_info: [new TencentLeadsResult.TencentLeads(leads_create_time: "2024-05-04 12:23:23", leads_action_time: "2024-05-04 12:23:23")], page_info: new PageInfo(total_page: 1))) | [new AdLeadsMappingDataEntity()] | null    | funcMap

    }


    @Unroll
    def "syncTencentAdGroupToSubMarketingEventObj"() {
        given:
        tencentAdGroupDAO.queryAdGroupTotalCount(*_) >> totalCount
        adAccountManager.queryAccountById(*_) >> adAccount
        adObjectFieldMappingDAO.getByApiName(*_) >> null
        tencentAdGroupDAO.pageAdGroupEntity(*_) >> []
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(*_) >> { println "batchSyncAd" }
        when:
        tencentAdMarketingManager.syncTencentAdGroupToSubMarketingEventObj("88146", "acc")
        then:
        noExceptionThrown()
        where:
        totalCount | adAccount
        0L         | null
        1L         | new AdAccountEntity()

    }

    @Unroll
    def "syncTencentCampaignToMarketingEventObj"() {
        given:
        tencentCampaignDAO.queryCampaignTotalCount(*_) >> totalCount
        adAccountManager.queryAccountById(*_) >> adAccount
        adObjectFieldMappingDAO.getByApiName(*_) >> null
        tencentCampaignDAO.pageCampaignEntityData(*_) >> []
        refreshDataManager.batchSyncCampaignToMarketingEventObj(*_) >> { println "batchSyncAd" }
        when:
        tencentAdMarketingManager.syncTencentCampaignToMarketingEventObj("88146", 11L, "acc")
        then:
        noExceptionThrown()
        where:
        totalCount | adAccount
        0L         | null
        1L         | null
        1L         | new AdAccountEntity()

    }

    @Unroll
    def "refreshTencentAdGroupPeriodically"() {
        given:
        adTokenManager.getTencentAccessToken(*_) >> token
        campaignApiManager.getTencentAdGroup(*_) >> adGroupResult
        tencentAdGroupDAO.queryTencentAdGroupList(*_) >> existAdGroup
        tencentAdGroupDAO.batchAddTencentAdGroup(*_) >> 1
        tencentAdGroupDAO.batchUpdateTencentAdGroup(*_) >> { println "batchUpdateTencentAdGroup" }
        when:
        tencentAdMarketingManager.refreshTencentAdGroupPeriodically(new AdAccountEntity(), 1)
        then:
        noExceptionThrown()
        where:
        token                | adGroupResult                                                                                                                                                           | existAdGroup
        Optional.empty()     | null                                                                                                                                                                    | []
        Optional.of("token") | new TencentAdResult<>()                                                                                                                                                 | []
        Optional.of("token") | new TencentAdResult<>(code: 0, data: new TencentAdGroupResult(list: [new TencentAdGroupResult.TencentAdGroup()], page_info: new PageInfo(total_page: 1)))               | []
        Optional.of("token") | new TencentAdResult<>(code: 0, data: new TencentAdGroupResult(list: [new TencentAdGroupResult.TencentAdGroup(adgroup_id: 1L)], page_info: new PageInfo(total_page: 1))) | []
        Optional.of("token") | new TencentAdResult<>(code: 0, data: new TencentAdGroupResult(list: [new TencentAdGroupResult.TencentAdGroup(adgroup_id: 1L)], page_info: new PageInfo(total_page: 1))) | [new TencentAdGroupEntity(adgroupId: 1L)]
    }

    @Unroll
    def "refreshTencentCampaign"() {
        given:
        adTokenManager.getTencentAccessToken(*_) >> token
        campaignApiManager.getTencentCampaigns(*_) >> campaignResult
        tencentCampaignDAO.queryTencentCampaignList(*_) >> existCampagin
        tencentCampaignDAO.batchAddTencentCampaign(*_) >> 1
        tencentCampaignDAO.batchUpdateTencentCampaign(*_) >> 1
        when:
        tencentAdMarketingManager.refreshTencentCampaign("88146", new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        token                | campaignResult                                                                                                                                                              | existCampagin
        Optional.empty()     | null                                                                                                                                                                        | []
        Optional.of("token") | new TencentAdResult<>()                                                                                                                                                     | []
        Optional.of("token") | new TencentAdResult<>(code: 0, data: new TencentCampaignResult(list: [new TencentCampaignResult.TencentCampaign()], page_info: new PageInfo(total_page: 1)))                | []
        Optional.of("token") | new TencentAdResult<>(code: 0, data: new TencentCampaignResult(list: [new TencentCampaignResult.TencentCampaign(campaign_id: 1L)], page_info: new PageInfo(total_page: 1))) | []
        Optional.of("token") | new TencentAdResult<>(code: 0, data: new TencentCampaignResult(list: [new TencentCampaignResult.TencentCampaign(campaign_id: 1L)], page_info: new PageInfo(total_page: 1))) | [new TencentCampaignEntity(campaignId: 1L)]
    }

    @Unroll
    def "initCampaignAndAdGroup"() {
        given:
        tencentCampaignDAO.getAllNameList(*_) >> null
        redisManager.getPrimaryId() >> "33333"
        tencentCampaignDAO.batchAddTencentCampaign(*_) >> 1
        adObjectFieldMappingDAO.getByApiName(*_) >> null
        refreshDataManager.batchSyncCampaignToMarketingEventObj(*_) >> { println "dddd" }
        tencentAdGroupDAO.batchAddTencentAdGroup(*_) >> 1
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(*_) >> { println "dddd" }

        when:
        tencentAdMarketingManager.initCampaignAndAdGroup(new AdAccountEntity())
        then:
        noExceptionThrown()

    }

    @Unroll
    def "refreshPrototypeRoomAccountData"() {
        given:
        adAccountManager.getAdPrototypeRoomAccount(*_) >> adAccountList
        tencentCampaignDAO.countByAdAccountId(*_) >> 1
        tencentCampaignDAO.getAllNameList(*_) >> null
        tencentCampaignDAO.batchAddTencentCampaign(*_) >> 1
        refreshDataManager.batchSyncCampaignToMarketingEventObj(*_) >> { printf "dd" }
        tencentAdGroupDAO.batchAddTencentAdGroup(*_) >> 1
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(*_) >> { printf "dddd" }
        tencentCampaignDAO.scanByAdAccountId(*_) >> [new TencentCampaignEntity(campaignId: 1L)]
        tencentAdGroupDAO.queryByCampaignIdList(*_) >> adGroupList
        tencentAdGroupDataDAO.batchAddTencentAdGroupData(*_) >> 1
        redisManager.getPrimaryId(*_) >> "1"

        when:
        tencentAdMarketingManager.refreshPrototypeRoomAccountData("ea", *********)
        then:
        noExceptionThrown()
        where:
        adAccountList           | adGroupList
        []                      | []
        [new AdAccountEntity()] | []
        [new AdAccountEntity()] | [new TencentAdGroupEntity(adgroupId: 1L, campaignId: 1L), new TencentAdGroupEntity(adgroupId: 2L, campaignId: 1L)]

    }

    @Unroll
    def "batchUpdateTencentAdGroupData"() {
        given:
        tencentAdGroupDataDAO.batchUpdateTencentAdGroupData(*_) >> { printf "ddd" }
        when:
        tencentAdMarketingManager.batchUpdateTencentAdGroupData("ea", "aa", list)
        then:
        noExceptionThrown()
        where:
        list << [[], [new TencentAdGroupDataResult.TencentAdGroupData(date: new Date())]]
    }
}
