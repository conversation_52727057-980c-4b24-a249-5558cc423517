package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.mankeep.api.result.BaseUserInfoResult
import com.facishare.marketing.api.arg.GetCustomizeFormDataByIdArg
import com.facishare.marketing.api.arg.customizeFormData.FormDataUserArgContainer
import com.facishare.marketing.api.result.CustomizeFormDataDetailResult
import com.facishare.marketing.api.result.QueryFormUserDataResult
import com.facishare.marketing.api.service.ClueManagementService
import com.facishare.marketing.api.service.CustomizeFormDataService
import com.facishare.marketing.api.service.FsBindService
import com.facishare.marketing.api.service.NoticeService
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService
import com.facishare.marketing.common.typehandlers.value.*
import com.facishare.marketing.outapi.arg.result.AssociateCrmLeadModel
import com.facishare.marketing.outapi.arg.result.AssociateWxServiceModel
import com.facishare.marketing.outapi.service.ClueDefaultSettingService
import com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService
import com.facishare.marketing.outapi.service.WxServiceMarketingAccountAssociationService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.manager.CustomizeFormDataDAOManager
import com.facishare.marketing.provider.dao.pay.FsPayOrderDao
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendTaskDAO
import com.facishare.marketing.provider.dto.CreateFormDataDTO
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO
import com.facishare.marketing.provider.dto.FormBindObjectStatisticsDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import com.facishare.marketing.provider.dto.kis.BranchSearchFormBindObjectStatisticsDTO
import com.facishare.marketing.provider.entity.*
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity
import com.facishare.marketing.provider.entity.landing.LandingObjCustomizeUserRelation
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.entity.pay.FsPayOrder
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmLeadAccountRelationEntity
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult
import com.facishare.marketing.provider.innerResult.crm.CreateObjResult
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager
import com.facishare.marketing.provider.manager.baidu.UtmDataManger
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.landing.LandingObjCustomizeUserRelationManager
import com.facishare.marketing.provider.manager.pay.FsPayOrderManager
import com.facishare.marketing.provider.manager.qr.QRCodeManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager
import com.facishare.marketing.provider.mq.sender.DelayQueueSender
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.CrmV2MappingManager
import com.facishare.marketing.provider.service.marketingplugin.DataPermissionPluginService
import com.facishare.wechat.union.core.api.service.WechatFanService
import com.fxiaoke.crmrestapi.common.data.*
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.result.DuplicateSearchResult
import com.fxiaoke.crmrestapi.result.DuplicatesearchQueryResult
import com.fxiaoke.crmrestapi.result.RelatedDuplicateSearchResult
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.github.mybatis.pagination.Page
import spock.lang.Specification
import spock.lang.Unroll

class CustomizeFormDataManagerSpec extends Specification {
    def fsMessageManager = Mock(FsMessageManager)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def noticeDAO = Mock(NoticeDAO)
    def productDAO = Mock(ProductDAO)
    def activityDAO = Mock(ActivityDAO)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def customizeFormDataObjectDAO = Mock(CustomizeFormDataObjectDAO)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def fileV2Manager = Mock(FileV2Manager)
    def photoManager = Mock(PhotoManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def crmV2MappingManager = Mock(CrmV2MappingManager)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def kmUserManager = Mock(KmUserManager)
    def objectManager = Mock(ObjectManager)
    def wxServiceMarketingAccountAssociationService = Mock(WxServiceMarketingAccountAssociationService)
    def crmLeadMarketingAccountAssociationService = Mock(CrmLeadMarketingAccountAssociationService)
    def wechatFanService = Mock(WechatFanService)
    def activityManager = Mock(ActivityManager)
    def qrCodeManager = Mock(QRCodeManager)
    def photoAssociationDAO = Mock(PhotoAssociationDAO)
    def objectTagManager = Mock(ObjectTagManager)
    def groupSendTaskDAO = Mock(QywxGroupSendTaskDAO)
    def conferenceManager = Mock(ConferenceManager)
    def customizeFormDataDAOManager = Mock(CustomizeFormDataDAOManager)
    def fsPayOrderManager = Mock(FsPayOrderManager)
    def customerCustomizeFormDataManager = Mock(CustomerCustomizeFormDataManager)
    def areaManager = Mock(AreaManager)
    def userMarketingAccountAssociationManager = Mock(UserMarketingAccountAssociationManager)
    def userMarketingAccountRelationManager = Mock(UserMarketingAccountRelationManager)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def customizeTicketManager = Mock(CustomizeTicketManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def memberManager = Mock(MemberManager)
    def safetyManagementManager = Mock(SafetyManagementManager)
    def clueManagementManager = Mock(ClueManagementManager)
    def utmDataManger = Mock(UtmDataManger)
    def executeTaskDetailManager = Mock(ExecuteTaskDetailManager)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def spreadChannelManager = Mock(SpreadChannelManager)
    def campaignMergeDataResetManager = Mock(CampaignMergeDataResetManager)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def eieaConverter = Mock(EIEAConverter)
    def userMarketingAccountService = Mock(UserMarketingAccountService)
    def marketingStatLogPersistorManger = Mock(MarketingStatLogPersistorManger)
    def saveClueFailNoticeConfigDAO = Mock(SaveClueFailNoticeConfigDAO)
    def noticeService = Mock(NoticeService)
    def fsBindService = Mock(FsBindService)
    def hexagonSiteObjectDAO = Mock(HexagonSiteObjectDAO)
    def userMarketingBrowserUserRelationDao = Mock(UserMarketingBrowserUserRelationDao)
    def userMarketingAccountDAO = Mock(UserMarketingAccountDAO)
    def clueDefaultSettingService = Mock(ClueDefaultSettingService)
    def marketingEventCommonSettingDAO = Mock(MarketingEventCommonSettingDAO)
    def fsPayOrderDao = Mock(FsPayOrderDao)
    def landingObjCustomizeUserRelationManager = Mock(LandingObjCustomizeUserRelationManager)
    def userMarketingCrmLeadAccountRelationDao = Mock(UserMarketingCrmLeadAccountRelationDao)
    def adOCPCUploadManager = Mock(AdOCPCUploadManager)
    def delayQueueSender = Mock(DelayQueueSender)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def dataPermissionPluginService = Mock(DataPermissionPluginService)
    def clueManagementService = Mock(ClueManagementService)
    def customizeFormDataService = Mock(CustomizeFormDataService)
    def marketingActivityObjectRelationDAO = Mock(MarketingActivityObjectRelationDAO)
    def objectDescribeService = Mock(ObjectDescribeService)

    def manager = new CustomizeFormDataManager(
            fsMessageManager: fsMessageManager,
            customizeFormDataDAO: customizeFormDataDAO,
            marketingActivityExternalConfigDao: marketingActivityExternalConfigDao,
            noticeDAO: noticeDAO,
            productDAO: productDAO,
            activityDAO: activityDAO,
            marketingLiveDAO: marketingLiveDAO,
            activityEnrollDataDAO: activityEnrollDataDAO,
            photoManager: photoManager,
            customizeFormDataUserDAO: customizeFormDataUserDAO,
            fileV2Manager: fileV2Manager,
            customizeFormDataObjectDAO: customizeFormDataObjectDAO,
            crmV2Manager: crmV2Manager,
            crmV2MappingManager: crmV2MappingManager,
            fsAddressBookManager: fsAddressBookManager,
            kmUserManager: kmUserManager,
            objectManager: objectManager,
            wxServiceMarketingAccountAssociationService: wxServiceMarketingAccountAssociationService,
            crmLeadMarketingAccountAssociationService: crmLeadMarketingAccountAssociationService,
            wechatFanService: wechatFanService,
            activityManager: activityManager,
            qrCodeManager: qrCodeManager,
            photoAssociationDAO: photoAssociationDAO,
            objectTagManager: objectTagManager,
            groupSendTaskDAO: groupSendTaskDAO,
            conferenceManager: conferenceManager,
            customizeFormDataDAOManager: customizeFormDataDAOManager,
            fsPayOrderManager: fsPayOrderManager,
            customerCustomizeFormDataManager: customerCustomizeFormDataManager,
            areaManager: areaManager,
            userMarketingAccountAssociationManager: userMarketingAccountAssociationManager,
            userMarketingAccountRelationManager: userMarketingAccountRelationManager,
            campaignMergeDataManager: campaignMergeDataManager,
            campaignMergeDataDAO: campaignMergeDataDAO,
            customizeTicketManager: customizeTicketManager,
            conferenceDAO: conferenceDAO,
            memberManager: memberManager,
            safetyManagementManager: safetyManagementManager,
            clueManagementManager: clueManagementManager,
            utmDataManger: utmDataManger,
            executeTaskDetailManager: executeTaskDetailManager,
            hexagonPageDAO: hexagonPageDAO,
            spreadChannelManager: spreadChannelManager,
            campaignMergeDataResetManager: campaignMergeDataResetManager,
            contentMarketingEventMaterialRelationDAO: contentMarketingEventMaterialRelationDAO,
            hexagonSiteDAO: hexagonSiteDAO,
            eieaConverter: eieaConverter,
            userMarketingAccountService: userMarketingAccountService,
            marketingStatLogPersistorManger: marketingStatLogPersistorManger,
            saveClueFailNoticeConfigDAO: saveClueFailNoticeConfigDAO,
            noticeService: noticeService,
            fsBindService: fsBindService,
            hexagonSiteObjectDAO: hexagonSiteObjectDAO,
            userMarketingBrowserUserRelationDao: userMarketingBrowserUserRelationDao,
            userMarketingAccountDAO: userMarketingAccountDAO,
            clueDefaultSettingService: clueDefaultSettingService,
            marketingEventCommonSettingDAO: marketingEventCommonSettingDAO,
            fsPayOrderDao: fsPayOrderDao,
            landingObjCustomizeUserRelationManager: landingObjCustomizeUserRelationManager,
            userMarketingCrmLeadAccountRelationDao: userMarketingCrmLeadAccountRelationDao,
            adOCPCUploadManager: adOCPCUploadManager,
            delayQueueSender: delayQueueSender,
            crmMetadataManager: crmMetadataManager,
            dataPermissionPluginService: dataPermissionPluginService,
            clueManagementService: clueManagementService,
            customizeFormDataService: customizeFormDataService,
            marketingActivityObjectRelationDAO: marketingActivityObjectRelationDAO,
            objectDescribeService: objectDescribeService,
            needCheckEnrollFieldEa: "1,2"
    )

    def "createFormData"() {
        given:
        fileV2Manager.getApathByTApath(*_) >> null
        customizeFormDataDAOManager.insertCustomizeFormData(*_) >> 1

        def dto = new CreateFormDataDTO(
                ea: "1",
                fsUserId: 1,
                formHeadSetting: new FormHeadSetting(headPhotoPath: ["TA_1", "A_1"]),
                formMoreSetting: new FormMoreSetting(synchronousCRM: true)
        )

        when:
        manager.createFormData(dto)

        then:
        noExceptionThrown()
    }

    def "createCustomizeFormDataQRCode"() {
        given:
        photoAssociationDAO.getDataByTypeFormIdAndMarketingEventId(*_) >> qri
        photoManager.querySinglePhoto(*_) >> hpe
        qrCodeManager.saveQrCodeAndPhotoAssociationData(*_) >> new QRCodeManager.CreateQRCodeResult(qrCodeApath: "a", qrCodeUrl: "u")

        when:
        manager.createCustomizeFormDataQRCode("1", "1", "1")

        then:
        noExceptionThrown()

        where:
        qri   | hpe
        []    | null
        ["1"] | null
        ["1"] | new PhotoEntity(path: "p", url: "u")
    }

    def "setCustomizeFormDataHeadPhoto"() {
        given:
        def arg = new CustomizeFormDataDetailResult(
                formHeadSetting: new FormHeadSetting(
                        headPhotoPath: ["1"]
                )
        )

        fileV2Manager.getUrlByPath(*_) >> "1"

        when:
        manager.setCustomizeFormDataHeadPhoto(arg)

        then:
        noExceptionThrown()
    }

    def "setCustomizeFormDataHeadPhoto2"() {
        given:
        fileV2Manager.getUrlByPath(*_) >> "1"

        when:
        manager.setCustomizeFormDataHeadPhoto(ptl)

        then:
        noExceptionThrown()

        where:
        ptl   | a
        []    | 1
        ["1"] | 1
    }

    def "setCustomizeFormDataFileInfo"() {
        given:
        fileV2Manager.getDiskFilePreviewUrl(*_) >> "1"
        def fif = new FormSuccessSetting.FileInfo()
        fif.setFilePath("1")
        def arg =
                new CustomizeFormDataDetailResult(
                        formSuccessSetting: new FormSuccessSetting(
                                fileInfo: fif
                        )
                )

        when:
        manager.setCustomizeFormDataFileInfo(arg)

        then:
        noExceptionThrown()

    }

    def "checkCustomizeFormDataStatus"() {
        when:
        def result = manager.checkCustomizeFormDataStatus(ett, "1", nd)

        then:
        with(result) {
            result.errCode == code
        }

        where:
        ett                                    | nd    | code
        null                                   | true  | 80500
        new CustomizeFormDataEntity(status: 1) | false | 80501
        new CustomizeFormDataEntity(status: 2) | true  | 80502
        new CustomizeFormDataEntity(status: 0) | true  | 0
    }

    def "updateCustomizeFormDataObject"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "2")
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 1
        customizeFormDataObjectDAO.insertCustomizeFormDataObject(*_) >> 1

        when:
        manager.updateCustomizeFormDataObject(fid, "1", 1, "1", 1, 1, "1", null)

        then:
        noExceptionThrown()

        where:
        fid  | a
        "1"  | 1
        null | 1
    }

    def "bindCustomizeFormDataObject"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "2")
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 1
        customizeFormDataObjectDAO.insertCustomizeFormDataObject(*_) >> 1

        when:
        manager.bindCustomizeFormDataObject("1", "1", 1, "1", 1, null, "1", null)

        then:
        noExceptionThrown()

    }

    def "unBindCustomizeFormDataObject"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "2")
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 1

        when:
        manager.unBindCustomizeFormDataObject("1", 1, "1")

        then:
        noExceptionThrown()
    }

    def "unBindCustomizeFormDataObjectBatch"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingFormByObjectIdList(*_) >> [new CustomizeFormDataObjectEntity(formId: "2")]
        customizeFormDataObjectDAO.deleteCustomizeFormDataObjectBatch(*_) >> 1

        when:
        manager.unBindCustomizeFormDataObjectBatch(["1"], 1, "1")

        then:
        noExceptionThrown()
    }

    def "unBindCustomizeFormDataObjectAndDeleteForm"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "2")
        customizeFormDataObjectDAO.deleteCustomizeFormDataObject(*_) >> 1
        customizeFormDataDAOManager.updateCustomizeFormDataStatus(*_) >> 1

        when:
        manager.unBindCustomizeFormDataObjectAndDeleteForm("1", 1, "1", 1)
        then:
        noExceptionThrown()
    }

    def "unBindCustomizeFormDataObjectAndDeleteFormBatch"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingFormByObjectIdList(*_) >> [new CustomizeFormDataObjectEntity(formId: "2")]
        customizeFormDataObjectDAO.deleteCustomizeFormDataObjectBatch(*_) >> 1
        customizeFormDataDAOManager.updateCustomizeFormDataStatus(*_) >> 1

        when:
        manager.unBindCustomizeFormDataObjectAndDeleteFormBatch(["1"], 1, "1", 1)

        then:
        noExceptionThrown()
    }

    def "bindObjectStatistics"() {
        given:
        customizeFormDataObjectDAO.bindObjectStatistics(*_) >> [new FormBindObjectStatisticsDTO(objectType: 1, objectCount: 1)]

        when:
        manager.bindObjectStatistics("1")

        then:
        noExceptionThrown()
    }

    def "bindObjectStatistics2"() {
        given:
        customizeFormDataObjectDAO.branchSearchBindObjectStatistics(*_) >> [new BranchSearchFormBindObjectStatisticsDTO(statisticsResult: "[{\"object_type\": \"1\", \"object_count\": \"1\"}]")]

        when:
        manager.bindObjectStatistics(["1"])

        then:
        noExceptionThrown()
    }

    def "checkAddLeadsObjectAuth"() {
        given:
        crmV2Manager.funcPermissionCheck(*_) >> null

        when:
        manager.checkAddLeadsObjectAuth("1", 1)

        then:
        noExceptionThrown()
    }

    def "getBindFormDataByObject"() {
        given:
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(status: 2)
        hexagonSiteDAO.getFormBySiteIds(*_) >> hsl
        hexagonSiteObjectDAO.getObjectBindingHexagonSite(*_) >> new HexagonSiteObjectEntity(siteId: "1")
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> obf
        customizeFormDataObjectDAO.deleteCustomizeFormDataObjectById(*_) >> true

        when:
        manager.getBindFormDataByObject("1", "1", ot)

        then:
        noExceptionThrown()

        where:
        ot   | hsl                                   | obf
        null | null                                  | null
        16   | null                                  | null
        4    | [new HexagonSiteListDTO(formId: "1")] | null
        26   | [new HexagonSiteListDTO(formId: "1")] | null
        66   | [new HexagonSiteListDTO(formId: "1")] | null
        66   | [new HexagonSiteListDTO(formId: "1")] | new CustomizeFormDataObjectEntity(formId: "1", id: "1")
    }

    def "getBindFormDataByObjects"() {
        given:
        customizeFormDataObjectDAO.getObjectBindingFormByObjectIds(*_) >> cfoe
        customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(*_) >> cfe

        when:
        manager.getBindFormDataByObjects(ea, ["1"])

        then:
        noExceptionThrown()

        where:
        ea   | cfoe                                                            | cfe
        null | null                                                            | null
        "1"  | null                                                            | null
        "1"  | [new CustomizeFormDataObjectEntity(formId: "1", objectId: "1")] | null
        "1"  | [new CustomizeFormDataObjectEntity(formId: "1", objectId: "1")] | [new CustomizeFormDataEntity(id: "1")]

    }

    def "getBindFormClueNum"() {
        given:
        customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(*_) >> cfd
        customizeFormDataUserDAO.getFormInfoAndClueNameByFormsAndObject(*_) >> [new CustomizeFormClueNumDTO(formId: "1")]

        when:
        manager.getBindFormClueNum(fids, "1", 1)

        then:
        noExceptionThrown()

        where:
        fids  | cfd
        []    | null
        ["1"] | null
        ["1"] | [new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), id: "2")]
    }

    def "getFormClueNumBySource"() {
        given:
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(ea: "1")
        customizeFormDataUserDAO.getFormClueCountBySourceType(*_) >> cfc

        when:
        manager.getFormClueNumBySource(fids, 1)

        then:
        noExceptionThrown()

        where:
        fids  | cfc
        []    | null
        ["1"] | []
        ["1"] | [new CustomizeFormClueNumDTO(formId: "1")]
    }

    def "getLatestEnrollDataForm"() {
        given:
        customizeFormDataUserDAO.getLatestEnrollDataFormByObject(*_) >> null
        customizeFormDataUserDAO.getLatestEnrollDataFormByMarketingActivityId(*_) >> null
        customizeFormDataUserDAO.getLatestEnrollDataFormByEventIdAndObject(*_) >> null

        when:
        manager.getLatestEnrollDataForm(tp, "1", 1, "1", "1", "1")

        then:
        noExceptionThrown()

        where:
        tp | a
        1  | 1
        2  | 1
        4  | 1
        7  | 1
    }

    def "getCustomizeFormDataUser"() {
        given:
        def formDataUserArgContainer = new FormDataUserArgContainer(
                type: tp,
                ea: "1",
                objectId: "1",
                objectType: ot,
                marketingActivityId: "1",
                usage: 1
        )

        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "1")
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> null
        customizeFormDataUserDAO.getLatestEnrollDataFormByObject(*_) >> ofd
        customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityId(*_) >> null
        customizeFormDataUserDAO.getLatestEnrollDataFormByMarketingActivityId(*_) >> mfd

        when:
        manager.getCustomizeFormDataUser(formDataUserArgContainer, new Page(pageNo: 1, pageSize: 1))

        then:
        noExceptionThrown()

        where:
        tp | ot | ofd                           | mfd
        1  | 1  | null                          | null
        1  | 1  | new CustomizeFormDataEntity() | null
        2  | 1  | new CustomizeFormDataEntity() | null
        2  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity()
        3  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity()
    }

    def "getCustomizeFormDataUser V2"() {
        given:
        def formDataUserArgContainer = new FormDataUserArgContainer(
                type: tp,
                ea: "1",
                objectId: "1",
                objectType: ot,
                marketingActivityId: "1",
                usage: 1
        )

        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "1")
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> cfd
        customizeFormDataUserDAO.getLatestEnrollDataFormByObject(*_) >> ofd
        customizeFormDataUserDAO.getCustomizeFormDataUserByFormId(*_) >> null
        customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityId(*_) >> null
        customizeFormDataUserDAO.getLatestEnrollDataFormByMarketingActivityId(*_) >> mfd
        customizeFormDataUserDAO.getLatestEnrollDataFormByEventIdAndObject(*_) >> efd
        customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndObject(*_) >> null
        customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndFormId(*_) >> null
        hexagonPageDAO.getPageByFormId(*_) >> hpe
        customizeFormDataUserDAO.getCustomizeFormDataUserByActivityd(*_) >> null
        groupSendTaskDAO.getGroupSendAttachments(*_) >> cnt
        marketingActivityObjectRelationDAO.queryByMarketingActivityId(*_) >> mel

        when:
        manager.getCustomizeFormDataUser(formDataUserArgContainer, new Page(pageNo: 1, pageSize: 1))

        then:
        noExceptionThrown()

        where:
        tp | ot | ofd                           | mfd                           | cfd                                    | efd                           | hpe                       | cnt | mel
        3  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | null                                   | null                          | null                      | 1   | null
        3  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | null                          | null                      | 1   | null
        4  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | null                          | null                      | 1   | null
        4  | 16 | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | new CustomizeFormDataEntity() | null                      | 1   | null
        4  | 16 | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | new CustomizeFormDataEntity() | [new HexagonPageEntity()] | 1   | null
        4  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | new CustomizeFormDataEntity() | null                      | 1   | null
        8  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | new CustomizeFormDataEntity() | null                      | 1   | null
        5  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | null                                   | new CustomizeFormDataEntity() | null                      | 1   | [new MarketingActivityObjectRelationEntity()]
        5  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | new CustomizeFormDataEntity() | null                      | 1   | [new MarketingActivityObjectRelationEntity()]
        5  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | null                                   | new CustomizeFormDataEntity() | null                      | 1   | []
        5  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | null                                   | new CustomizeFormDataEntity() | null                      | 0   | []
        5  | 1  | new CustomizeFormDataEntity() | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(status: 1) | new CustomizeFormDataEntity() | null                      | 0   | []

    }

    def "getCustomizeFormDataUser V3"() {
        given:
        def formDataUserArgContainer = new FormDataUserArgContainer(
                type: tp,
                ea: "fs",
                objectId: "1",
                objectType: ot,
                marketingActivityId: "1",
                usage: 1,
                needMarketingEventAndActivityDetail: true
        )

        safetyManagementManager.phoneNumberSensitive(*_) >> {}
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(status: 1, crmApiName: "a",
                formMoreSetting: new FormMoreSetting(saveCrmObjectType: sot))
        conferenceManager.getExtraDataNameByObjectIds(*_) >> ["1": "1"]
        conferenceManager.getEnrollNameByObjectType(*_) >> ["1": "1"]
        crmV2Manager.getAllObjDescribeApiNameAndDisplayName(*_) >> ["1": "1"]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg()]
        kmUserManager.batchGetBaseUserInfo(*_) >> ["1": new BaseUserInfoResult()]
        crmV2Manager.getObjectNameMapByIds(*_) >> ["1": "1"]
        safetyManagementManager.turnOnPhoneNumberSensitive(*_) >> true
        fsPayOrderDao.queryByIds(*_) >> [new FsPayOrder(id: "1", status: 1, amount: 1), new FsPayOrder(id: "2", status: 2, amount: 1)]
        crmV2Manager.getList(*_) >> new com.fxiaoke.crmrestapi.common.data.Page<ObjectData>(dataList: [new ObjectData(["outer_uid": "1", "name": "name"])])
        areaManager.getAreaNameByValue(*_) >> ["A": "a"]
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(id: "1", bindCrmObjectType: 1, bindCrmObjectId: "1")]

        customizeFormDataUserDAO.getCustomizeFormDataUserByActivityd(*_) >>
                [new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                        marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "百度", utmMedium: "SEM", name: "1"),
                        payOrderId: "1", spreadFsUid: 1, uid: "1", outTenantId: "1", campaignId: "1", leadId: "1", otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(objectId: "1", apiName: "LeadsObj")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "搜狗", utmMedium: "SEM", name: "1"), payOrderId: "2", leadId: "2"),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "360", utmMedium: "SEO", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "bing", utmMedium: "信息流", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "神马", utmMedium: "主动营销", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "今日头条", utmMedium: "aaaaaaaa", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "抖音", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "腾讯", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "搜狐新闻", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "凤凰网", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "网易新闻", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "微信", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "公众号", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "微博", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "UC", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "知乎", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "EDM", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "sms", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "email", name: "1")),
                 new CustomizeFormDataUserEntity(ea: "1", submitContent: new CustomizeFormDataEnroll(country: "C", province: "G", city: "SZ", district: "NS",
                         marketingSourceName: "other", marketingSourceType: "advertisement", marketingSourceSite: "********", utmSource: "1222321312", name: "1")),
                ]

        when:
        manager.getCustomizeFormDataUser(formDataUserArgContainer, new Page(pageNo: 1, pageSize: 1))

        then:
        noExceptionThrown()

        where:
        tp  | ot | sot
        999 | 1  | 0
        8   | 1  | 1
        8   | 1  | 0
    }

    def "getAllCustomizeFormDataEnrollByFormId"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserByFormId(*_) >> [new CustomizeFormDataUserEntity(submitContent: new CustomizeFormDataEnroll())]

        when:
        manager.getAllCustomizeFormDataEnrollByFormId(fId)

        then:
        noExceptionThrown()

        where:
        fId  | a
        null | 1
        "1"  | 1
    }

    def "batchesGetFanInfo"() {
        given:
        def qfud = [
                new QueryFormUserDataResult(wxAppId: "1", openId: "1"),
                new QueryFormUserDataResult(wxAppId: "1", openId: "2"),
                new QueryFormUserDataResult(wxAppId: "2", openId: "3")
        ]

        crmMetadataManager.listV3(*_) >> new InnerPage<ObjectData>(dataList: [new ObjectData("wx_app_id": "1", "wx_open_id": "2")])
        fileV2Manager.batchGetUrlByPath(*_) >> ["1": "1"]

        when:
        manager.batchesGetFanInfo("1", qfud)

        then:
        noExceptionThrown()

    }

    def "generateExcelTitleList"() {
        given:
        def fil = new FieldInfoList()
        fil.add(new FieldInfo(label: "1"))

        when:
        manager.generateExcelTitleList(fil, 2, 7, 7)

        then:
        noExceptionThrown()
    }

    def "buildExportEnrollsData"() {
        given:
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> null
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "1")
        customizeFormDataUserDAO.getLatestEnrollDataFormByObject(*_) >> ledf
        activityManager.getActivityLinkObject(*_) >> []
        customizeFormDataUserDAO.getCustomizeFormDataUserByObjectsWithOutPage(*_) >> []
        customizeFormDataUserDAO.getCustomizeFormDataUserByObjectWithOutPage(*_) >> []
        objectManager.getObjectName(*_) >> "1"
        customizeFormDataUserDAO.getLatestEnrollDataFormByMarketingActivityId(*_) >> madf
        safetyManagementManager.turnOnPhoneNumberSensitive(*_) >> true
        safetyManagementManager.phoneNumberSensitive(*_) >> {}
        customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityIdWithOutPage(*_) >> []

        when:
        manager.buildExportEnrollsData("1", "1", ot, "1", "1", tp, usg, st)

        then:
        noExceptionThrown()

        where:
        tp | usg | st | ot | ledf                          | madf
        1  | 1   | 1  | 16 | null                          | null
        1  | 1   | 1  | 16 | new CustomizeFormDataEntity() | null
        1  | 1   | 1  | 13 | new CustomizeFormDataEntity() | null
        2  | 1   | 1  | 13 | new CustomizeFormDataEntity() | null
        2  | 1   | 1  | 13 | new CustomizeFormDataEntity() | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"))
    }

    def "buildExportEnrollsData V2"() {
        given:
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> cfd
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity(formId: "1")
        customizeFormDataUserDAO.getLatestEnrollDataFormByObject(*_) >> ledf
        activityManager.getActivityLinkObject(*_) >> []
        customizeFormDataUserDAO.getCustomizeFormDataUserByObjectsWithOutPage(*_) >> []
        customizeFormDataUserDAO.getCustomizeFormDataUserByObjectWithOutPage(*_) >> []
        objectManager.getObjectName(*_) >> "1"
        customizeFormDataUserDAO.getLatestEnrollDataFormByMarketingActivityId(*_) >> madf
        safetyManagementManager.turnOnPhoneNumberSensitive(*_) >> true
        safetyManagementManager.phoneNumberSensitive(*_) >> {}
        customizeFormDataUserDAO.getCustomizeFormDataUserByMarketingActivityIdWithOutPage(*_) >> []
        customizeFormDataUserDAO.getCustomizeFormDataUserByFormIdWithOutPage(*_) >> []
        customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndFormIdWithOutPage(*_) >> [new CustomizeFormDataUserEntity(submitContent: new CustomizeFormDataEnroll())]
        customizeFormDataUserDAO.getCustomizeFormDataUserByEventIdAndObjectWithOutPage(*_) >> []
        customizeFormDataUserDAO.getLatestEnrollDataFormByEventIdAndObject(*_) >> mkdf
        hexagonPageDAO.getPageByFormId(*_) >> hpg
        groupSendTaskDAO.getGroupSendAttachments(*_) >> cnt
        marketingActivityObjectRelationDAO.queryByMarketingActivityId(*_) >> rel

        when:
        manager.buildExportEnrollsData("1", "1", ot, "1", "1", tp, usg, st)

        then:
        noExceptionThrown()

        where:
        tp | usg | st | ot | ledf | madf | cfd                                                                             | mkdf                                                                                                                  | hpg                       | cnt | rel
        3  | 1   | 1  | 16 | null | null | null                                                                            | null                                                                                                                  | null                      | 1   | null
        3  | 1   | 1  | 16 | null | null | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name")) | null                                                                                                                  | null                      | 1   | null
        4  | 1   | 1  | 16 | null | null | null                                                                            | null                                                                                                                  | null                      | 1   | null
        4  | 1   | 1  | 16 | null | null | null                                                                            | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"))                                       | null                      | 1   | null
        4  | 1   | 1  | 16 | null | null | null                                                                            | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"))                                       | [new HexagonPageEntity()] | 1   | null
        4  | 1   | 1  | 12 | null | null | null                                                                            | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), formBodySetting: new FieldInfoList()) | null                      | 1   | null
        5  | 1   | 1  | 12 | null | null | null                                                                            | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), formBodySetting: new FieldInfoList()) | null                      | 1   | [new MarketingActivityObjectRelationEntity()]
        5  | 1   | 1  | 12 | null | null | null                                                                            | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), formBodySetting: new FieldInfoList()) | null                      | 1   | [new MarketingActivityObjectRelationEntity()]
        5  | 1   | 1  | 12 | null | null | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name")) | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), formBodySetting: new FieldInfoList()) | null                      | 1   | []
        5  | 1   | 1  | 12 | null | null | null                                                                            | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), formBodySetting: new FieldInfoList()) | null                      | 0   | []
        5  | 1   | 1  | 12 | null | null | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name")) | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "name"), formBodySetting: new FieldInfoList()) | null                      | 0   | []

    }

    def "generateExcelEnrollInfosList"() {
        given:
        def fil = new FieldInfoList()
        fil.add(new FieldInfo(type: "unknown"))
        fil.add(new FieldInfo(type: "image", apiName: "1"))

        fileV2Manager.batchGetUrlByPath(*_) >> ["1": "1"]
        safetyManagementManager.turnOnPhoneNumberSensitive(*_) >> true
        safetyManagementManager.phoneNumberSensitive(*_) >> {}
        spreadChannelManager.queryChannelMapData(*_) >> ["1": "1"]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg()]
        fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(*_) >> [1: "1"]
        crmV2Manager.getList(*_) >> new com.fxiaoke.crmrestapi.common.data.Page<ObjectData>(dataList: [new ObjectData(["enterpriserelation_id": "1", "name": "name", "outer_uid": "1"])])
        fsPayOrderDao.queryByIds(*_) >> [new FsPayOrder(id: "1", amount: 123, status: 1), new FsPayOrder(id: "2", amount: 123, status: 2)]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "1"

        when:
        manager.generateExcelEnrollInfosList("1", cel, fil, 2, 7, tp)

        then:
        noExceptionThrown()

        where:
        cel                                                                                                                                                                                                                           | tp
        [new CustomizeFormDataUserEntity(payOrderId: "1", createTime: new Date(), spreadFsUid: 1, outTenantId: "1", outUid: "1", submitContent: new CustomizeFormDataEnroll(picMap: ["1": [new PicContainer(path: "1", ext: "2")]])),
         new CustomizeFormDataUserEntity(payOrderId: "2", createTime: new Date(), spreadFsUid: 1, outTenantId: "1", outUid: "1", submitContent: new CustomizeFormDataEnroll(picMap: ["1": [new PicContainer(path: "1", ext: "2")]])),
         new CustomizeFormDataUserEntity(payOrderId: "3", createTime: new Date(), spreadFsUid: 1, outTenantId: "1", outUid: "1", submitContent: new CustomizeFormDataEnroll(picMap: ["1": [new PicContainer(path: "1", ext: "2")]]))] | 7

        [new CustomizeFormDataUserEntity(payOrderId: "1", createTime: new Date(), spreadFsUid: 1, outTenantId: "1", outUid: "1", submitContent: new CustomizeFormDataEnroll(picMap: ["1": [new PicContainer(path: "1", ext: "2")]])),
         new CustomizeFormDataUserEntity(payOrderId: "2", createTime: new Date(), spreadFsUid: 1, outTenantId: "1", outUid: "1", submitContent: new CustomizeFormDataEnroll(picMap: ["1": [new PicContainer(path: "1", ext: "2")]])),
         new CustomizeFormDataUserEntity(payOrderId: "3", createTime: new Date(), spreadFsUid: 1, outTenantId: "1", outUid: "1", submitContent: new CustomizeFormDataEnroll(picMap: ["1": [new PicContainer(path: "1", ext: "2")]]))] | 8
    }

    def "generateEnrollData"() {
        given:
        def fil = new FieldInfoList()
        fil.add(new FieldInfo(type: "unknown", apiName: "1"))

        def cfd = new CustomizeFormDataUserEntity(createTime: new Date())

        when:
        manager.generateEnrollData(cfd, fil, null, true)

        then:
        noExceptionThrown()
    }

    def "handlerSpecialTypeEnrollData"() {
        given:
        def arg = new CustomizeFormDataUserEntity(
                objectType: 4,
                objectId: "1",
                parentObjectId: "1",
                parentObjectType: 4
        )

        productDAO.getById(*_) >> new ProductEntity(id: "1")
        productDAO.updateSubmitCount(*_) >> 1

        when:
        manager.handlerSpecialTypeEnrollData(arg)

        then:
        noExceptionThrown()
    }

    def "syncHandleCampaignMembersDataSaveStatus"() {
        given:
        def objMap = [
                "_id"                     : "1",
                "object_describe_api_name": "LeadsObj"
        ]

        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(marketingEventId: "1")
        campaignMergeDataManager.getCampaignMergeObjIdByEntity(*_) >> "a"
        campaignMergeDataManager.updateCampaignMembersObj(*_) >> {}

        when:
        manager.syncHandleCampaignMembersDataSaveStatus("1", me, "1", objMap)

        then:
        noExceptionThrown()

        where:
        me   | a
        "1"  | 1
        null | 1
    }

    def "getTime"() {
        given:
        double v = 123.0
        def map = [
                "last_modified_time": v
        ]

        when:
        CustomizeFormDataManager.getTime(map)

        then:
        noExceptionThrown()
    }

    def "getPriority"() {
        given:
        def map = [
                "priority": 12
        ]

        when:
        CustomizeFormDataManager.getPriority(map)

        then:
        noExceptionThrown()
    }

    def "getCustomerByEnterpriserelationId"() {
        given:
        crmV2Manager.getList(*_) >> lst

        when:
        manager.getCustomerByEnterpriserelationId("1", "1")

        then:
        noExceptionThrown()

        where:
        lst                                                                                               | a
        null                                                                                              | 1
        new com.fxiaoke.crmrestapi.common.data.Page<ObjectData>(dataList: [new ObjectData(["_id": "1"])]) | 1
    }

    def "handleRelatedDuplicateSearchData"() {
        given:
        crmV2Manager.duplicateSearchResult(*_) >> new DuplicateSearchResult()
        customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(*_) >> 1

        when:
        manager.handleRelatedDuplicateSearchData("1", "1", null, "1")

        then:
        noExceptionThrown()
    }

    def "handleLeadsSpecialField"() {
        given:
        def cfd = new CustomizeFormDataEnroll(
                utmMedium: "1",
                utmSource: "1",
                utmCampaig: "1",
                utmContent: "1",
                utmTerm: "1",
                searchKeyword: "1",
                userStatus: "1",
                sensorsUserId: "1",
                marketingSourceName: "1"
        )

        def data = ["1": "1"]

        crmV2Manager.createHeaderObj(*_) >> new HeaderObj(1)
        objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(data: new ControllerGetDescribeResult(describe: new ObjectDescribe(fields: ["field_lcg2a__c": new FieldDescribe(), "field_200Je__c": new FieldDescribe()])))

        when:
        manager.handleLeadsSpecialField(ea, cfd, data)

        then:
        noExceptionThrown()

        where:
        ea       | a
        null     | 1
        "fs"     | 1
        "fs1111" | 1

    }

    def "conversionCrmErrorMessage"() {

        when:
        manager.conversionCrmErrorMessage(msg)

        then:
        noExceptionThrown()

        where:
        msg                  | a
        null                 | 1
        "response code!=200" | 1

    }

    def "associateWxUser"() {
        given:
        wxServiceMarketingAccountAssociationService.associateWxService(*_) >> awr

        when:
        manager.associateWxUser("1", "1", ea)

        then:
        noExceptionThrown()

        where:
        ea   | awr
        null | null
        "1"  | new com.facishare.marketing.common.result.Result<AssociateWxServiceModel.AssociateWxServiceResult>(errCode: 1)
        "1"  | new com.facishare.marketing.common.result.Result<AssociateWxServiceModel.AssociateWxServiceResult>(errCode: 0, data: new AssociateWxServiceModel.AssociateWxServiceResult())

    }

    def "bindBrowserUserAndCrmId"() {
        given:
        userMarketingAccountRelationManager.bindBrowserUserAndLead(*_) >> null

        when:
        manager.bindBrowserUserAndCrmId("1", ea, "1", "1")

        then:
        noExceptionThrown()

        where:
        ea   | a
        null | 1
        "1"  | 1
    }

    def "saveCrmLead"() {
        given:
        def cffm = new FieldMappings()

        def cfd = new CustomizeFormDataEntity(
                crmFormFieldMapV2: cffm
        )
        def cdu = new CustomizeFormDataUserEntity(
                marketingEventId: "1",
                marketingActivityId: "1",
                id: "1",
                spreadFsUid: 1,
                channelValue: "1",
                objectId: "1",
                objectType: 1
        )

        def spy = Spy(manager)

        when:
        spy.saveCrmLead(ea, 1, new CustomizeFormDataEnroll(), cfd, ud, li, cdu)

        then:
        noExceptionThrown()

        where:
        ea   | ud   | li
        "1"  | true | null
        null | true | "1"
        "1"  | true | "1"

    }


    def "saveCrmLead V2"() {
        given:
        def cffm = new FieldMappings()
        cffm.add(new FieldMappings.FieldMapping(mankeepFieldName: "mk", crmFieldName: "fn"))

        def cfd = new CustomizeFormDataEntity(
                crmFormFieldMapV2: cffm
        )
        def cdu = new CustomizeFormDataUserEntity(
                marketingEventId: me,
                marketingActivityId: "1",
                id: "1",
                spreadFsUid: 1,
                channelValue: "1",
                objectId: "1",
                objectType: 1,
                submitContent: new CustomizeFormDataEnroll(
                        phone: "***********"
                )
        )
        def spy = Spy(manager)

        crmV2Manager.getObjectFieldNameList(*_) >> st
        marketingEventCommonSettingDAO.getSettingByEa(*_) >> new MarketingEventCommonSettingEntity(openMergePhone: op)
        campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(*_) >> [new CampaignMergeDataEntity(campaignMembersObjId: "1")]
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIdOrderByCreateTime(*_) >> new CustomizeFormDataUserEntity(leadId: lid, otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(), formId: "1")
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(campaignMemberMap: cffm)
        crmV2MappingManager.createCustomizeFormDataToCampaignFieldDataMap(*_) >> ["1": "1"]
        crmV2Manager.updateCampaign(*_) >> null
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["1": "1"]
        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(marketingEventId: "1")
        crmV2Manager.queryDuplicate(*_) >> qd
        crmV2Manager.relatedDuplicateSearch(*_) >> rds
        customizeFormDataUserDAO.updateCustomizeFormDataUserCrmObjectBind(*_) >> 1
        spy.syncHandleCampaignMembersDataSaveStatus(*_) >> {}
        spy.handleRelatedDuplicateSearchData(*_) >> false

        when:
        spy.saveCrmLead(ea, 1, new CustomizeFormDataEnroll(), cfd, ud, li, cdu)

        then:
        noExceptionThrown()

        where:
        ea  | ud    | li  | st                | op    | lid  | me   | qd                                                         | rds
        "1" | true  | "1" | ["1"] as HashSet  | false | null | "1"  | null                                                       | null
        "1" | false | "1" | ["fn"] as HashSet | false | null | "1"  | null                                                       | null
        "1" | false | "1" | ["fn"] as HashSet | false | "1"  | "1"  | null                                                       | null
        "1" | false | "1" | ["fn"] as HashSet | true  | "1"  | "1"  | null                                                       | null
        "1" | false | "1" | ["fn"] as HashSet | true  | "1"  | null | new DuplicatesearchQueryResult(data: [], duplicateMode: 1) | new RelatedDuplicateSearchResult(results: [new RelatedDuplicateSearchResult.DataList(dataList: [["life_status": "invalid", "object_describe_api_name": "AccountObj"]])])
        "1" | false | "1" | ["fn"] as HashSet | true  | "1"  | null | new DuplicatesearchQueryResult(data: [], duplicateMode: 2) | null

    }

    def "saveCrmLead V3"() {
        given:
        def cffm = new FieldMappings()
        cffm.add(new FieldMappings.FieldMapping(mankeepFieldName: "mk", crmFieldName: "fn"))

        def cfd = new CustomizeFormDataEntity(
                crmFormFieldMapV2: cffm,
                crmPoolId: "1"
        )
        def cdu = new CustomizeFormDataUserEntity(
                marketingEventId: me,
                marketingActivityId: "1",
                id: "1",
                spreadFsUid: 1,
                channelValue: "1",
                objectId: "1",
                objectType: 1,
                submitContent: new CustomizeFormDataEnroll(
                        phone: "***********"
                ),
                partner: true,
                landingObjId: "1",
                saveCrmStatus: stt
        )
        def spy = Spy(manager)

        crmV2Manager.getObjectFieldNameList(*_) >> st
        marketingEventCommonSettingDAO.getSettingByEa(*_) >> new MarketingEventCommonSettingEntity(openMergePhone: op)
        campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(*_) >> [new CampaignMergeDataEntity(campaignMembersObjId: "1")]
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIdOrderByCreateTime(*_) >> new CustomizeFormDataUserEntity(leadId: lid, otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(), formId: "1")
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(campaignMemberMap: cffm)
        crmV2MappingManager.createCustomizeFormDataToCampaignFieldDataMap(*_) >> ["1": "1"]
        crmV2Manager.updateCampaign(*_) >> null
        crmV2MappingManager.createCustomizeFormDataToCrmLeadFieldDataMap(*_) >> ["1": "1"]
        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(marketingEventId: "1")
        crmV2Manager.queryDuplicate(*_) >> qd
        crmV2Manager.relatedDuplicateSearch(*_) >> rds
        customizeFormDataUserDAO.updateCustomizeFormDataUserCrmObjectBind(*_) >> 1
        spreadChannelManager.buildCRMChannelData(*_) >> {}
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> null
        clueManagementManager.addCustomizeFormCommonData(*_) >> ["1": "1"]
        crmV2Manager.createLead(*_) >> cl

        spy.syncHandleCampaignMembersDataSaveStatus(*_) >> {}
        spy.handleRelatedDuplicateSearchData(*_) >> true
        spy.handleLeadsSpecialField(*_) >> {}
        spy.getCustomerByEnterpriserelationId(*_) >> "1"

        when:
        spy.saveCrmLead(ea, 1, new CustomizeFormDataEnroll(marketingPromotionSourceId: "1"), cfd, ud, li, cdu)

        then:
        noExceptionThrown()

        where:
        ea  | ud    | li  | st                | op   | lid | me  | qd                                                         | rds  | cl                                 | stt
        "1" | false | "1" | ["fn"] as HashSet | true | "1" | "1" | new DuplicatesearchQueryResult(data: [], duplicateMode: 2) | null | null                               | 1
        "1" | false | "1" | ["fn"] as HashSet | true | "1" | "1" | new DuplicatesearchQueryResult(data: [], duplicateMode: 2) | null | new CreateLeadResult(leadId: "1")  | 1
        "1" | false | "1" | ["fn"] as HashSet | true | "1" | "1" | new DuplicatesearchQueryResult(data: [], duplicateMode: 2) | null | new CreateLeadResult(leadId: "1")  | 3
        "1" | false | "1" | ["fn"] as HashSet | true | "1" | "1" | new DuplicatesearchQueryResult(data: [], duplicateMode: 2) | null | new CreateLeadResult(leadId: null) | 3

    }

    def "getEnrollType"() {
        given:
        def arg = new CustomizeFormDataUserEntity(
                sourceType: st,
                objectType: ot,
                marketingActivityId: "1",
                uid: ud
        )

        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity(associateIdType: at)

        when:
        manager.getEnrollType(arg)

        then:
        noExceptionThrown()

        where:
        st   | ot | at   | ud
        1    | 1  | 15   | null
        null | 28 | 15   | null
        null | 16 | 15   | null
        null | 16 | 17   | null
        null | 16 | 1001 | null
        null | 16 | 1004 | null
        null | 16 | 1999 | "1"
        null | 16 | 1999 | null
    }

    def "getSystemPromotionChannelType"() {
        given:
        def arg = new CustomizeFormDataUserEntity(
                channelValue: cv,
                sourceType: st,
                submitContent: new CustomizeFormDataEnroll(
                        utmMedium: um
                ),
                marketingActivityId: md,
                uid: "1"
        )

        spreadChannelManager.getChannelByMarketingActivityId(*_) >> "1"

        when:
        manager.getSystemPromotionChannelType(arg)

        then:
        noExceptionThrown()

        where:
        cv   | st | um          | md
        "1"  | 7  | "1"         | null
        null | 7  | "SEM"       | null
        null | 7  | "SEO"       | null
        null | 7  | "fn674s2jN" | null
        null | 6  | "fn674s2jN" | "1"
        null | 6  | "fn674s2jN" | null
    }

    def "getMarketingSourceType"() {
        given:
        def arg = new CustomizeFormDataUserEntity(
                submitContent: new CustomizeFormDataEnroll(
                        marketingSourceType: mt
                )
        )

        when:
        manager.getMarketingSourceType(arg)

        then:
        noExceptionThrown()

        where:
        mt     | a
        null   | 1
        "aaa"  | 1
        "link" | 1
    }

    def "getMarketingSourceName"() {
        given:
        def arg = new CustomizeFormDataUserEntity(
                sourceType: st,
                submitContent: sc
        )

        when:
        manager.getMarketingSourceName(arg)

        then:
        noExceptionThrown()

        where:
        st   | sc
        null | null
        7    | null
        7    | new CustomizeFormDataEnroll(marketingSourceName: "aaa")
        7    | new CustomizeFormDataEnroll(marketingSourceName: "wechat")
    }

    def "saveEnrollDataToCrm"() {
        given:
        def cfd = new CustomizeFormDataEntity()
        def cfdu = new CustomizeFormDataUserEntity(
                openId: oid,
                wxAppId: "1",
                spreadFsUid: sfs,
                submitContent: new CustomizeFormDataEnroll(
                        leadSaveStatus: lss,
                        leadId: lid
                ),
                ipAddr: "123",
                userAgent: "ua",
                fingerPrint: fp,
                marketingEventId: "1"
        )

        clueDefaultSettingService.getClueCreator(*_) >> "1"
        customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessage(*_) >> 1
        objectTagManager.batchAddTagsToUserMarketings(*_) >> {}
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity()
        userMarketingAccountRelationManager.bindWxUserAndLead(*_) >> null
        userMarketingAccountRelationManager.bindWxUserAndCustomer(*_) >> null
        userMarketingAccountRelationManager.bindWxUserAndContact(*_) >> {}
        utmDataManger.syncUtmFormDataToROI(*_) >> {}
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> an

        def spy = Spy(manager)
        spy.checkAddLeadsObjectAuth(*_) >> false
        spy.saveCrmLead(*_) >> {}
        spy.bindBrowserUserAndCrmId(*_) >> {}

        when:
        spy.saveEnrollDataToCrm(cfd, cfdu)

        then:
        noExceptionThrown()

        where:
        oid  | fp   | sfs  | lss | lid  | an
        "1"  | null | null | 3   | "1"  | null
        "1"  | null | 1    | 3   | "1"  | null
        "1"  | null | 1    | 3   | "1"  | null
        "1"  | null | 1    | 2   | "1"  | null
        "1"  | null | 1    | 2   | null | null
        "1"  | null | 1    | 4   | "1"  | new CustomizeFormDataUserEntity(otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(apiName: "AccountObj"))
        "1"  | null | 1    | 4   | "1"  | new CustomizeFormDataUserEntity(otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(apiName: "ContactObj"))
        "1"  | null | 1    | 4   | "1"  | new CustomizeFormDataUserEntity(otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(apiName: "********"))
        "1"  | null | 1    | 4   | "1"  | null
        "1"  | null | 1    | 666 | "1"  | null
        null | "1"  | null | 3   | "1"  | null
        null | "1"  | 1    | 3   | "1"  | null
        null | "1"  | 1    | 2   | "1"  | null
        null | "1"  | 1    | 2   | null | null
        "1"  | null | 1    | 4   | "1"  | new CustomizeFormDataUserEntity(otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(apiName: "AccountObj"))
        "1"  | null | 1    | 666 | "1"  | null
    }

    @Unroll
    def "saveObjectToCrm"() {
        given:
        def cfd = new CustomizeFormDataEntity(
                ea: ea,
                crmFormFieldMapV2: ffm
        )

        def cfdu = new CustomizeFormDataUserEntity(
                spreadFsUid: sfs,
                marketingEventId: me
        )

        crmV2Manager.getDetail(*_) >> new ObjectData(owner: 1)
        customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(*_) >> 1

        when:
        manager.saveObjectToCrm(cfd, cfdu, ud)

        then:
        noExceptionThrown()

        where:
        ea   | ud    | sfs  | me   | ffm
        null | false | 1    | null | null
        null | true  | 1    | null | null
        null | false | null | "1"  | null
        null | false | null | null | null
        "1"  | false | null | null | null
        "1"  | false | null | null | new FieldMappings()
    }

    def "saveObjectToCrm V2"() {
        given:
        def ffm = new FieldMappings()
        ffm.add(new FieldMappings.FieldMapping(mankeepFieldName: "mkf", crmFieldName: cfn))
        def cfd = new CustomizeFormDataEntity(
                ea: ea,
                crmFormFieldMapV2: ffm
        )

        def cfdu = new CustomizeFormDataUserEntity(
                spreadFsUid: sfs,
                marketingEventId: me,
                extraDataId: "1"
        )

        Set<String> st = new HashSet<>()
        st.add("a")
        crmV2Manager.getDetail(*_) >> new ObjectData(owner: 1)
        customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrmMessage(*_) >> 1
        customizeFormDataUserDAO.updateCustomizeFormDataUserSaveCrm(*_) >> 1
        crmV2Manager.getObjectFieldNameListByApiName(*_) >> st
        crmV2MappingManager.createCustomizeFormDataToObjFieldDataMap(*_) >> ["1": "1"]
        clueManagementManager.addCustomizeFormOrganizationData(*_) >> ["1": "1"]
        clueManagementManager.addCustomizeFormCommonData(*_) >> ["1": "1"]
        crmV2Manager.updateCrmObj(*_) >> new CreateObjResult(id: cid)

        when:
        manager.saveObjectToCrm(cfd, cfdu, ud)

        then:
        noExceptionThrown()

        where:
        ea  | ud    | sfs  | me   | cfn   | cid
        "1" | false | null | null | "cfn" | null
        "1" | false | null | null | "a"   | null
        "1" | true  | null | null | "a"   | null
        "1" | true  | null | null | "a"   | "1"
    }

    def "bindBrowserUserRepeatDataToUserMarketing"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> new CustomizeFormDataUserEntity(otherCrmObjectBind: oco)
        userMarketingAccountRelationManager.bindBrowserUserAndCustomer(*_) >> null
        userMarketingAccountRelationManager.bindBrowserUserAndContact(*_) >> null

        when:
        manager.bindBrowserUserRepeatDataToUserMarketing(new CustomizeFormDataUserEntity(), "1", "1", "1")

        then:
        noExceptionThrown()

        where:
        oco                                                           | a
        null                                                          | 1
        new CustomizeFormBindOtherCrmObject(apiName: "AccountObj")    | 1
        new CustomizeFormBindOtherCrmObject(apiName: "ContactObj")    | 1
        new CustomizeFormBindOtherCrmObject(apiName: "ContactObj123") | 1
    }

    def "bindWxUserRepeatDataToUserMarketing"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> new CustomizeFormDataUserEntity(otherCrmObjectBind: oco)
        userMarketingAccountRelationManager.bindBrowserUserAndCustomer(*_) >> null
        userMarketingAccountRelationManager.bindBrowserUserAndContact(*_) >> null
        userMarketingAccountRelationManager.bindWxUserAndLead(*_) >> null

        when:
        manager.bindWxUserRepeatDataToUserMarketing(new CustomizeFormDataEntity(), new CustomizeFormDataUserEntity(), new CustomizeFormDataEnroll())

        then:
        noExceptionThrown()

        where:
        oco                                                           | a
        null                                                          | 1
        new CustomizeFormBindOtherCrmObject(apiName: "AccountObj")    | 1
        new CustomizeFormBindOtherCrmObject(apiName: "ContactObj")    | 1
        new CustomizeFormBindOtherCrmObject(apiName: "ContactObj123") | 1
    }

    def "associateMarketingUserId"() {
        given:
        def cfd = new CustomizeFormDataEntity(
                ea: "1"
        )

        def cfdu = new CustomizeFormDataUserEntity(
                fingerPrint: "1",
                submitContent: new CustomizeFormDataEnroll(
                        phone: "1"
                )
        )

        userMarketingAccountAssociationManager.associate(*_) >> null

        when:
        manager.associateMarketingUserId(cfd, cfdu)

        then:
        noExceptionThrown()
    }

    def "sendNotice"() {
        given:
        def spy = Spy(manager)
        spy.sendSaveClueFailMessage(*_) >> {}

        when:
        spy.sendNotice("1", new CustomizeFormDataUserEntity())

        then:
        noExceptionThrown()
    }

    def "sendSaveClueFailMessage"() {
        given:
        saveClueFailNoticeConfigDAO.querySaveClueFailNoticeConfigByEaAndType(*_) >> scf
        customizeFormDataUserDAO.getSaveCrmFailLeadsCountByMarketingEventId(*_) >> 1
        crmV2Manager.getDetail(*_) >> new ObjectData(["owner": "1"])
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> cfd
        fsMessageManager.sendSaveLeadsFailFxMessage(*_) >> {}


        when:
        manager.sendSaveClueFailMessage("1", "1", "1", ij)

        then:
        noExceptionThrown()

        where:
        scf                                                                                               | ij    | cfd
        null                                                                                              | true  | null
        new SaveClueFailNoticeConfigEntity(sendType: 2)                                                   | false | null
        new SaveClueFailNoticeConfigEntity(sendType: 3, clueType: 0, receiverType: 1, sendScope: "1,2,3") | false | null
        new SaveClueFailNoticeConfigEntity(sendType: 3, clueType: 0, receiverType: 2, sendScope: "1,2,3") | false | null
        new SaveClueFailNoticeConfigEntity(sendType: 3, clueType: 1, receiverType: 1, sendScope: "1,2,3") | false | null
        new SaveClueFailNoticeConfigEntity(sendType: 3, clueType: 1, receiverType: 1, sendScope: "1,2,3") | false | new CustomizeFormDataEntity(formHeadSetting: new FormHeadSetting(name: "1"))

    }

    def "updateEnrollDataToCrm"() {
        given:
        def cfd = new CustomizeFormDataEntity()
        def cfdu = new CustomizeFormDataUserEntity(
                wxAppId: "1",
                submitContent: new CustomizeFormDataEnroll(
                ),
                ipAddr: "123",
                userAgent: "ua",
                marketingEventId: "1"
        )

        when:
        manager.updateEnrollDataToCrm(cfd, cfdu)

        then:
        noExceptionThrown()

    }

    def "checkUserIsEnrolledWithNoHandleActivity"() {
        given:
        activityManager.getActivityLinkObject(*_) >> []
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByObjectIds(*_) >> []
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndObjectIds(*_) >> []
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndObjectIds(*_) >> []
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFsUserInfoAndObjectIds(*_) >> []

        when:
        manager.checkUserIsEnrolledWithNoHandleActivity(
                "1", new CustomizeFormDataEntity(), "1", ot, wxid, oid, fp, ea, fsu, phone
        )

        then:
        noExceptionThrown()

        where:
        ot | wxid | oid  | fp   | ea   | fsu  | phone
        13 | null | null | null | null | null | "137"
        13 | "1"  | "1"  | null | null | null | null
        13 | null | null | "1"  | null | null | null
        13 | null | null | null | "1"  | 1    | null
        19 | null | null | null | null | null | "137"
        19 | "1"  | "1"  | null | null | null | null
        19 | null | null | "1"  | null | null | null
        19 | null | null | null | "1"  | 1    | null

    }

    def "checkWxAppUserIsEnrolledWithNoHandleActivity"() {
        given:
        activityManager.getActivityLinkObject(*_) >> []
        customizeFormDataUserDAO.queryCustomizeFormDataUserByPhoneAndMarketingEventId(*_) >> []
        customizeFormDataUserDAO.getMarketingUserAccountByEaAndUid(*_) >> "1"
        customizeFormDataUserDAO.listByUserMarketingIds(*_) >> ["1"]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByCrmLeadIds(*_) >> [new CustomizeFormDataUserEntity()]

        when:
        manager.checkWxAppUserIsEnrolledWithNoHandleActivity("1", new CustomizeFormDataEntity(ea: "1", id: "1"), "1", ot, "1", phone)

        then:
        noExceptionThrown()

        where:
        ot | phone
        13 | "137"
        13 | null

    }

    def "updateLandingCustomizeUserRelation"() {
        given:
        userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadId(*_) >> new UserMarketingCrmLeadAccountRelationEntity(userMarketingId: "1")
        landingObjCustomizeUserRelationManager.updateLeadIdAndUserMarketingId(*_) >> {}
        crmV2Manager.updateLead(*_) >> null

        when:
        manager.updateLandingCustomizeUserRelation("1", "1", new LandingObjCustomizeUserRelation(landingObjId: "1"))

        then:
        noExceptionThrown()
    }

    def "getFormEnrollDataBindObject"() {

        when:
        manager.getFormEnrollDataBindObject(arg)

        then:
        noExceptionThrown()

        where:
        arg                                                                                                                                     | a
        null                                                                                                                                    | 1
        new CustomizeFormDataUserEntity(saveCrmStatus: 1)                                                                                       | 1
        new CustomizeFormDataUserEntity(saveCrmStatus: 3, leadId: "1")                                                                          | 1
        new CustomizeFormDataUserEntity(saveCrmStatus: 3, otherCrmObjectBind: new CustomizeFormBindOtherCrmObject(apiName: "1", objectId: "1")) | 1
    }

    def "formatEnrollDataIncludeSpecialField"() {
        given:
        def fif = new FieldInfo(type: "image", apiName: "texts1")
        def cfe = new CustomizeFormDataUserEntity(submitContent: new CustomizeFormDataEnroll(texts1: ["1", "2"]))

        def picmap = ["1": "1"]

        when:
        manager.formatEnrollDataIncludeSpecialField(fif, cfe, picmap, false)

        then:
        noExceptionThrown()
    }

    def "formatEnrollDataPicObject"() {

        when:
        manager.formatEnrollDataPicObject(["1", "2"], pmp, rpl)

        then:
        noExceptionThrown()

        where:
        pmp        | rpl
        null       | false
        ["1": "1"] | false
        ["1": "1"] | true

    }

    def "copy"() {
        given:
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()
        objectTagManager.queryObjectTag(*_) >> new ObjectTagEntity()
        objectTagManager.addOrUpdateObjectTag(*_) >> true
        customizeFormDataDAOManager.insertCustomizeFormData(*_) >> 1

        when:
        manager.copy("1", "1", 1)

        then:
        noExceptionThrown()
    }

    def "setCustomizeFormToMarketingEvent"() {
        given:
        conferenceDAO.getAllDataByEa(*_) >> [
                new ActivityEntity(ea: "1", id: "1"),
                new ActivityEntity(ea: "1", id: "1", marketingEventId: "1")
        ]
        campaignMergeDataResetManager.enterpriseStop(*_) >> false
        customizeFormDataObjectDAO.getObjectBindingForm(*_) >> new CustomizeFormDataObjectEntity()
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity(status: 0)
        contentMarketingEventMaterialRelationDAO.save(*_) >> {}

        when:
        manager.setCustomizeFormToMarketingEvent("setAll")

        then:
        noExceptionThrown()
    }

    def "saveEnrollDataToCrmByActivityEnroll"() {
        given:
        def spy = Spy(manager)
        spy.saveEnrollDataToCrm(*_) >> false

        conferenceDAO.getConferenceById(*_) >> new ActivityEntity()
        campaignMergeDataManager.activityEnrollIdToCampaignId(*_) >> ["1"]
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(*_) >> [new CustomizeFormDataUserEntity(formId: "1")]
        customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(*_) >> [new CustomizeFormDataEntity(id: "1")]
        campaignMergeDataManager.addCampaignMergeDataByUserEnroll(*_) >> "1"
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 1L
        campaignMergeDataManager.campaignIdToActivityEnrollId(*_) >> ["1"]
        campaignMergeDataManager.updateConferenceReviewStatus(*_) >> {}

        when:
        spy.saveEnrollDataToCrmByActivityEnroll(
                [new ActivityEnrollDataEntity(activityId: "1", id: "1")],
                true, 1, "1"
        )

        then:
        noExceptionThrown()
    }

    def "getCustomizeFormDataById"() {
        given:
        def arg = new GetCustomizeFormDataByIdArg(
                id: "1", needDisableData: true
        )

        customizeFormDataDAO.getCustomizeFormDataById(*_) >> cfd
        objectTagManager.queryObjectTag(*_) >> new ObjectTagEntity()
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity()

        def spy = Spy(manager)
        spy.checkCustomizeFormDataStatus(*_) >> res
        spy.setCustomizeFormDataFileInfo(*_) >> null
        spy.setCustomizeFormDataHeadPhoto(*_) >> new CustomizeFormDataDetailResult(formMoreSetting: new FormMoreSetting(), id: "1", createTime: new Date(), updateTime: new Date())

        when:
        spy.getCustomizeFormDataById(arg)

        then:
        noExceptionThrown()

        where:
        cfd                                                                 | res
        null                                                                | null
        new CustomizeFormDataEntity()                                       | new com.facishare.marketing.common.result.Result(errCode: 1)
        new CustomizeFormDataEntity(formMoreSetting: new FormMoreSetting()) | new com.facishare.marketing.common.result.Result(errCode: 0)
    }

    @Unroll
    def "deleteEnrollData"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> cfu
        crmV2Manager.bulkInvalidWithResult(*_) >> {}
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> cmd
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(*_) >> [new CustomizeFormDataUserEntity(id: "1")]
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 1
        customizeFormDataUserDAO.deleteById(*_) >> 1
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> new CustomizeFormDataEntity()

        when:
        manager.deleteEnrollData(eui, "1", 1)

        then:
        noExceptionThrown()

        where:
        eui  | cfu                                                                 | cmd
        null | null                                                                | null
        "1"  | null                                                                | null
        "1"  | new CustomizeFormDataUserEntity(saveCrmStatus: 0, leadId: "1")      | null
        "1"  | new CustomizeFormDataUserEntity(saveCrmStatus: 0, extraDataId: "1") | null
        "1"  | new CustomizeFormDataUserEntity(campaignId: "1")                    | null
        "1"  | new CustomizeFormDataUserEntity(campaignId: "1", saveCrmStatus: 0)  | new CampaignMergeDataEntity(bindCrmObjectId: "1", bindCrmObjectType: 1, campaignMembersObjId: "1")
    }

    def "checkEnrollField"() {
        given:
        def fil = new FieldInfoList()
        fil.add(new FieldInfo(apiName: "phone", isVerify: iv))
        def customizeFormDataEntity = new CustomizeFormDataEntity(
                ea: "1", formBodySetting: fil
        )

        hexagonPageDAO.getById(*_) >> hp

        when:
        manager.checkEnrollField(customizeFormDataEntity, new CustomizeFormDataEnroll(), 27, "1", "1")

        then:
        noExceptionThrown()

        where:
        hp                                                                                    | iv
        new HexagonPageEntity(content: "{\"components\": [{\"typeValue\": \"step-form\"}]}")  | true
        new HexagonPageEntity(content: "{\"components\": [{\"typeValue\": \"step-form2\"}]}") | true
        new HexagonPageEntity(content: "{\"components\": [{\"typeValue\": \"step-form2\"}]}") | false
    }

    def "saveConferenceParticipantsToCrm"() {
        given:
        customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(*_) >> cfdl
        customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(*_) >> [new CustomizeFormDataEntity(id: "2", formMoreSetting: new FormMoreSetting(syncToMember: true)), new CustomizeFormDataEntity(id: "2", formMoreSetting: new FormMoreSetting(syncToMember: true))]
        crmMetadataManager.batchGetByIdsV3(*_) >> [new ObjectData(["_id": "1"])]
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(id: "1", bindCrmObjectId: "1", campaignMembersObjId: "1")]
        customizeFormDataUserDAO.updateCustomizeFormDataUserCrmMessageWithIdList(*_) >> 1
        landingObjCustomizeUserRelationManager.getByEnrollIdList(*_) >> [new LandingObjCustomizeUserRelation(enrollId: "1")]
        marketingStatLogPersistorManger.sendLeadData(*_) >> {}
        campaignMergeDataManager.addCampaignMergeDataByUserEnroll(*_) >> "1"
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 1L
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> new CustomizeFormDataUserEntity(leadId: "1")
        memberManager.saveLeadToMember(*_) >> null

        def spy = Spy(manager)
        spy.saveEnrollDataToCrm(*_) >> true

        when:
        spy.saveConferenceParticipantsToCrm("1", cids)

        then:
        1 == 1

        where:
        cids  | cfdl
        []    | null
        ["1"] | null
        ["1"] | [new CustomizeFormDataUserEntity(leadId: "1", campaignId: "1"),
                 new CustomizeFormDataUserEntity(leadId: "1", campaignId: "1", saveCrmStatus: 1, formId: "2"),
                 new CustomizeFormDataUserEntity(leadId: "1", campaignId: "1", saveCrmStatus: 2, formId: "1"),
                 new CustomizeFormDataUserEntity(leadId: "1", campaignId: "1", saveCrmStatus: 3, formId: "1"),
                 new CustomizeFormDataUserEntity(leadId: "1", campaignId: "1", saveCrmStatus: 9),
                 new CustomizeFormDataUserEntity(campaignId: "1", saveCrmStatus: 0),
                 new CustomizeFormDataUserEntity(leadId: "2", campaignId: "1", saveCrmStatus: 0, id: "1"),
                 new CustomizeFormDataUserEntity(leadId: "1", campaignId: "1", saveCrmStatus: 0)]
        ["1"] | [new CustomizeFormDataUserEntity(campaignId: "1", saveCrmStatus: 0)]
    }

    def "saveFromDataByEnrollids"() {
        given:
        def fmp = new FieldMappings()
        fmp.add(new FieldMappings.FieldMapping())

        customizeFormDataUserDAO.querySaveFailedCrmEntityByIds(*_) >> cfu
        customizeFormDataUserDAO.queryCustomizeFormDataUserEntityByIds(*_) >> cfd
        customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(*_) >> [new CustomizeFormDataEntity(id: "1", formMoreSetting: new FormMoreSetting(saveCrmObjectType: scot, syncToMember: true), crmFormFieldMapV2: fmp)]
        executeTaskDetailManager.hasSameTaskExecute(*_) >> false
        landingObjCustomizeUserRelationManager.getByEnrollIdList(*_) >> [new LandingObjCustomizeUserRelation(enrollId: "1")]
        executeTaskDetailManager.checkTaskAndAddIfNotExist(*_) >> false
        executeTaskDetailManager.taskComplete(*_) >> {}
        campaignMergeDataManager.addCampaignMergeDataByUserEnroll(*_) >> "1"
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 1L
        customizeFormDataUserDAO.getCustomizeFormDataUserById(*_) >> new CustomizeFormDataUserEntity(leadId: "1", marketingEventId: "1", submitContent: new CustomizeFormDataEnroll())
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> new MarketingLiveEntity(tags: ["1"])
        crmLeadMarketingAccountAssociationService.associateCrmLead(*_) >> new com.facishare.marketing.common.result.Result<AssociateCrmLeadModel.AssociateCrmLeadResult>(errCode: 0, data: new AssociateCrmLeadModel.AssociateCrmLeadResult())
        memberManager.saveLeadToMember(*_) >> null

        def spy = Spy(manager)
        spy.saveObjectToCrm(*_) >> true
        spy.saveEnrollDataToCrm(*_) >> true
        spy.updateEnrollDataToCrm(*_) >> true
        spy.updateLandingCustomizeUserRelation(*_) >> {}

        when:
        spy.saveFromDataByEnrollids(ids)

        then:
        1 == 1

        where:
        ids   | cfu                                                                                                            | cfd                                                         | scot
        []    | []                                                                                                             | []                                                          | 1
        ["1"] | []                                                                                                             | []                                                          | 1
        ["1"] | []                                                                                                             | [new CustomizeFormDataUserEntity(leadId: "1", formId: "1")] | 1
        ["1"] | []                                                                                                             | [new CustomizeFormDataUserEntity(leadId: "1", formId: "1")] | 2
        ["1"] | [new CustomizeFormDataUserEntity()]                                                                            | []                                                          | 2
        ["1"] | [new CustomizeFormDataUserEntity(formId: "1")]                                                                 | []                                                          | 2
        ["1"] | [new CustomizeFormDataUserEntity(formId: "1", id: "1"), new CustomizeFormDataUserEntity(formId: "2", id: "1")] | []                                                          | 1
        ["1"] | [new CustomizeFormDataUserEntity(formId: "1", id: "1"), new CustomizeFormDataUserEntity(formId: "2", id: "1")] | []                                                          | 2
    }
}
