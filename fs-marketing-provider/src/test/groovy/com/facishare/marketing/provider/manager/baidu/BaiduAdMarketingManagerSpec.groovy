package com.facishare.marketing.provider.manager.baidu

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO
import com.facishare.marketing.api.vo.baidu.QueryAccountInfoVO
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.util.DateUtil
import com.facishare.marketing.provider.baidu.BaiduHttpManager
import com.facishare.marketing.provider.baidu.GetAccountInfoResultData
import com.facishare.marketing.provider.baidu.GetOcpcTargetPackageResult
import com.facishare.marketing.provider.baidu.RequestResult
import com.facishare.marketing.provider.baidu.ResultHeader
import com.facishare.marketing.provider.baidu.adgroup.AdGroupManager
import com.facishare.marketing.provider.baidu.adgroup.GetAdGroupResultData
import com.facishare.marketing.provider.baidu.campaign.GetCampaignResultData
import com.facishare.marketing.provider.baidu.feed.GetAdGroupFeedResult
import com.facishare.marketing.provider.baidu.feed.GetCampaignFeedResult
import com.facishare.marketing.provider.baidu.feed.GetProjectFeedResult
import com.facishare.marketing.provider.baidu.keyword.AdKeywordManager
import com.facishare.marketing.provider.baidu.keyword.AdKeywordResultData
import com.facishare.marketing.provider.baidu.report.GetReportDataResult
import com.facishare.marketing.provider.baidu.report.RealTimeResultType
import com.facishare.marketing.provider.baidu.report.ReportApiManager
import com.facishare.marketing.provider.dao.baidu.AdGroupDAO
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO
import com.facishare.marketing.provider.dao.baidu.BaiduAdGroupDataDAO
import com.facishare.marketing.provider.dao.baidu.BaiduAdGroupFeedDAO
import com.facishare.marketing.provider.dao.baidu.BaiduAdGroupFeedDataDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDataDAO
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignFeedDAO
import com.facishare.marketing.provider.dao.baidu.BaiduDataStatusDAO
import com.facishare.marketing.provider.dao.baidu.BaiduProjectFeedDAO
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity
import com.facishare.marketing.provider.entity.baidu.AdGroupEntity
import com.facishare.marketing.provider.entity.baidu.AdKeywordEntity
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity
import com.facishare.marketing.provider.entity.baidu.BaiduAdGroupDataEntity
import com.facishare.marketing.provider.entity.baidu.BaiduAdGroupFeedDataEntity
import com.facishare.marketing.provider.entity.baidu.BaiduAdGroupFeedEntity
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignDataEntity
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity
import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity
import com.facishare.marketing.provider.entity.baidu.CampaignDataOverviewDTOEntity
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignFeedEntity
import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity
import com.facishare.marketing.provider.entity.baidu.BaiduProjectFeedEntity
import com.facishare.marketing.provider.entity.baidu.CampaignDataOverviewDTOEntity
import com.facishare.marketing.provider.manager.CampaignMergeDataManager
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager
import com.facishare.marketing.provider.manager.crmobjectcreator.AdvertisingDetailsObjManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.google.common.collect.Maps
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class BaiduAdMarketingManagerSpec extends Specification {

    def adAccountManager = Mock(AdAccountManager)
    def baiduDataStatusDAO = Mock(BaiduDataStatusDAO)
    def accountApiManager = Mock(AccountApiManager)
    def campaignDataManager = Mock(CampaignDataManager)
    def baiduCampaignDataDAO = Mock(BaiduCampaignDataDAO)
    def campaignDAO = Mock(BaiduCampaignDAO)
    def campaignApiManager = Mock(CampaignApiManager)
    def baiduCampaignDAO = Mock(BaiduCampaignDAO)
    def reportApiManager = Mock(ReportApiManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def appVersionManager = Mock(AppVersionManager)
    def adGroupManager = Mock(AdGroupManager)
    def adKeywordManager = Mock(AdKeywordManager)
    def adKeywordDAO = Mock(AdKeywordDAO)
    def adObjectFieldMappingDAO = Mock(AdObjectFieldMappingDAO)
    def eieaConverter = Mock(EIEAConverter)
    def metadataActionService = Mock(MetadataActionService)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def utmDataManger = Mock(UtmDataManger)
    def refreshDataManager = Mock(RefreshDataManager)
    def adTokenManager = Mock(AdTokenManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def advertisingDetailsObjManager = Mock(AdvertisingDetailsObjManager)
    def adCommonManager = Mock(AdCommonManager)
    def redisManager = Mock(RedisManager)
    def baiduHttpManager = Mock(BaiduHttpManager)
    def dataPermissionManager = Mock(DataPermissionManager)
    def baiduProjectFeedDAO = Mock(BaiduProjectFeedDAO)
    def baiduCampaignFeedDAO = Mock(BaiduCampaignFeedDAO)
    def baiduAdGroupFeedDAO = Mock(BaiduAdGroupFeedDAO)
    def baiduAdGroupDataDAO = Mock(BaiduAdGroupDataDAO)
    def baiduAdGroupFeedDataDAO = Mock(BaiduAdGroupFeedDataDAO)
    def adGroupDAO = Mock(AdGroupDAO)
    def campaignList = "[\"需求词\",\"通用词\",\"行业词\",\"品牌词\",\"泛词\",\"ocpc-CRM解决方案优选\",\"ocpc-营销自动化工具推荐\",\"ocpc-移动营销解决方案\",\"ocpc-精准营销工具\",\"智能化营销工具\",\"销售增长策略实施\",\"销售管理系统\",\"营销策略优化\"]"
    def baiduCampaignToKeyword = "{\"需求词\":[\"CRM\",\"客户管理系统\",\"销售管理系统\",\"CRM纷享销客\",\"SCRM\"],\"通用词\":[\"销售漏斗 crm\",\"线索管理系统\",\"CRM系统\",\"销售系统\"],\"品牌词\":[\"纷享\",\"纷享销客\",\"纷享CRM\",\"纷享营销通\",\"纷享SCRM\"],\"ocpc-CRM解决方案优选\":[\"客户关系管理软件\",\"CRM解决方案推荐\",\"CRM系统优化\",\"销售效率提升\",\"客户满意度提升\"],\"ocpc-营销自动化工具推荐\":[\"营销自动化工具\",\"个性化营销工具推荐\",\"营销自动化平台优选\",\"个性化营销\",\"客户关怀策略\"],\"智能化营销工具\":[\"销售增长策略\",\"营销自动化\",\"RFM模型营销\"],\"销售增长策略实施\":[\"ABM营销\",\"B2B增长\",\"客户关系管理\",\"销售效率提升\",\"CRM系统\"],\"销售管理系统\":[\"销售工具\",\"销售流程管理\",\"销售管理工具\"],\"营销策略优化\":[\"营销策略\",\"营销优化\",\"数字营销\",\"策略分析\"],\"泛词\":[\"客户数据分析\",\"数据分析工具\",\"CDP平台\",\"数据驱动营销\",\"数据分析软件\"],\"ocpc-移动营销解决方案\":[\"移动广告\",\"移动推广\",\"移动营销工具\",\"移动应用推广\"],\"ocpc-精准营销工具\":[\"精准营销\",\"个性化营销\",\"定向广告\",\"精准推广\",\"目标受众\"],\"行业词\":[\"销售管理系统\",\"CRM系统\"]}"

    def baiduAdMarketingManager = new BaiduAdMarketingManager(
            "adAccountManager": adAccountManager,
            "baiduDataStatusDAO": baiduDataStatusDAO,
            "accountApiManager": accountApiManager,
            "campaignDataManager": campaignDataManager,
            "baiduCampaignDataDAO": baiduCampaignDataDAO,
            "campaignApiManager": campaignApiManager,
            "baiduCampaignDAO": baiduCampaignDAO,
            "campaignDAO": campaignDAO,
            "adGroupDAO": adGroupDAO,
            "reportApiManager": reportApiManager,
            "crmV2Manager": crmV2Manager,
            "appVersionManager": appVersionManager,
            "adGroupManager": adGroupManager,
            "adKeywordManager": adKeywordManager,
            "adKeywordDAO": adKeywordDAO,
            "adObjectFieldMappingDAO": adObjectFieldMappingDAO,
            "eieaConverter": eieaConverter,
            "metadataActionService": metadataActionService,
            "campaignMergeDataManager": campaignMergeDataManager,
            "utmDataManger": utmDataManger,
            "refreshDataManager": refreshDataManager,
            "adTokenManager": adTokenManager,
            "crmMetadataManager": crmMetadataManager,
            "advertisingDetailsObjManager": advertisingDetailsObjManager,
            "adCommonManager": adCommonManager,
            "redisManager": redisManager,
            "baiduHttpManager": baiduHttpManager,
            "campaignList": campaignList,
            "baiduCampaignToKeyword": baiduCampaignToKeyword,
            "dataPermissionManager": dataPermissionManager,
            "baiduProjectFeedDAO": baiduProjectFeedDAO,
            "baiduCampaignFeedDAO": baiduCampaignFeedDAO,
            "baiduAdGroupFeedDAO": baiduAdGroupFeedDAO,
            "baiduAdGroupDataDAO": baiduAdGroupDataDAO,
            "baiduAdGroupFeedDataDAO": baiduAdGroupFeedDataDAO
    )

    @Unroll
    def "isValidAccount"() {
        given:
        accountApiManager.getAccountInfo(*_) >> accountInfo
        campaignDataManager.queryAccountInvalidCode(*_) >> null
        adTokenManager.getBaiduAccessToken(_) >> "token"
        when:
        baiduAdMarketingManager.isValidAccount(new AdAccountEntity(username: "11", password: "sss", token: "ddddd"))
        then:
        noExceptionThrown()
        where:
        accountInfo << [null, new RequestResult<GetAccountInfoResultData>()]
    }

    @Unroll
    def "refreshAccountInfo"() {
        given:
        accountApiManager.getAccountInfo(*_) >> accountInfo
        adAccountManager.updateAccountRefreshData(*_) >> true
        def spy = Spy(baiduAdMarketingManager)
        spy.syncCampaignToMarketingEventObj(*_) >> { println "sync" }
        when:
        spy.refreshAccountInfo(new AdAccountEntity(username: "11", password: "sss", token: "ddddd"))
        then:
        noExceptionThrown()
        where:
        accountInfo << [null, new RequestResult<>(header: getHeader(), data: [new GetAccountInfoResultData()])]
    }

    @Unroll
    def "refreshCampaignInfo"() {
        given:
        adTokenManager.getBaiduAccessToken(*_) >> "token"
        campaignApiManager.getCampaign(*_) >> campaignResult
        baiduCampaignDAO.queryCampaignByCampaignId(*_) >> null
        baiduCampaignDAO.addCampaign(*_) >> { println "addCampaign" }
        baiduCampaignDAO.updateCampaignForRefresh(*_) >> { println "updateCampaignForRefresh" }

        when:
        baiduAdMarketingManager.refreshCampaignInfo(new AdAccountEntity(username: "11", password: "sss", token: "ddddd"))
        then:
        noExceptionThrown()
        where:
        campaignResult << [null,
                           new RequestResult<>(header: getHeader(), data: []),
                           new RequestResult<>(header: getHeader(), data: [new GetCampaignResultData()])]
    }

    @Shared
    Map<String, Object> ocpcRowMap = Maps.newHashMap()

    @Unroll
    def "refreshCampaignData"() {
        given:
        reportApiManager.getRealTimeData(*_) >> requestResult
        reportApiManager.getBaiduReportData(*_) >> ocpcResult
        baiduCampaignDataDAO.queryCampaignDataByDate(*_) >>> [null, new BaiduCampaignDataEntity()]
        baiduCampaignDataDAO.insertCampaignDataIgnore(*_) >> { println "insertCampaignData" }
        baiduCampaignDataDAO.updateCampaignRefreshData(*_) >> 1
        def spy = Spy(baiduAdMarketingManager)
        spy.buildAdvertisingDetailsObj(*_) >> { println "sync" }

        ocpcRowMap.put("campaignId", 1L)
        ocpcRowMap.put("date", "2024-04-02")
        ocpcRowMap.put("ocpcConversions", 2L)
        ocpcRowMap.put("deepOCPCConversions", 2L)


        when:
        baiduAdMarketingManager.refreshCampaignData(new AdAccountEntity(username: "11", password: "sss", token: "ddddd"), 1)
        then:
        noExceptionThrown()
        where:
        requestResult                                                                                           | ocpcResult
        null                                                                                                    | null
        new RequestResult<>(header: getHeader(), data: [new RealTimeResultType(id: 1L, kpis: ["2", "3", "4"])]) | [new GetReportDataResult(rowList: [ocpcRowMap])]
    }

    @Shared
    def objectData = new ObjectData()

    @Unroll
    def "refreshMarketingEventLeads"() {
        given:
        baiduCampaignDAO.listRelateMarketingEventCampaign(*_) >> existCampaignList
        crmV2Manager.getCrmObjectEntityTotalCount(*_) >> 1
        crmV2Manager.getList(*_) >> crmPage
        def spy = Spy(baiduAdMarketingManager)
        spy.saveCampaignData(*_) >> { println "sync" }

        objectData.put("create_time", System.currentTimeMillis() - (1000 * 60 * 60 * 24))
        objectData.put("marketing_event_id", "11")
        when:
        baiduAdMarketingManager.refreshMarketingEventLeads(new AdAccountEntity(username: "11", password: "sss", token: "ddddd"), 1, [])
        then:
        noExceptionThrown()
        where:
        existCampaignList                                 | crmPage
        null                                              | null
        [new BaiduCampaignEntity(marketingEventId: "11")] | new Page<>()
        [new BaiduCampaignEntity(marketingEventId: "11")] | new Page<>(dataList: [objectData])
    }

    @Unroll
    def "refreshAllData"() {
        given:
        adCommonManager.isPurchaseAdLicense(*_) >> purchaseAdLicense
        adAccountManager.queryAccountById(*_) >> adAccountEntity
        accountApiManager.getAccountInfo(*_) >> accountInfo
        campaignDataManager.queryAccountInvalidCode(*_) >> invalideCode
        adCommonManager.isSyncAdKeyword(_) >> true
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(*_) >> { println "sync" }
        refreshDataManager.syncKeywordServingPlanV2(*_) >> { println "sync" }
        refreshDataManager.syncMarketingTermServingLinesDataByKeyword(*_) >> { println "sync" }

        def spy = Spy(baiduAdMarketingManager)
        spy.refreshBaiduCampaignData(*_) >> { println "sync" }
        spy.refreshAdGroup(*_) >> { println "sync" }
        spy.syncCampaignToMarketingEventObj(*_) >> { println "sync" }
        spy.refreshMarketingEventOOCPCLaunchByAccountId(*_) >> { println "sync" }
        spy.refreshKeyword(*_) >> { println "sync" }
        spy.refreshProjectFeed(*_) >> { println "sync" }
        spy.syncProjectFeedToMarketingEventObj(*_) >> { println "sync" }
        spy.refreshCampaignFeed(*_) >> { println "sync" }
        spy.syncCampaignFeedToMarketingEventObj(*_) >> { println "sync" }
        spy.refreshAdGroupFeed(*_) >> { println "sync" }
        spy.syncAdGroupFeedToMarketingEventObj(*_) >> { println "sync" }

        when:
        spy.refreshAllData("88146", "dd", "dddd")
        then:
        noExceptionThrown()
        where:
        purchaseAdLicense | adAccountEntity                                     | accountInfo           | invalideCode
        false             | null                                                | null                  | null
        true              | null                                                | null                  | null
        true              | new AdAccountEntity(status: -1)                     | null                  | null
        true              | new AdAccountEntity(status: 0, refreshToken: "fff") | new RequestResult<>() | SHErrorCode.PARAMS_ERROR
        true              | new AdAccountEntity(status: 0, refreshToken: "fff") | new RequestResult<>() | SHErrorCode.SUCCESS

    }


    @Unroll
    def "refreshBaiduCampaignData"() {
        given:
        campaignApiManager.getCampaign(*_) >> campaignResult
        baiduCampaignDAO.queryCampaignByCampaignByIds(*_) >> existCampaignList
        baiduCampaignDAO.queryNotQueryCampaignData(*_) >> ["111"]
        baiduCampaignDAO.updateCampaignStatusByIds(*_) >> { println "ggggg" }
        baiduCampaignDAO.batchAddCampaign(*_) >> 1
        baiduCampaignDAO.batchUpdateCampaign(*_) >> { println "fffff" }
        when:
        baiduAdMarketingManager.refreshBaiduCampaignData(new AdAccountEntity(), "百度")
        then:
        noExceptionThrown()
        where:
        campaignResult                                                                                                                         | existCampaignList
        null                                                                                                                                   | null
        new RequestResult<>(header: getHeader(), data: [new GetCampaignResultData()])                                                          | null
        new RequestResult<>(header: getHeader(), data: [new GetCampaignResultData(campaignId: 1L), new GetCampaignResultData(campaignId: 2L)]) | [new BaiduCampaignEntity(campaignId: 1L)]

    }


    @Unroll
    def "refreshAdGroup"() {
        given:
        baiduCampaignDAO.queryCampaignTotalCount(*_) >> totalCount
        baiduCampaignDAO.pageCampaignIds(*_) >> [1L]
        adGroupManager.getAdGroupResultDataList(*_) >> new RequestResult<>(header: getHeader(), data: [new GetAdGroupResultData(adgroupId: 1L, campaignId: 11L), new GetAdGroupResultData(adgroupId: 2L, campaignId: 22L)])
        adGroupDAO.queryAdgroupByAdgroupIds(*_) >> existAdgroupList
        adGroupDAO.batchInsert(*_) >> 1
        adGroupDAO.batchUpdate(*_) >> 1
        when:
        baiduAdMarketingManager.refreshAdGroup(new AdAccountEntity(), "百度")
        then:
        noExceptionThrown()
        where:
        totalCount | existAdgroupList
        0          | null
        1          | []
        1          | [new AdGroupEntity(adGroupId: 1L)]
    }

    @Unroll
    def "refreshKeyword"() {
        given:
        adGroupDAO.getRefreshAdgroupTotalCount(*_) >> totalCount
        adGroupDAO.pageRefreshAdgroupIds(*_) >> [1L]
        adKeywordManager.getKeywordResultDataList(*_) >> Optional.of(new RequestResult<>(data: [new AdKeywordResultData(keywordId: 1L)]))
        adKeywordDAO.queryAdKeywordByIds(*_) >> existKeywordList
        adKeywordDAO.batchInsert(*_) >> 1
        adKeywordDAO.batchUpdate(*_) >> 1

        when:
        baiduAdMarketingManager.refreshKeyword(new AdAccountEntity(), "百度")
        then:
        noExceptionThrown()
        where:
        totalCount | existKeywordList
        0          | null
        1          | []
        1          | [new AdKeywordEntity(keywordId: 1L)]
    }

    @Shared
    def objectData2 = new ObjectData()

    @Shared
    def objectData3 = new ObjectData()

    @Unroll
    def "buildTermServingLinesData"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 88146
        adKeywordDAO.queryByCampaignIdList(*_) >> [new AdKeywordEntity(campaignId: 1L)]
        refreshDataManager.queryCrmMarketingKeywordPlanByUserName(*_) >> crmPage
        refreshDataManager.createTermServingLinesObj(*_) >> null
        objectData2.put("id", "id")
        objectData2.put("marketing_keyword_id", "marketing_keyword_id")
        objectData3.put("id", "id")
        objectData3.put("marketing_keyword_id", "marketing_keyword_id")
        when:
        baiduAdMarketingManager.buildTermServingLinesData(new AdAccountEntity(), [new BaiduCampaignEntity(campaignId: 1L, marketingEventId: "11")], [new BaiduCampaignDataEntity(campaignId: 1L, pv: 1000L, click: 333L, cost: 33333D, actionDate: new Date())])
        then:
        noExceptionThrown()
        where:
        crmPage << [null, new Page<>(dataList: [objectData2, objectData3])]
    }

    @Unroll
    def "initCampaignAndKeyword"() {
        given:
        baiduCampaignDAO.getAllNameByAdAccountId(*_) >> null
        redisManager.getPrimaryId() >> "1"
        baiduCampaignDAO.batchAddCampaign(*_) >> 1
        adObjectFieldMappingDAO.getByApiName(*_) >> null
        refreshDataManager.batchSyncCampaignToMarketingEventObj(*_) >> { println "fffff" }
        adKeywordDAO.batchInsert(*_) >> 1
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(*_) >> { println "fffff" }
        refreshDataManager.syncKeywordServingPlan(*_) >> { println "fffff" }
        when:
        baiduAdMarketingManager.initCampaignAndKeyword(new AdAccountEntity())
        then:
        noExceptionThrown()

    }

    @Unroll
    def "queryAccountInfo"() {
        given:
        adAccountManager.queryAccountByEaAndSource(*_) >> adAccountEntityList
        baiduDataStatusDAO.queryRefreshStatus(*_) >> statusEntity

        when:
        baiduAdMarketingManager.queryAccountInfo(new QueryAccountInfoVO())
        then:
        noExceptionThrown()
        where:
        adAccountEntityList                                                     | statusEntity
        []                                                                      | null
        [new AdAccountEntity(pcBalance: 1D, balance: 1D, budget: 1D, cost: 1D)] | null
        [new AdAccountEntity(pcBalance: 1D, balance: 1D, budget: 1D, cost: 1D)] | new BaiduDataStatusEntity(refreshStatus: 1)
    }

    @Unroll
    def "getDataOverview"() {
        given:
        baiduCampaignDataDAO.queryCampaignDataOverview(*_) >> new CampaignDataOverviewDTOEntity(cost: 3D, click: 6)
        campaignDAO.getMarketingEventIdsByEaAndAdAccountId(*_) >> eventIdList
        campaignDataManager.getLeadsCountByMarketingEvent(*_) >> 1
        when:
        baiduAdMarketingManager.getDataOverview(new GetDataOverviewVO(startTime: new Date(), endTime: new Date()))
        then:
        noExceptionThrown()
        where:
        eventIdList << [[], ["id"]]
    }

    @Unroll
    def "reindexAdvertisingDetailsObj"() {
        given:
        baiduCampaignDataDAO.countByEa(*_) >> 1
        baiduCampaignDataDAO.scanById(*_) >> list
        baiduCampaignDAO.queryMarketingEventIdByCampaignIdList(*_) >> [new BaiduCampaignEntity(campaignId: 1L, adAccountId: "1", marketingEventId: "event")]
        when:
        baiduAdMarketingManager.reindexAdvertisingDetailsObj("ea")
        then:
        noExceptionThrown()
        where:
        list << [[], [new BaiduCampaignDataEntity(pv: 1, click: 1, cost: 2D, campaignId: 1L, actionDate: new Date())]]
    }


    @Unroll
    def "syncKeywordByKeywordIds"() {
        given:
        adAccountManager.queryAccountById(*_) >> adAccountEntity
        RequestResult requestResult = new RequestResult(data: [])
        adKeywordManager.getKeywordResultDataList(*_) >> Optional.of(requestResult)
        adKeywordDAO.queryAdKeywordByIds(*_) >> []
        refreshDataManager.batchSyncKeywordToMarketingKeywordObj(*_) >> { println "dddd" }
        adKeywordDAO.queryAdKeywordByIds(*_) >> []
        adTokenManager.getBaiduAccessToken(*_) >> "ddd"
        when:
        baiduAdMarketingManager.syncKeywordByKeywordIds("ea", "adAccountId", keywordIdList)
        then:
        noExceptionThrown()
        where:
        keywordIdList | adAccountEntity
        []            | null
        [1L]          | null
        [1L]          | new AdAccountEntity()
    }


    @Unroll
    def "syncCampaignToMarketingEventObj"() {
        given:
        baiduCampaignDAO.queryRefereshCampaignTotalCount(*_) >> totalCount
        baiduCampaignDAO.pageCampaignEntityData(*_) >> []
        refreshDataManager.batchSyncCampaignToMarketingEventObj(*_) >> { println "dddd" }
        when:
        baiduAdMarketingManager.syncCampaignToMarketingEventObj("ea", new AdAccountEntity(), "百度")
        then:
        noExceptionThrown()
        where:
        totalCount << [0, 1]
    }

    @Unroll
    def "syncCampaignMember"() {
        given:
        adCommonManager.isPurchaseAdLicense(*_) >> isPurchaseAdLicense
        adObjectFieldMappingDAO.getByApiName(*_) >> new AdObjectFieldMappingEntity()
        utmDataManger.utmCreateMarketingEvent(*_) >> marketingEventIdOpt
        utmDataManger.utmCreateMarketingKeyword(*_) >> Optional.of("ke")
        utmDataManger.updateLeadROIData(*_) >> null
        eieaConverter.enterpriseAccountToId(*_) >> 1
        metadataActionService.edit(*_) >> editResult
        ObjectData objectData1 = new ObjectData()
        objectData1.put("company", "company")
        objectData1.put("name", "name")
        crmV2Manager.getDetail(*_) >> objectData1
        campaignMergeDataManager.getPhoneByObject(*_) >> "110"
        crmV2Manager.addCampaignMembersObjByLock(*_) >> "ddd"
        campaignMergeDataManager.addCampaignDataOnlyUnLock(*_) >> "ddd"
        utmDataManger.utmCreateMarketingKeywordPlan(*_) >> Optional.of("dddddd")
        when:
        baiduAdMarketingManager.syncCampaignMember("ea", "id", source, campaignName, keyword, true, null, null, 1L)
        then:
        noExceptionThrown()
        where:
        source | campaignName | keyword | isPurchaseAdLicense | marketingEventIdOpt | editResult
        null   | null         | null    | false               | null                | null
        null   | "cam"        | null    | false               | null                | null
        "百度" | "cam"        | null    | false               | null                | null
        "百度" | "cam"        | null    | false               | null                | null
        "百度" | "cam"        | "key"   | true                | Optional.of("ddd")  | null
        "百度" | "cam"        | null    | true                | Optional.of("ddd")  | new com.fxiaoke.crmrestapi.common.result.Result(code: -1)
        "百度" | "cam"        | null    | true                | Optional.of("ddd")  | new com.fxiaoke.crmrestapi.common.result.Result(code: 0)
    }

    @Unroll
    def "refreshPrototypeRoomAccountData"() {
        given:
        adAccountManager.getAdPrototypeRoomAccount(*_) >> accountList
        baiduCampaignDAO.countByAdAccountId(*_) >> 10
        baiduCampaignDAO.scanByAdAccountId(*_) >> baiduCampaignEntityList
        baiduCampaignDataDAO.batchInsert(*_) >> 1
        baiduCampaignDAO.getAllNameByAdAccountId(*_) >> null
        redisManager.getPrimaryId() >> "1"
        baiduCampaignDAO.batchAddCampaign(*_) >> 1
        refreshDataManager.batchSyncCampaignToMarketingEventObj(*_) >> { printf "dd" }
        adKeywordDAO.batchInsert(*_) >> 1
        refreshDataManager.syncAdKeywordToMarketingKeywordObj(*_) >> { printf "dd" }
        refreshDataManager.syncKeywordServingPlanV2(*_) >> { printf "dd" }
        when:
        baiduAdMarketingManager.refreshPrototypeRoomAccountData("ea", 1000000)
        then:
        noExceptionThrown()
        where:
        accountList                     | baiduCampaignEntityList
        []                              | []
        [new AdAccountEntity(id: "id")] | [new BaiduCampaignEntity(id: "id")]
    }


    @Unroll
    def "refreshMarketingEventOOCPCLaunchByEa"() {
        given:
        adAccountManager.queryAccountByEaAndSource(*_) >> accountList
        def spy = Spy(baiduAdMarketingManager)
        spy.refreshMarketingEventOOCPCLaunchByAccountId(*_) >> { printf "ddd" }
        when:
        spy.refreshMarketingEventOOCPCLaunchByEa("ea")
        then:
        noExceptionThrown()
        where:
        accountList << [[], [new AdAccountEntity()]]
    }


    @Unroll
    def "refreshAdGroupData"() {
        given:
        adGroupDAO.count(*_) >> adGroupCount
        adTokenManager.getBaiduAccessToken(*_) >> "token"
        reportApiManager.getBaiduReportData(*_) >> reportDataResultList
        baiduAdGroupDataDAO.queryByAdGroupIdListAndActionDateList(*_) >> [new BaiduAdGroupDataEntity(adGroupId: 1L, actionDate: DateUtil.parse("2024-09-12" + " 00:00:00"))]
        baiduAdGroupDataDAO.batchInsert(*_) >> 1
        baiduAdGroupDataDAO.batchUpdateDataById(*_) >> 1
        adGroupDAO.queryByAdgroupIdList(*_) >> [new AdGroupEntity(adGroupId: 1L, marketingEventId: "id")]
        advertisingDetailsObjManager.tryUpdateOrCreateObj(*_) >> { printf "ddd" }
        when:
        baiduAdMarketingManager.refreshAdGroupData(new AdAccountEntity(), 1)
        then:
        noExceptionThrown()
        where:
        adGroupCount | reportDataResultList
        0            | null
        10           | []
        10           | [new GetReportDataResult()]
        10           | [new GetReportDataResult(totalRowCount: 501, rowList: [["adGroupId": 1D, "date": "2024-09-12", "impression": 1D, "cost": 2D, "click": 1D], ["adGroupId": 2D, "date": "2024-09-12", "impression": 1D, "cost": 2D, "click": 1D]])]
    }

    @Unroll
    def "refreshAdGroupFeedData"() {
        given:
        baiduAdGroupFeedDAO.count(*_) >> adGroupCount
        adTokenManager.getBaiduAccessToken(*_) >> "token"
        reportApiManager.getBaiduReportData(*_) >> reportDataResultList
        baiduAdGroupFeedDataDAO.queryByAdGroupFeedIdListAndActionDateList(*_) >> [new BaiduAdGroupFeedDataEntity(adGroupFeedId: 1L, actionDate: DateUtil.parse("2024-09-12" + " 00:00:00"))]
        baiduAdGroupFeedDataDAO.batchInsert(*_) >> 1
        baiduAdGroupFeedDataDAO.batchUpdateDataById(*_) >> 1
        adGroupDAO.queryByAdgroupIdList(*_) >> [new AdGroupEntity(adGroupId: 1L, marketingEventId: "id")]
        advertisingDetailsObjManager.tryUpdateOrCreateObj(*_) >> { printf "ddd" }
        when:
        baiduAdMarketingManager.refreshAdGroupFeedData(new AdAccountEntity(), 1)
        then:
        noExceptionThrown()
        where:
        adGroupCount | reportDataResultList
        0            | null
        10           | []
        10           | [new GetReportDataResult()]
        10           | [new GetReportDataResult(totalRowCount: 501, rowList: [["adGroupId": 1D, "date": "2024-09-12", "impression": 1D, "cost": 2D, "click": 1D], ["adGroupId": 2D, "date": "2024-09-12", "impression": 1D, "cost": 2D, "click": 1D]])]
    }

    @Unroll
    def "refreshProjectFeed"() {
        given:
        adTokenManager.getBaiduAccessToken(*_) >> "token"
        campaignApiManager.getProjectFeed(*_) >> rsult
        baiduProjectFeedDAO.getByProjectFeedIdList(*_) >> [new BaiduProjectFeedEntity(projectFeedId: 1L, projectFeedName: "old")]
        baiduProjectFeedDAO.batchInsert(*_) >> 1
        baiduProjectFeedDAO.batchUpdate(*_) >> { printf "ddd" }
        when:
        baiduAdMarketingManager.refreshProjectFeed(new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        rsult << [null, new RequestResult(data: [new GetProjectFeedResult(projectFeedId: 1L, projectFeedName: "new"), new GetProjectFeedResult(projectFeedId: 2L, projectFeedName: "new")], header: getHeader())]

    }

    @Unroll
    def "refreshCampaignFeed"() {
        given:
        adTokenManager.getBaiduAccessToken(*_) >> "token"
        campaignApiManager.getCampaignFeed(*_) >> rsult
        baiduCampaignFeedDAO.getByCampaignFeedIdList(*_) >> [new BaiduCampaignFeedEntity(campaignFeedId: 1L, campaignFeedName: "old")]
        baiduCampaignFeedDAO.batchInsert(*_) >> 1
        baiduCampaignFeedDAO.batchUpdate(*_) >> { printf "ddd" }
        when:
        baiduAdMarketingManager.refreshCampaignFeed(new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        rsult << [null, new RequestResult(data: [new GetCampaignFeedResult(campaignFeedId: 1L, campaignFeedName: "new"), new GetCampaignFeedResult(campaignFeedId: 2L, campaignFeedName: "new")], header: getHeader())]
    }

    @Unroll
    def "refreshAdGroupFeed"() {
        given:
        adTokenManager.getBaiduAccessToken(*_) >> "token"
        baiduCampaignFeedDAO.getAllCampaignFeedIdList(*_) >> allCampaignFeedIdList
        campaignApiManager.getAdgroupFeed(*_) >> result
        baiduAdGroupFeedDAO.getByAdGroupFeedIdList(*_) >> [new BaiduAdGroupFeedEntity(adgroupFeedId: 1L, adgroupFeedName: "old")]
        baiduAdGroupFeedDAO.batchInsert(*_) >> 1
        baiduAdGroupFeedDAO.batchUpdate(*_) >> { printf "ddd" }
        when:
        baiduAdMarketingManager.refreshAdGroupFeed(new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        allCampaignFeedIdList | result
        []                    | null
        [1L]                  | null
        [1L]                  | new RequestResult(data: [new GetAdGroupFeedResult(adgroupFeedId: 1L, adgroupFeedName: "new"), new GetAdGroupFeedResult(adgroupFeedId: 2L, adgroupFeedName: "new")], header: getHeader())
    }


    @Unroll
    def "refreshMarketingEventOOCPCLaunchByAccountId"() {
        given:
        baiduCampaignDAO.countByAdAccountId(*_) >> campaignTotalCount
        adTokenManager.getBaiduAccessToken(*_) >> accessToken
        baiduHttpManager.getOcpcTargetPackageList(*_) >> [new GetOcpcTargetPackageResult(scopeList: [new GetOcpcTargetPackageResult.TargetPackageBindInfo(levelId: 1L)])]
        baiduCampaignDAO.scanByAdAccountId(*_) >> baiduCampaignEntityList
        crmV2Manager.editObjectData(*_) >> null
        def objectData = new ObjectData();
        objectData.put("_id", "id")
        objectData.put("ocpc_launch", "no")
        crmMetadataManager.batchGetByIdsV3(*_) >> [objectData]
        when:
        baiduAdMarketingManager.refreshMarketingEventOOCPCLaunchByAccountId("ea", new AdAccountEntity())
        then:
        noExceptionThrown()
        where:
        campaignTotalCount | accessToken | baiduCampaignEntityList
        0                  | null        | []
        1                  | null        | []
        1                  | "token"     | []
        1                  | "token"     | [new BaiduCampaignEntity(id: "id", campaignId: 1L, marketingEventId: "id")]
    }

    ResultHeader getHeader() {
        ResultHeader header = new ResultHeader()
        header.setStatus(0)
        return header
    }


}
