package com.facishare.marketing.provider.manager.conference

import com.facishare.converter.EIEAConverter
import com.facishare.mankeep.api.outService.service.OutConferenceService
import com.facishare.marketing.api.arg.CreateObjectDataModel
import com.facishare.marketing.api.result.CrmFieldResult
import com.facishare.marketing.api.result.EnumDetailResult
import com.facishare.marketing.api.result.conference.QueryEnrollReviewResult
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult
import com.facishare.marketing.api.vo.conference.CreateOrUpdateConferenceVO
import com.facishare.marketing.api.vo.conference.DeleteParticipantVO
import com.facishare.marketing.api.vo.conference.QueryConferenceParticipantsVO
import com.facishare.marketing.common.contstant.ConferenceReplaceConstants
import com.facishare.marketing.common.contstant.ConferenceReplaceConstants.BlockDataEnum
import com.facishare.marketing.common.enums.ActivitySignOrEnrollEnum
import com.facishare.marketing.common.enums.ActivityStatusEnum
import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum
import com.facishare.marketing.common.enums.conference.*
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.*
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.*
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO
import com.facishare.marketing.provider.dao.manager.ConferenceDAOManager
import com.facishare.marketing.provider.dao.manager.CustomizeFormDataDAOManager
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO
import com.facishare.marketing.provider.dao.ticket.CustomizeTicketDAO
import com.facishare.marketing.provider.dao.ticket.WxTicketReceiveDAO
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO
import com.facishare.marketing.provider.dto.campaignMergeData.PageCampaignParticipantsDTO
import com.facishare.marketing.provider.dto.conference.ConferenceInvitationUserDTO
import com.facishare.marketing.provider.dto.conference.EnrollNoticeTaskDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import com.facishare.marketing.provider.entity.*
import com.facishare.marketing.provider.entity.conference.*
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity
import com.facishare.marketing.provider.entity.sms.ExtraSmsParamObject
import com.facishare.marketing.provider.innerResult.BatchShortUrlResult
import com.facishare.marketing.provider.innerResult.UserRelationPartnerInfo
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.ding.DingManager
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager
import com.facishare.marketing.provider.manager.image.ImageCreator
import com.facishare.marketing.provider.manager.image.ImageDrawer
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.qywx.QywxMiniAppMessageManager
import com.facishare.marketing.provider.manager.sms.mw.SmsParamManager
import com.facishare.marketing.provider.manager.user.UserRelationManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.rest.ShortUrlManager
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.result.ActionAddResult
import com.fxiaoke.crmrestapi.result.BulkDeleteResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.ObjectDataService
import com.google.common.collect.Maps
import com.facishare.marketing.common.typehandlers.value.FieldInfoList
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Method

/**
 * Test for ConferenceManager
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
class ConferenceManagerTest extends Specification {

    def conferenceManager = new ConferenceManager()

    // Mock dependencies
    def fileV2Manager = Mock(FileV2Manager)
    def photoManager = Mock(PhotoManager)
    def imageCreator = Mock(ImageCreator)
    def conferenceDAO = Mock(ConferenceDAO)
    def conferenceDAOManager = Mock(ConferenceDAOManager)
    def activityDAO = Mock(ActivityDAO)
    def conferenceInvitationUserDAO = Mock(ConferenceInvitationUserDAO)
    def userRelationManager = Mock(UserRelationManager)
    def safetyManagementManager = Mock(SafetyManagementManager)
    def eieaConverter = Mock(EIEAConverter)
    def metadataActionService = Mock(MetadataActionService)
    def marketingEventManager = Mock(MarketingEventManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def notificationSettingDAO = Mock(ConferenceNotificationSettingDAO)
    def smsParamManager = Mock(SmsParamManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def customizeTicketManager = Mock(CustomizeTicketManager)
    def shortUrlManager = Mock(ShortUrlManager)
    def outConferenceService = Mock(OutConferenceService)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def fileManager = Mock(FileManager)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def fsMessageManager = Mock(FsMessageManager)
    def noticeManager = Mock(NoticeManager)
    def inviteParticipantDAO = Mock(ConferenceInviteParticipantDAO)
    def conferenceUserGroupDAO = Mock(ConferenceUserGroupDAO)
    def redisManager = Mock(RedisManager)
    def objectDataService = Mock(ObjectDataService)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def activityManager = Mock(ActivityManager)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def spreadChannelManager = Mock(SpreadChannelManager)
    def conferenceSignInJumpSettingDAO = Mock(ConferenceSignInJumpSettingDAO)
    def conferenceSignInSuccessSettingDAO = Mock(ConferenceSignInSuccessSettingDAO)
    def objectManager = Mock(ObjectManager)
    def noticeDAO = Mock(NoticeDAO)
    def customizeTicketDAO = Mock(CustomizeTicketDAO)
    def wxTicketReceiveDAO = Mock(WxTicketReceiveDAO)
    def memberAccessibleCampaignDAO = Mock(MemberAccessibleCampaignDAO)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def hexagonTemplateSiteDAO = Mock(HexagonTemplateSiteDAO)
    def hexagonTemplatePageDAO = Mock(HexagonTemplatePageDAO)
    def hexagonSiteManager = Mock(HexagonSiteManager)
    def customizeFormDataDAO = Mock(CustomizeFormDataDAO)
    def customizeFormDataDAOManager = Mock(CustomizeFormDataDAOManager)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def memberManager = Mock(MemberManager)
    def memberConfigDao = Mock(MemberConfigDao)
    def qywxMiniAppMessageManager = Mock(QywxMiniAppMessageManager)
    def dingManager = Mock(DingManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def hexagonSiteDAOManager = Mock(HexagonSiteDAOManager)

    def setup() {
        conferenceManager.fileV2Manager = fileV2Manager
        conferenceManager.photoManager = photoManager
        conferenceManager.imageCreator = imageCreator
        conferenceManager.conferenceDAO = conferenceDAO
        conferenceManager.conferenceDAOManager = conferenceDAOManager
        conferenceManager.activityDAO = activityDAO
        conferenceManager.conferenceInvitationUserDAO = conferenceInvitationUserDAO
        conferenceManager.userRelationManager = userRelationManager
        conferenceManager.safetyManagementManager = safetyManagementManager
        conferenceManager.eieaConverter = eieaConverter
        conferenceManager.metadataActionService = metadataActionService
        conferenceManager.marketingEventManager = marketingEventManager
        conferenceManager.crmV2Manager = crmV2Manager
        conferenceManager.notificationSettingDAO = notificationSettingDAO
        conferenceManager.smsParamManager = smsParamManager
        conferenceManager.campaignMergeDataDAO = campaignMergeDataDAO
        conferenceManager.customizeTicketManager = customizeTicketManager
        conferenceManager.shortUrlManager = shortUrlManager
        conferenceManager.outConferenceService = outConferenceService
        conferenceManager.activityEnrollDataDAO = activityEnrollDataDAO
        conferenceManager.campaignMergeDataManager = campaignMergeDataManager
        conferenceManager.fileManager = fileManager
        conferenceManager.fsAddressBookManager = fsAddressBookManager
        conferenceManager.fsMessageManager = fsMessageManager
        conferenceManager.noticeManager = noticeManager
        conferenceManager.inviteParticipantDAO = inviteParticipantDAO
        conferenceManager.conferenceUserGroupDAO = conferenceUserGroupDAO
        conferenceManager.redisManager = redisManager
        conferenceManager.objectDataService = objectDataService
        conferenceManager.metadataControllerServiceManager = metadataControllerServiceManager
        conferenceManager.activityManager = activityManager
        conferenceManager.customizeFormDataManager = customizeFormDataManager
        conferenceManager.spreadChannelManager = spreadChannelManager
        conferenceManager.conferenceSignInJumpSettingDAO = conferenceSignInJumpSettingDAO
        conferenceManager.conferenceSignInSuccessSettingDAO = conferenceSignInSuccessSettingDAO
        conferenceManager.objectManager = objectManager
        conferenceManager.noticeDAO = noticeDAO
        conferenceManager.customizeTicketDAO = customizeTicketDAO
        conferenceManager.wxTicketReceiveDAO = wxTicketReceiveDAO
        conferenceManager.memberAccessibleCampaignDAO = memberAccessibleCampaignDAO
        conferenceManager.customizeFormDataUserDAO = customizeFormDataUserDAO
        conferenceManager.hexagonPageDAO = hexagonPageDAO
        conferenceManager.hexagonSiteDAO = hexagonSiteDAO
        conferenceManager.hexagonTemplateSiteDAO = hexagonTemplateSiteDAO
        conferenceManager.hexagonTemplatePageDAO = hexagonTemplatePageDAO
        conferenceManager.hexagonSiteManager = hexagonSiteManager
        conferenceManager.customizeFormDataDAO = customizeFormDataDAO
        conferenceManager.customizeFormDataDAOManager = customizeFormDataDAOManager
        conferenceManager.contentMarketingEventMaterialRelationDAO = contentMarketingEventMaterialRelationDAO
        conferenceManager.memberManager = memberManager
        conferenceManager.memberConfigDao = memberConfigDao
        conferenceManager.qywxMiniAppMessageManager = qywxMiniAppMessageManager
        conferenceManager.dingManager = dingManager
        conferenceManager.crmMetadataManager = crmMetadataManager
        conferenceManager.hexagonSiteDAOManager = hexagonSiteDAOManager
        conferenceManager.host = "http://test.com"
    }

    @Unroll
    def "createConferenceTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> existingEntity
        conferenceDAOManager.addConference(*_) >> "conferenceId"

        when:
        def result = conferenceManager.createConference(vo, marketingEventId)

        then:
        result != null
        if (existingEntity != null) {
            result.status == ActivityStatusEnum.UNPUBLISHED.getStatus()
            result.location == vo.location
        } else {
            result.id != null
            result.ea == vo.ea
            result.marketingEventId == marketingEventId
            result.title == vo.title
        }

        where:
        vo                    | marketingEventId    || existingEntity
        createTestVO()        | "marketingEventId1" || null
        createTestVO()        | "marketingEventId2" || new ActivityEntity(id: "existingId")
    }

    @Unroll
    def "addConferencePhotoTest"() {
        given:
        fileV2Manager.getApathByTApath(*_) >> taResult
        fileV2Manager.getApathByApath(*_) >> "apath"
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> { }
        imageCreator.getImageDrawer(*_) >> Mock(ImageDrawer) {
            draw(*_) >> "cardPhotoApath"
        }

        expect:
        conferenceManager.addConferencePhoto(conferenceId, path, ea, fsUserId) == expectedResult

        where:
        conferenceId | path      | ea   | fsUserId || expectedResult                                      || taResult
        "confId"     | "TA_test" | "ea" | 1        || Result.newSuccess()                                 || new FileV2Manager.FileManagerPicResult(urlAPath: "apath", thumbUrlApath: "thumb")
        "confId"     | "TA_test" | "ea" | 1        || Result.newError(SHErrorCode.CONFERENCE_UPDATE_IMAGE_FAIL) || null
        "confId"     | "A_test"  | "ea" | 1        || Result.newSuccess()                                 || null
        "confId"     | "C_test"  | "ea" | 1        || Result.newSuccess()                                 || null
    }

    @Unroll
    def "syncConferenceDataFromCrmTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        conferenceDAOManager.updateConferenceStatus(*_) >> { }
        conferenceDAOManager.addConference(*_) >> conferenceId
        conferenceDAOManager.updateConference(*_) >> { }
        crmV2Manager.getObjectFieldDescribesList(*_) >> []
        activityDAO.getById(*_) >> activityEntityAfterCreation
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> { }
        def spy = Spy(conferenceManager)
        spy.resetConferenceIndexPage(*_) >> { }
        spy.createDefaultConferenceSite(*_) >> { }

        when:
        spy.syncConferenceDataFromCrm(ea, fsUserId, marketingEventId, objectData, op)

        then:
        noExceptionThrown()

        where:
        ea        | fsUserId | marketingEventId    | objectData           | op        | conferenceId   || activityEntity                                                      || activityEntityAfterCreation
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "i"       | "conferenceId" || null                                                                || new ActivityEntity(id: "conferenceId", activityDetailSiteId: null)
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "i"       | "conferenceId" || null                                                                || new ActivityEntity(id: "conferenceId", activityDetailSiteId: "siteId")
        "sbtjt888"| 1        | "marketingEventId"  | createObjectDataWithSbtLocation() | "i" | "conferenceId" || null                                                                || new ActivityEntity(id: "conferenceId", activityDetailSiteId: "siteId")
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "i"       | null           || null                                                                || null
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "i"       | "conferenceId" || new ActivityEntity(id: "existingId")                                || new ActivityEntity(id: "conferenceId", activityDetailSiteId: "siteId")
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "u"       | null           || new ActivityEntity(id: "id", location: "location", title: "title") || null
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "d"       | null           || new ActivityEntity(id: "id")                                       || null
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "invalid" | null           || new ActivityEntity(id: "id")                                       || null
        "ea"      | 1        | "marketingEventId"  | new ObjectData()     | "other"   | null           || null                                                                || null
    }

    @Unroll
    def "addConferenceFromCrmLine677AfterTest"() {
        given:
        // 确保会议创建成功，以便执行677行后面的代码
        conferenceDAO.getConferenceByMarketingEventId(*_) >> null
        conferenceDAOManager.addConference(*_) >> "newConferenceId"
        crmV2Manager.getObjectFieldDescribesList(*_) >> []
        activityDAO.getById(*_) >> activityEntityAfterCreation
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> { }
        contentMarketingEventMaterialRelationDAO.save(*_) >> { }
        def spy = Spy(conferenceManager)
        spy.createDefaultConferenceSite(*_) >> { }
        // Mock异步线程池执行
        activityManager.createActivityQrCode(*_) >> { }
        activityManager.createActivitySignInQrCode(*_) >> { }

        when:
        spy.syncConferenceDataFromCrm(ea, 1, "marketingEventId", objectData, "i")

        then:
        noExceptionThrown()
        // 验证关键方法被调用
        1 * spy.createDefaultConferenceSite("newConferenceId")
        1 * contentMarketingEventMaterialRelationDAO.save(_)
        if (activityEntityAfterCreation?.activityDetailSiteId) {
            1 * crmV2Manager.updateMarketingEvenObjLandingPage(ea, "marketingEventId", 1, _)
        }

        where:
        ea        | objectData                           || activityEntityAfterCreation
        "ea"      | new ObjectData()                     || new ActivityEntity(id: "newConferenceId", activityDetailSiteId: null)
        "ea"      | new ObjectData()                     || new ActivityEntity(id: "newConferenceId", activityDetailSiteId: "siteId")
        "sbtjt888"| createObjectDataWithSbtLocation()    || new ActivityEntity(id: "newConferenceId", activityDetailSiteId: "siteId")
    }

    @Unroll
    def "updateConferenceToCrmMarketingEventTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        fileV2Manager.getNpathByApath(*_) >> npathResult
        metadataActionService.edit(*_) >> new com.fxiaoke.crmrestapi.common.result.Result()

        when:
        def result
        def exceptionThrown = false
        try {
            result = conferenceManager.updateConferenceToCrmMarketingEvent(ea, fsUserId, marketingEventId, vo)
        } catch (NullPointerException e) {
            exceptionThrown = true
        }

        then:
        if (shouldThrowException) {
            exceptionThrown == true
        } else {
            exceptionThrown == false
            result != null
        }

        where:
        ea        | fsUserId | marketingEventId    | vo                        | npathResult || shouldThrowException
        "ea"      | 1        | "marketingEventId"  | createTestVO()            | "npath"     || false
        "ea"      | 1        | "marketingEventId"  | createTestVO()            | null        || false
        "sbtjt888"| 1        | "marketingEventId"  | createTestVO()            | "npath"     || false
        "ea"      | 1        | "marketingEventId"  | createTestVOWithNullCoverImage() | null || false
        "ea"      | 1        | "marketingEventId"  | createTestVOWithNullCreateObjectDataModel() | null || true
    }

    @Unroll
    def "getMarketingEventTypeDescTest"() {
        given:
        def crmFieldResult = new CrmFieldResult()
        crmFieldResult.enumDetails = [
            new EnumDetailResult(itemCode: "type1", itemName: "Type 1"),
            new EnumDetailResult(itemCode: "type2", itemName: "Type 2")
        ]
        def crmFieldResultOptional = crmFieldResultPresent ? Optional.of(crmFieldResult) : Optional.empty()

        when:
        def result = conferenceManager.getMarketingEventTypeDesc(crmFieldResultOptional, eventType)

        then:
        result == expectedResult

        where:
        crmFieldResultPresent | eventType || expectedResult
        true                  | "type1"   || "Type 1"
        true                  | "type2"   || "Type 2"
        true                  | "type3"   || null
        false                 | "type1"   || null
    }

    @Unroll
    def "sendConferenceNotificationTest"() {
        given:
        def activityEntity = new ActivityEntity(ea: "test")
        def notificationSettings = new ActivityNotificationSettings()
        notificationSettings.smsType = ConferenceSmsType.FS.getType()
        notificationSettings.enrollSuccess = true
        notificationSettings.enrollSuccessTemplateId = "template123"
        def notificationSettingEntity = new ConferenceNotificationSettingEntity()
        notificationSettingEntity.activityNotificationSettings = notificationSettings

        notificationSettingDAO.getNotificationSettingByEa(*_) >> notificationSettingEntity
        smsParamManager.buildParamValueMap(*_) >> []

        when:
        conferenceManager.sendConferenceNotification(notificationType, phoneList, activityEntity, extraSmsParamObjectMap)

        then:
        noExceptionThrown()

        where:
        notificationType                                      | phoneList      | extraSmsParamObjectMap
        ConferenceNotificationTypeEnum.ENROLL_SUCCESS.getType() | ["123456789"] | Maps.newHashMap()
        ConferenceNotificationTypeEnum.ENROLL_SUCCESS.getType() | []            | null
        ConferenceNotificationTypeEnum.ENROLL_SUCCESS.getType() | ["123456789"] | ["123456789": new ExtraSmsParamObject()]
    }

    private CreateOrUpdateConferenceVO createTestVO() {
        def vo = new CreateOrUpdateConferenceVO()
        vo.ea = "test"
        vo.title = "title"
        vo.location = "location"
        vo.fsUserId = 1
        vo.startTime = 1000L
        vo.endTime = 2000L
        vo.conferenceDetails = "details"
        vo.showActivityList = true
        vo.mapAddress = "address"
        vo.mapLocation = new MapLocation()
        vo.coverImagePath = "test/path"
        vo.createObjectDataModel = new CreateObjectDataModel.Arg(objectData: new ObjectData(tenantId: 1000))
        return vo
    }

    private CreateOrUpdateConferenceVO createTestVOWithNullCoverImage() {
        def vo = createTestVO()
        vo.coverImagePath = null
        return vo
    }

    private CreateOrUpdateConferenceVO createTestVOWithNullCreateObjectDataModel() {
        def vo = createTestVO()
        vo.createObjectDataModel = null
        return vo
    }

    private ObjectData createObjectDataWithSbtLocation() {
        def objectData = new ObjectData()
        objectData.put("name", "Test Conference")
        objectData.put("begin_time", System.currentTimeMillis())
        objectData.put("end_time", System.currentTimeMillis() + 3600000)
        objectData.put("location", "Regular Location")
        objectData.put("sbt_marketing_even_location", "SBT Special Location")
        objectData.put("event_type", "type1")
        return objectData
    }

    @Unroll
    def "createConferenceTicketAndAttachedInfoTest"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        activityManager.getActivityIdByObject(*_) >> activityId
        conferenceDAO.getConferenceByMarketingEventId(*_) >> conferenceByMarketingEvent
        def spy = Spy(conferenceManager)
        spy.addActivityEnrollData(*_) >> { }
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> { }

        when:
        spy.createConferenceTicketAndAttachedInfo(ea, objectType, objectId, spreadFsUserId, enrollSourceType, campaignMergeDataId, marketingEventId)

        then:
        noExceptionThrown()

        where:
        ea   | objectType                                    | objectId   | spreadFsUserId | enrollSourceType | campaignMergeDataId | marketingEventId | activityEntity                                                    | activityId   | conferenceByMarketingEvent
        "ea" | ObjectTypeEnum.ACTIVITY.getType()            | "actId"    | 1              | 1                | "campId"            | "mktId"          | null                                                              | null         | null
        "ea" | ObjectTypeEnum.ACTIVITY.getType()            | "actId"    | 1              | 1                | "campId"            | "mktId"          | new ActivityEntity(id: "actId", enrollReview: false)             | null         | null
        "ea" | ObjectTypeEnum.ACTIVITY.getType()            | "actId"    | 1              | 1                | "campId"            | "mktId"          | new ActivityEntity(id: "actId", enrollReview: true)              | null         | null
        "ea" | ObjectTypeEnum.ACTIVITY_INVITATION.getType() | "invId"    | 1              | 1                | "campId"            | "mktId"          | null                                                              | null         | null
        "ea" | ObjectTypeEnum.ACTIVITY_INVITATION.getType() | "invId"    | 1              | 1                | "campId"            | "mktId"          | new ActivityEntity(id: "actId", enrollReview: false)             | "actId"      | null
        "ea" | ObjectTypeEnum.ACTIVITY_INVITATION.getType() | "invId"    | 1              | 1                | "campId"            | "mktId"          | new ActivityEntity(id: "actId", enrollReview: true)              | "actId"      | null
        "ea" | 999                                           | "otherId"  | 1              | 1                | "campId"            | "mktId"          | null                                                              | null         | null
        "ea" | 999                                           | "otherId"  | 1              | 1                | "campId"            | "mktId"          | null                                                              | null         | new ActivityEntity(id: "confId", enrollReview: false)
        "ea" | 999                                           | "otherId"  | 1              | 1                | "campId"            | "mktId"          | null                                                              | null         | new ActivityEntity(id: "confId", enrollReview: true)
        "ea" | 999                                           | "otherId"  | 1              | 1                | "campId"            | ""               | null                                                              | null         | null
        "ea" | 999                                           | "otherId"  | 1              | 1                | "campId"            | null             | null                                                              | null         | null
    }

    @Unroll
    def "createConferenceTicketAndAttachedInfoWithCustomizeFormDataUserEntityTest"() {
        given:
        def spy = Spy(conferenceManager)
        spy.createConferenceEnrollAttachedInfo(*_) >> { }
        activityManager.getActivityIdByObject(*_) >> activityId
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> { }

        when:
        spy.createConferenceTicketAndAttachedInfo(ea, customizeFormDataUserEntity, qrSourceType, qrSourceId, campaignMergeDataId)

        then:
        noExceptionThrown()

        where:
        ea   | customizeFormDataUserEntity                                                                                    | qrSourceType | qrSourceId | campaignMergeDataId | activityId
        "ea" | null                                                                                                           | 1            | "qrId"     | null                | null
        "ea" | null                                                                                                           | 1            | "qrId"     | ""                  | null
        "ea" | createCustomizeFormDataUserEntity()                                                                           | 1            | "qrId"     | "campId"            | "actId"
        "ea" | new CustomizeFormDataUserEntity(objectId: "objId", objectType: ObjectTypeEnum.ACTIVITY.getType(), marketingEventId: "mktId") | 1 | "qrId" | "campId" | "actId"
    }

    private CustomizeFormDataUserEntity createCustomizeFormDataUserEntity() {
        def entity = new CustomizeFormDataUserEntity()
        entity.objectId = "objId"
        entity.objectType = ObjectTypeEnum.ACTIVITY.getType()
        entity.marketingEventId = "mktId"
        entity.spreadFsUid = 1
        return entity
    }

    @Unroll
    def "createConferenceTicketAndAttachedInfoNoActivityObjTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        def spy = Spy(conferenceManager)
        spy.createConferenceEnrollAttachedInfo(*_) >> { }
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> { }

        when:
        spy.createConferenceTicketAndAttachedInfoNoActivityObj(ea, customizeFormDataUserEntity, campaignMergeDataId)

        then:
        noExceptionThrown()

        where:
        ea   | customizeFormDataUserEntity                                                                                    | campaignMergeDataId | activityEntity
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: null)                                                       | "campId"            | null
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: "")                                                         | "campId"            | null
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: "mktId")                                                    | null                | null
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: "mktId")                                                    | ""                  | null
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: "mktId")                                                    | "campId"            | null
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: "mktId")                                                    | "campId"            | new ActivityEntity(id: "actId", enrollReview: false)
        "ea" | new CustomizeFormDataUserEntity(marketingEventId: "mktId")                                                    | "campId"            | new ActivityEntity(id: "actId", enrollReview: true)
    }

    @Unroll
    def "createConferenceEnrollAttachedInfoByCampaignObjTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        def spy = Spy(conferenceManager)
        spy.addActivityEnrollData(*_) >> { }
        campaignMergeDataManager.updateCampaignMergeDataInviteStatus(*_) >> { }
        campaignMergeDataManager.updateCampaignMembersObjByCampaignMergeId(*_) >> { }

        when:
        spy.createConferenceEnrollAttachedInfoByCampaignObj(marketingEventId, campaignMergeDataId, memberType, ea)

        then:
        noExceptionThrown()

        where:
        marketingEventId | campaignMergeDataId | memberType                                                    | ea   | activityEntity
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue()   | "ea" | null
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue()   | "ea" | new ActivityEntity(id: "actId", enrollReview: false)
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue()   | "ea" | new ActivityEntity(id: "actId", enrollReview: true)
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.OTHER.getValue()         | "ea" | new ActivityEntity(id: "actId", enrollReview: false)
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.REVIEW.getValue()          | "ea" | new ActivityEntity(id: "actId", enrollReview: true)
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.PARTICIPATE.getValue()     | "ea" | new ActivityEntity(id: "actId", enrollReview: false)
        "mktId"          | "campId"            | CampaignMembersObjMemberStatusEnum.PARTICIPATE.getValue()     | "ea" | new ActivityEntity(id: "actId", enrollReview: true)
        "mktId"          | "campId"            | "OTHER_STATUS"                                                | "ea" | new ActivityEntity(id: "actId", enrollReview: false)
    }

    @Unroll
    def "generateExcelParticipantsListDirectTest"() {
        given:
        // 使用反射直接测试private方法
        def method = ConferenceManager.class.getDeclaredMethod("generateExcelParticipantsList",
            String.class, String.class, String.class, List.class, CustomizeFormDataEntity.class)
        method.setAccessible(true)

        // Mock所有依赖
        customizeFormDataManager.conversionEnrollDataPic(*_) >> [:]
        customizeFormDataManager.conversionEnrollDataFileAttachment(*_) >> fileAttachmentMap
        spreadChannelManager.queryChannelMapData(*_) >> ["channel1": "Channel Label"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "Channel Label"
        safetyManagementManager.turnOnPhoneNumberSensitive(*_) >> phoneNumberSensitive
        safetyManagementManager.phoneNumberSensitive(*_) >> { }
        customizeFormDataManager.buildAreaInfoByEnrollData(*_) >> { }
        customizeFormDataManager.formatEnrollDataIncludeSpecialField(*_) >> formatResult
        objectManager.getObjectName(*_) >> "Test Object"
        def spy = Spy(conferenceManager)
        spy.queryCampaignMembersObjByFilter(*_) >> objectDataList
        spy.queryCampaignMembersObjBusinessOwnerMap(*_) >> ownerNameMap
        conferenceInvitationUserDAO.queryLatestConferenceInvitationUserByCampaignMergeDataId(*_) >> invitationUserList
        fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(*_) >> fsEmployeeMap
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> fsEmployeeMap
        userRelationManager.getPartnerInfoByFsUserIdList(*_) >> partnerInfoMap
        campaignMergeDataManager.getLatestActivityEnrollDataByCampaignId(*_) >> enrollMap
        spy.getGroupNameByIdsStr(*_) >> groupNames

        when:
        def result = method.invoke(spy, conferenceId, marketingEventId, ea, campaignParticipantsList, customizeFormDataEntity)

        then:
        result != null
        result instanceof List
        result.size() == expectedSize

        where:
        conferenceId | marketingEventId | ea   | campaignParticipantsList                          | customizeFormDataEntity           | objectDataList              | ownerNameMap              | invitationUserList                     | fsEmployeeMap                    | partnerInfoMap                          | enrollMap                                            | phoneNumberSensitive | groupNames           | formatResult      | fileAttachmentMap || expectedSize
        "confId"     | "mktId"          | "ea" | []                                                | null                              | []                          | [:]                       | []                                     | [:]                              | [:]                                     | [:]                                                  | false                | []                   | "formatted"       | [:]               || 0
        "confId"     | "mktId"          | "ea" | [createPageCampaignParticipantsDTOBasic()]        | null                              | [createObjectDataWithName()]| ["objId1": "Owner Name"]  | []                                     | [:]                              | [:]                                     | [:]                                                  | false                | ["Group1"]           | "formatted"       | [:]               || 1
        "confId"     | "mktId"          | "ea" | [createPageCampaignParticipantsDTOWithData()]     | createCustomizeFormDataEntity()   | [createObjectDataWithName()]| ["objId1": "Owner Name"]  | [createConferenceInvitationUserDTO()]  | [1: createFSEmployeeMsg()]       | [:]                                     | ["campaignId1": createCustomizeFormDataUserEntityWithContent()] | false                | ["Group1", "Group2"] | "formatted"       | [:]               || 1
        "confId"     | "mktId"          | "ea" | [createPageCampaignParticipantsDTOWithPartner()]  | createCustomizeFormDataEntity()   | []                          | [:]                       | []                                     | [999999: createFSEmployeeMsg()]  | [999999: createUserRelationPartnerInfo()]          | [:]                                                  | true                 | []                   | "formatted"       | [:]               || 1
        "confId"     | "mktId"          | "ea" | [createPageCampaignParticipantsDTOWithFileAttachment()] | createCustomizeFormDataEntityWithFileField() | [] | [:] | [] | [:] | [:] | ["campaignId3": createCustomizeFormDataUserEntityWithFileAttachment()] | false | [] | createFileAttachmentList() | ["file1.pdf": "http://example.com/file1.pdf"] || 1
    }

    @Unroll
    def "addConferenceFromCrmRetryLogicTest"() {
        given:
        // 模拟重试逻辑：前几次返回存在的会议，最后一次返回null
        def callCount = 0
        conferenceDAO.getConferenceByMarketingEventId(*_) >> {
            callCount++
            return callCount <= retryCount ? existingConference : null
        }
        conferenceDAOManager.addConference(*_) >> "newConferenceId"
        crmV2Manager.getObjectFieldDescribesList(*_) >> []
        activityDAO.getById(*_) >> new ActivityEntity(id: "newConferenceId", activityDetailSiteId: "siteId")
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> { }
        def spy = Spy(conferenceManager)
        spy.createDefaultConferenceSite(*_) >> { }

        when:
        spy.syncConferenceDataFromCrm("ea", 1, "marketingEventId", new ObjectData(), "i")

        then:
        noExceptionThrown()

        where:
        retryCount | existingConference
        0          | null  // 立即创建新会议
        3          | new ActivityEntity(id: "existingId")  // 重试3次后创建新会议
        10         | new ActivityEntity(id: "existingId")  // 重试10次后直接返回（达到最大重试次数）
    }

    @Unroll
    def "addConferenceFromCrmLine678AfterCoverageTest"() {
        given:
        // 专门测试678行后面的代码覆盖
        conferenceDAO.getConferenceByMarketingEventId(*_) >> null  // 确保会创建新会议
        conferenceDAOManager.addConference(*_) >> "newConferenceId"  // 确保返回有效ID
        crmV2Manager.getObjectFieldDescribesList(*_) >> []
        activityDAO.getById(*_) >> activityEntityAfterCreation  // 678行调用
        crmV2Manager.updateMarketingEvenObjLandingPage(*_) >> { }  // 682行调用
        contentMarketingEventMaterialRelationDAO.save(*_) >> { }  // 692行调用
        def spy = Spy(conferenceManager)
        spy.createDefaultConferenceSite(*_) >> { }  // 677行调用
        // Mock异步线程池执行 - 694-698行
        activityManager.createActivityQrCode(*_) >> { }
        activityManager.createActivitySignInQrCode(*_) >> { }

        when:
        spy.syncConferenceDataFromCrm("ea", 1, "marketingEventId", new ObjectData(), "i")

        then:
        noExceptionThrown()
        // 验证678行后面的关键方法被调用
        1 * spy.createDefaultConferenceSite("newConferenceId")  // 677行
        1 * activityDAO.getById("newConferenceId")  // 678行
        1 * contentMarketingEventMaterialRelationDAO.save(_)  // 692行
        // 根据activityDetailSiteId是否存在决定是否调用682行
        if (activityEntityAfterCreation?.activityDetailSiteId) {
            1 * crmV2Manager.updateMarketingEvenObjLandingPage("ea", "marketingEventId", 1, _)  // 682行
        } else {
            0 * crmV2Manager.updateMarketingEvenObjLandingPage(*_)
        }

        where:
        activityEntityAfterCreation << [
            new ActivityEntity(id: "newConferenceId", activityDetailSiteId: null),  // 678行后，679行条件为false
            new ActivityEntity(id: "newConferenceId", activityDetailSiteId: ""),    // 678行后，679行条件为false
            new ActivityEntity(id: "newConferenceId", activityDetailSiteId: "siteId123")  // 678行后，679行条件为true，执行680-683行
        ]
    }

    @Unroll
    def "sendNotificationByEnrollIdsTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        campaignMergeDataDAO.getCampaignMergeDataByConferenceEnrollIdsAndReviewStatus(*_) >> campaignMergeDataList
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 123456L
        shortUrlManager.batchCreateShortUrl(*_) >> createShortUrlResult()
        campaignMergeDataDAO.queryLatestFormDataUserByActivityEnrollId(*_) >> []
        def spy = Spy(conferenceManager)
        spy.sendConferenceNotification(*_) >> { }

        when:
        spy.sendNotificationByEnrollIds(activityId, conferenceEnrollIds, reviewStatus, reviewFailedMsg)

        then:
        noExceptionThrown()

        where:
        activityId | conferenceEnrollIds | reviewStatus | reviewFailedMsg || activityEntity                                    || campaignMergeDataList
        null       | ["id1"]             | 1            | "failed"        || null                                              || []
        "actId"    | []                  | 1            | "failed"        || new ActivityEntity(id: "actId", ea: "test")       || []
        "actId"    | ["id1"]             | null         | "failed"        || new ActivityEntity(id: "actId", ea: "test")       || []
        "actId"    | ["id1"]             | 1            | "failed"        || null                                              || []
        "actId"    | ["id1"]             | 1            | "failed"        || new ActivityEntity(id: "actId", ea: "test")       || []
        "actId"    | ["id1"]             | 1            | "failed"        || new ActivityEntity(id: "actId", ea: "test")       || [createCampaignMergeData()]
        "actId"    | ["id1"]             | 2            | "failed"        || new ActivityEntity(id: "actId", ea: "test")       || [createCampaignMergeData()]
    }

    @Unroll
    def "buildTicketParamTest"() {
        given:
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> ticketCode
        smsParamManager.getShortUrl(*_) >> "http://short.url"

        when:
        def extraSmsParamObjectMap = Maps.newHashMap()
        conferenceManager.buildTicketParam(activityId, ea, campaignMergeDataEntity, extraSmsParamObjectMap)

        then:
        if (campaignMergeDataEntity.phone) {
            assert extraSmsParamObjectMap.size() == 1
            assert extraSmsParamObjectMap.containsKey(campaignMergeDataEntity.phone)
        } else {
            assert extraSmsParamObjectMap.size() == 0
        }

        where:
        activityId | ea   | campaignMergeDataEntity                                      || ticketCode
        "actId"    | "ea" | new CampaignMergeDataEntity(id: "id1", phone: "123456789")   || 123456L
        "actId"    | "ea" | new CampaignMergeDataEntity(id: "id1", phone: "123456789")   || null
        "actId"    | "ea" | new CampaignMergeDataEntity(id: "id1", phone: null)          || 123456L
    }

    private CampaignMergeDataEntity createCampaignMergeData() {
        def entity = new CampaignMergeDataEntity()
        entity.id = "campaignId1"
        entity.phone = "123456789"
        entity.ea = "test"
        return entity
    }

    private def createShortUrlResult() {
        return Mock(BatchShortUrlResult) {
            getShortUrlMapping() >> ["http://long.url": "http://short.url"]
        }
    }

    @Unroll
    def "getEnrollParamToWechatServiceTemplateTest"() {
        given:
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserId(*_) >> enrollDataEntity
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> campaignMergeDataList
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 123456L

        when:
        def result = conferenceManager.getEnrollParamToWechatServiceTemplate(ea, campaignIds, marketingEventId)

        then:
        result.isPresent() == expectedPresent

        where:
        ea   | campaignIds | marketingEventId    | enrollDataEntity                                    | campaignMergeDataList           | activityEntity                                    || expectedPresent
        ""   | ["id1"]     | "marketingEventId"  | new ActivityEnrollDataEntity(activityId: "actId")  | [createCampaignMergeData()]     | new ActivityEntity(id: "actId", ea: "test")       || false
        "ea" | []          | "marketingEventId"  | new ActivityEnrollDataEntity(activityId: "actId")  | [createCampaignMergeData()]     | new ActivityEntity(id: "actId", ea: "test")       || false
        "ea" | ["id1"]     | "marketingEventId"  | null                                                | [createCampaignMergeData()]     | new ActivityEntity(id: "actId", ea: "test")       || false
        "ea" | ["id1"]     | "marketingEventId"  | new ActivityEnrollDataEntity(activityId: "actId")  | []                              | new ActivityEntity(id: "actId", ea: "test")       || false
        "ea" | ["id1"]     | "marketingEventId"  | new ActivityEnrollDataEntity(activityId: "actId")  | [createCampaignMergeData()]     | new ActivityEntity(id: "actId", ea: "test")       || true
        "ea" | ["id1"]     | ""                  | new ActivityEnrollDataEntity(activityId: "actId")  | [createCampaignMergeData()]     | null                                              || true
    }

    @Unroll
    def "buildPhoneContentEnrollListTest"() {
        given:
        activityEnrollDataDAO.getEntityById(*_) >> enrollDataEntity
        conferenceDAO.getConferenceById(*_) >> activityEntity
        campaignMergeDataManager.activityEnrollIdToCampaignId(*_) >> campaignMergeDataIds
        crmV2Manager.getAllObjectFieldDescribesList(*_) >> []
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> campaignMergeDataList
        crmV2Manager.getList(*_) >> createPageData()
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 123456L
        shortUrlManager.batchCreateShortUrl(*_) >> createShortUrlResult()
        smsParamManager.buildParamValueMap(*_) >> [new PhoneContentResult("123456789", Maps.newHashMap())]

        when:
        def result = conferenceManager.buildPhoneContentEnrollList(conferenceEnrollIds, isExistsVars)

        then:
        result != null
        result.size() >= 0

        where:
        conferenceEnrollIds | isExistsVars | enrollDataEntity                                    | activityEntity                                    | campaignMergeDataIds | campaignMergeDataList
        []                  | true         | null                                                | null                                              | []                   | []
        ["id1"]             | true         | null                                                | null                                              | []                   | []
        ["id1"]             | true         | new ActivityEnrollDataEntity(activityId: "actId")  | null                                              | []                   | []
        ["id1"]             | true         | new ActivityEnrollDataEntity(activityId: "actId")  | new ActivityEntity(id: "actId", ea: "test")       | []                   | []
        ["id1"]             | true         | new ActivityEnrollDataEntity(activityId: "actId")  | new ActivityEntity(id: "actId", ea: "test")       | ["campaignId1"]      | [createCampaignMergeData()]
        ["id1"]             | false        | new ActivityEnrollDataEntity(activityId: "actId")  | new ActivityEntity(id: "actId", ea: "test")       | ["campaignId1"]      | [createCampaignMergeData()]
    }

    private def createPageData() {
        return Mock(Page) {
            getDataList() >> [new ObjectData()]
        }
    }

    @Unroll
    def "getConferencePathTest"() {
        given:
        def file = Mock(File) {
            delete() >> true
        }
        fileManager.savaFile(*_) >> file
        fileManager.file2Bytes(*_) >> "test content".bytes
        fileManager.uploadFileToAWarehouse(*_) >> "uploaded/path"

        when:
        def result = conferenceManager.getConferencePath(content)

        then:
        result == "uploaded/path"

        where:
        content << ["test content", "", "long content with special chars 中文"]
    }

    @Unroll
    def "getConferenceDetailsByPathTest"() {
        given:
        fileV2Manager.downloadAFile(*_) >> fileBytes

        when:
        def result = conferenceManager.getConferenceDetailsByPath(path)

        then:
        result == expectedResult

        where:
        path        | fileBytes                || expectedResult
        "test/path" | "test content".bytes     || "test content"
        "test/path" | null                     || ""
        "test/path" | "".bytes                 || ""
    }

    @Unroll
    def "buildExportParticipantsDataTest"() {
        given:
        def vo = createQueryConferenceParticipantsVO()
        conferenceDAO.getConferenceById(*_) >> activityEntity
        customizeFormDataManager.getBindFormDataByObject(*_) >> customizeFormDataEntity
        objectManager.getObjectName(*_) >> "Test Conference"
        campaignMergeDataDAO.queryCampaignParticipantsData(*_) >> campaignParticipantsList
        // Mock generateExcelParticipantsList方法的所有依赖，让它真正执行
        def spy = Spy(conferenceManager)
        spy.queryCampaignMembersObjByFilter(*_) >> objectDataList
        spy.queryCampaignMembersObjBusinessOwnerMap(*_) >> ownerNameMap
        conferenceInvitationUserDAO.queryLatestConferenceInvitationUserByCampaignMergeDataId(*_) >> invitationUserList
        fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(*_) >> fsEmployeeMap
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> fsEmployeeMap
        userRelationManager.getPartnerInfoByFsUserIdList(*_) >> partnerInfoMap
        campaignMergeDataManager.getLatestActivityEnrollDataByCampaignId(*_) >> enrollMap
        customizeFormDataManager.conversionEnrollDataPic(*_) >> [:]
        customizeFormDataManager.conversionEnrollDataFileAttachment(*_) >> [:]
        spreadChannelManager.queryChannelMapData(*_) >> ["channel1": "Channel Label"]
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "Channel Label"
        safetyManagementManager.turnOnPhoneNumberSensitive(*_) >> phoneNumberSensitive
        spy.getGroupNameByIdsStr(*_) >> groupNames
        customizeFormDataManager.buildAreaInfoByEnrollData(*_) >> { }
        customizeFormDataManager.formatEnrollDataIncludeSpecialField(*_) >> "formatted data"
        safetyManagementManager.phoneNumberSensitive(*_) >> { }

        when:
        def result = spy.buildExportParticipantsData(vo)

        then:
        result != null
        result.fileName == expectedFileName
        if (campaignParticipantsList != null && !campaignParticipantsList.isEmpty()) {
            result.enrollInfoList != null
            result.enrollInfoList != null && result.enrollInfoList.size() >= 0
        }

        where:
        activityEntity                                    | customizeFormDataEntity           | campaignParticipantsList                    | objectDataList                                      | ownerNameMap              | invitationUserList                                    | fsEmployeeMap                                    | partnerInfoMap                                          | enrollMap                                                    | phoneNumberSensitive | groupNames           || expectedFileName
        new ActivityEntity(id: "actId", ea: "test", marketingEventId: "mktId") | null | [] | [] | [:] | [] | [:] | [:] | [:] | false | [] || "Test Conference"
        new ActivityEntity(id: "actId", ea: "test", marketingEventId: "mktId") | createCustomizeFormDataEntity() | [createPageCampaignParticipantsDTOBasic()] | [createObjectDataWithName()] | ["objId1": "Owner Name"] | [] | [:] | [:] | [:] | false | ["Group1"] || "Test Conference"
        new ActivityEntity(id: "actId", ea: "test", marketingEventId: "mktId") | createCustomizeFormDataEntity() | [createPageCampaignParticipantsDTOWithData()] | [createObjectDataWithName()] | ["objId1": "Owner Name"] | [createConferenceInvitationUserDTO()] | [1: createFSEmployeeMsg()] | [:] | ["campaignId1": createCustomizeFormDataUserEntityWithContent()] | false | ["Group1", "Group2"] || "Test Conference"
        new ActivityEntity(id: "actId", ea: "test", marketingEventId: "mktId") | createCustomizeFormDataEntity() | [createPageCampaignParticipantsDTOWithPartner()] | [] | [:] | [] | [999999: createFSEmployeeMsg()] | [999999: createUserRelationPartnerInfo()] | [:] | true | [] || "Test Conference"
        new ActivityEntity(id: "actId", ea: "test", marketingEventId: "mktId") | createCustomizeFormDataEntity() | [createPageCampaignParticipantsDTOWithFileAttachment()] | [] | [:] | [] | [:] | [:] | ["campaignId3": createCustomizeFormDataUserEntityWithFileAttachment()] | false | [] || "Test Conference"
        null                                              | null                              | []                                          | []                                                  | [:]                       | []                                                    | [:]                                              | [:]                                                     | [:]                                                          | false                | []                   || null
    }

    private QueryConferenceParticipantsVO createQueryConferenceParticipantsVO() {
        def vo = new QueryConferenceParticipantsVO()
        vo.conferenceId = "confId"
        vo.ea = "test"
        vo.groupUserId = ["user1", "user2"]
        return vo
    }

    @Unroll
    def "sendReviewMessageTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        activityEnrollDataDAO.getLatestEnrollByConferenceId(*_) >> enrollDataEntity
        fsAddressBookManager.getEmployeeIdsByEa(*_) >> [1, 2, 3]
        fsAddressBookManager.getEmployeeIdsByCircleIds(*_) >> [4, 5, 6]
        fsAddressBookManager.getEmployeeInfo(*_) >> createFSEmployeeMsg()
        noticeDAO.addNotice(*_) >> { }
        noticeManager.urlDispatcher(*_) >> "{}"
        fsMessageManager.doSendFXMessage(*_) >> { }

        when:
        def result = conferenceManager.sendReviewMessage(conferenceId)

        then:
        result == expectedResult

        where:
        conferenceId | activityEntity                                                                                    | enrollDataEntity                                    || expectedResult
        "confId"     | null                                                                                              | null                                                || false
        "confId"     | new ActivityEntity(id: "confId", enrollReview: false)                                             | null                                                || true
        "confId"     | new ActivityEntity(id: "confId", enrollReview: true, enrollCheckEmployee: null)                  | null                                                || true
        "confId"     | createActivityEntityWithReview()                                                                 | new ActivityEnrollDataEntity(createTime: new Date()) || true
    }

    @Unroll
    def "sendConferenceEnrollNoticeTaskTest"() {
        given:
        conferenceDAO.queryCheckEnrollNotice(*_) >> enrollNoticeTaskList
        campaignMergeDataDAO.queryCampaignParticipantsDataByMarketingEventIds(*_) >> participantsList
        activityEnrollDataDAO.queryConferenceEnrollReviewInfo(*_) >> enrollReviewResults
        conferenceDAO.updateSendCheckEnrollNoticeFlag(*_) >> updateResult
        def spy = Spy(conferenceManager)
        spy.sendReviewMessage(*_) >> true
        spy.sendQywxConferenceEnrollNoticeRealTime(*_) >> { }

        when:
        spy.sendConferenceEnrollNoticeTask()

        then:
        noExceptionThrown()

        where:
        enrollNoticeTaskList | participantsList | enrollReviewResults | updateResult
        []                   | []               | []                  | 0
        [createEnrollNoticeTaskDTO()] | [] | [] | 0
        [createEnrollNoticeTaskDTO()] | [createPageCampaignParticipantsDTO()] | [] | 0
        [createEnrollNoticeTaskDTO()] | [createPageCampaignParticipantsDTO()] | [createQueryEnrollReviewResult()] | 1
    }

    @Unroll
    def "sendConferenceEnrollNoticeRealTimeTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        def spy = Spy(conferenceManager)
        spy.sendReviewMessage(*_) >> true

        when:
        spy.sendConferenceEnrollNoticeRealTime(conferenceId)

        then:
        noExceptionThrown()

        where:
        conferenceId | activityEntity
        "confId"     | null
        "confId"     | new ActivityEntity(id: "confId", enrollReview: false)
        "confId"     | new ActivityEntity(id: "confId", enrollReview: true, enrollNoticePoint: ConferenceEnrollCheckType.REAL_TIME.getType())
        "confId"     | new ActivityEntity(id: "confId", enrollReview: true, enrollNoticePoint: ConferenceEnrollCheckType.TWO_HOURS.getType())
    }

    private def createFSEmployeeMsg() {
        return Mock(FsAddressBookManager.FSEmployeeMsg) {
            getFullName() >> "Test User"
        }
    }

    private ActivityEntity createActivityEntityWithReview() {
        def entity = new ActivityEntity()
        entity.id = "confId"
        entity.enrollReview = true
        entity.enrollCheckEmployee = "[1,2]"
        entity.enrollCheckDepartment = "[3,4]"
        entity.title = "Test Conference"
        entity.ea = "test"
        entity.createBy = 1
        return entity
    }

    private def createEnrollNoticeTaskDTO() {
        return Mock(EnrollNoticeTaskDTO) {
            getConferenceId() >> "confId"
            getEa() >> "test"
            getMarketingEventId() >> "marketingEventId"
            getEnrollCheckEmployee() >> "[1,2]"
            getEnrollNoticePoint() >> ConferenceEnrollCheckType.TWO_HOURS.getType()
        }
    }

    private def createPageCampaignParticipantsDTO() {
        return Mock(PageCampaignParticipantsDTO) {
            getId() >> "participantId"
        }
    }

    private def createPageCampaignParticipantsDTOBasic() {
        return Mock(PageCampaignParticipantsDTO) {
            getId() >> "campaignId1"
            getName() >> "Test User"
            getCampaignMembersObjId() >> "objId1"
            getBindCrmObjectType() >> 1
            getSpreadFsUserId() >> null
            getEnrollSourceObjectId() >> "sourceId1"
            getEnrollSourceObjectType() >> 1
            getChannelValue() >> "channel1"
            getInviteStatus() >> 1
            getReviewStatus() >> 1
            getCode() >> 12345
            getSignIn() >> 1
            getSignInTime() >> new Date()
            getGroupUserId() >> "1,2"
            getPhone() >> "13800138000"
        }
    }

    private def createPageCampaignParticipantsDTOWithData() {
        return Mock(PageCampaignParticipantsDTO) {
            getId() >> "campaignId1"
            getName() >> null  // 测试从ObjectData获取名称的场景
            getCampaignMembersObjId() >> "objId1"
            getBindCrmObjectType() >> 1
            getSpreadFsUserId() >> 1  // 测试推广人场景
            getEnrollSourceObjectId() >> "sourceId1"
            getEnrollSourceObjectType() >> 1
            getChannelValue() >> "channel1"
            getInviteStatus() >> 1
            getReviewStatus() >> 1
            getCode() >> 12345
            getSignIn() >> 1
            getSignInTime() >> new Date()
            getGroupUserId() >> "1,2"
            getPhone() >> "13800138000"
        }
    }

    private def createPageCampaignParticipantsDTOWithPartner() {
        return Mock(PageCampaignParticipantsDTO) {
            getId() >> "campaignId2"
            getName() >> "Partner User"
            getCampaignMembersObjId() >> null
            getBindCrmObjectType() >> 2
            getSpreadFsUserId() >> 999999  // 伙伴虚拟用户ID
            getEnrollSourceObjectId() >> "sourceId2"
            getEnrollSourceObjectType() >> 2
            getChannelValue() >> "channel2"
            getInviteStatus() >> 2
            getReviewStatus() >> 2
            getCode() >> 67890
            getSignIn() >> 0
            getSignInTime() >> null
            getGroupUserId() >> null
            getPhone() >> "13900139000"
        }
    }

    private def createConferenceInvitationUserDTO() {
        return Mock(ConferenceInvitationUserDTO) {
            getCampaignMergeDataId() >> "campaignId1"
            getUserId() >> 1
        }
    }

    private def createUserRelationPartnerInfo() {
        return Mock(UserRelationPartnerInfo) {
            getOuterTenantName() >> "Partner Company"
            getOuterUserName() >> "Partner User"
        }
    }

    private def createObjectDataWithName() {
        def objectData = new ObjectData()
        objectData.id = "objId1"
        objectData.put("name", "Object User Name")
        return objectData
    }

    private def createCustomizeFormDataUserEntityWithContent() {
        def entity = createCustomizeFormDataUserEntity()
        entity.submitContent = Mock(CustomizeFormDataEnroll) {
            getPhone() >> "13800138000"
        }
        return entity
    }

    private def createPageCampaignParticipantsDTOWithFileAttachment() {
        return Mock(PageCampaignParticipantsDTO) {
            getId() >> "campaignId3"
            getName() >> "File User"
            getCampaignMembersObjId() >> null
            getBindCrmObjectType() >> 1
            getSpreadFsUserId() >> null
            getEnrollSourceObjectId() >> "sourceId3"
            getEnrollSourceObjectType() >> 1
            getChannelValue() >> "channel3"
            getInviteStatus() >> 1
            getReviewStatus() >> 1
            getCode() >> 11111
            getSignIn() >> 1
            getSignInTime() >> new Date()
            getGroupUserId() >> null
            getPhone() >> "13700137000"
        }
    }

    private def createCustomizeFormDataUserEntityWithFileAttachment() {
        def entity = createCustomizeFormDataUserEntity()
        entity.submitContent = Mock(CustomizeFormDataEnroll) {
            getPhone() >> "13700137000"
        }
        return entity
    }

    private def createCustomizeFormDataEntityWithFileField() {
        def entity = Mock(CustomizeFormDataEntity) {
            getFormHeadSetting() >> Mock(FormHeadSetting) {
                setName(*_) >> { }
                setTitle(*_) >> { }
            }
            getFormBodySetting() >> createFieldInfoListWithFileField()
            getFormFootSetting() >> Mock(FormFootSetting)
            getFormMoreSetting() >> Mock(FormMoreSetting)
            getFormSuccessSetting() >> Mock(FormSuccessSetting)
        }
        return entity
    }

    private def createFieldInfoListWithFileField() {
        def fileField = Mock(FieldInfo) {
            getType() >> FieldInfo.Type.FILE_ATTACHMENT.getValue()
            getType() >> "fileField"
        }
        def fieldInfoList = Mock(FieldInfoList) {
            iterator() >> [fileField].iterator()
            size() >> 1
        }
        return fieldInfoList
    }

    private def createFileAttachmentList() {
        def fileContainer = Mock(FileAttachmentContainer) {
            getPath() >> "http://example.com/file1.pdf"
        }
        return [fileContainer]
    }

    private def createQueryEnrollReviewResult() {
        return Mock(QueryEnrollReviewResult) {
            getConferenceId() >> "confId"
        }
    }

    @Unroll
    def "updateInviteStatusAfterInvitingTest"() {
        given:
        inviteParticipantDAO.findByIds(*_) >> inviteParticipantEntities
        inviteParticipantDAO.updateInviteSendStatus(*_) >> { }

        when:
        conferenceManager.updateInviteStatusAfterInviting(id)

        then:
        noExceptionThrown()

        where:
        id   | inviteParticipantEntities
        "id" | []
        "id" | [new ConferenceInviteParticipantEntity(status: ConferenceInviteStatusEnum.UN_INVITE.getStatus())]
        "id" | [new ConferenceInviteParticipantEntity(status: ConferenceInviteStatusEnum.INVITED_UN_SIGN_UP.getStatus())]
    }

    @Unroll
    def "updateInviteStatusAfterSignInTest"() {
        given:
        inviteParticipantDAO.updateInviteStatusByPhone(*_) >> { }
        inviteParticipantDAO.updateInviteStatusByPhoneList(*_) >> { }

        when:
        if (phoneList != null) {
            conferenceManager.updateInviteStatusAfterSignIn(conferenceId, phoneList)
        } else {
            conferenceManager.updateInviteStatusAfterSignIn(conferenceId, phone)
        }

        then:
        noExceptionThrown()

        where:
        conferenceId | phone        | phoneList
        "confId"     | "123456789"  | null
        "confId"     | null         | ["123456789", "987654321"]
        ""           | null         | ["123456789"]
        "confId"     | null         | []
        null         | null         | ["123456789"]
    }

    @Unroll
    def "createGroupUserInfoTest"() {
        given:
        conferenceUserGroupDAO.getConferenceUserGroupByEaConferenceIdAndName(*_) >> existingGroup
        conferenceUserGroupDAO.insertConferenceUserGroup(*_) >> { ConferenceUserGroupEntity entity ->
            entity.id = 123
        }

        when:
        def result = conferenceManager.createGroupUserInfo(ea, fsUserId, conferenceId, groupName)

        then:
        result != null

        where:
        ea   | fsUserId | conferenceId | groupName   || existingGroup
        "ea" | 1        | "confId"     | "testGroup" || null
        "ea" | 1        | "confId"     | "testGroup" || new ConferenceUserGroupEntity(id: 456)
    }

    @Unroll
    def "getGroupNameByIdsStrTest"() {
        given:
        conferenceUserGroupDAO.getConferenceUserGroupById(*_) >> userGroupEntity

        when:
        def result = conferenceManager.getGroupNameByIdsStr(ids)

        then:
        if (ids == null || ids.isEmpty()) {
            result == null
        } else {
            result != null
        }

        where:
        ids      | userGroupEntity
        null     | null
        ""       | null
        "1,2,3"  | new ConferenceUserGroupEntity(groupName: "Test Group")
        "1"      | null
    }

    @Unroll
    def "deleteParticipantsTest"() {
        given:
        def vo = new DeleteParticipantVO()
        vo.campaignId = campaignId
        vo.verifyBindingObject = verifyBinding
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> campaignMergeDataEntity
        campaignMergeDataDAO.deleteCampaignMergeData(*_) >> 1
        campaignMergeDataManager.campaignIdToActivityEnrollId(*_) >> enrollIds
        activityEnrollDataDAO.getActivityEnrollDataById(*_) >> enrollDataEntity
        customizeTicketDAO.getTicketByAssociationAndDataUserId(*_) >> null
        wxTicketReceiveDAO.getWxTicketReceiveByFormDataUserId(*_) >> null
        memberAccessibleCampaignDAO.deleteMemberAccessibleCampaignDataByCampaignId(*_) >> { }
        customizeFormDataUserDAO.unBindCampaignData(*_) >> 1
        activityDAO.decEnrollCount(*_) >> 1
        activityEnrollDataDAO.deleteById(*_) >> 1

        when:
        def result = conferenceManager.deleteParticipants(vo)

        then:
        result.errCode == expectedErrorCode

        where:
        campaignId | verifyBinding | campaignMergeDataEntity                                                    | enrollIds | enrollDataEntity                                    || expectedErrorCode
        "campId"   | false         | null                                                                       | []        | null                                                || SHErrorCode.CONFERENCE_PARTICIPATN_NOT_FOUND.getErrorCode()
        "campId"   | true          | new CampaignMergeDataEntity(campaignMembersObjId: "objId")                | []        | null                                                || SHErrorCode.CONFERENCE_PARTICIPATN_SAVED_LEAD.getErrorCode()
        "campId"   | false         | new CampaignMergeDataEntity(campaignMembersObjId: null)                   | ["enrollId"] | new ActivityEnrollDataEntity(activityId: "actId") || SHErrorCode.SUCCESS.getErrorCode()
        "campId"   | false         | new CampaignMergeDataEntity(campaignMembersObjId: "")                     | []        | null                                                || SHErrorCode.SUCCESS.getErrorCode()
    }

    @Unroll
    def "addActivityEnrollDataTest"() {
        given:
        redisManager.lock(*_) >> lockSuccess
        redisManager.unLock(*_) >> { }
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> existingEnrollData
        activityDAO.getById(*_) >> activityEntity
        activityEnrollDataDAO.insertActivityEnrollData(*_) >> { }
        activityDAO.incrementEnrollCount(*_) >> { }

        when:
        conferenceManager.addActivityEnrollData(enrollDataEntity)

        then:
        noExceptionThrown()

        where:
        enrollDataEntity                                                                                    | lockSuccess | existingEnrollData | activityEntity
        null                                                                                                | true        | []                 | null
        new ActivityEnrollDataEntity(formDataUserId: "formUserId", activityId: "actId")                   | false       | []                 | null
        new ActivityEnrollDataEntity(formDataUserId: "formUserId", activityId: "actId")                   | true        | [new ActivityEnrollDataEntity()] | null
        new ActivityEnrollDataEntity(formDataUserId: "formUserId", activityId: "actId")                   | true        | []                 | new ActivityEntity(marketingEventId: "mktId", ea: "test")
        new ActivityEnrollDataEntity(formDataUserId: "formUserId", activityId: "actId", ea: "test")       | true        | []                 | null
    }

    @Unroll
    def "queryCampaignMembersObjOwnerMapTest"() {
        given:
        crmV2Manager.getCampaignMembersObjByFilter(*_) >> objectDataList
        fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(*_) >> employeeMap

        when:
        def result = conferenceManager.queryCampaignMembersObjOwnerMap(ea, marketingEventId, campaignMembersObjIds)

        then:
        result != null

        where:
        ea   | marketingEventId    | campaignMembersObjIds | objectDataList                                                    | employeeMap
        ""   | "mktId"             | ["objId1"]            | []                                                                | [:]
        "ea" | "mktId"             | []                    | []                                                                | [:]
        "ea" | "mktId"             | ["objId1"]            | []                                                                | [:]
        "ea" | "mktId"             | ["objId1"]            | [createObjectDataWithOwner()]                                     | [1: createFSEmployeeMsg()]
        "ea" | "mktId"             | ["objId1"]            | [new ObjectData(id: "objId1", owner: -10000)]                     | [:]
    }

    @Unroll
    def "queryCampaignMembersObjByFilterTest"() {
        given:
        crmV2Manager.getCampaignMembersObjByFilter(*_) >> objectDataList

        when:
        def result = conferenceManager.queryCampaignMembersObjByFilter(ea, marketingEventId, campaignMembersObjIds)

        then:
        result.size() == expectedSize

        where:
        ea   | marketingEventId | campaignMembersObjIds | objectDataList                || expectedSize
        "ea" | "mktId"          | []                    | []                            || 0
        "ea" | "mktId"          | ["objId1"]            | []                            || 0
        "ea" | "mktId"          | ["objId1"]            | [new ObjectData(id: "objId1")] || 1
    }

    private ObjectData createObjectDataWithOwner() {
        def objectData = new ObjectData()
        objectData.id = "objId1"
        objectData.owner = 1
        return objectData
    }

    @Unroll
    def "createMarketingEventObjTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        fileV2Manager.getNpathByApath(*_) >> "npath"
        metadataActionService.add(*_) >> createActionAddResult()

        when:
        def result = conferenceManager.createMarketingEventObj(vo)

        then:
        result != null

        where:
        vo << [createTestVO()]
    }

    @Unroll
    def "updateTest"() {
        given:
        def spy = Spy(conferenceManager)
        spy.getConferencePath(*_) >> "conference/path"

        when:
        def result = spy.update(vo, conferenceEntity)

        then:
        result != null
        result.title == vo.title
        result.location == vo.location

        where:
        vo                                                                    | conferenceEntity
        createTestVO()                                                        | new ActivityEntity(id: "confId")
        createTestVOWithDetails()                                             | new ActivityEntity(id: "confId")
    }

    @Unroll
    def "queryMarketingEventCountByTitleTest"() {
        given:
        metadataControllerServiceManager.getTotal(*_) >> totalCount

        when:
        def result = conferenceManager.queryMarketingEventCountByTitle(ea, title)

        then:
        result == expectedResult

        where:
        ea   | title        | totalCount || expectedResult
        "ea" | "Test Title" | 5          || 5
        "ea" | "Test Title" | null       || 0
        "ea" | "Test Title" | 0          || 0
    }

    @Unroll
    def "syncCrmInvalidConferenceTest"() {
        given:
        conferenceDAO.getNormalMarketingEventByEa(*_) >> marketingEventIds
        metadataControllerServiceManager.detail(*_) >> { throw exception }
        conferenceDAO.updateConferenceStatusByMarketingEvent(*_) >> { }

        when:
        conferenceManager.syncCrmInvalidConference(ea)

        then:
        noExceptionThrown()

        where:
        ea   | marketingEventIds        | exception
        "ea" | []                       | null
        "ea" | ["mktId1", "mktId2"]     | new RuntimeException("数据已作废或已删除")
        "ea" | ["mktId1"]               | new RuntimeException("其他错误")
    }

    @Unroll
    def "bulkDeleteTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDataService.bulkDelete(*_) >> createBulkDeleteResult()

        when:
        def result = conferenceManager.bulkDelete(ea, fsUserId, ids)

        then:
        result != null

        where:
        ea   | fsUserId | ids
        "ea" | 1        | ["id1", "id2"]
        "ea" | null     | ["id1"]
    }

    private def createActionAddResult() {
        return Mock(com.fxiaoke.crmrestapi.common.result.Result) {
            getData() >> Mock(ActionAddResult) {
                objectData: new ObjectData()
            }
        }
    }

    private def createBulkDeleteResult() {
        return Mock(com.fxiaoke.crmrestapi.common.result.Result) {
            getData() >> Mock(BulkDeleteResult)
        }
    }

    private CreateOrUpdateConferenceVO createTestVOWithDetails() {
        def vo = createTestVO()
        vo.updateConferenceDetails = true
        vo.conferenceDetails = "Test conference details"
        return vo
    }

    @Unroll
    def "signInUserByPhoneTest"() {
        given:
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> campaignMergeDataList
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> enrollDataList
        campaignMergeDataManager.updateSignInStatus(*_) >> { }
        campaignMergeDataDAO.queryCampaignEnrollDataIdentityInfo(*_) >> []
        activityManager.handleActivitySignInOtherProcess(*_) >> { }

        when:
        def result = conferenceManager.signInUserByPhone(ea, marketingEventId, phone)

        then:
        result.errCode == expectedErrorCode

        where:
        ea   | marketingEventId    | phone        | activityEntity                                    | campaignMergeDataList           | enrollDataList                                                                                    || expectedErrorCode
        ""   | "mktId"             | "123456789"  | null                                              | []                              | []                                                                                                || SHErrorCode.NO_DATA.getErrorCode()
        "ea" | "mktId"             | "123456789"  | null                                              | []                              | []                                                                                                || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "ea" | "mktId"             | "123456789"  | new ActivityEntity(id: "actId")                   | []                              | []                                                                                                || SHErrorCode.NO_DATA.getErrorCode()
        "ea" | "mktId"             | "123456789"  | new ActivityEntity(id: "actId")                   | [createCampaignMergeData()]     | [new ActivityEnrollDataEntity(reviewStatus: ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus())] || SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS.getErrorCode()
        "ea" | "mktId"             | "123456789"  | new ActivityEntity(id: "actId")                   | [createCampaignMergeData()]     | [createReviewedEnrollData()]                                                                      || SHErrorCode.SUCCESS.getErrorCode()
    }

    @Unroll
    def "signInUserByCampaignIdTest"() {
        given:
        campaignMergeDataDAO.getCampaignMergeDataById(*_) >> campaignMergeData
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> enrollDataList
        campaignMergeDataManager.updateSignInStatus(*_) >> { }
        campaignMergeDataDAO.queryCampaignEnrollDataIdentityInfo(*_) >> []
        activityManager.handleActivitySignInOtherProcess(*_) >> { }
        activityManager.handleConferenceTag(*_) >> { }

        when:
        def result = conferenceManager.signInUserByCampaignId(campaignId, tagId)

        then:
        result.errCode == expectedErrorCode

        where:
        campaignId | tagId  | campaignMergeData                                                                    | activityEntity                  | enrollDataList                || expectedErrorCode
        ""         | null   | null                                                                                 | null                            | []                            || SHErrorCode.NO_DATA.getErrorCode()
        "campId"   | null   | null                                                                                 | null                            | []                            || SHErrorCode.NO_DATA.getErrorCode()
        "campId"   | null   | new CampaignMergeDataEntity(marketingEventId: "mktId", ea: "test")                  | null                            | []                            || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "campId"   | "tag1" | new CampaignMergeDataEntity(marketingEventId: "mktId", ea: "test", phone: "123456") | new ActivityEntity(id: "actId") | [createReviewedEnrollData()]  || SHErrorCode.SUCCESS.getErrorCode()
    }

    @Unroll
    def "createDefaultConferenceSiteTest"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        hexagonSiteDAO.getById(*_) >> createHexagonSiteEntity()
        hexagonSiteDAOManager.insert(*_) >> { }
        hexagonSiteDAOManager.deleteById(*_) >> { }
        hexagonPageDAO.insert(*_) >> { }
        hexagonPageDAO.deleteById(*_) >> { }
        hexagonPageDAO.deleteByIds(*_) >> { }
        hexagonPageDAO.getPageByFormIdAndSiteId(*_) >> [createHexagonPageEntity()]
        hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(*_) >> templateSite
        hexagonTemplateSiteDAO.getFormByTemplateSiteId(*_) >> createHexagonSiteListDTO()
        hexagonTemplatePageDAO.getBySiteId(*_) >> []
        hexagonSiteManager.copyPageFromTemplate(*_) >> ["newPageId": "pageId", "newFormId": "formId"]
        customizeFormDataDAOManager.deleteCustomizeFormData(*_) >> { }
        contentMarketingEventMaterialRelationDAO.save(*_) >> { }
        contentMarketingEventMaterialRelationDAO.deleteContentMarketingEventMaterialRelationById(*_) >> { }
        conferenceDAO.updateConferenceDetailSiteId(*_) >> { }
        customizeFormDataManager.bindCustomizeFormDataObject(*_) >> { }
        customizeFormDataManager.unBindCustomizeFormDataObject(*_) >> { }

        when:
        conferenceManager.createDefaultConferenceSite(conferenceId)

        then:
        noExceptionThrown()

        where:
        conferenceId | activityEntity                                                                                    | templateSite
        "confId"     | null                                                                                              | null
        "confId"     | new ActivityEntity(id: "confId", activityDetailSiteId: "existingSiteId")                         | null
        "confId"     | new ActivityEntity(id: "confId", activityDetailSiteId: null, ea: "test", title: "Test Conference") | null
        "confId"     | new ActivityEntity(id: "confId", activityDetailSiteId: null, ea: "test", title: "Test Conference") | createHexagonTemplateSiteEntity()
    }

    private ActivityEnrollDataEntity createReviewedEnrollData() {
        def entity = new ActivityEnrollDataEntity()
        entity.reviewStatus = ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()
        entity.signIn = ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType()
        entity.id = "enrollId"
        return entity
    }

    private def createHexagonSiteEntity() {
        return Mock(HexagonSiteEntity) {
            getId() >> "templateSiteId"
        }
    }

    private def createHexagonPageEntity() {
        return Mock(HexagonPageEntity) {
            getId() >> "pageId"
        }
    }

    private def createHexagonSiteListDTO() {
        return Mock(HexagonSiteListDTO) {
            getFormId() >> "formId"
        }
    }

    private def createHexagonTemplateSiteEntity() {
        return Mock(HexagonTemplateSiteEntity) {
            getId() >> "templateSiteId"
        }
    }

    @Unroll
    def "getConferenceStatisticTest"() {
        given:
        activityDAO.getActivityEntityByMarketingEventId(*_) >> activityEntity
        campaignMergeDataDAO.getCampaignStatisticData(*_) >> campaignStatisticDTO
        campaignMergeDataDAO.getChannelValueStatisticData(*_) >> channelStatisticList
        spreadChannelManager.queryChannelMapData(*_) >> channelValueMap
        spreadChannelManager.getChannelLabelByChannelValue(*_) >> "Test Channel"

        when:
        def result = conferenceManager.getConferenceStatistic(ea, marketingEventId)

        then:
        result.errCode == expectedErrorCode
        if (result.isSuccess()) {
            assert result.data != null
            assert result.data.enrollCount != null
        }

        where:
        ea   | marketingEventId | activityEntity                  | campaignStatisticDTO           | channelStatisticList | channelValueMap        || expectedErrorCode
        "ea" | "mktId"          | null                            | null                           | []                   | [:]                    || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "ea" | "mktId"          | new ActivityEntity(id: "actId") | null                           | []                   | [:]                    || SHErrorCode.SUCCESS.getErrorCode()
        "ea" | "mktId"          | new ActivityEntity(id: "actId") | createCampaignStatisticDTO()   | []                   | [:]                    || SHErrorCode.SUCCESS.getErrorCode()
        "ea" | "mktId"          | new ActivityEntity(id: "actId") | createCampaignStatisticDTO()   | [createChannelStatisticDTO()] | ["ch1": "Channel 1"] || SHErrorCode.SUCCESS.getErrorCode()
    }

    private CampaignStatisticDTO createCampaignStatisticDTO() {
        def dto = new CampaignStatisticDTO()
        dto.campaignCount = 100
        dto.invitedCount = 80
        dto.notInvitedCount = 20
        dto.pendingReviewCount = 10
        dto.bindCampaignObjCount = 50
        dto.signInCount = 60
        dto.errorDataCount = 5
        return dto
    }

    private CampaignStatisticDTO createChannelStatisticDTO() {
        def dto = new CampaignStatisticDTO()
        dto.channelValue = "ch1"
        dto.channelValueCount = 25
        return dto
    }

    @Unroll
    def "getSignInSettingTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        conferenceSignInJumpSettingDAO.getJumpSettingInfo(*_) >> jumpSettingEntity

        when:
        def result = conferenceManager.getSignInSetting(conferenceId)

        then:
        result.errCode == expectedErrorCode
        if (result.isSuccess()) {
            assert result.data != null
        }

        where:
        conferenceId | activityEntity                                    | jumpSettingEntity                                                                || expectedErrorCode
        "confId"     | null                                              | null                                                                             || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "confId"     | new ActivityEntity(id: "confId", ea: "test")      | null                                                                             || SHErrorCode.SUCCESS.getErrorCode()
        "confId"     | new ActivityEntity(id: "confId", ea: "test")      | new ConferenceSignInJumpSettingEntity(jumpObjectType: 1, jumpUrl: "http://test.com") || SHErrorCode.SUCCESS.getErrorCode()
    }

    @Unroll
    def "getSignInSuccessSettingTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        conferenceSignInSuccessSettingDAO.findByEntity(*_) >> successSettingEntityList

        when:
        def result = conferenceManager.getSignInSuccessSetting(conferenceId)

        then:
        result.errCode == expectedErrorCode
        if (result.isSuccess()) {
            assert result.data != null
        }

        where:
        conferenceId | activityEntity                                    | successSettingEntityList                                                                                || expectedErrorCode
        "confId"     | null                                              | null                                                                                                    || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "confId"     | new ActivityEntity(id: "confId", ea: "test")      | []                                                                                                      || SHErrorCode.SUCCESS.getErrorCode()
        "confId"     | new ActivityEntity(id: "confId", ea: "test")      | [new ConferenceSignInSuccessSetting()] || SHErrorCode.SUCCESS.getErrorCode()
    }

    @Unroll
    def "getSimpleDetailTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        photoManager.querySinglePhotoByEa(*_) >> photoEntity
        fileV2Manager.getUrlByPath(*_) >> "http://default.cover.url"
        hexagonPageDAO.getHomePage(*_) >> createHexagonPageEntity()
        photoManager.querySingleCpathPhoto(*_) >> null

        when:
        def result = conferenceManager.getSimpleDetail(conferenceId)

        then:
        result.errCode == expectedErrorCode
        if (result.isSuccess()) {
            assert result.data != null
        }

        where:
        conferenceId | activityEntity                                                                                    | photoEntity                                                || expectedErrorCode
        "confId"     | null                                                                                              | null                                                       || SHErrorCode.ACTIVITY_NOT_EXIST.getCode()
        "confId"     | createActivityWithTimeAndLocation()                                                              | null                                                       || SHErrorCode.SUCCESS.getCode()
        "confId"     | createActivityWithTimeAndLocation()                                                              | new PhotoEntity(id: "photoId", url: "photo/url")          || SHErrorCode.SUCCESS.getCode()
    }

    @Unroll
    def "getConferenceTimeFlowStatusTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity

        when:
        def result = conferenceManager.getConferenceTimeFlowStatus(activityEntity)

        then:
        result == expectedStatus

        where:
        conferenceId | activityEntity                                                                                                                                    || expectedStatus
        "confId"     | null                                                                                                                                              || ConferenceTimeFlowStatusEnum.NOT_STARTED.getStatus()
        "confId"     | new ActivityEntity(id: "confId", startTime: new Date(System.currentTimeMillis() + 3600000), endTime: new Date(System.currentTimeMillis() + 7200000))            || ConferenceTimeFlowStatusEnum.NOT_STARTED.getStatus()
        "confId"     | new ActivityEntity(id: "confId", startTime: new Date(System.currentTimeMillis() - 3600000), endTime: new Date(System.currentTimeMillis() + 3600000))            || ConferenceTimeFlowStatusEnum.PROCESSING.getStatus()
        "confId"     | new ActivityEntity(id: "confId", startTime: new Date(System.currentTimeMillis() - 7200000), endTime: new Date(System.currentTimeMillis() - 3600000))            || ConferenceTimeFlowStatusEnum.END.getStatus()
    }

    private ActivityEntity createActivityWithTimeAndLocation() {
        def entity = new ActivityEntity()
        entity.id = "confId"
        entity.ea = "test"
        entity.title = "Test Conference"
        entity.location = "Test Location"
        entity.startTime = new Date(System.currentTimeMillis() + 3600000)
        entity.endTime = new Date(System.currentTimeMillis() + 7200000)
        entity.mapAddress = "Test Address"
        entity.mapLocation = new MapLocation()
        return entity
    }

    @Unroll
    def "canPublicConferenceTest"() {
        given:
        customizeFormDataManager.getBindFormDataByObject(*_) >> customizeFormDataEntity

        when:
        def result = conferenceManager.canPublicConference(activityEntity)

        then:
        result == expectedResult

        where:
        activityEntity                                                                                                                                                    | customizeFormDataEntity                                || expectedResult
        null                                                                                                                                                              | null                                                    || false
        new ActivityEntity(id: "confId", ea: "test", conferenceDetails: null)                                                                                            | null                                                    || false
        new ActivityEntity(id: "confId", ea: "test", conferenceDetails: "")                                                                                              | null                                                    || false
        new ActivityEntity(id: "confId", ea: "test", conferenceDetails: "Test details")                                                                                  | null                                                    || false
        new ActivityEntity(id: "confId", ea: "test", conferenceDetails: "Test details")                                                                                  | new CustomizeFormDataEntity(id: "formId") || true
    }

    @Unroll
    def "getEnrollNameByObjectTypeTest"() {
        given:
        crmV2Manager.addCampaignMembersObj(*_) >> objectName

        when:
        def result = conferenceManager.getEnrollNameByObjectType(ea, objectType, objectId)

        then:
        result == expectedResult

        where:
        ea   | objectType                        | objectId  | objectName      || expectedResult
        "ea" | ObjectTypeEnum.LEAD.getType()  | "objId"   | "Test Lead"     || "Test Lead"
        "ea" | ObjectTypeEnum.LEAD.getType()  | "objId"   | null            || null
        "ea" | "InvalidType"                     | "objId"   | "Test Object"   || null
        "ea" | ObjectTypeEnum.LEAD.getType()  | null      | "Test Lead"     || null
    }

    @Unroll
    def "getEnrollNameMapByObjectsTest"() {
        given:
        crmV2Manager.getObjectNameMapByIds(*_) >> objectNameMap

        when:
        def result = conferenceManager.getEnrollNameMapByObjects(ea, objectType, objectIds)

        then:
        result.size() == expectedSize

        where:
        ea   | objectType                        | objectIds           | objectNameMap                                || expectedSize
        "ea" | ObjectTypeEnum.LEAD.getType()  | ["id1", "id2"]      | ["id1": "Lead 1", "id2": "Lead 2"]          || 2
        "ea" | ObjectTypeEnum.LEAD.getType()  | []                  | [:]                                         || 0
        "ea" | 1000                     | ["id1"]             | ["id1": "Object 1"]                        || 0
        "ea" | ObjectTypeEnum.LEAD.getType()  | ["id1", "id2"]      | [:]                                         || 0
    }

    @Unroll
    def "resetConferenceIndexPageTest"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        hexagonSiteDAO.getById(*_) >> createHexagonSiteEntity()
        hexagonPageDAO.getPageByFormIdAndSiteId(*_) >> pageEntities
        def spy = Spy(conferenceManager)
        spy.handleIndexPage(*_) >> "updated content"

        when:
        spy.resetConferenceIndexPage(conferenceId)

        then:
        noExceptionThrown()

        where:
        conferenceId | activityEntity                                                                                    | pageEntities
        "confId"     | null                                                                                              | []
        "confId"     | new ActivityEntity(id: "confId", activityDetailSiteId: null)                                     | []
        "confId"     | new ActivityEntity(id: "confId", activityDetailSiteId: "siteId")                                 | []
        "confId"     | new ActivityEntity(id: "confId", activityDetailSiteId: "siteId")                                 | [createHexagonPageEntity()]
    }

    @Unroll
    def "createConferenceSiteTest"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        hexagonSiteDAO.getById(*_) >> templateSiteEntity
        hexagonSiteDAOManager.insert(*_) >> { }
        hexagonSiteDAOManager.deleteById(*_) >> { }
        hexagonPageDAO.getById(*_) >> createHexagonPageEntity()
        hexagonPageDAO.insert(*_) >> { }
        hexagonPageDAO.deleteById(*_) >> { }
        hexagonPageDAO.deleteByIds(*_) >> { }
        hexagonPageDAO.getPageByFormIdAndSiteId(*_) >> [createHexagonPageEntity()]
        hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(*_) >> templateSite
        hexagonTemplateSiteDAO.getFormByTemplateSiteId(*_) >> createHexagonSiteListDTO()
        hexagonTemplateSiteDAO.getById(*_) >> templateSite
        hexagonTemplatePageDAO.getBySiteId(*_) >> []
        hexagonSiteManager.copyPageFromTemplate(*_) >> ["newPageId": "pageId", "newFormId": "formId"]
        customizeFormDataDAO.getCustomizeFormDataById(*_) >> createCustomizeFormDataEntity()
        customizeFormDataDAOManager.insertCustomizeFormData(*_) >> { }
        customizeFormDataDAOManager.deleteCustomizeFormData(*_) >> { }
        contentMarketingEventMaterialRelationDAO.save(*_) >> { }
        contentMarketingEventMaterialRelationDAO.updateIsApplyObject(*_) >> { }
        contentMarketingEventMaterialRelationDAO.deleteContentMarketingEventMaterialRelationById(*_) >> { }
        memberManager.isOpenMember(*_) >> false
        memberConfigDao.getByEa(*_) >> null
        conferenceDAO.updateConferenceDetailSiteId(*_) >> { }
        customizeFormDataManager.bindCustomizeFormDataObject(*_) >> { }
        customizeFormDataManager.unBindCustomizeFormDataObject(*_) >> { }
        photoManager.querySinglePhoto(*_) >> null
        fileV2Manager.getSpliceUrl(*_) >> "http://test.com/image.jpg"

        when:
        conferenceManager.createConferenceSite(conferenceId, marketingTemplateId)

        then:
        noExceptionThrown()

        where:
        conferenceId | marketingTemplateId | activityEntity                                                                                    | templateSiteEntity                    | templateSite
        "confId"     | null                | null                                                                                              | null                                  | null
        "confId"     | null                | new ActivityEntity(id: "confId", activityDetailSiteId: "existingSiteId")                         | null                                  | null
        "confId"     | null                | new ActivityEntity(id: "confId", activityDetailSiteId: null, ea: "test", title: "Test Conference") | createHexagonSiteEntity()             | null
        "confId"     | "templateId"        | new ActivityEntity(id: "confId", activityDetailSiteId: null, ea: "test", title: "Test Conference") | createHexagonSiteEntity()             | createHexagonTemplateSiteEntity()
    }

    private def createCustomizeFormDataEntity() {
        return Mock(CustomizeFormDataEntity) {
            getFormHeadSetting() >> Mock(FormHeadSetting) {
                setName(*_) >> { }
                setTitle(*_) >> { }
            }
            getFormBodySetting() >> Mock(FieldInfoList)
            getFormFootSetting() >> Mock(FormFootSetting)
            getFormMoreSetting() >> Mock(FormMoreSetting)
            getFormSuccessSetting() >> Mock(FormSuccessSetting)
        }
    }

    @Unroll
    def "resetConferenceFormByPageTest"() {
        given:
        hexagonPageDAO.getById(*_) >> hexagonPageEntity
        conferenceDAO.getActivityByDetailSiteId(*_) >> activityEntity
        hexagonSiteDAO.getFormBySiteIds(*_) >> hexagonSiteListDTOList
        customizeFormDataManager.getBindFormDataByObject(*_) >> conferenceBindForm
        customizeFormDataManager.unBindCustomizeFormDataObject(*_) >> { }
        customizeFormDataManager.bindCustomizeFormDataObject(*_) >> { }

        when:
        conferenceManager.resetConferenceFormByPage(pageId, lastTimeHomeFormId)

        then:
        noExceptionThrown()

        where:
        pageId   | lastTimeHomeFormId | hexagonPageEntity                                                                    | activityEntity                                    | hexagonSiteListDTOList | conferenceBindForm
        "pageId" | "formId"           | null                                                                                 | null                                              | []                     | null
        "pageId" | "formId"           | createHexagonPageEntity()                                                            | null                                              | []                     | null
        "pageId" | "formId"           | createHexagonPageEntity()                                                            | new ActivityEntity(id: "actId", ea: "test")      | []                     | null
        "pageId" | "formId"           | createHexagonPageEntity()                                                            | new ActivityEntity(id: "actId", ea: "test")      | []                     | createCustomizeFormDataEntity()
        "pageId" | "formId"           | createHexagonPageEntity()                                                            | new ActivityEntity(id: "actId", ea: "test")      | [createHexagonSiteListDTO()] | null
    }

    @Unroll
    def "getExtraDataNameByObjectIdsTest"() {
        given:
        crmMetadataManager.batchGetByIdsV3(*_) >> objectDataList

        when:
        def result = conferenceManager.getExtraDataNameByObjectIds(ea, extraDataIds, crmApiName)

        then:
        result.size() == expectedSize

        where:
        ea   | extraDataIds      | crmApiName | objectDataList                                                    || expectedSize
        "ea" | []                | "Lead"     | []                                                                || 0
        "ea" | ["id1", "id2"]    | "Lead"     | []                                                                || 0
        "ea" | ["id1", "id2"]    | "Lead"     | [new ObjectData(id: "id1", name: "Lead 1"), new ObjectData(id: "id2", name: "Lead 2")] || 2
        "ea" | ["id1"]           | "Lead"     | [new ObjectData(id: "id1", name: "Lead 1")]                       || 1
    }

    @Unroll
    def "skipConfenrecHexagonContentItemTest"() {
        when:
        def result = conferenceManager.skipConfenrecHexagonContentItem(blockDataEnum)

        then:
        result == expectedResult

        where:
        blockDataEnum                                                           || expectedResult
        ConferenceReplaceConstants.BlockDataEnum.MEMBER_AUTO_SIGN_UP            || true
        ConferenceReplaceConstants.BlockDataEnum.CONFERENCE_SHAREOPTS_CONTAINER || true
        ConferenceReplaceConstants.BlockDataEnum.CONFERENCE_NAME_CONTAINER      || true
        ConferenceReplaceConstants.BlockDataEnum.MEMBER_AUTO_SIGN_UP            || false
    }

    @Unroll
    def "sendQywxConferenceEnrollNoticeRealTimeTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        activityEnrollDataDAO.getLatestEnrollByConferenceId(*_) >> enrollDataEntity
        fsAddressBookManager.getEmployeeIdsByEa(*_) >> [1, 2, 3]
        fsAddressBookManager.getEmployeeIdsByCircleIds(*_) >> [4, 5, 6]
        fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(*_) >> [1: createFSEmployeeMsg()]
        qywxMiniAppMessageManager.sendConferenceCheckEnroll(*_) >> { }

        when:
        conferenceManager.sendQywxConferenceEnrollNoticeRealTime(conferenceId)

        then:
        noExceptionThrown()

        where:
        conferenceId | activityEntity                                                                                    | enrollDataEntity
        "confId"     | null                                                                                              | null
        "confId"     | new ActivityEntity(id: "confId", enrollReview: false)                                             | null
        "confId"     | createActivityEntityWithReview()                                                                 | new ActivityEnrollDataEntity(createTime: new Date())
    }

    @Unroll
    def "sendDingdingConferenceEnrollNoticeRealTimeTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        activityEnrollDataDAO.getLatestEnrollByConferenceId(*_) >> enrollDataEntity
        dingManager.sendConferenceCheckEnroll(*_) >> { }

        when:
        conferenceManager.sendDingdingConferenceEnrollNoticeRealTime(conferenceId)

        then:
        noExceptionThrown()

        where:
        conferenceId | activityEntity                                                                                    | enrollDataEntity
        "confId"     | null                                                                                              | null
        "confId"     | new ActivityEntity(id: "confId", enrollReview: false)                                             | null
        "confId"     | createActivityEntityWithReview()                                                                 | new ActivityEnrollDataEntity(createTime: new Date())
    }

    @Unroll
    def "createConferenceTicketAndAttachedInfoWithObjectTypeTest"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        def spy = Spy(conferenceManager)
        spy.addActivityEnrollData(*_) >> { }
        spy.createConferenceEnrollAttachedInfo(*_) >> { }
        customizeTicketManager.createConferenceCustomizeTicket(*_) >> 123456L

        when:
        spy.createConferenceTicketAndAttachedInfo(ea, objectType, objectId, spreadFsUserId, enrollSourceType, campaignMergeDataId, marketingEventId)

        then:
        noExceptionThrown()

        where:
        ea   | objectType | objectId | spreadFsUserId | enrollSourceType | campaignMergeDataId | marketingEventId | activityEntity
        "ea" | 1          | "objId"  | 1              | 1                | "campaignId"        | "mktId"          | null
        "ea" | 1          | "objId"  | 1              | 1                | "campaignId"        | "mktId"          | new ActivityEntity(id: "actId", ea: "test")
        "ea" | 2          | "objId"  | 1              | 1                | "campaignId"        | "mktId"          | null
    }

    @Unroll
    def "updateConferenceStatusTest"() {
        given:
        conferenceDAO.getConferenceById(*_) >> activityEntity
        conferenceDAOManager.updateConferenceStatus(*_) >> { }
        contentMarketingEventMaterialRelationDAO.save(*_) >> { }
        def spy = Spy(conferenceManager)
        spy.canPublicConference(*_) >> canPublic

        when:
        def result = spy.updateConferenceStatus(id, status, ea, fsUserId)

        then:
        result.errCode == expectedErrorCode

        where:
        id       | status | ea   | fsUserId | activityEntity                                    | canPublic || expectedErrorCode
        "confId" | 999    | "ea" | 1        | null                                              | false     || SHErrorCode.PARAMS_ERROR.getErrorCode()
        "confId" | 1      | "ea" | 1        | null                                              | false     || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "confId" | 1      | "ea" | 1        | new ActivityEntity(id: "confId", ea: "test")      | false     || SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode()
        "confId" | 1      | "ea" | 1        | new ActivityEntity(id: "confId", ea: "test")      | true      || SHErrorCode.SUCCESS.getErrorCode()
        "confId" | 2      | "ea" | 1        | new ActivityEntity(id: "confId", ea: "test")      | false     || SHErrorCode.SUCCESS.getErrorCode()
    }

    @Unroll
    def "queryCampaignMembersObjBusinessOwnerMapTest"() {
        given:
        def spy = Spy(conferenceManager)
        spy.getCampaignMembersObjBusinessOwnerByObj(*_) >> businessOwner

        when:
        def result = spy.queryCampaignMembersObjBusinessOwnerMap(ea, objectDataList)

        then:
        result.size() == expectedSize

        where:
        ea   | objectDataList                                                    | businessOwner || expectedSize
        "ea" | []                                                                | null          || 0
        "ea" | [new ObjectData(id: "id1")]                                       | null          || 0
        "ea" | [new ObjectData(id: "id1")]                                       | "owner1"      || 1
        "ea" | [new ObjectData(id: "id1"), new ObjectData(id: "id2")]           | "owner1"      || 2
    }

    @Unroll
    def "getCampaignMembersObjBusinessOwnerByObjTest"() {
        when:
        def result = conferenceManager.getCampaignMembersObjBusinessOwnerByObj(objectData)

        then:
        result == expectedResult

        where:
        objectData                                                                                                                                    || expectedResult
        null                                                                                                                                          || null
        new ObjectData()                                                                                                                              || null
        createObjectDataWithCampaignMembersType("Lead", "business_owner_name")                                                                       || "Test Owner"
        createObjectDataWithCampaignMembersType("", "business_owner_name")                                                                           || null
        createObjectDataWithCampaignMembersType("InvalidType", "business_owner_name")                                                               || null
    }

    @Unroll
    def "queryCampaignMembersObjMapByFieldTest"() {
        given:
        def spy = Spy(conferenceManager)
        spy.queryCampaignMembersObjByFilter(*_) >> objectDataList

        when:
        def result = spy.queryCampaignMembersObjMapByField(ea, marketingEventId, campaignMembersObjIds)

        then:
        result.size() == expectedSize

        where:
        ea   | marketingEventId | campaignMembersObjIds | objectDataList                                                    || expectedSize
        ""   | "mktId"          | ["id1"]               | []                                                                || 0
        "ea" | "mktId"          | []                    | []                                                                || 0
        "ea" | "mktId"          | ["id1"]               | []                                                                || 0
        "ea" | "mktId"          | ["id1"]               | [new ObjectData(id: "id1")]                                       || 1
        "ea" | "mktId"          | ["id1", "id2"]        | [new ObjectData(id: "id1"), new ObjectData(id: "id2")]           || 2
    }

    @Unroll
    def "getConferenceTimeFlowStatusWithEntityTest"() {
        given:
        def spy = Spy(conferenceManager)
        spy.canPublicConference(*_) >> canPublic

        when:
        def result = spy.getConferenceTimeFlowStatus(activityEntity)

        then:
        result == expectedStatus

        where:
        activityEntity                                                                                                                                                    | canPublic || expectedStatus
        new ActivityEntity(status: ActivityStatusEnum.DELETED.getStatus())                                                                                               | false     || ConferenceTimeFlowStatusEnum.DELETED.getStatus()
        new ActivityEntity(status: ActivityStatusEnum.DISABLED.getStatus())                                                                                              | false     || ConferenceTimeFlowStatusEnum.DISABLED.getStatus()
        new ActivityEntity(status: ActivityStatusEnum.UNPUBLISHED.getStatus())                                                                                           | true      || ConferenceTimeFlowStatusEnum.UNPUBLISHED.getStatus()
        new ActivityEntity(status: ActivityStatusEnum.UNPUBLISHED.getStatus())                                                                                           | false     || ConferenceTimeFlowStatusEnum.UNPUBLISHED_NO.getStatus()
        createActivityWithFutureTime()                                                                                                                                   | false     || ConferenceTimeFlowStatusEnum.NOT_STARTED.getStatus()
        createActivityWithCurrentTime()                                                                                                                                  | false     || ConferenceTimeFlowStatusEnum.PROCESSING.getStatus()
        createActivityWithPastTime()                                                                                                                                     | false     || ConferenceTimeFlowStatusEnum.END.getStatus()
    }

    private ObjectData createObjectDataWithCampaignMembersType(String campaignMembersType, String businessOwnerField) {
        def objectData = new ObjectData()
        objectData.put("campaign_members_type", campaignMembersType)
        objectData.put(businessOwnerField, "Test Owner")
        return objectData
    }

    private ActivityEntity createActivityWithFutureTime() {
        def entity = new ActivityEntity()
        entity.status = ActivityStatusEnum.ENABLED.getStatus()
        entity.startTime = new Date(System.currentTimeMillis() + 3600000)
        entity.endTime = new Date(System.currentTimeMillis() + 7200000)
        return entity
    }

    private ActivityEntity createActivityWithCurrentTime() {
        def entity = new ActivityEntity()
        entity.status = ActivityStatusEnum.ENABLED.getStatus()
        entity.startTime = new Date(System.currentTimeMillis() - 1800000)
        entity.endTime = new Date(System.currentTimeMillis() + 1800000)
        return entity
    }

    private ActivityEntity createActivityWithPastTime() {
        def entity = new ActivityEntity()
        entity.status = ActivityStatusEnum.ENABLED.getStatus()
        entity.startTime = new Date(System.currentTimeMillis() - 7200000)
        entity.endTime = new Date(System.currentTimeMillis() - 3600000)
        return entity
    }

}