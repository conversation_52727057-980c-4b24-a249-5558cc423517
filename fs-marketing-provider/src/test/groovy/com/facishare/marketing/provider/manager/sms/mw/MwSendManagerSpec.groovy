package com.facishare.marketing.provider.manager.sms.mw

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.sms.GroupSenderArg
import com.facishare.marketing.api.result.MobileMarketingUserResult
import com.facishare.marketing.api.result.ReportResult
import com.facishare.marketing.api.result.sms.OneSMSDetailResult
import com.facishare.marketing.api.result.sms.ShortUrlResult
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult
import com.facishare.marketing.api.service.sms.SendService
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.SmsContentParam
import com.facishare.marketing.common.typehandlers.value.SmsContentParamList
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.MarketingUserGroupDao
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.sms.QuotaDAO
import com.facishare.marketing.provider.dao.sms.SmsChannelConfigDAO
import com.facishare.marketing.provider.dao.sms.SmsTrialDao
import com.facishare.marketing.provider.dao.sms.mw.*
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.entity.sms.SmsChannelConfigEntity
import com.facishare.marketing.provider.entity.sms.SmsTrialEntity
import com.facishare.marketing.provider.entity.sms.mw.*
import com.facishare.marketing.provider.innerArg.MwSendDetailUpdateMqArg
import com.facishare.marketing.provider.innerArg.mw.CreateSendTaskArg
import com.facishare.marketing.provider.innerResult.BatchShortUrlResult
import com.facishare.marketing.provider.innerResult.CreateMiniAppForwardUrlResult
import com.facishare.marketing.provider.innerResult.mw.ReportResults
import com.facishare.marketing.provider.innerResult.mw.SendRequestResult
import com.facishare.marketing.provider.innerResult.sms.mw.TemplateQueryResult.TemplateQueryDetail
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.crmobjectcreator.SmsSendRecordObjManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.marketing.provider.manager.sms.QuotaManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.mq.sender.DelayQueueSender
import com.facishare.marketing.provider.mq.sender.GroupSmsStatusSender
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.IntegralServiceManager
import com.facishare.marketing.provider.remote.rest.ShortUrlManager
import com.fxiaoke.crmrestapi.common.data.FieldDescribe
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Specification
import spock.lang.Unroll

class MwSendManagerSpec extends Specification {
    def trialSignatureId = "trialSignatureId"
    def host = "host"
    def shortLinkDomain = "domain"
    def templateDao = Mock(MwSmsTemplateDao)
    def signatureDao = Mock(MwSmsSignatureDao)
    def smsSendDao = Mock(MwSmsSendDao)
    def mwSmsTemplateDao = Mock(MwSmsTemplateDao)
    def fileV2Manager = Mock(FileV2Manager)
    def quotaManager = Mock(QuotaManager)
    def mwSmsManager = Mock(MwSmsManager)
    def sendService = Mock(SendService)
    def quotaDAO = Mock(QuotaDAO)
    def objectDescribeService = Mock(ObjectDescribeService)
    def liveManager = Mock(LiveManager)
    def marketingUserGroupManager = Mock(MarketingUserGroupManager)
    def conferenceManager = Mock(ConferenceManager)
    def smsChannelConfigDAO = Mock(SmsChannelConfigDAO)
    def mwAccountDao = Mock(MwAccountDao)
    def smsParamManager = Mock(SmsParamManager)
    def marketingUserGroupDao = Mock(MarketingUserGroupDao)
    def groupSmsStatusSender = Mock(GroupSmsStatusSender)
    def enterpriseSpreadRecordManager = Mock(EnterpriseSpreadRecordManager)
    def mwTemplateManager = Mock(MwTemplateManager)
    def miniProgramAuthManager = Mock(MiniProgramAuthManager)
    def mwSmsSendMarketingEventRelationDao = Mock(MwSmsSendMarketingEventRelationDao)
    def shortUrlManager = Mock(ShortUrlManager)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def smsTrialDao = Mock(SmsTrialDao)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def activityManager = Mock(ActivityManager)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def customizeMiniAuthorizeManager = Mock(CustomizeMiniAuthorizeManager)
    def wechatAccountManager = Mock(WechatAccountManager)
    def eieaConverter = Mock(EIEAConverter)
    def httpManager = Mock(HttpManager)
    def centerHost = "centerHost"
    def crmV2Manager = Mock(CrmV2Manager)
    def smsSendRecordObjManager = Mock(SmsSendRecordObjManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def metadataActionService = Mock(MetadataActionService)
    def delayQueueSender = Mock(DelayQueueSender)
    def integralServiceManager = Mock(IntegralServiceManager)
    def hostName = "hostname"

    def manager = new MwSendManager(
            trialSignatureId: trialSignatureId,
            host: host,
            shortLinkDomain: shortLinkDomain,
            templateDao: templateDao,
            signatureDao: signatureDao,
            smsSendDao: smsSendDao,
            mwSmsTemplateDao: mwSmsTemplateDao,
            fileV2Manager: fileV2Manager,
            quotaManager: quotaManager,
            mwSmsManager: mwSmsManager,
            sendService: sendService,
            objectDescribeService: objectDescribeService,
            quotaDAO: quotaDAO,
            liveManager: liveManager,
            marketingUserGroupManager: marketingUserGroupManager,
            conferenceManager: conferenceManager,
            smsChannelConfigDAO: smsChannelConfigDAO,
            mwAccountDao: mwAccountDao,
            smsParamManager: smsParamManager,
            marketingUserGroupDao: marketingUserGroupDao,
            groupSmsStatusSender: groupSmsStatusSender,
            enterpriseSpreadRecordManager: enterpriseSpreadRecordManager,
            mwTemplateManager: mwTemplateManager,
            miniProgramAuthManager: miniProgramAuthManager,
            mwSmsSendMarketingEventRelationDao: mwSmsSendMarketingEventRelationDao,
            shortUrlManager: shortUrlManager,
            marketingActivityExternalConfigDao: marketingActivityExternalConfigDao,
            smsTrialDao: smsTrialDao,
            activityManager: activityManager,
            userMarketingAccountManager: userMarketingAccountManager,
            campaignMergeDataManager: campaignMergeDataManager,
            campaignMergeDataDAO: campaignMergeDataDAO,
            customizeMiniAuthorizeManager: customizeMiniAuthorizeManager,
            wechatAccountManager: wechatAccountManager,
            eieaConverter: eieaConverter,
            httpManager: httpManager,
            centerHost: centerHost,
            crmV2Manager: crmV2Manager,
            smsSendRecordObjManager: smsSendRecordObjManager,
            crmMetadataManager: crmMetadataManager,
            metadataActionService: metadataActionService,
            eIEAConverter: eieaConverter,
            delayQueueSender: delayQueueSender,
            integralServiceManager: integralServiceManager,
            hostName: hostName
    )

    def "createSendTask"() {
        given:
        def arg = new CreateSendTaskArg(
                signatureId: sid,
                templateId: tid,
                templateEntity: new MwSmsTemplateEntity(),
                ea: "ea",
                channelType: 1,
                receiver: "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789011",
                taskType: 2,
                scheduleTime: st,
                groupType: gt,
                taPath: "123",
                phones: [new PhoneContentResult(phone: phone)],
                templateContentWithMarketingActivityId: "aaa"
        )

        signatureDao.getSignatureById(*_) >> null
        smsChannelConfigDAO.queryChannelConfig(*_) >> null
        signatureDao.getSignatureByEa(*_) >> new MwSmsSignatureEntity()
        templateDao.getTemplateById(*_) >> new MwSmsTemplateEntity()
        fileV2Manager.downloadAFile(*_) >> null
        smsSendDao.insertSendEntity(*_) >> true
        smsSendDao.batchInsertDetail(*_) >> true
        smsSendDao.updateSendEntityStatus(*_) >> true
        smsSendDao.updateSendEntity(*_) >> true
        quotaManager.reduceQuotaLeftCount(*_) >> false
        enterpriseSpreadRecordManager.filterAndUpsert(*_) >> fau
        quotaManager.calcSendSMSCount(*_) >> 1

        when:
        def result = manager.createSendTask(arg, isSyncSend, isSendType)

        then:
        with(result) {
            code == result.errCode
        }

        where:
        sid   | tid  | st         | isSyncSend | isSendType | gt   | phone                                          | fau                                              || code
        null  | "1"  | new Date() | false      | true       | 5    | "***********"                                  | ["***********"]                                  || 0
        "123" | null | null       | true       | true       | null | "123"                                          | null                                             || 60004
        null  | null | null       | true       | true       | null | "123"                                          | null                                             || 60025
        null  | "1"  | new Date() | true       | true       | 1    | "123"                                          | null                                             || 60032
        null  | "1"  | new Date() | true       | true       | 5    | "***********"                                  | ["***********"]                                  || 0
        null  | "1"  | new Date() | true       | true       | 5    | "12321312312312312312312312312312313213123123" | ["12321312312312312312312312312312313213123123"] || 60039 //isEmpty逻辑
        null  | "1"  | new Date() | false      | true       | 1    | "***********"                                  | ["***********"]                                  || 0
        null  | "1"  | new Date() | false      | true       | 5    | "12321312312312312312312312312312313213123123" | ["12321312312312312312312312312312313213123123"] || 0
    }

    def "updateSendTask"() {
        given:
        def arg = new CreateSendTaskArg(
                signatureId: sid,
                channelType: 1,
                ea: "ea",
                templateId: tid,
                templateEntity: new MwSmsTemplateEntity(),
                taskType: 2,
                scheduleTime: st,
                phones: phone
        )
        signatureDao.getSignatureById(*_) >> null
        smsChannelConfigDAO.queryChannelConfig(*_) >> null
        signatureDao.getSignatureByEa(*_) >> new MwSmsSignatureEntity()
        templateDao.getTemplateById(asType(String.class)) > null
        smsSendDao.getSMSSendById(*_) >> sendEntity
        smsSendDao.querySendDetailBySendId(*_) >> []

        def spy = Spy(manager)
        spy.updateTaskAndDetailToDb(*_) >> ud
        spy.createSendDetail(*_) >> cr

        when:
        def result = spy.updateSendTask(arg)

        then:
        with(result) {
            code == result.errCode
        }

        where:
        sid  | tid   | sendEntity                                       | st         | phone                      | ud    | cr                                                                                 || code
        "a"  | null  | null                                             | null       | null                       | true  | null                                                                               || 60004
        null | "123" | null                                             | null       | null                       | true  | null                                                                               || 60009
        null | null  | null                                             | null       | null                       | true  | null                                                                               || 60026
        null | null  | new MwSmsSendEntity(status: 1)                   | null       | null                       | true  | null                                                                               || 60028
        null | null  | new MwSmsSendEntity(status: 0)                   | null       | null                       | true  | null                                                                               || 60025
        null | null  | new MwSmsSendEntity(status: 0, templateId: null) | new Date() | null                       | true  | null                                                                               || -1
        null | null  | new MwSmsSendEntity(status: 0, templateId: "1")  | new Date() | null                       | false | null                                                                               || 0
        null | null  | new MwSmsSendEntity(status: 0, templateId: "1")  | new Date() | [new PhoneContentResult()] | false | null                                                                               || 60029
        null | null  | new MwSmsSendEntity(status: 0, templateId: "1")  | new Date() | [new PhoneContentResult()] | true  | null                                                                               || 60029
        null | null  | new MwSmsSendEntity(status: 0, templateId: "1")  | new Date() | [new PhoneContentResult()] | true  | new Result<List<MwSendDetailEntity>>(errCode: 2)                                   || 2
        null | null  | new MwSmsSendEntity(status: 0, templateId: "1")  | new Date() | [new PhoneContentResult()] | true  | new Result<List<MwSendDetailEntity>>(errCode: 0, data: [])                         || 60024
        null | null  | new MwSmsSendEntity(status: 0, templateId: "1")  | new Date() | [new PhoneContentResult()] | true  | new Result<List<MwSendDetailEntity>>(errCode: 0, data: [new MwSendDetailEntity()]) || 0
    }

    def "saveDetailAndUpdateSendEntity"() {
        given:
        List<MwSendDetailEntity> detailEntityList = Mock()
        detailEntityList.size() >> 2001

        smsSendDao.batchInsertDetail(*_) >> true
        smsSendDao.updateSendEntity(*_) >> true

        when:
        manager.saveDetailAndUpdateSendEntity(detailEntityList, new MwSmsSendEntity())

        then:
        noExceptionThrown()
    }

    def "saveTaskAndDetailToDb"() {
        given:
        List<MwSendDetailEntity> del = Mock()
        del.size() >> 2001

        smsSendDao.batchInsertDetail(*_) >> true
        templateDao.addMwTemplate(*_) >> true
        mwTemplateManager.uploadOrModifyTemplate(*_) >> new Result<String>(errCode: 0, data: "123")
        templateDao.setTemplateTplid(*_) >> {}

        when:
        manager.saveTaskAndDetailToDb(new MwSmsSendEntity(channelType: 6), del, new MwSmsTemplateEntity())

        then:
        noExceptionThrown()

    }

    def "saveSendEntityAndTemplateToDb"() {
        given:
        templateDao.addMwTemplate(*_) >> true
        mwTemplateManager.uploadOrModifyTemplate(*_) >> new Result<String>(errCode: 0, data: "123")
        templateDao.setTemplateTplid(*_) >> {}
        smsSendDao.insertSendEntity(*_) >> true

        when:
        def result = manager.saveSendEntityAndTemplateToDb(se, new MwSmsTemplateEntity())

        then:
        success == result

        where:
        se                                  || success
        null                                || false
        new MwSmsSendEntity(channelType: 6) || true
    }

    def "updateTaskAndDetailToDb"() {
        given:
        List<MwSendDetailEntity> del = Mock()
        del.size() >> 2001

        templateDao.addMwTemplate(*_) >> true
        mwTemplateManager.uploadOrModifyTemplate(*_) >> new Result<String>(errCode: 0, data: "123")
        templateDao.setTemplateTplid(*_) >> {}
        smsSendDao.updateSendEntity(*_) >> true
        smsSendDao.deleteSMSSendDetailById(*_) >> true
        smsSendDao.batchInsertDetail(*_) >> true

        when:
        def result = manager.updateTaskAndDetailToDb(se, del, new MwSmsTemplateEntity())

        then:
        success == result

        where:
        se                                  || success
        null                                || false
        new MwSmsSendEntity(channelType: 6) || true
    }

    def "createSendDetailFromConferenceInvite"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                groupType: 4,
                userGroupIds: ugi,
                ea: "1"
        )

        fileV2Manager.downloadAFile(*_) >> []
        activityManager.getActivityEntityByMarketingEventId(*_) >> aebmeOpt
        shortUrlManager.createShortUrl(*_) >> Optional.ofNullable("123")
        marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(*_) >> pn
        quotaManager.calcSendSMSCount(*_) >> 1
        marketingUserGroupManager.listMobileByMarketingUserGroupIds(*_) >> mids

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 3, content: "ctt"), arg, calc)

        then:
        result.errCode == code

        where:
        ugi   | aebmeOpt                                                                            | pn | calc  | mids                                                   || code
        []    | Optional.ofNullable(null)                                                           | 0  | true  | null                                                   || -1
        ["1"] | Optional.ofNullable(null)                                                           | 0  | true  | null                                                   || 80801
        ["1"] | Optional.ofNullable(new ActivityEntity())                                           | 0  | true  | null                                                   || 60036
        ["1"] | Optional.ofNullable(new ActivityEntity(startTime: new Date(), endTime: new Date())) | 1  | true  | null                                                   || 0
        ["1"] | Optional.ofNullable(new ActivityEntity(startTime: new Date(), endTime: new Date())) | 1  | false | [new MobileMarketingUserResult(mobile: "***********")] || 0

    }

    def "createSendDetailFromLiveInvite"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                userGroupIds: ugi,
                ea: "1",
                groupType: 8
        )

        liveManager.getMarketingLiveEntityByMarketingEventId(*_) >> mleOpt
        marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(*_) >> pn
        quotaManager.calcSendSMSCount(*_) >> 1
        marketingUserGroupManager.listMobileByMarketingUserGroupIds(*_) >> mids
        shortUrlManager.createShortUrl(*_) >> Optional.ofNullable("123")

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 5, content: "ctt"), arg, calc)

        then:
        result.errCode == code

        where:
        ugi   | mleOpt                                                                                   | pn | calc  | mids                                                   || code
        []    | null                                                                                     | 0  | true  | []                                                     || -1
        ["1"] | Optional.ofNullable(null)                                                                | 0  | true  | []                                                     || 80801
        ["1"] | Optional.ofNullable(new MarketingLiveEntity(startTime: new Date(), endTime: new Date())) | 0  | true  | []                                                     || 60036
        ["1"] | Optional.ofNullable(new MarketingLiveEntity(startTime: new Date(), endTime: new Date())) | 1  | true  | []                                                     || 0
        ["1"] | Optional.ofNullable(new MarketingLiveEntity(startTime: new Date(), endTime: new Date())) | 1  | false | []                                                     || 60036
        ["1"] | Optional.ofNullable(new MarketingLiveEntity(startTime: new Date(), endTime: new Date())) | 1  | false | [new MobileMarketingUserResult(mobile: "***********")] || 0

    }


    def "createSendDetailFromPhones"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                ea: "1",
                groupType: 7,
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********")]
        )
        def smsCtt = ""
        smsCtt.length() >> 10001

        def spy = Spy(manager)
        spy.buildSmsContent(*_) >> smsCtt

        quotaManager.calcSendSMSCount(*_) >> 1

        when:
        def result = spy.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 999, content: "ctt"), arg, true)

        then:
        noExceptionThrown()
    }

    def "calcSpendingQuotaInfoForUserGroup"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                ea: "1",
                groupType: 2,
                userGroupIds: ["1", "2", "3"],
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********")]
        )

        quotaManager.calcSendSMSCount(*_) >> 1
        marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(*_) >> tsc

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 999, content: "ctt"), arg, true)

        then:
        result.errCode == code

        where:
        tsc || code
        0   || 60036
        1   || 0
    }

    def "createSendDetailFromUserGroup"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                ea: ea,
                groupType: 2,
                userGroupIds: ugi,
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********")],
                channelType: 1
        )

        quotaManager.calcSendSMSCount(*_) >> 1
        marketingUserGroupManager.listMobileByMarketingUserGroupIds(*_) >> [new MobileMarketingUserResult(mobile: ""), new MobileMarketingUserResult(mobile: "***********")]
        enterpriseSpreadRecordManager.filterAndUpsert(*_) >> vps

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 999, content: "ctt"), arg, false)

        then:
        result.errCode == code

        where:
        ea  | ugi        | vps                                || code
        "1" | ["1", "2"] | []                                 || 7009
        "1" | ["1"]      | []                                 || 7009
        "1" | ["1"]      | ["***********", "***********", ""] || 0
        "1" | ["1"]      | ["", "", ""]                       || 60039
    }

    def "createSendDetailFromConferenceEnroll"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                ea: "1",
                groupType: 3,
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********")],
                channelType: 1,
                campaignIds: ["1", "2", "3"]
        )

        campaignMergeDataManager.campaignIdToActivityEnrollId(*_) >> []
        conferenceManager.buildPhoneContentEnrollList(*_) >> []

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 999, content: "ctt"), arg, false)

        then:
        noExceptionThrown()
    }

    def "createSendDetailFromLiveEnroll"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                ea: "1",
                groupType: 6,
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********")],
                channelType: 1,
                campaignIds: ["1", "2", "3"]
        )

        liveManager.buildLivePhoneContentList(*_) >> []

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 999, content: "ctt"), arg, false)

        then:
        noExceptionThrown()
    }

    def "createSendDetailFromMarketingActivity"() {
        given:

        def arg = new CreateSendTaskArg(
                longUrls: ["1"],
                templateContentWithMarketingActivityId: "123",
                ea: "1",
                groupType: 8,
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********")],
                channelType: 1,
                campaignIds: ["1", "2", "3"]
        )
        double start = 2321312.0d
        double end = 2321312.0d

        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(phone: "***********"), new CampaignMergeDataEntity(phone: "***********"), new CampaignMergeDataEntity(phone: "")]
        crmV2Manager.getDetail(*_) >> new ObjectData(["begin_time": start, "end_time": end])
        crmV2Manager.getObjectDataEnTextVal(*_) >> ["1": "2", "3": null]

        when:
        def result = manager.createSendDetail(new MwSmsSendEntity(), new MwSmsSignatureEntity(svrName: "svr"), new MwSmsTemplateEntity(sceneType: 999, content: "ctt"), arg, false)

        then:
        noExceptionThrown()
    }

    @Unroll
    def "createSendDetailCount"() {
        given:
        def arg = new CreateSendTaskArg(
                templateId: tid,
                groupType: gt,
                templateEntity: te,
                phones: [new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********"), new PhoneContentResult(phone: "***********7777777777"), new PhoneContentResult(phone: "")],
                channelType: ct,
                ea: "1",
                userGroupIds: ["1", "2", "3"],
                campaignIds: ["1", "2", "3"]
        )

        templateDao.getTemplateById(*_) >> new MwSmsTemplateEntity(sceneType: 3)
        marketingUserGroupDao.getTotalUserPhoneNumByMarketingUserGroupIds(*_) >> 1
        marketingUserGroupManager.listMobileByMarketingUserGroupIds(*_) >> [new MobileMarketingUserResult(mobile: "***********"), new MobileMarketingUserResult(mobile: ""), new MobileMarketingUserResult(mobile: "***********")]
        enterpriseSpreadRecordManager.filterAndUpsert(*_) >> vp
        campaignMergeDataManager.campaignIdToActivityEnrollId(*_) >> []
        conferenceManager.buildPhoneContentEnrollList(*_) >> [new PhoneContentResult()]
        liveManager.buildLivePhoneContentList(*_) >> [new PhoneContentResult()]
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> []

        when:
        def result = manager.createSendDetailCount(arg)

        then:
        result == count

        where:
        tid  | gt  | te                                      | ct | vp                                 || count
        "1"  | 4   | null                                    | 1  | []                                 || 1
        null | 666 | new MwSmsTemplateEntity(sceneType: 5)   | 1  | []                                 || 1
        null | 5   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | ["***********", "", "***********"] || 1
        null | 7   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | ["***********", "", "***********"] || 1
        null | 2   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | []                                 || 0
        null | 2   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | ["***********", "", "***********"] || 1
        null | 3   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | []                                 || 1
        null | 6   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | []                                 || 1
        null | 8   | new MwSmsTemplateEntity(sceneType: 9)   | 1  | []                                 || 0
        null | 666 | new MwSmsTemplateEntity(sceneType: 999) | 1  | []                                 || 0
    }

    def "reCreateSendDetailByTemplate"() {
        given:
        smsSendDao.querySendDetailBySendId(*_) >> [new MwSendDetailEntity(phone: "***********", params: ["aaa"])]
        quotaManager.getTemplateParamCnt(*_) >> tpc
        quotaManager.calcSendSMSCount(*_) >> 1

        when:
        def result = manager.reCreateSendDetailByTemplate(
                new MwSmsSendEntity(id: "1"),
                new MwSmsSignatureEntity(svrName: "svr"),
                new MwSmsTemplateEntity(content: "abcd{1}cda")
        )

        then:
        isNull == (result == null)

        where:
        tpc || isNull
        3    | true
        1    | false
    }

    def "handleContent"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        miniProgramAuthManager.generateScheme(*_) >> new Result<String>(errCode: 0)
        sendService.getShortUrl(*_) >> new Result<ShortUrlResult>(errCode: 0, data: new ShortUrlResult(shortUrl: "123"))
        crmV2Manager.getDetail(*_) >> new ObjectData(["a": od])
        crmV2Manager.getFieldDescribe(*_) >> new FieldDescribe(["type": type, "target_api_name": "api"])

        def pl = new SmsContentParamList()
        pl.add(new SmsContentParam(key: "a.a.b"))
        pl.add(new SmsContentParam(key: "a.c"))
        pl.add(new SmsContentParam(key: ".##cca", type: tp, value: vl))

        def spy = Spy(manager)
        spy.handleValue(*_) >> "aaa"

        when:
        def result = spy.handleContent(
                new MwSmsSendEntity(objectId: 1),
                new MwSmsTemplateEntity(
                        smsContentParam: pl,
                        content: "abcd\${a.a.b}cc\${a.c}vvvvv\${.##cca}rsefbfdbsdf\${adsdsadasd"
                ),
                new CreateSendTaskArg()
        )

        then:
        noExceptionThrown()

        where:
        tp      | od         | type               | vl
        "minip" | "aa"       | "object_reference" | "{}"
        "h5obj" | "aa"       | "object_reference" | "\${dataId}"
        "233"   | "aa"       | "object_reference" | "aaa"
        "233"   | "aa"       | "object_reference" | "aaa"
        "233"   | ["111"]    | "employee"         | "aaa"
        "233"   | ["-10000"] | "employee"         | "aaa"
    }

    def "handleValue"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData(["f": fv, "name": "name", "other__o": "aaa__o"])
        crmV2Manager.getFieldDescribe(*_) >> new FieldDescribe([
                "type"           : type,
                "target_api_name": "api",
                "options"        : [[label: "促销活动", value: "other"], [label: "促销活动2", value: "a"]]
        ])


        when:
        manager.handleValue("1", 1, "api", "oid", "f")

        then:
        noExceptionThrown()

        where:
        type               | fv
        "object_reference" | "aaa"
        "employee"         | ["-10000"]
        "employee"         | ["-123"]
        "select_one"       | "other"
        "select_one"       | "a"
        "select_many"      | ["other", "a"]
        "department"       | ["1"]
        "233"              | ["1"]
    }

    def "formatValue"() {

        when:
        manager.formatValue(tp, od, "f")

        then:
        noExceptionThrown()

        where:
        tp           | od
        "a"          | null
        "a"          | new ObjectData(["f": null])
        "date"       | new ObjectData(["f": 123456L])
        "date_time"  | new ObjectData(["f": 123456L])
        "time"       | new ObjectData(["f": 123456L])
        "percentile" | new ObjectData(["f": 1233.3d])
        "123123"     | new ObjectData(["f": "123"])
    }

    def "filterPhoneObjectUsingOneSMSDetailResultType"() {
        given:
        def pos = [
                new OneSMSDetailResult(mobile: ""),
                new OneSMSDetailResult(mobile: "***********"),
                new OneSMSDetailResult(mobile: "***********"),
                new OneSMSDetailResult(mobile: "***********77777777"),
        ]

        enterpriseSpreadRecordManager.getValidSendList(*_) >> ["***********"]
        enterpriseSpreadRecordManager.filterAndUpsert(*_) >> ["***********"]

        when:
        manager.filterPhoneObjectUsingOneSMSDetailResultType("1", 1, pos, 1, isCalc)

        then:
        a == 1

        where:
        isCalc || a
        true   || 1
        false  || 1
    }

    def "buildSendContent"() {
        when:
        manager.buildSendContent("aaa", "abcde", params)

        then:
        a == 1

        where:
        params || a
        []     || 1
        ["1"]  || 1
    }

    def "buildSmsContentBySignatureId"() {
        given:
        signatureDao.getSignatureById(*_) >> se

        when:
        manager.buildSmsContentBySignatureId(sid, "123123")

        then:
        noExceptionThrown()

        where:
        sid   | se
        null  | null
        "123" | null
        "123" | new MwSmsSignatureEntity(svrName: "svr")
    }

    def "buildSmsContentById"() {
        given:
        signatureDao.getSignatureById(*_) >> se
        templateDao.getTemplateById(*_) >> new MwSmsTemplateEntity()
        smsParamManager.getShowParamNameTemplate(*_) >> "s"

        when:
        manager.buildSmsContentById(sid, "tid")

        then:
        noExceptionThrown()

        where:
        sid   | se
        null  | null
        "123" | null
        "123" | new MwSmsSignatureEntity(svrName: "svr")
    }

    def "deleteSend"() {
        given:
        smsSendDao.deleteSMSSendById(*_) >> true
        smsSendDao.deleteSMSSendDetailById(*_) >> true

        when:
        manager.deleteSend("sms")

        then:
        1 == 1
    }

    @Unroll
    def "executeGroupSend"() {
        given:
        smsSendDao.updateSendFlag(*_) >> usf
        smsSendDao.updateSendTimeById(*_) >> 1
        templateDao.getTemplateById(*_) >> te
        signatureDao.getSignatureById(*_) >> sn

        def spy = Spy(manager)
        spy.multiSend(*_) >> true
        spy.batchSend(*_) >> true

        when:
        def result = spy.executeGroupSend(se)

        then:
        success == result

        where:
        se                                                                 | usf   | te                               | sn                         || success
        null                                                               | false | null                             | null                       || false
        new MwSmsSendEntity(type: 2, scheduleTime: new Date(12313123123L)) | false | null                             | null                       || false
        new MwSmsSendEntity(type: 6, scheduleTime: new Date(12313123123L)) | true  | null                             | null                       || false
        new MwSmsSendEntity(type: 6, scheduleTime: new Date(12313123123L)) | true  | new MwSmsTemplateEntity()        | null                       || false
        new MwSmsSendEntity(type: 6, scheduleTime: new Date(12313123123L)) | true  | new MwSmsTemplateEntity(type: 2) | new MwSmsSignatureEntity() || false
        new MwSmsSendEntity(type: 6, scheduleTime: new Date(12313123123L)) | true  | new MwSmsTemplateEntity(type: 1) | new MwSmsSignatureEntity() || false
    }

    def "batchSend"() {
        given:
        mwAccountDao.queryAccount(*_) >> new MwAccountEntity()
        smsSendDao.querySendDetailCountBySendId(*_) >> 1
        smsSendDao.pageQuerySendDetailBySendId(*_) >> del
        smsSendDao.updateSendEntityStatus(*_) >> true
        groupSmsStatusSender.send(*_) >> {}
        smsSendDao.updateSendEntityStatus(*_) >> true
        mwSmsManager.batchSend(*_) >> new SendRequestResult()

        when:
        def result = manager.batchSend(
                new MwSmsSendEntity(ea: "1", id: "1"),
                new MwSmsSignatureEntity(svrName: "svr"),
                new MwSmsTemplateEntity(content: "ctt", sceneType: 3)
        )

        then:
        success == result

        where:
        del                        || success
        []                         || false
        [new MwSendDetailEntity()] || false
    }

    def "doTrackSmsLink"() {
        given:
        shortUrlManager.getShortUrl2LongUrlMapV2(*_) >> map
        marketingActivityExternalConfigDao.getByEaAndAssociateMsg(*_) >> new MarketingActivityExternalConfigEntity(marketingActivityId: "1")
        mwSmsSendMarketingEventRelationDao.getEntityBySendId(*_) >> new MwSmsSendMarketingEventRelationEntity(marketingEventId: "1")
        wechatAccountManager.getNotEmptyWxAppIdByEa(*_) >> "1"
        wechatAccountManager.getAccessTokenByWxAppId(*_) >> "asdsa"
        shortUrlManager.batchGetGeneratedWxAppUrl(*_) >> ["l?&marketingActivityId=1&ph233one=***********&marketingEventId=1&sceneType=9&sceneId=1": "2131312"]
        customizeMiniAuthorizeManager.createMiniAppForwardUrl(*_) >> new CreateMiniAppForwardUrlResult(urlLink: "123")
        shortUrlManager.batchCreateShortUrlV2(*_) >> new BatchShortUrlResult(shortUrlMapping: ["1": "2"])

        when:
        manager.doTrackSmsLink("1", [new MwSendDetailEntity(content: "123", sendId: "1", phone: "***********")])

        then:
        a == 1

        where:
        map                                            || a
        null                                           || 1
        ["https://wxaurl.cns": "l?", "domainss": "ll"] || 1
    }

    def "updateSendResult"() {
        given:
        smsSendDao.updateSendResult(*_) >> true
        quotaDAO.increaseQuotaLeftCount(*_) >> true
        smsSendDao.updateSendDetailStatus(*_) >> true
        signatureDao.getSignatureById(*_) >> new MwSmsSignatureEntity(ea: "1")
        mwSmsSendMarketingEventRelationDao.getEntityBySendId(*_) >> new MwSmsSendMarketingEventRelationEntity(marketingEventId: "1")
        templateDao.getTemplateById(*_) >> new MwSmsTemplateEntity(name: "name")
        smsSendRecordObjManager.createObj(*_) >> {}

        def task1 = new MwSendManager.SendRequestTask()
        task1.setDetailEntityList([new MwSendDetailEntity(fee: 1)])

        def task2 = new MwSendManager.SendRequestTask()
        task2.setRequestResult(new SendRequestResult(result: 1))
        task2.setDetailEntityList([new MwSendDetailEntity(fee: 1)])

        def task3 = new MwSendManager.SendRequestTask()
        task3.setRequestResult(new SendRequestResult(result: 0))
        task3.setDetailEntityList([new MwSendDetailEntity(fee: 1)])

        def tasks = [task1, task2, task3]

        when:
        def result = manager.updateSendResult(
                new MwSmsSendEntity(actualSenderCount: 6, ea: "1", templateId: "1", totalFee: 233, channelType: 7),
                tasks
        )

        then:
        1 == 1
    }

    def "reduceQuotaAndSend"() {
        given:
        def sendEntity = new MwSmsSendEntity(
                signatureId: "123",
                type: tp,
                totalFee: 123
        )

        signatureDao.getSignatureById(*_) >> new MwSmsSignatureEntity(ea: "1")
        quotaManager.reduceQuotaLeftCount(*_) >> rd
        smsSendDao.updateSendEntityStatus(*_) >> true

        when:
        boolean result = manager.reduceQuotaAndSend(sendEntity)

        then:
        success == result

        where:
        rd    | tp || success
        false | 2  || false
        true  | 2  || true
        true  | 1  || true
    }

    def "initDetailTemplateIdAndChannelType"() {
        given:
        smsSendDao.countBySpecialTime(*_) >> 2

        smsSendDao.scanBySpecialTime(*_) >> [new MwSmsSendEntity(id: "1"), new MwSmsSendEntity(id: "2", channelType: 2)]
        smsSendDao.countTemplateAndChannelTypeIsNull(*_) >> 1
        smsSendDao.updateDetailTemplateIdAndChannelType(*_) >> 1

        when:
        manager.initDetailTemplateIdAndChannelType("1")

        then:
        noExceptionThrown()
    }

    def "fixSmsSendRecordObjSendStatus"() {
        given:
        smsSendDao.countBySpecialTime(*_) >> 2
        smsSendDao.scanBySpecialTime(*_) >> [new MwSmsSendEntity(id: "1", ea: "1"), new MwSmsSendEntity(id: "2", channelType: 2, ea: "1")]
        smsSendDao.getSendDetailBySmsSendIdAndStatus2Page(*_) >> [new MwSendDetailEntity()]

        when:
        manager.fixSmsSendRecordObjSendStatus("1")

        then:
        noExceptionThrown()
    }

    def "queryMwTemplateStatus"() {
        given:
        mwSmsTemplateDao.querySyncTemplateEntity(*_) >> te
        mwTemplateManager.queryTemplateStatus(*_) >> rs
        mwSmsTemplateDao.updateTemplateNameAndContent(*_) >> true
        mwSmsTemplateDao.updateTemplateStatusAndReply(*_) >> true

        when:
        manager.queryMwTemplateStatus()

        then:
        a == 1

        where:
        te                                                            | rs                                                                                                        || a
        []                                                            | null                                                                                                      || 1
        [new MwSmsTemplateEntity()]                                   | null                                                                                                      || 1
        [new MwSmsTemplateEntity(tplid: "1")]                         | new Result<Map<String, TemplateQueryDetail>>(errCode: 1)                                                  || 1
        [new MwSmsTemplateEntity(tplid: "1", parentId: "1", id: "2")] | new Result<Map<String, TemplateQueryDetail>>(errCode: 0, data: ["1": new TemplateQueryDetail(status: 0)]) || 1
    }

    def "saveSendIdAndMarketingEventToDB"() {
        given:
        mwSmsSendMarketingEventRelationDao.addMwSmsSendMarketingEventRelation(*_) >> true

        when:
        def result = manager.saveSendIdAndMarketingEventToDB(
                new MwSmsSendEntity(id: i),
                "1",
                new GroupSenderArg(marketingEventId: "1")
        )

        then:
        succ == result

        where:
        i    || succ
        null || false
        "1"  || true
    }

    @Unroll
    def "cancelSendSms"() {
        given:
        smsSendDao.getSMSSendById(*_) >> se
        smsSendDao.updateSendFlag(*_) >> sf
        quotaManager.increaseQuotaLeftCount(*_) >> iqc
        smsSendDao.updateSendEntity(*_) >> use
        smsSendDao.batchUpdateSendDetailStatusBySendId(*_) >> 1

        when:
        def result = manager.cancelSendSms("1", "1")

        then:
        //def e = thrown(NullPointerException)
        1 == 1

        where:
        se                                                     | sf    | iqc   | use
        null                                                   | false | false | false
        new MwSmsSendEntity(type: 2, status: 3, totalFee: 123) | false | false | false
        new MwSmsSendEntity(type: 2, status: 3, totalFee: 123) | true  | false | false
        new MwSmsSendEntity(type: 2, status: 3, totalFee: 123) | true  | true  | false
        new MwSmsSendEntity(type: 2, status: 3, totalFee: 123) | true  | true  | true
    }

    def "getSignatureOrTrial null"() {
        when:
        manager.getSignatureOrTrial(null, null, null)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getSignatureOrTrial"() {
        given:
        signatureDao.getSignatureById(*_) >> new MwSmsSignatureEntity(status: 1)
        smsTrialDao.querySmsTrial(*_) >> te
        smsChannelConfigDAO.queryChannelConfig(*_) >> new SmsChannelConfigEntity(signatureId: "1")

        when:
        manager.getSignatureOrTrial("1", sid, 1)
        then:
        noExceptionThrown()

        where:
        sid  | entity | te
        "1"  | null   | null
        null | null   | new SmsTrialEntity()

    }

    def "multiSend"() {
        given:
        mwAccountDao.queryAccount(*_) >> new MwAccountEntity()
        smsSendDao.querySendDetailCountBySendId(*_) >> 1
        smsSendDao.pageQuerySendDetailBySendId(*_) >> del
        smsSendDao.updateSendEntityStatus(*_) >> true
        groupSmsStatusSender.send(*_) >> {}
        shortUrlManager.getShortUrl2LongUrlMapV2(*_) >> null
        smsSendDao.updateSendEntityStatus(*_) >> true
        mwSmsManager.multiSend(*_) >> new SendRequestResult()


        when:
        def result = manager.multiSend(
                new MwSmsSendEntity(channelType: 1, ea: "1", id: "1"),
                new MwSmsSignatureEntity(svrName: "svr"),
                new MwSmsTemplateEntity(sceneType: 5)
        )

        then:
        success == result

        where:
        del                                                             || success
        []                                                              || false
        [new MwSendDetailEntity(phone: "***********", content: "cttt")] || false
    }

    @Unroll
    def "getRpt"() {
        given:
        mwAccountDao.queryAccountByStatus(*_) >> ael
        mwSmsManager.getRpt(*_) >> rr
        smsSendDao.getSMSSendById(*_) >> null

        when:
        def result = manager.getRpt(100)

        then:
        result == success

        where:
        ael                     | rr                                                                                                                                                       || success
        []                      | null                                                                                                                                                     || false
        [new MwAccountEntity()] | null                                                                                                                                                     || true
        [new MwAccountEntity()] | new ReportResults(result: 1)                                                                                                                             || true
        [new MwAccountEntity()] | new ReportResults(result: 0, rpts: [])                                                                                                                   || true
        [new MwAccountEntity()] | new ReportResults(result: 0, rpts: [new ReportResult()])                                                                                                 || true
        [new MwAccountEntity()] | new ReportResults(result: 0, rpts: [new ReportResult(custid: "id", mobile: "***********")])                                                              || true
        [new MwAccountEntity()] | new ReportResults(result: 0, rpts: [new ReportResult(custid: "1_213", mobile: "***********"), new ReportResult(custid: "1_212", mobile: "***********")]) || true
    }

    def "computeRptResult"() {
        given:
        smsSendDao.updateSendDetailErrStatus(*_) >> true
        smsSendDao.updateSendDetailStatus(*_) >> true
        smsSendDao.updateSendResultByPullRpt(*_) >> true
        quotaDAO.increaseQuotaLeftCount(*_) >> true

        when:
        manager.computeRptResult(
                ["1": new MwSmsSendEntity(ea: "1", rebackFee: 1)],
                ["1": new MwSendDetailEntity(id: "1", failCount: 2, status: 3)],
                ["1": [new ReportResult(status: 0), new ReportResult(status: 1), new ReportResult(status: 0)]]
        )

        then:
        noExceptionThrown()
    }

    def "updateSmsSendRecordObjStatusToFail"() {
        given:
        def arg = new MwSendDetailUpdateMqArg(
                ea: "1",
                sendDetailEntityList: [new MwSendDetailEntity(sendId: "1", id: "1")]
        )

        smsSendDao.getSMSSendById(*_) >> sse
        crmMetadataManager.listV3(*_) >> new InnerPage<ObjectData>(dataList:
                [
                        new ObjectData(["send_detail_id": ""]),
                        new ObjectData(["send_detail_id": "2", "send_status": "failure"]),
                        new ObjectData(["send_detail_id": "1"])
                ]
        )
        eieaConverter.enterpriseAccountToId(*_) >> 1
        metadataActionService.edit(*_) >> {}
        signatureDao.getSignatureById(*_) >> new MwSmsSignatureEntity(ea: "1")

        when:
        manager.updateSmsSendRecordObjStatusToFail(arg)

        then:
        a == 1

        where:
        sse                   || a
        null                  || 1
        new MwSmsSendEntity() || 1

    }
}
