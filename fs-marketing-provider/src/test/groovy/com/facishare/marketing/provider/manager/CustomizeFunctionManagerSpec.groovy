package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import spock.lang.Specification

class CustomizeFunctionManagerSpec extends Specification{
    def eieaConverter = Mock(EIEAConverter)

    def manager = new CustomizeFunctionManager(
            eieaConverter: eieaConverter,
            customizeFunctionUrl: "http://172.31.100.247:4859"
    )

    def "executeCustomizeFunction"(){
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1

        when:
        manager.executeCustomizeFunction("1", "1", 1)

        then:
        noExceptionThrown()
    }
}
