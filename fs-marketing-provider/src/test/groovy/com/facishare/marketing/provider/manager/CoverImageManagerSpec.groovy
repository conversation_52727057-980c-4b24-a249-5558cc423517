package com.facishare.marketing.provider.manager

import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.provider.dao.ActivityDAO
import com.facishare.marketing.provider.dao.ArticleDAO
import com.facishare.marketing.provider.dao.ProductDAO
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.ArticleEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.ProductEntity
import com.facishare.marketing.provider.manager.image.ImageCreator
import com.facishare.marketing.provider.manager.image.material.MaterialPoster
import spock.lang.Specification

class CoverImageManagerSpec extends Specification {
    def fileV2Manager = Mock(FileV2Manager)
    def productDAO = Mock(ProductDAO)
    def articleDAO = Mock(ArticleDAO)
    def activityDAO = Mock(ActivityDAO)
    def photoManager = Mock(PhotoManager)
    def imageCreator = Mock(ImageCreator)

    def imageDrawer = Mock(MaterialPoster)

    def manager = new CoverImageManager(
            fileV2Manager: fileV2Manager,
            productDAO: productDAO,
            articleDAO: articleDAO,
            activityDAO: activityDAO,
            photoManager: photoManager,
            imageCreator: imageCreator
    )

    def "createMaterialPoster"() {
        given:
        fileV2Manager.downloadAFile(*_) >> qrb
        productDAO.queryProductDetail(*_) >> new ProductEntity(name: "name", summary: "s")
        photoManager.queryPhoto(*_) >> [new PhotoEntity(path: "path")]
        articleDAO.queryArticleDetail(*_) >> new ArticleEntity(title: "t", content: "ctt")
        activityDAO.getById(*_) >> new ActivityEntity(title: "t", startTime: new Date(), endTime: new Date(), location: "l")
        imageCreator.getImageDrawer(*_) >> imageDrawer
        imageDrawer.draw(*_) >> null

        when:
        manager.createMaterialPoster(ote, "1", "1", "1")

        then:
        a == 1

        where:
        ote                     | qrb    || a
        null                    | []     || 1
        ObjectTypeEnum.PRODUCT  | []     || 1
        ObjectTypeEnum.PRODUCT  | [1, 2] || 1
        ObjectTypeEnum.ARTICLE  | [1, 2] || 1
        ObjectTypeEnum.ACTIVITY | [1, 2] || 1

    }

    def "createLuckyMoneyIconCoverAsync"() {

        when:
        manager.createLuckyMoneyIconCoverAsync(ote, "1")
        imageCreator.getImageDrawer(*_) >> imageDrawer
        imageDrawer.draw(*_) >> draw
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> false
        articleDAO.queryArticleDetail(*_) >> ae
        photoManager.queryPhoto(*_) >> pel
        productDAO.queryProductDetail(*_) >> qpe

        then:
        a == 1

        where:
        ote                                                       | ae                  | pel                          | draw   | qpe                 || a
        null                                                      | null                | null                         | null   | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.CARD    | null                | null                         | "draw" | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.ARTICLE | null                | null                         | "draw" | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.ARTICLE | new ArticleEntity() | null                         | "draw" | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.ARTICLE | new ArticleEntity() | [new PhotoEntity()]          | "draw" | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.ARTICLE | new ArticleEntity() | [new PhotoEntity(path: "p")] | null   | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.ARTICLE | new ArticleEntity() | [new PhotoEntity(path: "p")] | "draw" | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.PRODUCT | new ArticleEntity() | [new PhotoEntity(path: "p")] | "draw" | null                || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.PRODUCT | new ArticleEntity() | []                           | "draw" | new ProductEntity() || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.PRODUCT | new ArticleEntity() | [new PhotoEntity()]          | "draw" | new ProductEntity() || 1
        com.facishare.mankeep.common.enums.ObjectTypeEnum.PRODUCT | new ArticleEntity() | [new PhotoEntity(path: "p")] | null   | new ProductEntity() || 1

    }

    def "createCardShareCoverAsync"() {
        given:
        imageCreator.getImageDrawer(*_) >> imageDrawer
        imageDrawer.draw(*_) >> "draw"
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> false

        when:
        manager.createCardShareCoverAsync("1", "1")

        then:
        1 == 1

    }

}
