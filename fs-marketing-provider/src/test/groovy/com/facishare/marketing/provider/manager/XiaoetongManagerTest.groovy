package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.result.live.XiaoetongAccessTokenResult
import com.facishare.marketing.api.result.live.XiaoetongAccountResult
import com.facishare.marketing.api.result.live.XiaoetongApiBaseResult
import com.facishare.marketing.api.result.live.XiaoetongLiveListResult
import com.facishare.marketing.api.vo.live.BindXiaoetongAccountVO
import com.facishare.marketing.api.vo.live.ListVO
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.common.typehandlers.value.FieldMappings
import com.facishare.marketing.common.util.DateUtil
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils
import com.facishare.marketing.outapi.service.ClueDefaultSettingService
import com.facishare.marketing.provider.crowd.manager.TargetCrowdOperationManager
import com.facishare.marketing.provider.crowd.utils.RepeatDateUtil
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.live.LiveUserAccountRelationDAO
import com.facishare.marketing.provider.dao.live.LiveUserStatusDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO
import com.facishare.marketing.provider.dao.live.XiaoetongAccountDAO
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignWxDTO
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics
import com.facishare.marketing.provider.entity.live.XiaoetongAccountEntity
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity
import com.facishare.marketing.provider.innerData.live.LoginedUserInfoInnerData
import com.facishare.marketing.provider.innerData.live.XiaoetongLiveListInnerData
import com.facishare.marketing.provider.innerData.live.XiaoetongLiveRecordByLiveData
import com.facishare.marketing.provider.innerData.live.XiaoetongLoginInnerData
import com.facishare.marketing.provider.innerData.live.XiaoetongLoginedUserRedirectResult
import com.facishare.marketing.provider.innerData.live.xiaoetong.XiaoetongGetLiveOverviewData
import com.facishare.marketing.provider.innerData.live.xiaoetong.XiaoetongUserData
import com.facishare.marketing.provider.innerData.live.xiaoetong.result.XiaoetongApiGetUserResult
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager
import com.facishare.marketing.provider.manager.qr.QRCodeManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo
import com.fxiaoke.crmrestapi.common.data.HeaderObj
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException
import com.fxiaoke.crmrestapi.result.ActionAddResult
import com.fxiaoke.crmrestapi.service.MetadataActionService
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import jetbrick.util.DateUtils
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.*

/**
 * Test for XiaoetongManager
 * <AUTHOR>
 * @date 2024/4/24 14:52
 */
/**
 * <AUTHOR>
 * @Date 2024/4/24
 * @Desc
 * */
/*@RunWith(PowerMockRunner.class)//PowerMock配置
@PowerMockRunnerDelegate(Sputnik.class)//PowerMock集成Spock配置
@PrepareForTest([ThreadPoolUtils.class])//PowerMock配置
@PowerMockIgnore("javax.net.ssl.*")*/
class XiaoetongManagerTest extends Specification {

    def xiaoetongManager = new XiaoetongManager()

    def httpManager = Mock(HttpManager)
    def redisManager = Mock(RedisManager)
    def xiaoetongAccountDAO = Mock(XiaoetongAccountDAO)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def eieaConverter = Mock(EIEAConverter)
    def marketingLiveStatisticsDAO = Mock(MarketingLiveStatisticsDAO)
    def liveUserAccountRelationDAO = Mock(LiveUserAccountRelationDAO)
    def liveUserStatusDAO = Mock(LiveUserStatusDAO)
    def browserUserRelationManager = Mock(BrowserUserRelationManager)
    def actionManager = Mock(ActionManager)
    def marketingPluginConfigDAO = Mock(MarketingPluginConfigDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def clueDefaultSettingService = Mock(ClueDefaultSettingService)
    def metadataActionService = Mock(MetadataActionService)
    def marketingStatLogPersistorManger = Mock(MarketingStatLogPersistorManger)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def marketingLeadSyncRecordObjManager = Mock(MarketingLeadSyncRecordObjManager)

    def setup() {
        xiaoetongManager.httpManager = httpManager
        xiaoetongManager.redisManager = redisManager
        xiaoetongManager.xiaoetongAccountDAO = xiaoetongAccountDAO
        xiaoetongManager.marketingLiveDAO = marketingLiveDAO
        xiaoetongManager.eieaConverter = eieaConverter
        xiaoetongManager.marketingLiveStatisticsDAO = marketingLiveStatisticsDAO
        xiaoetongManager.liveUserAccountRelationDAO = liveUserAccountRelationDAO
        xiaoetongManager.liveUserStatusDAO = liveUserStatusDAO
        xiaoetongManager.browserUserRelationManager = browserUserRelationManager
        xiaoetongManager.actionManager = actionManager
        xiaoetongManager.marketingPluginConfigDAO = marketingPluginConfigDAO
        xiaoetongManager.crmV2Manager = crmV2Manager
        xiaoetongManager.clueDefaultSettingService = clueDefaultSettingService
        xiaoetongManager.metadataActionService = metadataActionService
        xiaoetongManager.marketingStatLogPersistorManger = marketingStatLogPersistorManger
        xiaoetongManager.campaignMergeDataManager = campaignMergeDataManager
        xiaoetongManager.campaignMergeDataDAO = campaignMergeDataDAO
        xiaoetongManager.marketingLeadSyncRecordObjManager = marketingLeadSyncRecordObjManager

        //PowerMockito.mockStatic(ThreadPoolUtils.class)
    }

    @Unroll
    def "bindXiaoketongAccountTest"() {
        given:
        xiaoetongAccountDAO.insert(*_) >> 0
        xiaoetongAccountDAO.getByEa(*_) >> entityResult
        xiaoetongAccountDAO.update(*_) >> 0

        expect:
        xiaoetongManager.bindXiaoketongAccount(vo) == expectedResult

        where:
        vo                           || expectedResult || entityResult
        new BindXiaoetongAccountVO() || new Result<Void>(0, "成功", null) || new XiaoetongAccountEntity()
        new BindXiaoetongAccountVO() || new Result<Void>(0, "成功", null) || null
    }

    @Unroll
    def "getXiaoetongAccountTest"() {
        given:
        xiaoetongAccountDAO.getByEa(*_) >> entityResult

        expect:
        xiaoetongManager.getXiaoetongAccount(ea) == expectedResult

        where:
        ea   || expectedResult || entityResult
        "ea" || new Result<XiaoetongAccountResult>(0, "成功", new XiaoetongAccountResult(isBind: true)) || new XiaoetongAccountEntity()
        "ea" || new Result<XiaoetongAccountResult>(0, "成功", new XiaoetongAccountResult(isBind: false)) || null
    }

    @Unroll
    def "getMarketingLiveByXiaoetongIdTest"() {
        given:
        marketingLiveDAO.getMarketingLiveByXiaoetongId(*_) >> entityResult

        expect:
        xiaoetongManager.getMarketingLiveByXiaoetongId(xiaoetongId) == expectedResult

        where:
        xiaoetongId   || expectedResult || entityResult
        "xiaoetongId" || Optional.of(new MarketingLiveEntity()) || new MarketingLiveEntity()
        "xiaoetongId" || Optional.empty() || null
    }

    @Unroll
    def "testApiTest"() {
        given:
        httpManager.executeGetHttp(*_) >> httpResult
        redisManager.setXiaoetongAccessToken(*_) >> true
        xiaoetongAccountDAO.getByEa(*_) >> entityResult

        expect:
        xiaoetongManager.testApi(ea) == expectedResult

        where:
        ea   || expectedResult                                          || entityResult                 || httpResult
        "ea" || Result.newError(SHErrorCode.UN_BIND_XIAOETONG_ACCOUNT)  || null                         || null
        "ea" || Result.newError(SHErrorCode.XIAOETONG_NOT_BIND_ACCOUNT) || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data")
        "ea" || Result.newError(SHErrorCode.XIAOETONG_NOT_BIND_ACCOUNT) || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: null)
        "ea" || Result.newError(SHErrorCode.XIAOETONG_NOT_BIND_ACCOUNT) || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongAccessTokenResult(accessToken: null))
        "ea" || Result.newSuccess() || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongAccessTokenResult(accessToken: "accessToken"))
    }
    @Unroll
    def "getAccessTokenTest"() {
        given:
        httpManager.executeGetHttp(*_) >> httpResult
        redisManager.lock(*_) >> lockResult
        redisManager.unLock(*_) >> true
        redisManager.getXiaoetongAccessToken(*_) >> redisAccessToken
        redisManager.setXiaoetongAccessToken(*_) >> true

        expect:
        xiaoetongManager.getAccessToken(ea, appId, appSecret, clientId) == expectedResult

        where:
        clientId   | appId   | appSecret   | ea   || expectedResult || redisAccessToken || lockResult || httpResult
        "clientId" | "appId" | "appSecret" | null || null || null || true || null
        "clientId" | null | "appSecret" | "ea" || null || null || true || null
        "clientId" | "appId" | null | "ea" || null || null || true || null
        "clientId" | "appId" | "appSecret" | "ea" || Optional.of("accessToken") || "accessToken" || true || null
        "clientId" | "appId" | "appSecret" | "ea" || Optional.empty() || null || false || null
        "clientId" | "appId" | "appSecret" | "ea" || Optional.empty() || null || true || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data")
        "clientId" | "appId" | "appSecret" | "ea" || Optional.empty() || null || true || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: "data")
        "clientId" | "appId" | "appSecret" | "ea" || Optional.empty() || null || true || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongAccessTokenResult(accessToken: null))
        "clientId" | "appId" | "appSecret" | "ea" || Optional.of("accessToken") || null || true || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongAccessTokenResult(accessToken: "accessToken"))
        //"clientId" | "appId" | "appSecret" | "ea" || Optional.of("accessToken") || null || true || null
    }

    @Unroll
    def "getAccessTokenByEaTest"() {
        given:
        xiaoetongAccountDAO.getByEa(*_) >> entityResult

        expect:
        xiaoetongManager.getAccessTokenByEa(ea) == expectedResult

        where:
        ea   || expectedResult || entityResult
        null || null || null
        "ea" || null || null
        "ea" || null || new XiaoetongAccountEntity()
    }

    def method1(){
        return Result.newSuccess(new PageResult<com.facishare.marketing.api.result.live.XiaoetongLiveListResult>(totalCount: 0, result: Lists.newArrayList(), pageNum: 1, pageSize: 10));
    }

    def method2(){
        return new XiaoetongLiveListInnerData(list: Lists.newArrayList(new XiaoetongLiveListInnerData.liveListEntity()));
    }

    @Unroll
    def "getLiveListTest"() {

        given:
        httpManager.executePostHttp(*_) >> postResult
        httpManager.executeGetHttp(*_) >> null
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        redisManager.getXiaoetongAccessToken(*_) >> null
        redisManager.setXiaoetongAccessToken(*_) >> true
        xiaoetongAccountDAO.getByEa(*_) >> accountResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessToken(*_) >> tokenResult

        expect:
        spy.getLiveList(vo) == expectedResult

        where:
        vo           || expectedResult || accountResult || postResult || tokenResult
        new ListVO(pageNum: 1, pageSize: 10) || method1() || null || null || null
        new ListVO(pageNum: 1, pageSize: 10) || Result.newError(SHErrorCode.GET_XIAOETONG_ACCESS_TOKEN_FAILED) || new XiaoetongAccountEntity() || null || Optional.empty()
        new ListVO(pageNum: 1, pageSize: 10) || Result.newError(SHErrorCode.LIST_XIAOKETONG_LIVE_FAILED) || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data") || Optional.of("accessToken")
        new ListVO(pageNum: 1, pageSize: 10) || method1() || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongLiveListInnerData(list: null, page: 1, pageCount: 10)) || Optional.of("accessToken")
        new ListVO(pageNum: 1, pageSize: 10) || Result.newSuccess(new PageResult<com.facishare.marketing.api.result.live.XiaoetongLiveListResult>(result: Lists.newArrayList(new XiaoetongLiveListResult()), pageNum: 0, pageSize: 0, totalCount: 0)) || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: method2()) || Optional.of("accessToken")
    }

    @Unroll
    def "loginXiaoetongUserTest"() {
        given:
        httpManager.executePostHttp(*_) >> httpResult
        xiaoetongAccountDAO.getByEa(*_) >> entityResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessToken(*_) >> tokenResult

        expect:
        spy.loginXiaoetongUser(ea, phone, name) == expectedResult

        where:
        phone   | name   | ea   || expectedResult || tokenResult || entityResult || httpResult
        "phone" | "name" | "ea" || Optional.empty() || Optional.of("accessToken") || null || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: "data")
        "phone" | "name" | "ea" || Optional.empty() || Optional.empty() || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: "data")
        "phone" | "name" | "ea" || Optional.empty() || Optional.of("accessToken") || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data")
        "phone" | "name" | "ea" || Optional.of("1") || Optional.of("accessToken") || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongLoginInnerData(userId: "1"))
    }

    @Unroll
    def "getLoginedUserInfoTest"() {
        given:
        httpManager.executePostHttp(*_) >> httpResult
        xiaoetongAccountDAO.getByEa(*_) >> accountResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessToken(*_) >> tokenResult

        expect:
        spy.getLoginedUserInfo(ea, phone) == expectedResult

        where:
        phone   | ea   || expectedResult || tokenResult || accountResult || httpResult
        "phone" | "ea" || Optional.empty() || null || null || null
        "phone" | "ea" || Optional.empty() || Optional.empty() || new XiaoetongAccountEntity() || null
        "phone" | "ea" || Optional.empty() || Optional.of("accessToken") || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: new LoginedUserInfoInnerData())
        "phone" | "ea" || Optional.of("1") || Optional.of("accessToken") || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new LoginedUserInfoInnerData(userId: "1"))
    }

    @Unroll
    def "queryUserLoginRedirectTest"() {
        given:
        httpManager.executePostHttp(*_) >> httpResult
        xiaoetongAccountDAO.getByEa(*_) >> accountResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessToken(*_) >> tokenResult

        expect:
        spy.queryUserLoginRedirect(ea, userId, redirectUrl) == expectedResult

        where:
        redirectUrl   | ea   | userId   || expectedResult || accountResult || tokenResult || httpResult
        "redirectUrl" | "ea" | "userId" || Optional.empty() || null || null || null
        "redirectUrl" | "ea" | "userId" || Optional.empty() || new XiaoetongAccountEntity() || Optional.empty() || null
        "redirectUrl" | "ea" | "userId" || Optional.empty() || new XiaoetongAccountEntity() || Optional.of("accessToken") || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data")
        "redirectUrl" | "ea" | "userId" || Optional.of("url") || new XiaoetongAccountEntity() || Optional.of("accessToken") || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongLoginedUserRedirectResult(loginUrl: "url"))
    }

    @Unroll
    def "getLiveRecordDataByLiveNewTest"() {
        given:
        httpManager.executePostHttp(*_) >> httpResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessTokenByEa(*_) >> tokenResult

        expect:
        spy.getLiveRecordDataByLiveNew(ea, liveId) == expectedResult

        where:
        ea   | liveId   || expectedResult || tokenResult || httpResult
        "ea" | "liveId" || null || Optional.empty() || null
        "ea" | "liveId" || new XiaoetongLiveRecordByLiveData() || Optional.of("accessToken") || null
        "ea" | "liveId" || new XiaoetongLiveRecordByLiveData() || Optional.of("accessToken") || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data")
        "ea" | "liveId" || new XiaoetongLiveRecordByLiveData(total: 1, list: [new XiaoetongLiveRecordByLiveData.RecordByLiveInnerData()]) || Optional.of("accessToken") || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongLiveRecordByLiveData(total: 1, list: [new XiaoetongLiveRecordByLiveData.RecordByLiveInnerData()]))
    }

    @Unroll
    def "getUsersByApiV2Test"() {
        given:
        httpManager.executePostHttp(*_) >> httpResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessTokenByEa(*_) >> tokenResult
        //PowerMockito.when(ThreadPoolUtils.execute(Mockito.any(), Mockito.any())) >> void

        expect:
        spy.getUsersByApiV2(ea, userIds, xiaoetongLiveId) == expectedResult

        where:
        userIds     | ea   | xiaoetongLiveId   || expectedResult || tokenResult || httpResult
        [] | "ea" | "xiaoetongLiveId" || null || Optional.empty() || null
        ["1"] | "ea" | "xiaoetongLiveId" || null || Optional.empty() || null
        //["1"] | "ea" | "xiaoetongLiveId" || Lists.newCopyOnWriteArrayList() || Optional.of("accessToken") || null
        //["1"] | "ea" | "xiaoetongLiveId" || Lists.newCopyOnWriteArrayList() || Optional.of("accessToken") || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: "data")
        //["1"] | "ea" | "xiaoetongLiveId" || Lists.newCopyOnWriteArrayList() || Optional.of("accessToken") || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongApiGetUserResult())
    }

    @Unroll
    def "getLiveOverviewByApiTest"() {
        given:
        httpManager.executePostHttp(*_) >> httpResult
        xiaoetongAccountDAO.getByEa(*_) >> accountResult
        def spy = Spy(xiaoetongManager)
        spy.getAccessToken(*_) >> tokenResult

        expect:
        spy.getLiveOverviewByApi(ea, liveId, type) == expectedResult

        where:
        ea   | type   | liveId   || expectedResult || accountResult || httpResult || tokenResult
        "ea" | "type" | "liveId" || null || null || null || null
        "ea" | "type" | "liveId" || null || new XiaoetongAccountEntity() || null || Optional.empty()
        "ea" | "type" | "liveId" || null || new XiaoetongAccountEntity() || null || Optional.of("accessToken")
        "ea" | "type" | "liveId" || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: new XiaoetongGetLiveOverviewData())  || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 1, msg: "errMsg", data: new XiaoetongGetLiveOverviewData()) || Optional.of("accessToken")
        "ea" | "type" | "liveId" || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongGetLiveOverviewData()) || new XiaoetongAccountEntity() || new XiaoetongApiBaseResult(code: 0, msg: "errMsg", data: new XiaoetongGetLiveOverviewData()) || Optional.of("accessToken")
    }

    @Unroll
    def "syncXiaoetongLiveStatusByEaTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 1
        marketingLiveDAO.getNeedSyncXiaoetongLiveCountByCorpId(*_) >> countResult
        marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(*_) >> 1
        marketingLiveDAO.updateXiaoetongLiveStatus(*_) >> { printf "111"}
        def spy = Spy(xiaoetongManager)
        spy.getLiveList(*_) >>> liveListResult

        expect:
        spy.syncXiaoetongLiveStatusByEa(ea)

        where:
        ea   || expectedResult || countResult || liveListResult
        "ea" || true || 0 || Result.newSuccess(new PageResult<>(totalCount: 1, result: [new XiaoetongLiveListResult()]))
        "ea" || true || 1 || Result.newSuccess(null)
        "ea" || true || 1 || Result.newSuccess(new PageResult<>(totalCount: 1, result: Lists.newArrayList()))
        "ea" || true || 1 || [Result.newSuccess(new PageResult<>(totalCount: 2, result: [new XiaoetongLiveListResult(aliveState: null)])), Result.newSuccess(null)]
        "ea" || true || 1 || [Result.newSuccess(new PageResult<>(totalCount: 2, result: [new XiaoetongLiveListResult(aliveState: null)])), Result.newSuccess(new PageResult<>(result: null))]
        "ea" || true || 1 || [Result.newSuccess(new PageResult<>(totalCount: 2, result: [new XiaoetongLiveListResult(aliveState: 1)])), Result.newSuccess(new PageResult<>(result: [new XiaoetongLiveListResult(aliveState: 2)]))]
        "ea" || true || 1 || Result.newSuccess(new PageResult<>(totalCount: 3, result: [new XiaoetongLiveListResult(aliveState: 0), new XiaoetongLiveListResult(aliveState: 1), new XiaoetongLiveListResult(aliveState: 2)]))
    }

    @Unroll
    def "syncXiaoetongLiveDataByIdNewTest"() {
        given:
        httpManager.executePostHttp(*_) >> null
        httpManager.executeGetHttp(*_) >> null
        redisManager.lock(*_) >> true
        redisManager.unLock(*_) >> true
        redisManager.getXiaoetongAccessToken(*_) >> "getXiaoetongAccessTokenResponse"
        redisManager.setXiaoetongAccessToken(*_) >> true
        xiaoetongAccountDAO.getByEa(*_) >> new XiaoetongAccountEntity()
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> new MarketingLiveEntity(createTime: new Date(System.currentTimeMillis()))
        eieaConverter.enterpriseAccountToId(*_) >> 0
        marketingLiveStatisticsDAO.getByXiaoetongLiveId(*_) >> [new MarketingLiveStatistics()]
        marketingLiveStatisticsDAO.updateViewTimesAndViewUsers(*_) >> 0
        liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveId(*_) >> [new LiveUserStatusEntity(phone: "123", outerUserId: "1", viewTime: 10)]
        liveUserStatusDAO.batchInsert(*_) >> 0
        liveUserStatusDAO.batchUpdate(*_) >> 0
        browserUserRelationManager.getOrCreateBrowserUserIdByPhone(*_) >> null
        marketingPluginConfigDAO.queryMarketingPluginFieldMap(*_) >> new MarketingPluginConfigEntity()
        crmV2Manager.getObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.addCampaignMembersObjByLock(*_) >> "addCampaignMembersObjByLockResponse"
        crmV2Manager.leadDuplicateSearchByObjectV2(*_) >> new Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult())
        clueDefaultSettingService.getClueCreator(*_) >> 0
        metadataActionService.add(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()
        campaignMergeDataManager.addCampaignDataOnlyUnLock(*_) >> "addCampaignDataOnlyUnLockResponse"
        campaignMergeDataManager.getPhoneByObject(*_) >> "getPhoneByObjectResponse"
        campaignMergeDataDAO.updateCampaignMergeDataOuterUserId(*_) >> 0
        campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(*_) >> [new CampaignMergeDataEntity()]
        campaignMergeDataDAO.getPhoneByOpenIds(*_) >> [new CampaignWxDTO(openId: "openId4", phone: "999")]
        def spy = Spy(xiaoetongManager)
        spy.getLiveRecordDataByLiveNew(*_) >> liveRecordResult
        spy.getUsersByApiV2(*_) >> [new XiaoetongUserData(userId: "1", wxOpenId: "openId1", phone: "123"), new XiaoetongUserData(userId: "2", wxOpenId: "openId2", collectPhone: "456"),
                                    new XiaoetongUserData(wxOpenId: "openId3", bindPhone: "789"), new XiaoetongUserData(wxOpenId: "openId4") ]
        spy.getLiveOverviewByApi(*_) >> new XiaoetongApiBaseResult(code: 0, msg: "msg", data: new XiaoetongGetLiveOverviewData(stats: new XiaoetongGetLiveOverviewData.Stats(hisLearnedTimes: new XiaoetongGetLiveOverviewData.StatsInnerData())))

        expect:
        spy.syncXiaoetongLiveDataByIdNew(ea, marketingEventId, xiaoetongLiveId)

        where:
        marketingEventId   | ea   | xiaoetongLiveId || liveRecordResult
        "marketingEventId" | "ea" | "xiaoetongLiveId" || null
        "marketingEventId" | "ea" | "xiaoetongLiveId" || new XiaoetongLiveRecordByLiveData(list: [])
        "marketingEventId" | "ea" | "xiaoetongLiveId" || new XiaoetongLiveRecordByLiveData(list: [new XiaoetongLiveRecordByLiveData.RecordByLiveInnerData(userId: "1", stayTime: "10"), new XiaoetongLiveRecordByLiveData.RecordByLiveInnerData(userId: "2", stayTime: "10")])
    }

    @Shared
    def objData = new ObjectData()
    @Shared
    def resultMap = Maps.newHashMap();

    @Unroll
    def "campaignMergeDataEntityToCampaignMergeObjMapTest"() {
        given:
        objData.put("company", "大冲")
        objData.put("name", "大冲2")

        resultMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());
        resultMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getApiName());
        resultMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), null);
        resultMap.put(CampaignMembersObjApiNameEnum.LEADS_ID.getApiName(), null);
        resultMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), "大冲");
        resultMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), "大冲2");

        crmV2Manager.getDetail(*_) >> objectResult

        expect:
        xiaoetongManager.campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity) == expectedResult

        where:
        campaignMergeDataEntity       | ea   || expectedResult || objectResult
        new CampaignMergeDataEntity(bindCrmObjectType: 4) | "ea" || Maps.newHashMap() || null
        new CampaignMergeDataEntity(bindCrmObjectType: 0) | "ea" || Maps.newHashMap() || null
        new CampaignMergeDataEntity(bindCrmObjectType: 0) | "ea" || resultMap || objData
    }

    @Unroll
    def "syncCampaignMemberTest"() {
        given:
        crmV2Manager.getDetail(*_) >> crmV2ManagerResult
        crmV2Manager.addCampaignMembersObjByLock(*_) >> "addCampaignMembersObjByLockResponse"
        campaignMergeDataManager.addCampaignDataOnlyUnLock(*_) >> "addCampaignDataOnlyUnLockResponse"
        campaignMergeDataManager.getPhoneByObject(*_) >> "123"
        campaignMergeDataDAO.updateCampaignMergeDataOuterUserId(*_) >> 0
        campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(*_) >> campaignResultList

        expect:
        xiaoetongManager.syncCampaignMember(ea, leadId, marketingEventId, outerUserId, addCampaignMember, phone) == expectedResult

        where:
        marketingEventId   | phone   | outerUserId   | addCampaignMember | ea   | leadId   || expectedResult || campaignResultList || crmV2ManagerResult
        "marketingEventId" | "phone" | "outerUserId" | Boolean.TRUE      | "ea" | "leadId" || "1" || [new CampaignMergeDataEntity(id: "1")] || "crmV2ManagerResult"
        "marketingEventId" | "phone" | "outerUserId" | Boolean.TRUE      | "ea" | "leadId" || null || [] || null
        "marketingEventId" | "phone" | "outerUserId" | Boolean.TRUE      | "ea" | "leadId" || null || [] || new ObjectData()
    }

    @Unroll
    def "createHeaderObjTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> eieaResult

        expect:
        xiaoetongManager.createHeaderObj(ea, userId) == expectedResult

        where:
        ea   | userId || expectedResult || eieaResult
        "ea" | null   || new HeaderObj(1, -10000) || 1
    }

    @Shared
    def map = ["_id":"1"]

    @Unroll
    def "saveLeadsToCrmTest"() {
        given:
        eieaConverter.enterpriseAccountToId(*_) >> 0
        //marketingStatLogPersistorManger.sendLeadData(*_) >> void
        marketingPluginConfigDAO.queryMarketingPluginFieldMap(*_) >> pluginResult
        crmV2Manager.getObjectFieldDescribesList(*_) >> [new CrmUserDefineFieldVo()]
        crmV2Manager.getDetail(*_) >> new ObjectData()
        crmV2Manager.addCampaignMembersObjByLock(*_) >> "addCampaignMembersObjByLockResponse"
        crmV2Manager.leadDuplicateSearchByObjectV2(*_) >> duplicateResult
        clueDefaultSettingService.getClueCreator(*_) >> 0
        metadataActionService.add(*_) >> addResult
        campaignMergeDataManager.addCampaignDataOnlyUnLock(*_) >> "addCampaignDataOnlyUnLockResponse"
        campaignMergeDataManager.getPhoneByObject(*_) >> "getPhoneByObjectResponse"
        campaignMergeDataDAO.updateCampaignMergeDataOuterUserId(*_) >> 0
        campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(*_) >> campaignMergeDataResult

        expect:
        xiaoetongManager.saveLeadsToCrm(data, ea, marketingEventId)

        where:
        data                    | marketingEventId   | ea    || pluginResult || duplicateResult || campaignMergeDataResult || addResult
        new XiaoetongUserData() | "marketingEventId" | "ea"  || null || null || null || new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()
        new XiaoetongUserData() | "marketingEventId" | "ea"  || new MarketingPluginConfigEntity(crmFormFieldMap: FieldMappings.newInstance(Lists.newArrayList(new FieldMappings.FieldMapping(crmFieldName: "name")))) || new Result<CrmV2Manager.LeadDuplicateSearchResult>(1, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult()) || null || new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()
        new XiaoetongUserData(phone: "123") | "marketingEventId" | "ea"  || new MarketingPluginConfigEntity(crmFormFieldMap: FieldMappings.newInstance(Lists.newArrayList(new FieldMappings.FieldMapping(crmFieldName: "name")))) || new Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult(leadId: "1", duplicate: false)) || null || new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()
        new XiaoetongUserData(phone: "123") | "marketingEventId" | "ea"  || new MarketingPluginConfigEntity(crmFormFieldMap: FieldMappings.newInstance(Lists.newArrayList(new FieldMappings.FieldMapping(crmFieldName: "name")))) || new Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult(leadId: "1", duplicate: true)) || null || new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>()
        new XiaoetongUserData(phone: "123") | "marketingEventId" | "ea"  || new MarketingPluginConfigEntity(crmFormFieldMap: FieldMappings.newInstance(Lists.newArrayList(new FieldMappings.FieldMapping(crmFieldName: "name")))) || new Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult(leadId: "1", duplicate: true)) || null || new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: 1, message: "errMsg", data: new ActionAddResult(objectData: new ObjectData()))
        new XiaoetongUserData(phone: "123") | "marketingEventId" | "ea"  || new MarketingPluginConfigEntity(crmFormFieldMap: FieldMappings.newInstance(Lists.newArrayList(new FieldMappings.FieldMapping(crmFieldName: "name")))) || new Result<CrmV2Manager.LeadDuplicateSearchResult>(0, "errMsg", new CrmV2Manager.LeadDuplicateSearchResult(leadId: "1", duplicate: true)) || [new CampaignMergeDataEntity(id: "1")] || new com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult>(code: 0, data: new ActionAddResult(objectData: map))
    }

    @Unroll
    def "doHandleUserMergedMessageTest"() {
        given:
        xiaoetongAccountDAO.getByAppId(*_) >> entityResult
        liveUserAccountRelationDAO.updateOuterUserId(*_) >> 0

        expect:
        xiaoetongManager.doHandleUserMergedMessage(appId, sourceUserId, targetUserId)

        where:
        appId   | targetUserId   | sourceUserId   || entityResult
        "appId" | "targetUserId" | "sourceUserId" || null
        "appId" | "targetUserId" | "sourceUserId" || [new XiaoetongAccountEntity()]
    }

    @Unroll
    def "isXiaoetongUpgradedLiveTest"() {
        given:
        marketingLiveDAO.getByCorpIdAndMarketingEventId(*_) >> liveResult

        expect:
        xiaoetongManager.isXiaoetongUpgradedLive(ei, marketingEventId) == expectedResult

        where:
        ei | marketingEventId   || expectedResult || liveResult
        0  | "marketingEventId" || false || null
        0  | "marketingEventId" || true || new MarketingLiveEntity(platform: 3, createTime: new Date(System.currentTimeMillis() - 10 * 24 * 60 * 60 * 1000))
        0  | "marketingEventId" || false || new MarketingLiveEntity(platform: 3, createTime: DateUtil.parse("2019-01-01 00:00:00"))
    }

}