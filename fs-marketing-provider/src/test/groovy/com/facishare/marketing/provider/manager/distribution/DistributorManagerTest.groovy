package com.facishare.marketing.provider.manager.distribution

import com.facishare.marketing.api.result.distribution.QueryDistributorInfoResult
import com.facishare.marketing.api.result.distribution.QueryDistributorRecruitResult
import com.facishare.marketing.api.vo.QueryDistributorRecruitVO
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.DistributorFormDataEnroll
import com.facishare.marketing.provider.dao.CardDAO
import com.facishare.marketing.provider.dao.distribution.ClueDAO
import com.facishare.marketing.provider.dao.distribution.DistributePlanGradeDao
import com.facishare.marketing.provider.dao.distribution.DistributorDao
import com.facishare.marketing.provider.dao.distribution.DistributorFormSubmitDao
import com.facishare.marketing.provider.dao.distribution.OperatorDistributorDAO
import com.facishare.marketing.provider.dto.distribution.DistributorClueCountDTO
import com.facishare.marketing.provider.dto.distribution.QueryDistributorByOperatorDTO
import com.facishare.marketing.provider.dto.distribution.QueryOperatorByDistributorIdDTO
import com.facishare.marketing.provider.entity.CardEntity
import com.facishare.marketing.provider.entity.UserEntity
import com.facishare.marketing.provider.entity.distribution.DistributePlanGradeEntity
import com.facishare.marketing.provider.entity.distribution.DistributorBaseInfoResult
import com.facishare.marketing.provider.entity.distribution.DistributorEntity
import com.facishare.marketing.provider.entity.distribution.DistributorFormSubmitEntity
import com.facishare.marketing.provider.entity.distribution.OperatorDistributorEntity
import com.facishare.marketing.provider.manager.user.UserManager
import com.google.common.collect.Maps
import spock.lang.*

/**
 * Test for DistributorManager
 * <AUTHOR>
 * @date 2024/7/4 15:46
 */
class DistributorManagerTest extends Specification {

    def distributorManager = new DistributorManager()

    def distributorDao = Mock(DistributorDao)
    def clueDAO = Mock(ClueDAO)
    def cardDAO = Mock(CardDAO)
    def userManager = Mock(UserManager)
    def operatorDistributorDAO = Mock(OperatorDistributorDAO)
    def formSubmitDao = Mock(DistributorFormSubmitDao)
    def distributePlanGradeDao = Mock(DistributePlanGradeDao)

    def setup() {
        distributorManager.distributorDao = distributorDao
        distributorManager.clueDAO = clueDAO
        distributorManager.cardDAO = cardDAO
        distributorManager.userManager = userManager
        distributorManager.operatorDistributorDAO = operatorDistributorDAO
        distributorManager.formSubmitDao = formSubmitDao
        distributorManager.distributePlanGradeDao = distributePlanGradeDao
    }

    @Unroll
    def "queryDistributeInfoByOperatorTest"() {
        given:
        distributorDao.queryDistributorByIdAndOperorId(*_) >> new QueryDistributorByOperatorDTO(recruitId:distributorId )
        distributorDao.queryValidRecruitorsCount(*_) >> 0
        distributorDao.getDistributorByIds(*_) >> [new DistributorEntity()]
        clueDAO.queryClueCountByDistributorIds(*_) >> 0
        cardDAO.listByUids(*_) >> [new CardEntity()]
        userManager.listByUids(*_) >> [new UserEntity()]
        formSubmitDao.querySubmitDataByDistributorIds(*_) >> [new DistributorFormSubmitEntity()]
        distributePlanGradeDao.queryPlanGradesByPlanId(*_) >> [new DistributePlanGradeEntity()]

        def spr = Spy(distributorManager)
        def map = Maps.newHashMap()
        map.put(distributorId,new DistributorBaseInfoResult())
        spr.getDistributorFormDataMap(*_) >> map
        expect:
        spr.queryDistributeInfoByOperator(operatorId, distributorId) == expectedResult

        where:
        distributorId   | operatorId   || expectedResult
        "distributorId" | "operatorId" || new Result<QueryDistributorInfoResult>(0, "errMsg", new QueryDistributorInfoResult())
    }

    @Unroll
    def "getFormSubmitDataMapTest"() {
        given:
        formSubmitDao.querySubmitDataByDistributorIds(*_) >> [new DistributorFormSubmitEntity()]

        expect:
        distributorManager.getFormSubmitDataMap(distributorIds) == expectedResult

        where:
        distributorIds     || expectedResult
        ["distributorIds"] || ["expectedResult": new DistributorFormDataEnroll()]
    }

    @Unroll
    def "getFormSubmitEntityTest"() {
        given:
        formSubmitDao.querySubmitDataByDistributorId(*_) >> new DistributorFormSubmitEntity()

        expect:
        distributorManager.getFormSubmitEntity(distributorId) == expectedResult

        where:
        distributorId   || expectedResult
        "distributorId" || new DistributorFormDataEnroll()
    }

    @Unroll
    def "queryDistributorRecruitTest"() {
        given:
        distributorDao.getDistributorByIds(*_) >> [new DistributorEntity()]
        distributorDao.queryRecruitorsByDistributorId(*_) >> [new DistributorEntity(createTime: new Date(),id: "111",status: 1)]
        clueDAO.queryClueCountGroupByDistributors(*_) >> [new DistributorClueCountDTO(distributorId: "111",clueCount: 0)]
        cardDAO.listByUids(*_) >> [new CardEntity()]
        userManager.listByUids(*_) >> [new UserEntity()]
        operatorDistributorDAO.getOperatorIdByDistributorId(*_) >> new OperatorDistributorEntity()
        operatorDistributorDAO.queryOperatorByDistributorIds(*_) >> [new QueryOperatorByDistributorIdDTO(distributorId: "111",status: 1)]
        formSubmitDao.querySubmitDataByDistributorIds(*_) >> [new DistributorFormSubmitEntity()]
        distributePlanGradeDao.queryPlanGradesByPlanId(*_) >> [new DistributePlanGradeEntity()]

        def spr = Spy(distributorManager)
        def map = Maps.newHashMap()
        map.put("111",new DistributorBaseInfoResult())
        spr.getDistributorFormDataMap(*_) >> map
        expect:
        spr.queryDistributorRecruit(vo) == expectedResult

        where:
        vo                              || expectedResult
        new QueryDistributorRecruitVO(pageSize: 10,pageNum: 1) || new Result<PageResult<QueryDistributorRecruitResult>>(0, "errMsg", new PageResult<QueryDistributorRecruitResult>())
    }

    @Unroll
    def "getDistributorFormDataMapTest"() {
        given:
        distributorDao.getDistributorByIds(*_) >> [new DistributorEntity()]
        cardDAO.listByUids(*_) >> [new CardEntity()]
        userManager.listByUids(*_) >> [new UserEntity()]
        formSubmitDao.querySubmitDataByDistributorIds(*_) >> [new DistributorFormSubmitEntity()]
        distributePlanGradeDao.queryPlanGradesByPlanId(*_) >> [new DistributePlanGradeEntity()]

        expect:
        distributorManager.getDistributorFormDataMap(planId, distributorIds, distributors) == expectedResult

        where:
        distributors              | distributorIds     | planId   || expectedResult
        [new DistributorEntity()] | ["distributorIds"] | "planId" || ["expectedResult": new DistributorBaseInfoResult()]
    }

}