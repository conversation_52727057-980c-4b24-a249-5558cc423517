package com.facishare.marketing.provider.manager

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.conference.CheckSignInStatusArg
import com.facishare.marketing.api.arg.conference.QueryActivityEnrollTimeArg
import com.facishare.marketing.api.arg.conference.SignInArg
import com.facishare.marketing.api.arg.kis.RecordActionArg
import com.facishare.marketing.api.service.hexagon.HexagonService
import com.facishare.marketing.api.service.kis.KisActionService
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService
import com.facishare.marketing.common.enums.ObjectTypeEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.common.util.DateUtil
import com.facishare.marketing.outapi.service.MarketingFlowInstanceOuterService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO
import com.facishare.marketing.provider.dao.conference.ConferenceDAO
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationDAO
import com.facishare.marketing.provider.dao.conference.ConferenceTagDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO
import com.facishare.marketing.provider.dao.qr.QRPosterDAO
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity
import com.facishare.marketing.provider.entity.ActivityEnrollTimeConfigEntity
import com.facishare.marketing.provider.entity.ActivityEntity
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity
import com.facishare.marketing.provider.entity.ConferenceTagEntity
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.entity.qr.QRPosterEntity
import com.facishare.marketing.provider.manager.conference.ConferenceManager
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.qr.QRCodeManager
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager
import com.facishare.marketing.provider.mq.sender.DelayQueueSender
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.google.common.collect.Maps
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

class ActivityManagerSpec extends Specification {

    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def activityDAO = Mock(ActivityDAO)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def activityEnrollDataDAO = Mock(ActivityEnrollDataDAO)
    def qrPosterDAO = Mock(QRPosterDAO)
    def conferenceInvitationDAO = Mock(ConferenceInvitationDAO)
    def activityManager = Mock(ActivityManager)
    def qrCodeManager = Mock(QRCodeManager)
    def photoManager = Mock(PhotoManager)
    def marketingFlowInstanceOuterService = Mock(MarketingFlowInstanceOuterService)
    def photoAssociationDAO = Mock(PhotoAssociationDAO)
    def conferenceManager = Mock(ConferenceManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def customerCustomizeFormDataManager = Mock(CustomerCustomizeFormDataManager)
    def kisActionService = Mock(KisActionService)
    def campaignMergeDataDAO = Mock(CampaignMergeDataDAO)
    def campaignMergeDataManager = Mock(CampaignMergeDataManager)
    def browserUserRelationManager = Mock(BrowserUserRelationManager)
    def conferenceDAO = Mock(ConferenceDAO)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def conferenceTagDAO = Mock(ConferenceTagDAO)
    def userMarketingAccountService = Mock(UserMarketingAccountService)
    def marketingSceneDao = Mock(MarketingSceneDao)
    def sceneTriggerDao = Mock(SceneTriggerDao)
    def marketingLiveDAO = Mock(MarketingLiveDAO)
    def eieaConverter = Mock(EIEAConverter)
    def sceneTriggerTimedTaskDao = Mock(SceneTriggerTimedTaskDao)
    def triggerTaskInstanceDao = Mock(TriggerTaskInstanceDao)
    def triggerInstanceDao = Mock(TriggerInstanceDao)
    def hexagonService = Mock(HexagonService)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def objectManager = Mock(ObjectManager)
    def activityEnrollTimeConfigDAO = Mock(ActivityEnrollTimeConfigDAO)
    def userMarketingAccountAssociationManager = Mock(UserMarketingAccountAssociationManager)
    def delayQueueSender = Mock(DelayQueueSender)
    def multipleVenuesHexagonSiteId = ""


    def activityManagerTest = new ActivityManager(
            customizeFormDataUserDAO: customizeFormDataUserDAO,
            customizeFormDataManager: customizeFormDataManager,
            activityDAO: activityDAO,
            marketingActivityExternalConfigDao: marketingActivityExternalConfigDao,
            activityEnrollDataDAO: activityEnrollDataDAO,
            qrPosterDAO: qrPosterDAO,
            activityManager: activityManager,
            conferenceInvitationDAO: conferenceInvitationDAO,
            qrCodeManager: qrCodeManager,
            photoManager: photoManager,
            marketingFlowInstanceOuterService: marketingFlowInstanceOuterService,
            photoAssociationDAO: photoAssociationDAO,
            conferenceManager: conferenceManager,
            crmV2Manager: crmV2Manager,
            customerCustomizeFormDataManager: customerCustomizeFormDataManager,
            kisActionService: kisActionService,
            campaignMergeDataDAO: campaignMergeDataDAO,
            campaignMergeDataManager: campaignMergeDataManager,
            browserUserRelationManager: browserUserRelationManager,
            conferenceDAO: conferenceDAO,
            contentMarketingEventMaterialRelationDAO: contentMarketingEventMaterialRelationDAO,
            hexagonPageDAO: hexagonPageDAO,
            conferenceTagDAO: conferenceTagDAO,
            userMarketingAccountService: userMarketingAccountService,
            marketingSceneDao: marketingSceneDao,
            sceneTriggerDao: sceneTriggerDao,
            marketingLiveDAO: marketingLiveDAO,
            eieaConverter: eieaConverter,
            sceneTriggerTimedTaskDao: sceneTriggerTimedTaskDao,
            triggerTaskInstanceDao: triggerTaskInstanceDao,
            triggerInstanceDao: triggerInstanceDao,
            hexagonService: hexagonService,
            hexagonSiteDAO: hexagonSiteDAO,
            objectManager: objectManager,
            activityEnrollTimeConfigDAO: activityEnrollTimeConfigDAO,
            userMarketingAccountAssociationManager: userMarketingAccountAssociationManager,
            multipleVenuesHexagonSiteId: multipleVenuesHexagonSiteId,
            delayQueueSender: delayQueueSender
    )


    @Unroll
    def "getActivityEntityByMarketingEventId"() {
        given:
        activityDAO.getActivityEntityByMarketingEventId(*_) >> null
        when:
        activityManagerTest.getActivityEntityByMarketingEventId("88146", marketingEventId)
        then:
        noExceptionThrown()
        where:
        marketingEventId << [null, "eventId"]
    }

    @Unroll
    def "buildActivityEnrollDataSourceType"() {
        given:
        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> marketingActivityExternalConfigEntity
        when:
        activityManagerTest.buildActivityEnrollDataSourceType(customizeFormDataUserEntity, qrSourceType, qrSourceId)
        then:
        noExceptionThrown()
        where:
        customizeFormDataUserEntity                                | qrSourceType | qrSourceId | marketingActivityExternalConfigEntity
        null                                                       | 1            | "id"       | null
        new CustomizeFormDataUserEntity(marketingActivityId: "id") | null         | null       | new MarketingActivityExternalConfigEntity(associateIdType: 15)
        new CustomizeFormDataUserEntity(marketingActivityId: "id") | null         | null       | new MarketingActivityExternalConfigEntity(associateIdType: 17)
        new CustomizeFormDataUserEntity(marketingActivityId: "id") | null         | null       | new MarketingActivityExternalConfigEntity(associateIdType: 1001)
        new CustomizeFormDataUserEntity(uid: "id")                 | null         | null       | new MarketingActivityExternalConfigEntity(associateIdType: 1001)
        new CustomizeFormDataUserEntity()                          | null         | null       | new MarketingActivityExternalConfigEntity(associateIdType: 1001)
    }

    @Unroll
    def "activitySignIn"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        delayQueueSender.sendByObj(*_) >> { printf "dd" }
        campaignMergeDataDAO.getCampaignMergeDataByPhoneLike(*_) >> [new CampaignMergeDataEntity(id: "id")]
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> [new CampaignMergeDataEntity(id: "id")]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110"))]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110"))]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFsUserInfoAndEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110"))]
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> [new CampaignMergeDataEntity(id: "id")]
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> activityEnrollDataEntityList
        def spy = Spy(activityManagerTest)
        spy.handleConferenceTag(*_) >> { printf "ddd" }
        spy.handleActivitySignInOtherProcess(*_) >> { printf "ddd" }
        spy.handleConferenceTag(*_) >> { printf "ddd" }
        campaignMergeDataManager.updateSignInStatus(*_) >> { printf "ddd" }
        when:
        spy.activitySignIn(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                              | activityEntity                                                                    | activityEnrollDataEntityList
        new SignInArg(id: null)                                                          | null                                                                              | null
        new SignInArg(id: "id")                                                          | null                                                                              | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.minusDay(new Date(), 1))                     | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 2)           | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 3)           | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId", delaySingIn: true) | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId", phone: "110")      | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 0)]
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2)]
        new SignInArg(id: "id", openId: "openId", wxAppId: "wxAppId")                    | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new SignInArg(id: "id", fingerPrint: "finger", phone: "110")                     | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new SignInArg(id: "id", fingerPrint: "finger")                                   | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new SignInArg(id: "id", fingerPrint: "finger")                                   | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 0)]
        new SignInArg(id: "id", fingerPrint: "finger")                                   | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2)]
        new SignInArg(id: "id", fingerPrint: "finger")                                   | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new SignInArg(id: "id", enrollUserFsUid: 1)                                      | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | null
        new SignInArg(id: "id", enrollUserFsUid: 1)                                      | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | null
        new SignInArg(id: "id", enrollUserFsUid: 1)                                      | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 0)]
        new SignInArg(id: "id", enrollUserFsUid: 1)                                      | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2)]
        new SignInArg(id: "id", enrollUserFsUid: 1)                                      | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new SignInArg(id: "id")                                                          | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
    }

    @Unroll
    def "handleConferenceTag"() {
        given:
        conferenceTagDAO.queryById(*_) >> conferenceTagEntity
        userMarketingAccountService.batchAddTagNamesToCrmData(*_) >> new Result<Void>(errCode: 0)
        campaignMergeDataDAO.getCampaignMergeDataByIds(*_) >> [new CampaignMergeDataEntity(bindCrmObjectType: 1, bindCrmObjectId: "id")]
        when:
        activityManagerTest.handleConferenceTag(["Id"], "ea", tagId)
        then:
        noExceptionThrown()
        where:
        tagId | conferenceTagEntity
        null  | null
        "id"  | new ConferenceTagEntity(tags: "[{\"firstTagName\":\"1\", \"secondTagName\":\"2\"}]")
    }

    @Unroll
    def "getActivityLinkObject"() {
        given:
        qrPosterDAO.queryById(*_) >> qrPosterEntity
        conferenceInvitationDAO.getInvitationById(*_) >> new ConferenceInvitationEntity(activityId: "id")
        activityDAO.getById(*_) >> activityEntity
        qrPosterDAO.queryByMarketingEventIdAndForwardTypes(*_) >> [new QRPosterEntity(id: "id")]
        conferenceInvitationDAO.getInvitationIdByActivityId(*_) >> ["id"]
        contentMarketingEventMaterialRelationDAO.listByMarketingEventIdAndObjectType(*_) >> [new ContentMarketingEventMaterialRelationEntity(objectId: "id")]
        hexagonPageDAO.getPageIdsBySiteId(*_) >> ["id"]

        when:
        activityManagerTest.getActivityLinkObject("id", objectType, [ObjectTypeEnum.QR_POSTER, ObjectTypeEnum.ACTIVITY_INVITATION, ObjectTypeEnum.HEXAGON_SITE, ObjectTypeEnum.CUSTOMIZE_FORM])
        then:
        noExceptionThrown()
        where:
        objectType                                   | qrPosterEntity                                     | activityEntity
        ObjectTypeEnum.QR_POSTER.getType()           | new QRPosterEntity(forwardType: 4, targetId: "id") | null
        ObjectTypeEnum.QR_POSTER.getType()           | new QRPosterEntity(forwardType: 1, targetId: "id") | null
        ObjectTypeEnum.ACTIVITY_INVITATION.getType() | new QRPosterEntity(forwardType: 4, targetId: "id") | null
        ObjectTypeEnum.ACTIVITY_INVITATION.getType() | new QRPosterEntity(forwardType: 4, targetId: "id") | new ActivityEntity()
    }

    @Unroll
    def "getActivityIdByObject"() {
        given:
        qrPosterDAO.queryById(*_) >> qrPosterEntity
        conferenceInvitationDAO.getInvitationById(*_) >> new ConferenceInvitationEntity(activityId: "id")
        conferenceDAO.getConferenceByMarketingEventId(*_) >> new ActivityEntity(id: "id")
        when:
        activityManagerTest.getActivityIdByObject("id", objectType, "ea", "id")
        then:
        noExceptionThrown()
        where:
        objectType | qrPosterEntity
        13         | null
        24         | new QRPosterEntity(forwardType: 4)
        24         | new QRPosterEntity(forwardType: 5)
        25         | new QRPosterEntity(forwardType: 5)
        26         | new QRPosterEntity(forwardType: 5)
    }

    @Unroll
    def "createActivitySignInQrCode"() {
        given:
        qrCodeManager.createQRCode(*_) >> new QRCodeManager.CreateQRCodeResult()
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        when:
        activityManagerTest.createActivitySignInQrCode("id", "ea", "urlPath")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "createActivitySignInQrCodeAddTag"() {
        given:
        conferenceTagDAO.addConferenceTag(*_) >> 1
        qrCodeManager.createQRCode(*_) >> new QRCodeManager.CreateQRCodeResult()
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        when:
        activityManagerTest.createActivitySignInQrCodeAddTag("id", "ea", [new TagName(firstTagName: "first", secondTagName: "second")], h5Path, miniPath)
        then:
        noExceptionThrown()
        where:
        h5Path  | miniPath
        "path?" | "path?"
        "path"  | "path"
    }

    @Shared
    Map<String, String> extraMap = Maps.newHashMap()

    @Unroll
    def "createActivityQrCode"() {
        given:
        extraMap.put("a", "a")
        qrCodeManager.createQRCode(*_) >> new QRCodeManager.CreateQRCodeResult()
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        when:
        activityManagerTest.createActivityQrCode("id", "ea", "channel", extraMap)
        then:
        noExceptionThrown()
    }

    @Unroll
    def "createActivityFormQrCode"() {
        given:
        extraMap.put("a", "a")
        qrCodeManager.saveQrCodeAndPhotoAssociationData(*_) >> new QRCodeManager.CreateQRCodeResult()
        when:
        activityManagerTest.createActivityFormQrCode(new ActivityEntity(), "formId", "channel")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "checkViewMarketingEvenObjectAuth"() {
        given:
        crmV2Manager.checkPrivilege(*_) >> true
        when:
        activityManagerTest.checkViewMarketingEvenObjectAuth("ea", 1, "sss")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "sendActivitySignInRecord"() {
        given:
        browserUserRelationManager.getOrCreateBrowserUserIdByPhone(*_) >> Optional.of("110")
        kisActionService.record(*_) >> null
        when:
        activityManagerTest.sendActivitySignInRecord("id", "openId", "wxAppId", "fingerPrint", "ea", 1, "110")
        then:
        noExceptionThrown()
    }

    @Unroll
    def "checkSignInStatus"() {
        given:
        activityDAO.getById(*_) >> activityEntity
        delayQueueSender.sendByObj(*_) >> { printf "dd" }
        campaignMergeDataDAO.getCampaignMergeDataByPhoneLike(*_) >> [new CampaignMergeDataEntity(id: "id")]
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> [new CampaignMergeDataEntity(id: "id")]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110"))]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFingerPrintAndEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110"))]
        customizeFormDataUserDAO.queryCustomizeFormDataUsersByFsUserInfoAndEventId(*_) >> [new CustomizeFormDataUserEntity(campaignId: "id", submitContent: new CustomizeFormDataEnroll(phone: "110"))]
        campaignMergeDataDAO.getCampaignMergeDataByPhone(*_) >> [new CampaignMergeDataEntity(id: "id")]
        activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(*_) >> activityEnrollDataEntityList
        def spy = Spy(activityManagerTest)
        spy.handleConferenceTag(*_) >> { printf "ddd" }
        spy.handleActivitySignInOtherProcess(*_) >> { printf "ddd" }
        spy.handleConferenceTag(*_) >> { printf "ddd" }
        campaignMergeDataManager.updateSignInStatus(*_) >> { printf "ddd" }
        when:
        spy.checkSignInStatus(arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                      | activityEntity                                                                    | activityEnrollDataEntityList
        new CheckSignInStatusArg(id: null)                                       | null                                                                              | null
        new CheckSignInStatusArg(id: "id")                                       | null                                                                              | null
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.minusDay(new Date(), 1))                     | null
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 2)           | null
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 3)           | null
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 0)]
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2)]
        new CheckSignInStatusArg(id: "id", openId: "openId", wxAppId: "wxAppId") | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new CheckSignInStatusArg(id: "id", fingerPrint: "finger")                | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new CheckSignInStatusArg(id: "id", fingerPrint: "finger")                | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | null
        new CheckSignInStatusArg(id: "id", fingerPrint: "finger")                | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 0)]
        new CheckSignInStatusArg(id: "id", fingerPrint: "finger")                | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2)]
        new CheckSignInStatusArg(id: "id", fingerPrint: "finger")                | new ActivityEntity(endTime: DateUtil.plusDay(new Date(), 1), status: 4)           | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new CheckSignInStatusArg(enrollUserEa: "ea", enrollUserFsUid: 1)         | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | null
        new CheckSignInStatusArg(enrollUserEa: "ea", enrollUserFsUid: 1)         | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | null
        new CheckSignInStatusArg(enrollUserEa: "ea", enrollUserFsUid: 1)         | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 0)]
        new CheckSignInStatusArg(enrollUserEa: "ea", enrollUserFsUid: 1)         | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 2)]
        new CheckSignInStatusArg(enrollUserEa: "ea", enrollUserFsUid: 1)         | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new CheckSignInStatusArg(enrollUserEa: "ea", enrollUserFsUid: 1)         | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
        new CheckSignInStatusArg(id: "id")                                       | new ActivityEntity(ea: "ea", endTime: DateUtil.plusDay(new Date(), 1), status: 4) | [new ActivityEnrollDataEntity(reviewStatus: 1, signIn: 1)]
    }

    @Shared
    def shareObjectData = new ObjectData()

    @Unroll
    def "queryActivityEnrollEndTime"() {
        given:
        hexagonPageDAO.getById(*_) >> new HexagonPageEntity()
        objectManager.getObjectEa(*_) >> "ea"
        activityManager.getActivityIdByObject(*_) >> "id"
        activityDAO.getById(*_) >> activityEntity
        conferenceDAO.getConferenceByMarketingEventId(*_) >> activityEntity
        activityEnrollTimeConfigDAO.queryEnrollTime(*_) >> configEntity
        shareObjectData.put("event_type", "content_marketing")
        crmV2Manager.getDetail(*_) >> shareObjectData
        when:
        activityManagerTest.queryActivityEnrollEndTime(new QueryActivityEnrollTimeArg(objectType: objectType))
        then:
        noExceptionThrown()
        where:
        objectType | activityEntity                                                      | configEntity
        13         | new ActivityEntity(enrollEndTime: DateUtil.minusDay(new Date(), 1)) | null
        27         | new ActivityEntity()                                                | null
        27         | new ActivityEntity()                                                | new ActivityEnrollTimeConfigEntity(status: 1, enrollTip: "110", enrollTime: DateUtil.minusDay(new Date(), 1))
        27         | new ActivityEntity()                                                | new ActivityEnrollTimeConfigEntity(status: 0, enrollTip: "110")
        11         | new ActivityEntity()                                                | new ActivityEnrollTimeConfigEntity(status: 0, enrollTip: "110")
    }
}