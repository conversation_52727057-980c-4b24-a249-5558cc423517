package com.facishare.marketing.provider.service.qywx

import com.facishare.converter.EIEAConverter
import com.facishare.marketing.api.arg.CancelMaterialTopArg
import com.facishare.marketing.api.arg.CreateFanQrCodeArg
import com.facishare.marketing.api.arg.DeleteMaterialArg
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg
import com.facishare.marketing.api.arg.TopMaterialArg
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg
import com.facishare.marketing.api.arg.qywx.BindQywxQrCodeWithWebsiteArg
import com.facishare.marketing.api.arg.qywx.wxContact.AddFollowUpInfoArg
import com.facishare.marketing.api.arg.qywx.wxContact.AssociateContactArg
import com.facishare.marketing.api.arg.qywx.wxContact.QueryActionAndFollowUpArg
import com.facishare.marketing.api.arg.qywx.wxContact.QueryContactDetailArg
import com.facishare.marketing.api.arg.qywx.wxContact.QueryContactListArg
import com.facishare.marketing.api.arg.qywx.wxContact.QueryCrmUserIdArg
import com.facishare.marketing.api.arg.qywx.wxContact.QueryQywxGroupSendFsUserInfoArg
import com.facishare.marketing.api.data.usermarketingaccount.FilterData
import com.facishare.marketing.api.result.EditObjectGroupResult
import com.facishare.marketing.api.result.UserMarketingActionResult
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeOwnerResult
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeTagResult
import com.facishare.marketing.api.result.qywx.wxContact.AssociateContactResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryActionAndFollowUpResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryAddfanQrCodeResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactDetailResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactListResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactMeConfigResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryQywxGroupSendFsUserInfoResult
import com.facishare.marketing.api.result.qywx.wxContact.QueryWxUserMarketingUserStatusResult
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService
import com.facishare.marketing.api.vo.QueryWebsiteBindFanQrCodeVO
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult
import com.facishare.marketing.api.vo.qywx.CreateOrUpdateWebSiteFanQrCodeVO
import com.facishare.marketing.api.vo.qywx.QywxCreateAddFanQrCodeVO
import com.facishare.marketing.api.vo.qywx.UpdateQywxCustomerRemarkVO
import com.facishare.marketing.common.result.PageResult
import com.facishare.marketing.common.typehandlers.value.TagName
import com.facishare.marketing.common.typehandlers.value.TagNameList
import com.facishare.marketing.provider.dao.CardDAO
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO
import com.facishare.marketing.provider.dao.QywxAttachmentsRelationDAO
import com.facishare.marketing.provider.dao.UserMarketingMiniappAccountRelationDao
import com.facishare.marketing.provider.dao.UserMarketingWxWorkExternalUserRelationDao
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO
import com.facishare.marketing.provider.dao.qywx.QYWXContactFollowUpDAO
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeRelationDAO
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeUserMarketingRelationDAO
import com.facishare.marketing.provider.dao.qywx.QywxContactMeConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendResultDAO
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendTaskDAO
import com.facishare.marketing.provider.dto.QYWXContactFollowUpDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import com.facishare.marketing.provider.dto.qywx.QywxAddFanQrCodeDTO
import com.facishare.marketing.provider.dto.qywx.TemplateBindQrConfigIdDTO
import com.facishare.marketing.provider.entity.CardEntity
import com.facishare.marketing.provider.entity.FSBindEntity
import com.facishare.marketing.provider.entity.ObjectGroupEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.QywxAttachmentsRelationEntity
import com.facishare.marketing.provider.entity.QywxContactFollowUpEntity
import com.facishare.marketing.provider.entity.TriggerInstanceEntity
import com.facishare.marketing.provider.entity.TriggerTaskInstanceEntity
import com.facishare.marketing.provider.entity.TriggerTaskSnapshotEntity
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity
import com.facishare.marketing.provider.entity.qywx.QywxContactMeConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendResultEntity
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendTaskEntity
import com.facishare.marketing.provider.entity.qywx.QywxQrCodeBrowseUserRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData
import com.facishare.marketing.provider.innerResult.AssociationResult
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult
import com.facishare.marketing.provider.innerResult.qywx.GetContactMeResult
import com.facishare.marketing.provider.innerResult.qywx.GetExternalContactDetailResult
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult
import com.facishare.marketing.provider.innerResult.qywx.UploadMediaResult
import com.facishare.marketing.provider.manager.FileV2Manager
import com.facishare.marketing.provider.manager.FsAddressBookManager
import com.facishare.marketing.provider.manager.FsBindManager
import com.facishare.marketing.provider.manager.MarketingPromotionSourceArgObjectRelationManager
import com.facishare.marketing.provider.manager.ObjectGroupManager
import com.facishare.marketing.provider.manager.PhotoManager
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.TriggerTaskInstanceManager
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager
import com.facishare.marketing.provider.manager.kis.ObjectManager
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.CustomerGroupManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.marketing.provider.manager.qywx.QywxEmployeeManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.QywxUserManager
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager
import com.facishare.marketing.statistic.outapi.result.UserMarketingActionStatisticResult
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService
import com.facishare.organization.api.service.DepartmentProviderService
import com.facishare.organization.api.service.EmployeeProviderService
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.service.MetadataControllerService
import com.fxiaoke.crmrestapi.service.MetadataTagDataService
import spock.lang.*

/**
 * Test for QYWXContactServiceImpl
 * @date 2024/11/26 19:44
 */
class QYWXContactServiceImplTest extends Specification {

    def qYWXContactServiceImpl = new QYWXContactServiceImpl()

    def crmV2Manager = Mock(CrmV2Manager)
    def fileV2Manager = Mock(FileV2Manager)
    def metadataTagManager = Mock(MetadataTagManager)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def userMarketingAssociationManager = Mock(UserMarketingAccountAssociationManager)
    def qywxContactFollowUpDAO = Mock(QYWXContactFollowUpDAO)
    def userMarketingStatisticService = Mock(UserMarketingStatisticService)
    def userMarketingAccountManager = Mock(UserMarketingAccountManager)
    def cardDAO = Mock(CardDAO)
    def qywxManager = Mock(QywxManager)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def agentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qywxContactMeConfigDAO = Mock(QywxContactMeConfigDAO)
    def userMarketingMiniappAccountRelationDao = Mock(UserMarketingMiniappAccountRelationDao)
    def userMarketingWxWorkExternalUserRelationDao = Mock(UserMarketingWxWorkExternalUserRelationDao)
    def qywxCorpAgentConfigDAO = Mock(QywxCorpAgentConfigDAO)
    def qywxGroupSendTaskDAO = Mock(QywxGroupSendTaskDAO)
    def qywxGroupSendResultDAO = Mock(QywxGroupSendResultDAO)
    def eieaConverter = Mock(EIEAConverter)
    def metadataTagDataService = Mock(MetadataTagDataService)
    def httpManager = Mock(HttpManager)
    def redisManager = Mock(RedisManager)
    def userMarketingAccountService = Mock(UserMarketingAccountService)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def customerGroupManager = Mock(CustomerGroupManager)
    def qywxUserManager = Mock(QywxUserManager)
    def metadataControllerService = Mock(MetadataControllerService)
    def qywxAddFanQrCodeDAO = Mock(QywxAddFanQrCodeDAO)
    def qywxAddFanQrCodeRelationDAO = Mock(QywxAddFanQrCodeRelationDAO)
    def objectGroupManager = Mock(ObjectGroupManager)
    def objectGroupDAO = Mock(ObjectGroupDAO)
    def objectGroupRelationDAO = Mock(ObjectGroupRelationDAO)
    def objectTopManager = Mock(ObjectTopManager)
    def objectGroupRelationVisibleManager = Mock(ObjectGroupRelationVisibleManager)
    def wechatFriendsRecordObjDescribeManager = Mock(WechatFriendsRecordObjDescribeManager)
    def marketingPromotionSourceArgObjectRelationManager = Mock(MarketingPromotionSourceArgObjectRelationManager)
    def dataPermissionManager = Mock(DataPermissionManager)
    def departmentProviderService = Mock(DepartmentProviderService)
    def employeeProviderService = Mock(EmployeeProviderService)
    def qywxAddFanQrCodeUserMarketingRelationDAO = Mock(QywxAddFanQrCodeUserMarketingRelationDAO)
    def qywxAttachmentsRelationDAO = Mock(QywxAttachmentsRelationDAO)
    def qywxEmployeeManager = Mock(QywxEmployeeManager)
    def objectManager = Mock(ObjectManager)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def photoManager = Mock(PhotoManager)
    def fsBindManager = Mock(FsBindManager)

    def setup() {
        qYWXContactServiceImpl.crmV2Manager = crmV2Manager
        qYWXContactServiceImpl.fileV2Manager = fileV2Manager
        qYWXContactServiceImpl.metadataTagManager = metadataTagManager
        qYWXContactServiceImpl.wechatWorkExternalUserObjManager = wechatWorkExternalUserObjManager
        qYWXContactServiceImpl.userMarketingAssociationManager = userMarketingAssociationManager
        qYWXContactServiceImpl.qywxContactFollowUpDAO = qywxContactFollowUpDAO
        qYWXContactServiceImpl.userMarketingStatisticService = userMarketingStatisticService
        qYWXContactServiceImpl.userMarketingAccountManager = userMarketingAccountManager
        qYWXContactServiceImpl.cardDAO = cardDAO
        qYWXContactServiceImpl.qywxManager = qywxManager
        qYWXContactServiceImpl.marketingActivityExternalConfigDao = marketingActivityExternalConfigDao
        qYWXContactServiceImpl.agentConfigDAO = agentConfigDAO
        qYWXContactServiceImpl.qywxContactMeConfigDAO = qywxContactMeConfigDAO
        qYWXContactServiceImpl.userMarketingMiniappAccountRelationDao = userMarketingMiniappAccountRelationDao
        qYWXContactServiceImpl.userMarketingWxWorkExternalUserRelationDao = userMarketingWxWorkExternalUserRelationDao
        qYWXContactServiceImpl.qywxCorpAgentConfigDAO = qywxCorpAgentConfigDAO
        qYWXContactServiceImpl.qywxGroupSendTaskDAO = qywxGroupSendTaskDAO
        qYWXContactServiceImpl.qywxGroupSendResultDAO = qywxGroupSendResultDAO
        qYWXContactServiceImpl.eieaConverter = eieaConverter
        qYWXContactServiceImpl.metadataTagDataService = metadataTagDataService
        qYWXContactServiceImpl.httpManager = httpManager
        qYWXContactServiceImpl.redisManager = redisManager
        qYWXContactServiceImpl.userMarketingAccountService = userMarketingAccountService
        qYWXContactServiceImpl.fsAddressBookManager = fsAddressBookManager
        qYWXContactServiceImpl.customerGroupManager = customerGroupManager
        qYWXContactServiceImpl.qywxUserManager = qywxUserManager
        qYWXContactServiceImpl.metadataControllerService = metadataControllerService
        qYWXContactServiceImpl.qywxAddFanQrCodeDAO = qywxAddFanQrCodeDAO
        qYWXContactServiceImpl.qywxAddFanQrCodeRelationDAO = qywxAddFanQrCodeRelationDAO
        qYWXContactServiceImpl.objectGroupManager = objectGroupManager
        qYWXContactServiceImpl.objectGroupDAO = objectGroupDAO
        qYWXContactServiceImpl.objectGroupRelationDAO = objectGroupRelationDAO
        qYWXContactServiceImpl.objectTopManager = objectTopManager
        qYWXContactServiceImpl.objectGroupRelationVisibleManager = objectGroupRelationVisibleManager
        qYWXContactServiceImpl.wechatFriendsRecordObjDescribeManager = wechatFriendsRecordObjDescribeManager
        qYWXContactServiceImpl.marketingPromotionSourceArgObjectRelationManager = marketingPromotionSourceArgObjectRelationManager
        qYWXContactServiceImpl.dataPermissionManager = dataPermissionManager
        qYWXContactServiceImpl.departmentProviderService = departmentProviderService
        qYWXContactServiceImpl.employeeProviderService = employeeProviderService
        qYWXContactServiceImpl.qywxAddFanQrCodeUserMarketingRelationDAO = qywxAddFanQrCodeUserMarketingRelationDAO
        qYWXContactServiceImpl.qywxAttachmentsRelationDAO = qywxAttachmentsRelationDAO
        qYWXContactServiceImpl.qywxEmployeeManager = qywxEmployeeManager
        qYWXContactServiceImpl.objectManager = objectManager
        qYWXContactServiceImpl.hexagonSiteDAO = hexagonSiteDAO
        qYWXContactServiceImpl.photoManager = photoManager
        qYWXContactServiceImpl.fsBindManager = fsBindManager
    }

    @Unroll
    def "queryContactListTest"() {
        given:
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >>> [null, new QywxCorpAgentConfigEntity()]
        qywxManager.getAccessToken(*_) >> 'test'
        qywxManager.getQyWxustomerCList(*_) >> ['test']
        eieaConverter.enterpriseAccountToId(*_) >> 0
        metadataTagManager.getTagIdsByTagNames(*_) >>> [[:],[(new TagName()):'test']]
        metadataControllerService.list(*_) >>> [
                new com.fxiaoke.crmrestapi.common.result.Result(code: -1),
                new com.fxiaoke.crmrestapi.common.result.Result(code: 0, data: new com.fxiaoke.crmrestapi.common.data.Page(dataList: [new ObjectData()]))
        ]
        metadataTagManager.getObjectDataIdAndTagNameListDataMapByObjectDataIds(*_) >> [:]
        qywxContactFollowUpDAO.queryFollowerContactInfo(*_) >> [new QYWXContactFollowUpDTO()]
        // todo

        expect:
        def i = 0
        while (true) {
            def arg = new QueryContactListArg(
                    uid: 'test',
                    name: 'test',
                    fanQrCodeId: 'test',
                    tagNames: [new TagName()],
                    filterData: new FilterData()
            )
            qYWXContactServiceImpl.queryContactList(arg)
            i++
            if (i > 20) {
                return;
            }
        }
        true
    }

    @Unroll
    def "associateContactTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData(0, 0f)
        userMarketingAssociationManager.associate(*_) >> new AssociationResult("userMarketingAccountId")

        expect:
        def yxt666 = qYWXContactServiceImpl.associateContact(arg)
        true

        where:
        arg                       || expectedResult
        new AssociateContactArg() || new com.facishare.marketing.common.result.Result<AssociateContactResult>(0, "errMsg", new AssociateContactResult())
    }

    @Unroll
    def "queryContactDetailTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData(0, 0f)
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        metadataTagManager.getObjectDataIdAndTagNameListDataMapByObjectDataIds(*_) >> ["getObjectDataIdAndTagNameListDataMapByObjectDataIdsResponse": new ObjectDataIdAndTagNameListData(null, null, new TagNameList())]
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.getExternalContactDetail(*_) >> [new GetExternalContactDetailResult()]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(*_) >> new UserMarketingWxWorkExternalUserRelationEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryContactDetail(arg)
        true

        where:
        arg                         || expectedResult
        new QueryContactDetailArg() || new com.facishare.marketing.common.result.Result<QueryContactDetailResult>(0, "errMsg", new QueryContactDetailResult())
    }

    @Unroll
    def "addFollowUpInfoTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData(0, 0f)
        qywxContactFollowUpDAO.insertQywxContactFollowUp(*_) >> 0
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.addFollowUpInfo(arg)
        true

        where:
        arg                      || expectedResult
        new AddFollowUpInfoArg() || new com.facishare.marketing.common.result.Result(0, "errMsg", "data")
    }

    @Unroll
    def "queryActionAndFollowUpTest"() {
        given:
        qywxContactFollowUpDAO.getByQywxContactFollowUp(*_) >> [new QywxContactFollowUpEntity()]
        userMarketingStatisticService.pageUserMarketingActionStatistic(*_) >> new com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>>()
        userMarketingAccountManager.getUserMarketingActionResultPageResult(*_) >> new com.facishare.marketing.api.result.PageResult<UserMarketingActionResult>(null, [new UserMarketingActionResult()])
        cardDAO.queryCardInfoByUid(*_) >> new CardEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryActionAndFollowUp(arg)
        true

        where:
        arg                             || expectedResult
        new QueryActionAndFollowUpArg() || new com.facishare.marketing.common.result.Result<List<QueryActionAndFollowUpResult>>(0, "errMsg", [new QueryActionAndFollowUpResult()])
    }

    @Unroll
    def "setContactConfigByEmployeeTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.setContactMeConfig(*_) >> "setContactMeConfigResponse"
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxContactMeConfigDAO.insert(*_) >> 0
        qywxContactMeConfigDAO.getByUid(*_) >> new QywxContactMeConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxUserManager.getQyUserIdByFsUserInfo(*_) >> "getQyUserIdByFsUserInfoResponse"

        expect:
        def yxt666 = qYWXContactServiceImpl.setContactConfigByEmployee(uid, fsEa, fsUserId)
        true

        where:
        uid   | fsUserId | fsEa   || expectedResult
        "uid" | 0        | "fsEa" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "queryWxUserMarketingUserStatusTest"() {
        given:
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryWxUserMarketingUserStatus(ea, uid, qywxUserId)
        true

        where:
        uid   | qywxUserId   | ea   || expectedResult
        "uid" | "qywxUserId" | "ea" || new com.facishare.marketing.common.result.Result<QueryWxUserMarketingUserStatusResult>(0, "errMsg", new QueryWxUserMarketingUserStatusResult())
    }

    @Unroll
    def "queryContactMeConfigTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.getQyWxustomerCList(*_) >> ["getQyWxustomerCListResponse"]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxContactMeConfigDAO.getByUid(*_) >> new QywxContactMeConfigEntity()
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity()
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity()]
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryContactMeConfig(ea, uid, qywxUserId, employeeUserId)
        true

        where:
        uid   | qywxUserId   | ea   | employeeUserId   || expectedResult
        "uid" | "qywxUserId" | "ea" | "employeeUserId" || new com.facishare.marketing.common.result.Result<QueryContactMeConfigResult>(0, "errMsg", new QueryContactMeConfigResult())
    }

    @Unroll
    def "queryQywxGroupSendFsUserInfoTest"() {
        given:
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity()
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity()]
        qywxGroupSendTaskDAO.querySendTaskByMarketingAcivityId(*_) >> new QywxGroupSendTaskEntity()
        qywxGroupSendResultDAO.queryQywxGroupSendResultByMsgId(*_) >> [new QywxGroupSendResultEntity()]
        qywxUserManager.getFsUserIdByQyWxInfo(*_) >> 0
        fsBindManager.queryFSBindByFsEaAndFsUserId(*_) >> new FSBindEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryQywxGroupSendFsUserInfo(arg)
        true

        where:
        arg                                   || expectedResult
        new QueryQywxGroupSendFsUserInfoArg() || new com.facishare.marketing.common.result.Result<QueryQywxGroupSendFsUserInfoResult>(0, "errMsg", new QueryQywxGroupSendFsUserInfoResult())
    }

    @Unroll
    def "queryAllSpreadSendFsUserInfoTest"() {
        given:
        marketingActivityExternalConfigDao.getByMarketingActivityId(*_) >> new MarketingActivityExternalConfigEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryAllSpreadSendFsUserInfo(arg)
        true

        where:
        arg                                   || expectedResult
        new QueryQywxGroupSendFsUserInfoArg() || new com.facishare.marketing.common.result.Result<QueryQywxGroupSendFsUserInfoResult>(0, "errMsg", new QueryQywxGroupSendFsUserInfoResult())
    }

    @Unroll
    def "checkMyCustomerTest"() {
        given:
        userMarketingMiniappAccountRelationDao.getByEaAndUid(*_) >> new UserMarketingMiniappAccountRelationEntity()
        userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(*_) >> [new UserMarketingWxWorkExternalUserRelationEntity()]
        customerGroupManager.queryExternalContactDetail(*_) >> [new GetExternalContactDetailResult()]
        qywxUserManager.getQyUserIdByFsUserInfo(*_) >> "getQyUserIdByFsUserInfoResponse"
        fsBindManager.queryFSBindByUid(*_) >> new FSBindEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.checkMyCustomer(uid, fsUserUid)
        true

        where:
        uid   | fsUserUid   || expectedResult
        "uid" | "fsUserUid" || new com.facishare.marketing.common.result.Result<Boolean>(0, "errMsg", Boolean.TRUE)
    }

    @Unroll
    def "getQywxObjectIdByQywxExternUserIdTest"() {
        given:
        wechatWorkExternalUserObjManager.getObjectDataMap(*_) >> ["getObjectDataMapResponse": new ObjectData(0, 0f)]

        expect:
        def yxt666 = qYWXContactServiceImpl.getQywxObjectIdByQywxExternUserId(ea, qywxExternUserId)
        true

        where:
        qywxExternUserId   | ea   || expectedResult
        "qywxExternUserId" | "ea" || new com.facishare.marketing.common.result.Result<String>(0, "errMsg", "data")
    }

    @Unroll
    def "createAddfanQrCodeTest"() {
        given:
        fileV2Manager.downloadAFile(*_) >> [(byte) 0] as byte[]
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.setContactMeConfig(*_) >> "setContactMeConfigResponse"
        qywxManager.getContanctMe(*_) >> new GetContactMeResult()
        qywxManager.fliterUnActiveUser(*_) >> ["fliterUnActiveUserResponse"]
        qywxManager.batchGetEmployeeByTags(*_) >> ["batchGetEmployeeByTagsResponse"]
        qywxManager.handleQywxEmployeeUserId(*_) >> ["handleQywxEmployeeUserIdResponse"]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        httpManager.uploadFile(*_) >> new UploadMediaResult()
        qywxUserManager.getQywxUserIdByQywxDepartment(*_) >> ["getQywxUserIdByQywxDepartmentResponse"]
        qywxAddFanQrCodeDAO.insert(*_) >> 0
        qywxAddFanQrCodeDAO.getByEaAndState(*_) >> new QywxAddFanQrCodeEntity()
        dataPermissionManager.getNewDataPermissionSetting(*_) >> true
        dataPermissionManager.filterUserAccessibleQywxDeptIds(*_) >> [0]
        qywxAttachmentsRelationDAO.insert(*_) >> 0
        objectManager.convertNoticeContentTypeToObjectType(*_) >> 0
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO()]
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]

        expect:
        def yxt666 = qYWXContactServiceImpl.createAddfanQrCode(vo)
        true

        where:
        vo                             || expectedResult
        new QywxCreateAddFanQrCodeVO() || new com.facishare.marketing.common.result.Result<String>(0, "errMsg", "data")
    }

    @Unroll
    def "customizeCreateFanQrCodeTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.setContactMeConfig(*_) >> "setContactMeConfigResponse"
        qywxManager.getContanctMe(*_) >> new GetContactMeResult()
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        eieaConverter.enterpriseIdToAccount(*_) >> "enterpriseIdToAccountResponse"
        qywxUserManager.getQyUserIdByFsUserInfo(*_) >> [(0): "getQyUserIdByFsUserInfoResponse"]
        qywxAddFanQrCodeDAO.insert(*_) >> 0
        qywxAddFanQrCodeDAO.getByEaAndState(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeDAO.getByBindInfo(*_) >> new QywxAddFanQrCodeEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.customizeCreateFanQrCode(tenantId, fsUserId, vo)
        true

        where:
        fsUserId | tenantId | vo                       || expectedResult
        0        | 0        | new CreateFanQrCodeArg() || new com.facishare.marketing.common.result.Result<String>(0, "errMsg", "data")
    }

    @Unroll
    def "appendQrCodeIdToUrlTest"() {

        expect:
        qYWXContactServiceImpl.appendQrCodeIdToUrl(vo, entity)

        where:
        vo                             | entity                       || expectedResult
        new QywxCreateAddFanQrCodeVO() | new QywxAddFanQrCodeEntity() || true
    }

    @Unroll
    def "queryAddfanQrCodeTest"() {
        given:
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.getStaffDetail(*_) >> new StaffDetailResult()
        qywxManager.queryDepartment(*_) >> new DepartmentListResult()
        qywxManager.queryEmployeeInfo(*_) >> [new EmployeeTagResult()]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()
        qywxAttachmentsRelationDAO.getDetailByTargetId(*_) >> new QywxAttachmentsRelationEntity()
        objectManager.getObjectName(*_) >> "getObjectNameResponse"

        expect:
        def yxt666 = qYWXContactServiceImpl.queryAddfanQrCode(id)
        true

        where:
        id   || expectedResult
        "id" || new com.facishare.marketing.common.result.Result<QueryAddfanQrCodeResult>(0, "errMsg", new QueryAddfanQrCodeResult())
    }

    @Unroll
    def "updateAddfanQrCodeTest"() {
        given:
        fileV2Manager.downloadAFile(*_) >> [(byte) 0] as byte[]
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.updateContactMeConfig(*_) >> true
        qywxManager.fliterUnActiveUser(*_) >> ["fliterUnActiveUserResponse"]
        qywxManager.batchGetEmployeeByTags(*_) >> ["batchGetEmployeeByTagsResponse"]
        qywxManager.handleQywxEmployeeUserId(*_) >> ["handleQywxEmployeeUserIdResponse"]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        httpManager.uploadFile(*_) >> new UploadMediaResult()
        qywxUserManager.getQywxUserIdByQywxDepartment(*_) >> ["getQywxUserIdByQywxDepartmentResponse"]
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeDAO.updateById(*_) >> 0
        qywxAddFanQrCodeDAO.queryQrCodeByParentQrCodeId(*_) >> [new QywxAddFanQrCodeEntity()]
        dataPermissionManager.getNewDataPermissionSetting(*_) >> true
        dataPermissionManager.filterUserAccessibleQywxDeptIds(*_) >> [0]
        qywxAttachmentsRelationDAO.insert(*_) >> 0
        qywxAttachmentsRelationDAO.updateByTargetId(*_) >> 0
        qywxAttachmentsRelationDAO.getDetailByTargetId(*_) >> new QywxAttachmentsRelationEntity()
        objectManager.convertNoticeContentTypeToObjectType(*_) >> 0
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO()]
        photoManager.queryPhoto(*_) >> [new PhotoEntity()]

        expect:
        def yxt666 = qYWXContactServiceImpl.updateAddfanQrCode(vo)
        true

        where:
        vo                             || expectedResult
        new QywxCreateAddFanQrCodeVO() || new com.facishare.marketing.common.result.Result<String>(0, "errMsg", "data")
    }

    @Unroll
    def "updateBindQkQrCodeTest"() {
        given:
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeDAO.updateById(*_) >> 0
        qywxAddFanQrCodeDAO.queryQrCodeByParentQrCodeId(*_) >> [new QywxAddFanQrCodeEntity()]

        expect:
        qYWXContactServiceImpl.updateBindQkQrCode(ea, parentQrCodeId)

        where:
        parentQrCodeId   | ea   || expectedResult
        "parentQrCodeId" | "ea" || true
    }

    @Unroll
    def "deleteAddfanQrCodeTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.detelteContactMe(*_) >> true
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeDAO.updateStatusById(*_) >> 0

        expect:
        def yxt666 = qYWXContactServiceImpl.deleteAddfanQrCode(ea, id)
        true

        where:
        id   | ea   || expectedResult
        "id" | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "updateQywxCustomerRemarkTest"() {
        given:
        wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(*_) >> 0
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.updateCustomerRemark(*_) >> true
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        userMarketingAccountService.batchAddTagsToUserMarketings(*_) >> new com.facishare.marketing.common.result.Result<Boolean>(0, "errMsg", Boolean.TRUE)

        expect:
        def yxt666 = qYWXContactServiceImpl.updateQywxCustomerRemark(vo)
        true

        where:
        vo                               || expectedResult
        new UpdateQywxCustomerRemarkVO() || new com.facishare.marketing.common.result.Result<Boolean>(0, "errMsg", Boolean.TRUE)
    }

    @Unroll
    def "queryQywxEmployeeBaseInfoTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.queryAllStaff(*_) >> [new DepartmentStaffResult.StaffInfo()]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        dataPermissionManager.getNewDataPermissionSetting(*_) >> true
        dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(*_) >> [0]

        expect:
        def yxt666 = qYWXContactServiceImpl.queryQywxEmployeeBaseInfo(ea, status, fsUserId)
        true

        where:
        fsUserId | ea   | status || expectedResult
        0        | "ea" | 0      || new com.facishare.marketing.common.result.Result<List<EmployeeOwnerResult>>(0, "errMsg", [new EmployeeOwnerResult()])
    }

    @Unroll
    def "queryCrmUserIdByExternalUseridTest"() {
        given:
        wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(*_) >> new Result<Page<ObjectData>>()
        eieaConverter.enterpriseAccountToId(*_) >> 0

        expect:
        def yxt666 = qYWXContactServiceImpl.queryCrmUserIdByExternalUserid(arg)
        true

        where:
        arg                     || expectedResult
        new QueryCrmUserIdArg() || new com.facishare.marketing.common.result.Result<List<String>>(0, "errMsg", ["data"])
    }

    @Unroll
    def "bindQywxQrCodeWithWebsiteTest"() {
        given:
        fsAddressBookManager.getEmployeeInfo(*_) >> new FsAddressBookManager.FSEmployeeMsg()
        qywxAddFanQrCodeDAO.insert(*_) >> 0
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.bindQywxQrCodeWithWebsite(ea, fsUserId, vo)
        true

        where:
        fsUserId | vo                                 | ea   || expectedResult
        0        | new BindQywxQrCodeWithWebsiteArg() | "ea" || new com.facishare.marketing.common.result.Result<String>(0, "errMsg", "data")
    }

    @Unroll
    def "unbindQywxQrCodeWithWebsiteTest"() {
        given:
        fsAddressBookManager.getEmployeeInfo(*_) >> new FsAddressBookManager.FSEmployeeMsg()
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeDAO.updateFanQrCodeWebsiteBindStatus(*_) >> 0

        expect:
        def yxt666 = qYWXContactServiceImpl.unbindQywxQrCodeWithWebsite(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                                | ea   || expectedResult
        0        | new BindQywxQrCodeWithWebsiteArg() | "ea" || new com.facishare.marketing.common.result.Result<String>(0, "errMsg", "data")
    }

    @Unroll
    def "queryWebsiteBindFanQrCodeTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.getStaffDetail(*_) >> new StaffDetailResult()
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        metadataControllerService.list(*_) >> new Result<Page<ObjectData>>()
        qywxAddFanQrCodeDAO.queryBindWebsiteByEa(*_) >> [new QywxAddFanQrCodeEntity()]

        expect:
        def yxt666 = qYWXContactServiceImpl.queryWebsiteBindFanQrCode(vo)
        true

        where:
        vo                                || expectedResult
        new QueryWebsiteBindFanQrCodeVO() || new com.facishare.marketing.common.result.Result<List<QueryAddfanQrCodeResult>>(0, "errMsg", [new QueryAddfanQrCodeResult()])
    }

    @Unroll
    def "createOrUpdateWebsiteFanQrCodeTest"() {
        given:
        crmV2Manager.getDetail(*_) >> new ObjectData(0, 0f)
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.setContactMeConfig(*_) >> "setContactMeConfigResponse"
        qywxManager.getContanctMe(*_) >> new GetContactMeResult()
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeDAO.getBindWebsiteFanQrCodeById(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeDAO.getByEaAndState(*_) >> new QywxAddFanQrCodeEntity()
        qywxAddFanQrCodeRelationDAO.insert(*_) >> 0
        qywxAddFanQrCodeRelationDAO.queryByBrowseIdAndQrCodeId(*_) >> new QywxQrCodeBrowseUserRelationEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.createOrUpdateWebsiteFanQrCode(vo)
        true

        where:
        vo                                     || expectedResult
        new CreateOrUpdateWebSiteFanQrCodeVO() || new com.facishare.marketing.common.result.Result<QueryAddfanQrCodeResult>(0, "errMsg", new QueryAddfanQrCodeResult())
    }

    @Unroll
    def "deleteTemplateWebFanQrCodeByDaysTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.detelteContactMe(*_) >> true
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeRelationDAO.deleteByQrCodeId(*_) >> 0
        qywxAddFanQrCodeRelationDAO.getTemplateBindQrConfigIdsByCheckTime(*_) >> [new TemplateBindQrConfigIdDTO()]

        expect:
        def yxt666 = qYWXContactServiceImpl.deleteTemplateWebFanQrCodeByDays(deletePointDate)
        true

        where:
        deletePointDate                                                      || expectedResult
        new GregorianCalendar(2024, Calendar.NOVEMBER, 26, 19, 44).getTime() || new com.facishare.marketing.common.result.Result(0, "errMsg", "data")
    }

    @Unroll
    def "deleteHexagonWebFanQrCodeByDaysTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.detelteContactMe(*_) >> true
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeUserMarketingRelationDAO.deleteByQrCodeId(*_) >> 0
        qywxAddFanQrCodeUserMarketingRelationDAO.getHexagonBindQrConfigIdsByCheckTime(*_) >> [new TemplateBindQrConfigIdDTO()]

        expect:
        def yxt666 = qYWXContactServiceImpl.deleteHexagonWebFanQrCodeByDays(deletePointDate)
        true

        where:
        deletePointDate                                                      || expectedResult
        new GregorianCalendar(2024, Calendar.NOVEMBER, 26, 19, 44).getTime() || new com.facishare.marketing.common.result.Result(0, "errMsg", "data")
    }

    @Unroll
    def "editFanCodeGroupTest"() {
        given:
        objectGroupManager.editGroup(*_) >> new com.facishare.marketing.common.result.Result<EditObjectGroupResult>(0, "errMsg", new EditObjectGroupResult())
        objectGroupManager.isAppAdmin(*_) >> true

        expect:
        def yxt666 = qYWXContactServiceImpl.editFanCodeGroup(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                      | ea   || expectedResult
        0        | new EditObjectGroupArg() | "ea" || new com.facishare.marketing.common.result.Result<EditObjectGroupResult>(0, "errMsg", new EditObjectGroupResult())
    }

    @Unroll
    def "deleteFanCodeGroupTest"() {
        given:
        objectGroupManager.deleteGroup(*_) >> new com.facishare.marketing.common.result.Result(0, "errMsg", "data")
        objectGroupManager.isAppAdmin(*_) >> true

        expect:
        def yxt666 = qYWXContactServiceImpl.deleteFanCodeGroup(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                        | ea   || expectedResult
        0        | new DeleteObjectGroupArg() | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "setFanCodeGroupTest"() {
        given:
        qywxAddFanQrCodeDAO.getByIds(*_) >> [new QywxAddFanQrCodeEntity()]
        objectGroupDAO.getById(*_) >> new ObjectGroupEntity()
        objectGroupRelationDAO.batchInsert(*_) >> 0
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0

        expect:
        def yxt666 = qYWXContactServiceImpl.setFanCodeGroup(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                     | ea   || expectedResult
        0        | new SetObjectGroupArg() | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "deleteFanCodeBatchTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.detelteContactMe(*_) >> true
        qywxAddFanQrCodeDAO.getAddFanQrCodeByIds(*_) >> [new QywxAddFanQrCodeEntity()]
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(*_) >> 0

        expect:
        def yxt666 = qYWXContactServiceImpl.deleteFanCodeBatch(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                     | ea   || expectedResult
        0        | new DeleteMaterialArg() | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "topFanCodeTest"() {
        given:
        qywxAddFanQrCodeDAO.getById(*_) >> new QywxAddFanQrCodeEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.topFanCode(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                  | ea   || expectedResult
        0        | new TopMaterialArg() | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "cancelTopFanCodeTest"() {

        expect:
        def yxt666 = qYWXContactServiceImpl.cancelTopFanCode(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                        | ea   || expectedResult
        0        | new CancelMaterialTopArg() | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "addFanCodeGroupRoleTest"() {
        given:
        objectGroupManager.isAppAdmin(*_) >> true

        expect:
        def yxt666 = qYWXContactServiceImpl.addFanCodeGroupRole(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                             | ea   || expectedResult
        0        | new SaveObjectGroupVisibleArg() | "ea" || new com.facishare.marketing.common.result.Result<Void>(0, "errMsg", null)
    }

    @Unroll
    def "listFanCodeGroupTest"() {
        given:
        qywxAddFanQrCodeDAO.queryUnGroupAndCreateByMeCount(*_) >> 0
        qywxAddFanQrCodeDAO.queryCountCreateByMe(*_) >> 0
        qywxAddFanQrCodeDAO.queryCountByUnGrouped(*_) >> 0
        qywxAddFanQrCodeDAO.queryAccessibleCount(*_) >> 0
        objectGroupManager.getShowGroup(*_) >> new ObjectGroupListResult()

        expect:
        def yxt666 = qYWXContactServiceImpl.listFanCodeGroup(ea, fsUserId, arg)
        true

        where:
        fsUserId | arg                | ea   || expectedResult
        0        | new ListGroupArg() | "ea" || new com.facishare.marketing.common.result.Result<ObjectGroupListResult>(0, "errMsg", new ObjectGroupListResult())
    }

    @Unroll
    def "getGroupRoleTest"() {
        given:
        objectGroupRelationVisibleManager.getRoleRelationByGroupId(*_) >> [new ObjectGroupRoleRelationEntity()]

        expect:
        def yxt666 = qYWXContactServiceImpl.getGroupRole(groupId)
        true

        where:
        groupId   || expectedResult
        "groupId" || new com.facishare.marketing.common.result.Result<List<String>>(0, "errMsg", ["data"])
    }

    @Unroll
    def "queryQywxTagInfoTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.queryEmployeeInfo(*_) >> [new EmployeeTagResult()]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryQywxTagInfo(fsEa)
        true

        where:
        fsEa   || expectedResult
        "fsEa" || new com.facishare.marketing.common.result.Result<List<EmployeeTagResult>>(0, "errMsg", [new EmployeeTagResult()])
    }

    @Unroll
    def "buildQueryAddfanQrCodeResultListTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.getStaffDetail(*_) >> new StaffDetailResult()
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        eieaConverter.enterpriseAccountToId(*_) >> 0
        metadataControllerService.list(*_) >> new Result<Page<ObjectData>>()

        expect:
        def yxt666 = qYWXContactServiceImpl.buildQueryAddfanQrCodeResultList(ea, entities, resultList)
        true

        where:
        entities                       | ea   | resultList                      || expectedResult
        [new QywxAddFanQrCodeEntity()] | "ea" | [new QueryAddfanQrCodeResult()] || [new QueryAddfanQrCodeResult()]
    }

    @Unroll
    def "queryAddfanQrCodeListTest"() {
        given:
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 0
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> new InnerPage<ObjectData>()
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeDAO.getAccessiblePage(*_) >> [new QywxAddFanQrCodeDTO()]
        qywxAddFanQrCodeDAO.getCreateByMePage(*_) >> [new QywxAddFanQrCodeDTO()]
        qywxAddFanQrCodeDAO.noGroupPage(*_) >> [new QywxAddFanQrCodeDTO()]
        objectGroupRelationVisibleManager.getAccessibleGroup(*_) >> [new ObjectGroupEntity()]
        objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(*_) >> ["getAccessibleSubGroupIdListResponse"]
        wechatFriendsRecordObjDescribeManager.getTotalCustomerCount(*_) >> ["getTotalCustomerCountResponse": 0]

        expect:
        def yxt666 = qYWXContactServiceImpl.queryAddfanQrCodeList(ea, keyword, pageNum, pageSize, fsUserId, groupId, marketingEventId, isBandPoster)
        true

        where:
        isBandPoster | marketingEventId   | fsUserId | groupId   | pageSize | ea   | keyword   | pageNum || expectedResult
        0            | "marketingEventId" | 0        | "groupId" | 0        | "ea" | "keyword" | 0       || new com.facishare.marketing.common.result.Result<PageResult<QueryAddfanQrCodeResult>>(0, "errMsg", new PageResult<QueryAddfanQrCodeResult>())
    }

    @Unroll
    def "deleteConfigTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.detelteContactMe(*_) >> true
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxAddFanQrCodeDAO.queryDeleteConfigEa(*_) >> ["queryDeleteConfigEaResponse"]
        qywxAddFanQrCodeDAO.queryDeleteConfig(*_) >> [new QywxAddFanQrCodeEntity()]
        qywxAddFanQrCodeDAO.deletePosterConfigById(*_) >> 0

        expect:
        def yxt666 = qYWXContactServiceImpl.deleteConfig()
        true

        where:
        expectedResult << new com.facishare.marketing.common.result.Result(0, "errMsg", "data")
    }

    @Unroll
    def "queryAllQywxEmployeeBaseInfoTest"() {
        given:
        qywxManager.getAccessToken(*_) >> "getAccessTokenResponse"
        qywxManager.queryAllStaff(*_) >> [new DepartmentStaffResult.StaffInfo()]
        agentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()
        qywxCorpAgentConfigDAO.queryAgentByEa(*_) >> new QywxCorpAgentConfigEntity()

        expect:
        def yxt666 = qYWXContactServiceImpl.queryAllQywxEmployeeBaseInfo(ea, status, fsUserId)
        true

        where:
        fsUserId | ea   | status || expectedResult
        0        | "ea" | 0      || new com.facishare.marketing.common.result.Result<List<EmployeeOwnerResult>>(0, "errMsg", [new EmployeeOwnerResult()])
    }

    @Unroll
    def "getWechatCustomersCountByQrCodeTest"() {
        given:
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 0
        crmV2Manager.listCrmObjectScanByIdV3(*_) >> new InnerPage<ObjectData>()

        expect:
        def yxt666 = qYWXContactServiceImpl.getWechatCustomersCountByQrCode(ea, qrCodeIds)
        true

        where:
        ea   | qrCodeIds     || expectedResult
        "ea" | ["qrCodeIds"] || [new ObjectData(0, 0f)]
    }

    @Unroll
    def "getAvatarFormCrmDataTest"() {
        given:
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"

        expect:
        def yxt666 = qYWXContactServiceImpl.getAvatarFormCrmData(objectData, ea)
        true

        where:
        objectData            | ea   || expectedResult
        new ObjectData(0, 0f) | "ea" || "expectedResult"
    }

}