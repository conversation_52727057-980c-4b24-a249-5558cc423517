package com.facishare.marketing.provider.manager

import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import spock.lang.Specification

class CustomizeFormClueManagerSpec extends Specification {
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def hexagonSiteDAO = Mock(HexagonSiteDAO)

    def manager = new CustomizeFormClueManager(
            customizeFormDataUserDAO: customizeFormDataUserDAO,
            hexagonSiteDAO: hexagonSiteDAO
    )

    def "countClueNumByEa"() {
        given:
        customizeFormDataUserDAO.countClueNumByEa(*_) >> 1

        when:
        manager.countClueNumByEa(ea, nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | false
        "1"  | false
        "1"  | true
    }

    def "countClueByEaWithMarketingActivity"() {
        given:
        customizeFormDataUserDAO.countClueByEaWithMarketingActivity(*_) >> 1

        when:
        manager.countClueByEaWithMarketingActivity(ea, nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | false
        "1"  | false
        "1"  | true
    }

    def "countClueNumByFsUserId"() {
        given:
        customizeFormDataUserDAO.countClueNumByFsUserId(*_) >> 1

        when:
        manager.countClueNumByFsUserId(1, ea, nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | false
        "1"  | false
        "1"  | true
    }

    def "batchCountClueNumByFsUserIds"() {
        given:
        customizeFormDataUserDAO.batchCountClueNumByFsUserIds(*_) >> [new CustomizeFormClueNumDTO(fsUid: 2, count: 1)]

        when:
        manager.batchCountClueNumByFsUserIds(ids, "1", nf, 1)

        then:
        noExceptionThrown()

        where:
        ids | nf
        []  | true
        [1] | true
        [1] | false

    }

    def "batchCountClueNumByFsUserIdAndMarketingActivity"() {
        given:
        customizeFormDataUserDAO.batchCountClueNumByFsUserIdAndMarketingActivity(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: 2, count: 1)]

        when:
        manager.batchCountClueNumByFsUserIdAndMarketingActivity(1, "1", ma, nf, 1)

        then:
        noExceptionThrown()

        where:
        ma    | nf
        []    | true
        ["1"] | true
        ["1"] | false

    }

    def "batchCountClueNumByForpartner"() {
        given:
        customizeFormDataUserDAO.batchCountClueNumForPartner(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: 2, count: 1)]

        when:
        manager.batchCountClueNumByForpartner(1, "1", ma)

        then:
        a == 1

        where:
        ma    | a
        []    | 1
        ["1"] | 1
    }

    def "getClueInfoByMarketingActivityId"() {
        given:
        customizeFormDataUserDAO.countClueNumByMarketingActivityId(*_) >> 1
        customizeFormDataUserDAO.countClueInfoByMarketingActivityId(*_) >> [new CustomizeFormClueNumDTO(leadId: 1)]

        when:
        manager.getClueInfoByMarketingActivityId(ea, "1", nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "getClueInfoByMarketingActivityIdAndFsUserId"() {
        given:
        customizeFormDataUserDAO.countClueNumByFsUserIdAndMarketingActivityId(*_) >> 1
        customizeFormDataUserDAO.countClueInfoByMarketingActivityIdAndFsUserId(*_) >> [new CustomizeFormClueNumDTO(leadId: 1)]

        when:
        manager.getClueInfoByMarketingActivityIdAndFsUserId(ea, "1", 1, nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countClueNumByMarketingActivityId"() {
        given:
        customizeFormDataUserDAO.countClueNumByMarketingActivityId(*_) >> 1
        when:
        manager.countClueNumByMarketingActivityId(ea, "1", nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countClueNumByMarketingActivityIdForPartner"() {
        given:
        customizeFormDataUserDAO.countClueNumByMarketingActivityIdForPartner(*_) >> 1

        when:
        manager.countClueNumByMarketingActivityIdForPartner(ea, "1", nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true

    }

    def "batchCountClueNumByMarketingActivityIds"() {
        given:
        customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: "2", count: 1)]

        when:
        manager.batchCountClueNumByMarketingActivityIds(ea, ["1"], nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false

    }

    def "batchCountClueNumByMarketingActivityIdsV2"() {
        given:
        customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: "2", count: 1)]
        when:
        manager.batchCountClueNumByMarketingActivityIds(ea, ["1"], 1L, 2L)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countClueNumByFsUserIdAndMarketingActivityId"() {
        given:
        customizeFormDataUserDAO.countClueNumByFsUserIdAndMarketingActivityId(*_) >> 1

        when:
        manager.countClueNumByFsUserIdAndMarketingActivityId(ea, 1, "1", nf, 2)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countClueNumByFsUserIdAndMarketingActivityId2"() {
        given:
        customizeFormDataUserDAO.countClueNumByFsUserIdAndMarketingActivityId(*_) >> 1

        when:
        manager.countClueNumByFsUserIdAndMarketingActivityId(ea, 1, "1", nf, 1L, 2L)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "getFormDataUserIdByFsUserIdAndMarketingActivityId"() {
        given:
        customizeFormDataUserDAO.getFormDataUserIdByFsUserIdAndMarketingActivityId(*_) >> ["1"]

        when:
        manager.getFormDataUserIdByFsUserIdAndMarketingActivityId(ea, 1, "1", nf, 1L, 2L)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "batchCountClueNumByFsUserIdsAndMarketingActivityId"() {
        given:
        customizeFormDataUserDAO.batchCountClueNumByFsUserIdsAndMarketingActivityId(*_) >> [new CustomizeFormClueNumDTO(fsUid: 2, count: 1)]

        when:
        manager.batchCountClueNumByFsUserIdsAndMarketingActivityId(ea, [1], "1", nf, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countMarketingActivityClueNumByEa"() {
        given:
        customizeFormDataUserDAO.countMarketingActivityClueNumByEa(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: "2", count: 1)]

        when:
        manager.countMarketingActivityClueNumByEa(ea, nf, 1, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countMarketingActivityClueNumByEaAndType"() {
        given:
        customizeFormDataUserDAO.countMarketingActivityClueNumByEaAndType(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: "2", count: 1)]

        when:
        manager.countMarketingActivityClueNumByEaAndType(ea, nf, 1, 1, 1, 1, ["1"])

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countFsUidClueNumByEaAndType"() {
        given:
        customizeFormDataUserDAO.countFsUidClueNumByEaAndType(*_) >> [new CustomizeFormClueNumDTO(marketingActivityId: "2", count: 1)]

        when:
        manager.countFsUidClueNumByEaAndType(ea, nf, 1, 1, 1, 1, ["1"], [1])

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "countFsUserIdClueNumByEa"() {
        given:
        customizeFormDataUserDAO.countFsUserIdClueNumByEa(*_) >> [new CustomizeFormClueNumDTO(fsUid: 2, count: 1)]

        when:
        manager.countFsUserIdClueNumByEa(ea, nf, 1, 1)

        then:
        noExceptionThrown()

        where:
        ea   | nf
        null | true
        "1"  | true
        "1"  | false
    }

    def "queryHexagonSiteClueCount"() {
        given:
        hexagonSiteDAO.getFormBySiteIds(*_) >> [new HexagonSiteListDTO(formId: "1")]
        customizeFormDataUserDAO.queryHexagonSiteClueCount(*_) >> [new CustomizeFormClueNumDTO(objectId: "1", count: 1), new CustomizeFormClueNumDTO(objectId: "2", count: 1)]

        when:
        manager.queryHexagonSiteClueCount(sIds)

        then:
        a == 1

        where:
        sIds            | a
        []              | 1
        ["1", "2", "3"] | 1
    }

    def "queryClueChannelByMarketingEvent"() {
        given:
        customizeFormDataUserDAO.queryChannelStatisticsByMarketingEventId(*_) >> cfl
        when:
        manager.queryClueChannelByMarketingEvent("1", "1")

        then:
        a == 1

        where:
        cfl                                                        | a
        []                                                         | 1
        [new CustomizeFormClueNumDTO(channelValue: "1", count: 1)] | 1

    }
}
