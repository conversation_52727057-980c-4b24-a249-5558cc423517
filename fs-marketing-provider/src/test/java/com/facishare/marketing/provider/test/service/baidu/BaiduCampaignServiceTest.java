/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.baidu;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.AdThirdSyncDataArg;
import com.facishare.marketing.api.arg.advertiser.QueryAdGroupDetailArg;
import com.facishare.marketing.api.arg.advertiser.QueryAdGroupListArg;
import com.facishare.marketing.api.result.baidu.BaiduAdGroupVO;
import com.facishare.marketing.api.result.baidu.CampaignDetailResult;
import com.facishare.marketing.api.result.baidu.QuerySpreadChannelStatResult;
import com.facishare.marketing.api.result.baidu.TrendGraphDataResult;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.api.vo.advertiser.headlines.SyncHeadlinesLocalLeadsVO;
import com.facishare.marketing.api.vo.baidu.QueryCampaignDetailVO;
import com.facishare.marketing.api.vo.baidu.QueryCampaignListVO;
import com.facishare.marketing.api.vo.baidu.RelateMarketingEventVO;
import com.facishare.marketing.api.vo.baidu.TrendGraphDataVO;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.UserMarketingCrmCustomizeObjectRelationDao;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCustomizeObjectRelationEntity;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * Created by ranluch on 2019/12/4.
 */
@Slf4j
public class BaiduCampaignServiceTest extends BaseTest {
    @Autowired
    private BaiduCampaignService baiduCampaignService;
    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;
    @Autowired
    private BaiduAccountDAO accountDAO;

    @Autowired
    private UserMarketingCrmCustomizeObjectRelationDao userMarketingCrmCustomizeObjectRelationDao;

    @Test
    public void refreshLocalLeadByAccountId() {
        SyncHeadlinesLocalLeadsVO vo = new SyncHeadlinesLocalLeadsVO();
//        vo.setStartTime(DateUtil.getDayStartDate(new Date()).getTime());
        vo.setEndTime(DateUtil.getTimeStamp(new Date()));
        vo.setEa("88146");
        vo.setAdAccountId("2180290b71a047eea103ff04efc4e2f6");
        baiduCampaignService.refreshLocalLeadByAccountId(vo);
    }

    @Test
    public void queryCampaignList() {
        QueryCampaignListVO vo = new QueryCampaignListVO();
        vo.setEa("74164");
        vo.setAdAccountId("f266441754984118bca31f4e15b3d731");
        vo.setSource("1");
        vo.setStartTime(new Date(1654511520000L));
        vo.setEndTime(new Date(1654511520000L));
        vo.setPageNum(1);
        vo.setPageSize(100);
        Result<PageResult<CampaignDetailResult>> result = baiduCampaignService.queryCampaignList(vo);
        log.info(result.toString());
    }

    @Test
    public void queryCampaignDetail() {
        QueryCampaignDetailVO vo = new QueryCampaignDetailVO();
        vo.setId("1a8c2edb8d1e4f80bb93eccd199e7fbc");
        vo.setEa("74164");
        vo.setStartTime(DateUtil.getSomeDay(new Date(), -7));
        vo.setEndTime(DateUtil.getSomeDay(new Date(), -1));
        Result<CampaignDetailResult> result = baiduCampaignService.queryCampaignDetail(vo);
        log.info(result.toString());
    }

    @Test
    public void relateMarketingEvent() {
        RelateMarketingEventVO vo = new RelateMarketingEventVO();
        vo.setId("3ced8e0afa214aa1a4e9a1ca92d2b020");
        vo.setEa("74164");
        vo.setMarketingEventId("619cb62aaca83200019a414d");
        vo.setAdAccountId("5008fb1b69ce44e0935d18704a577792");
        Result<Void> result = baiduCampaignService.relateMarketingEvent(vo);
        log.info(result.toString());
    }

    @Test
    public void syncThirdLeadData() {
        AdThirdSyncDataArg arg = new AdThirdSyncDataArg();
        String json = "{\"data\":{\"wechat_id\":\"\",\"leads_pool_id\":\"64bf417721dc320001f4e8d3\",\"from_marketing\":true,\"mobile\":\"***********\",\"account__c\":\"北京易动纷享科技有限责任公司0\",\"promotion_channel\":\"ad\",\"remark\":\"暂无\",\"source\":\"10\",\"marketing_promotion_source_id\":\"666ac79a35625b0001ccab8e\",\"field_7yIho__c\":\"Z成案词新\",\"date__c\":\"\",\"marketing_event_id\":\"6500112e6eb1cb000131f3fa\",\"object_describe_api_name\":\"LeadsObj\",\"object_describe_id\":\"LeadsObj\",\"name\":\"卡券\",\"field_9jau8__c\":\"找法律服务公司还是找律师事务所\"},\"adSource\":\"巨量引擎\",\"ea\":\"83668\",\"planName\":\"营销行业化\",\"keyword\":\"闵行律师\",\"messageId\":\"0A78308F000174ABE57C41BA904A67A9\",\"checkForwardToOtherCloud\":true}";
        arg = JSONObject.parseObject(json, AdThirdSyncDataArg.class);
        arg.setCheckForwardToOtherCloud(false);
        arg.setClueId("hahahacluid----" + 2);
        arg.getData().put("name", "测试广告重复推送2");
        baiduCampaignService.syncThirdLeadData(arg);
    }

    @Test
    public void querySpreadChannelStatByMarketingEventId() {
        String ea = "74164";
        String marketingEventId = "6283051ee8c869000137acf2";
        Result<List<QuerySpreadChannelStatResult>> result = baiduCampaignService.querySpreadChannelStatByMarketingEventId(ea, marketingEventId, "3", 1L, 2L);
        log.info("querySpreadChannelStatByMarketingEventId result:{}", result);
    }

    @Test
    public void syncBaiduCampaignToCrmObjTest() {
        AdAccountEntity entity = accountDAO.queryAccountById("8b524863784b46f9a46b3b7f6557befb");
        baiduAdMarketingManager.syncCampaignToMarketingEventObj("83668", entity, "百度");
    }

    @Test
    public void getTrendGraphDataList() {
        TrendGraphDataVO vo = new TrendGraphDataVO();
        vo.setStartTime(DateUtil.minusDay(new Date(), 20));
        vo.setEndTime(new Date());
        vo.setEa("83668");
        vo.setFsUserId(1000);
        vo.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        vo.setMarketingEventId("66e18390fa05620007640301");
        vo.setDataType(TypeEnum.BAIDU_FEED_AD_GROUP.getCode());
        Result<TrendGraphDataResult> result = baiduCampaignService.getTrendGraphDataList(vo);
        log.info("getTrendGraphDataList结果： {}", result);
    }

    @Test
    public void refreshPrototypeRoomAccountDataTest() {
        baiduAdMarketingManager.refreshPrototypeRoomAccountData("82255", 1000);
    }

    @Test
    public void syncKeywordByKeywordIds() {
        baiduAdMarketingManager.syncKeywordByKeywordIds("88146", "ea81e82e4bd64648bdd9f6926f842ed7", Lists.newArrayList(696137284970L, 696137284896L));
    }

    @Test
    public void queryAdGroupList() {
        QueryAdGroupListArg arg = new QueryAdGroupListArg();
        arg.setEa("83668");
        arg.setAdAccountId("8b524863784b46f9a46b3b7f6557befb");
        arg.setStartTime(DateUtil.minusDay(new Date(), 10));
        arg.setEndTime(new Date());
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setKeyword("品牌词");
        arg.setKeywordType("campaign");
        Result<PageResult<BaiduAdGroupVO>> result = baiduCampaignService.queryAdGroupList(arg);
        log.info("结果: {}", JsonUtil.toJson(result));
    }

    @Test
    public void queryAdGroupDetail() {
        QueryAdGroupDetailArg arg = new QueryAdGroupDetailArg();
        arg.setEa("83668");
        arg.setStartTime(DateUtil.minusDay(new Date(), 16));
        arg.setEndTime(new Date());
        arg.setId("b1b7866b954147118e0f0de3f91c9696");
        Result<BaiduAdGroupVO> result = baiduCampaignService.queryAdGroupDetail(arg);
        log.info("结果: {}", JsonUtil.toJson(result));
    }

    @Test
    public void refreshKeywordByAccountIdTest() {
        baiduCampaignService.refreshKeywordByAccountId("8b524863784b46f9a46b3b7f6557befb");
    }

    @Test
    public void listByUserMarketingIds() {
        List<UserMarketingCustomizeObjectRelationEntity> list = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds("74164", Lists.newArrayList("d24151845602437bb035e1d5a379e873"));
        log.info("结果: {}", JsonUtil.toJson(list));
    }
}
