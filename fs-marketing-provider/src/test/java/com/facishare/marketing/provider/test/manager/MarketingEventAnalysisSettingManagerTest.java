/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.LeadStageOptionVO;
import com.facishare.marketing.api.vo.marketingevent.MarketingEventAnalysisSettingVO;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.entity.marketingEvent.MarketingEventAnalysisSettingEntity;
import com.facishare.marketing.provider.manager.marketingEvent.MarketingEventAnalysisSettingManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class MarketingEventAnalysisSettingManagerTest extends BaseTest {

    @Autowired
    private MarketingEventAnalysisSettingManager manager;

    @Test
    public void test() {
        List<MarketingEventAnalysisSettingEntity> list = Lists.newArrayList();

        MarketingEventAnalysisSettingEntity entity = new MarketingEventAnalysisSettingEntity();

        MarketingEventAnalysisSettingVO vo = new MarketingEventAnalysisSettingVO();
        LeadStageOptionVO mql = new LeadStageOptionVO();
        mql.setLabel("潜在线索");
        mql.setValue("Lead");
        mql.setSelected(false);
        vo.setMqlDefinition(Lists.newArrayList(mql));

        LeadStageOptionVO sql = new LeadStageOptionVO();
        sql.setLabel("潜在线索");
        sql.setValue("Lead");
        sql.setSelected(true);
        vo.setSqlDefinition(Lists.newArrayList(sql));
        String ea = "74164";
        entity.setEa(ea);
        entity.setId(UUIDUtil.getUUID());
        entity.setSetting(JsonUtil.toJson(vo));
        manager.batchInsert(Lists.newArrayList(entity));

        log.info("获取配置： {}", manager.getByEa(ea));

        mql.setLabel("市场认可线索(MQL)");
        mql.setValue("MQL");
        mql.setSelected(true);
        manager.updateSetting(ea, JsonUtil.toJson(vo));
    }

    @Test
    public void initLeadStageDefinitionFromAdBigScreenSettingTest() {
        manager.initLeadStageDefinitionFromAdBigScreenSetting(null);
    }

}
