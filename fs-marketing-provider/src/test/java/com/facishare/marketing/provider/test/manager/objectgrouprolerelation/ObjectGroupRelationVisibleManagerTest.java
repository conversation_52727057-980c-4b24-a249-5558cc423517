package com.facishare.marketing.provider.test.manager.objectgrouprolerelation;

import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Slf4j
public class ObjectGroupRelationVisibleManagerTest extends BaseTest {

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Test
    public void testInsert() {
        ObjectGroupRoleRelationEntity entity = new ObjectGroupRoleRelationEntity();
        entity.setGroupId("qqqqqq");
        entity.setRoleId("23333333");
        entity.setId(UUIDUtil.getUUID());
        entity.setEa("74164");
        objectGroupRelationVisibleManager.insertRoleRelation(entity);

        ObjectGroupRoleRelationEntity entity2 = new ObjectGroupRoleRelationEntity();
        entity2.setGroupId("qqqqqq");
        entity2.setRoleId("6hghtht");
        entity2.setId(UUIDUtil.getUUID());
        entity2.setEa("74164");
        objectGroupRelationVisibleManager.batchInsertRoleRelation(Collections.singletonList(entity2));
    }

    @Test
    public void deleteByGroupId() {
        objectGroupRelationVisibleManager.deleteVisibleByGroupId("qqqqqq");
    }


    @Test
    public void getByGroupIdList() {
        ObjectGroupRoleRelationEntity entity = new ObjectGroupRoleRelationEntity();
        entity.setGroupId("qqqqqq");
        entity.setRoleId("23333333");
        entity.setId(UUIDUtil.getUUID());
        entity.setEa("74164");
        objectGroupRelationVisibleManager.insertRoleRelation(entity);
        List<ObjectGroupRoleRelationEntity>  list = objectGroupRelationVisibleManager.getRoleRelationByGroupIdList(Collections.singletonList("qqqqqq"));
        log.info("getByGroupIdList result : {}", list);
    }

    @Test
    public void getAccessibleGroup() {
        objectGroupRelationVisibleManager.getAccessibleGroup("74164", 1071, ObjectTypeEnum.USER_MARKETING_GROUP.getType());
    }

}
