/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.ai;

import com.facishare.marketing.api.service.ai.AiChatService;
import com.facishare.marketing.api.vo.ai.ChatCompleteVO;
import com.facishare.marketing.api.vo.ai.CreatePromptVO;
import com.facishare.marketing.api.vo.ai.PageQueryPromptVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.manager.ai.AiChatManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class AiChatServiceTest extends BaseTest {

    @Autowired
    private AiChatService aiChatService;

    private String ea = "zhenju0111";
    private Integer fsUserId = 1000;

    @Test
    public void createPrompt() throws Exception {
        CreatePromptVO obj = new CreatePromptVO();
        obj.setTitle("测试");
        obj.setCategory("0");
        obj.setPrompt("测试");
        obj.setScopeType("SELF");
        Result result = aiChatService.createPrompt(ea, fsUserId, obj);
        log.warn("createPrompt:{}", result);
    }

    @Test
    public void pageQueryPrompt() throws Exception {
        PageQueryPromptVO vo = new PageQueryPromptVO();
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setCategory("COLLECTION");
        vo.setKeyword("测试");
        Result<PageResult> pageResultResult = aiChatService.pageQueryPrompt(ea, fsUserId, vo);
        log.warn("pageQueryPrompt:{}", pageResultResult);
    }

    @Test
    public void collectPrompt() throws Exception {
        aiChatService.markPrompt(ea, fsUserId, "65a645caf0c1ab0001d2930b");
    }

    @Test
    public void chatCompleteWithSession() throws Exception {
        ChatCompleteVO vo = new ChatCompleteVO();
        vo.setPrompt("你是什么助手?");
        Result result = aiChatService.chatCompleteWithSession(ea, fsUserId, vo);
        System.out.println("result:" + GsonUtil.toJson(result));
    }

    @Autowired
    private AiChatManager aiChatManager;

    @Test
    public void assignRecord() throws Exception {
        aiChatManager.assignRecord("88146");
    }
}
