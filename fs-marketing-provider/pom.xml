<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-marketing</artifactId>
    <groupId>com.facishare.marketing</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fs-marketing-provider</artifactId>
  <packaging>war</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <fs-redisson-support.version>1.0.0-SNAPSHOT</fs-redisson-support.version>
    <fs-eservice-rest-api.version>9.1.0-SNAPSHOT</fs-eservice-rest-api.version>
    <junit-platform.version>1.9.0</junit-platform.version>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.9</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
              <goal>report</goal>
            </goals>
            <configuration>
              <propertyName>surefire.argLine</propertyName>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.gmavenplus</groupId>
        <artifactId>gmavenplus-plugin</artifactId>
        <version>1.9.0</version>
        <executions>
          <execution>
            <goals>
              <goal>compileTests</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M5</version>
        <configuration>
          <includes>
            <include>**/*.class</include>
          </includes>
          <argLine>
            ${surefire.argLine}
            -XX:+IgnoreUnrecognizedVMOptions
          </argLine>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <version>5.8.2</version>
          </dependency>
        </dependencies>
      </plugin>



    </plugins>
  </build>

  <dependencies>

    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <version>${junit-platform.version}</version>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-commons</artifactId>
      <version>${junit-platform.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-engine</artifactId>
      <version>${junit-platform.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-timezone-api</artifactId>
      <version>1.0.3-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-timezone-core</artifactId>
      <version>1.0.3-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-enterpriserelation-rest-api2</artifactId>
      <version>2.1.4-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-wechat-miniprogram-rest-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.beust</groupId>
      <artifactId>jcommander</artifactId>
      <version>1.72</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.msg</groupId>
      <artifactId>fs-message-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-wechat-rest-api</artifactId>
      <version>2.0.1-SNAPSHOT</version>
    </dependency>

      <dependency>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-stone-commons-client</artifactId>
      </dependency>

    <!--Jedis -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alicp.jetcache</groupId>
      <artifactId>jetcache-anno</artifactId>
      <version>2.5.12</version>
      <exclusions>
        <exclusion>
          <artifactId>kryo</artifactId>
          <groupId>com.esotericsoftware</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-crm-rest-api</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>transport</artifactId>
          <groupId>org.elasticsearch.client</groupId>
        </exclusion>
        <exclusion>
          <artifactId>transport-netty4-client</artifactId>
          <groupId>org.elasticsearch.plugin</groupId>
        </exclusion>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-auth-rest-api</artifactId>
      <version>1.2.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-rule-rest-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-license-rest-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-other-rest-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-wechat-proxy-core-api</artifactId>
      <version>2.1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-wechat-rest-api</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
        <exclusion>
          <artifactId>i18n-client</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
        <exclusion>
          <artifactId>bcprov-jdk16</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-bpm-rest-api</artifactId>
      <version>1.1.4-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-app-center-api</artifactId>
      <version>1.0.72-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.mankeep</groupId>
      <artifactId>fs-mankeep-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-common-util</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-excelant</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-qixin-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-qixin-api</artifactId>
      <version>0.1.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.3.5</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-api</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-excelant</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-outapi</artifactId>
      <version>1.0.2-SNAPSHOT</version>
    </dependency>

    <!-- Dubbo -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>httpcore</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-runtime</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spotbugs-annotations</artifactId>
          <groupId>com.github.spotbugs</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Netty: Required by dubbo-->
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.training</groupId>
      <artifactId>fs-training-outer-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <!-- 接入dubbo监控 -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>core-filter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>rpc-trace</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <!-- 配置中心 -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>config-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>spring-support</artifactId>
    </dependency>

    <!-- 更改集 -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-change-set-api</artifactId>
    </dependency>

    <!-- mybatis -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mybatis-spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>

    <!-- Rocketmq -->
<!--    <dependency>-->
<!--      <groupId>com.facishare.open</groupId>-->
<!--      <artifactId>rocketmq-spring-support</artifactId>-->
<!--      <version>1.0.1-SNAPSHOT</version>-->
<!--    </dependency>-->

<!--    <dependency>-->
<!--      <groupId>io.protostuff</groupId>-->
<!--      <artifactId>protostuff-runtime</artifactId>-->
<!--      <version>1.5.9</version>-->
<!--    </dependency>-->

<!--    <dependency>-->
<!--      <groupId>io.protostuff</groupId>-->
<!--      <artifactId>protostuff-core</artifactId>-->
<!--      <version>1.5.9</version>-->
<!--    </dependency>-->

    <!-- Spring -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>org.mockito</groupId>-->
<!--      <artifactId>mockito-core</artifactId>-->
<!--      <version>5.14.2</version>-->
<!--    </dependency>-->

    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-all</artifactId>
        <version>1.10.19</version>
    </dependency>

    <dependency>
      <groupId>com.jayway.jsonpath</groupId>
      <artifactId>json-path</artifactId>
      <version>2.4.0</version>
    </dependency>


    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-result</artifactId>
      <version>${fs-open-common-result.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-storage</artifactId>
      <version>${fs-open-common-storage.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>mybatis</artifactId>
          <groupId>org.mybatis</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mybatis-spring</artifactId>
          <groupId>org.mybatis</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>morphia</artifactId>
          <groupId>org.mongodb.morphia</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-common-utils</artifactId>
      <version>${fs-open-common-utils.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-wechat-dubbo-rest-outer-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>

    <!--warehourse -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsi-proxy</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-warehouse-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-api</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 企业员工信息 -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-adapter-api</artifactId>
<!--      <version>1.0.0-SNAPSHOT</version>-->
      <exclusions>
        <exclusion>
          <artifactId>i18n-client</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-netdisk-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <artifactId>fs-uc-api</artifactId>
      <groupId>com.facishare</groupId>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-wechat-sender-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml-schemas</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>i18n-client</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-sms-api</artifactId>
      <version>0.0.4-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>javassist-3.14.0-GA</artifactId>
          <groupId>org.ow2.util.bundles</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-runtime</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-mq</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-active-session-manage-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>asm</groupId>
        </exclusion>
      </exclusions>
      <artifactId>fs-contacts-search-api</artifactId>
    </dependency>

    <!-- 消息服务 -->
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-msg-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-msg-common</artifactId>
    </dependency>

    <!-- 离职员工mq -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-mds-event</artifactId>
      <version>1.0.4-SNAPSHOT</version>
    </dependency>

    <!-- 企业员工 -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-api</artifactId>
      <version>${fs-organization-api.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba.rocketmq</groupId>
          <artifactId>rocketmq-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- http -->
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.9</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.11</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.9</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpasyncclient</artifactId>
      <version>4.1.4</version>
    </dependency>

    <!--神策-->
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>datapersist</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-pay-business-api</artifactId>
      <version>0.1.8-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>poi</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>zkclient</artifactId>
          <groupId>com.github.sgroschupf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>retrofit-spring</artifactId>
          <groupId>com.github.zhxing</groupId>
        </exclusion>
        <exclusion>
          <artifactId>i18n-client</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
        <exclusion>
          <groupId>com.fxiaoke</groupId>
          <artifactId>fs-enterpriserelation-rest-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-wechat-union-core-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>asm</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>io.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-runtime</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-metadata-provider</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-unittest-all</artifactId>
      <version>1.1.0-SNAPSHOT</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>morphia</artifactId>
          <groupId>org.mongodb.morphia</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- JUnit -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
      <version>4.13.2</version>
    </dependency>

    <dependency>
      <groupId>com.twelvemonkeys.imageio</groupId>
      <artifactId>imageio</artifactId>
      <version>3.0.2</version>
      <type>pom</type>
    </dependency>

    <dependency>
      <groupId>com.twelvemonkeys.imageio</groupId>
      <artifactId>imageio-jpeg</artifactId>
      <version>3.0.2</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>account-bind-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>logconfig-core</artifactId>
    </dependency>

    <!-- 创建CRM订单接口 -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-webhook-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <!-- pass license 变更 -->
    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-license-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-statistic-outapi</artifactId>
      <version>1.0.2-SNAPSHOT</version>
    </dependency>

    <!-- 二维码识别 -->
    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>javase</artifactId>
      <version>3.4.0</version>
    </dependency>

    <dependency>
      <groupId>com.google.zxing</groupId>
      <artifactId>core</artifactId>
      <version>3.4.0</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>qywx-message-send-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>qywx-account-bind-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>http-spring-support</artifactId>
    </dependency>
  <!--服务业务组-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-online-consult-out-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

  <!--获取配置SFA-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-app-config</artifactId>
      <version>8.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>3.4.1</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-user-login-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-plat-privilege-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-dingtalk-api</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mongo-spring-support</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-api</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>rpc-trace</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-pod-client</artifactId>
      <version>1.1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.facishare.mankeep</groupId>
      <artifactId>fs-mankeep-common</artifactId>
      <version>1.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-excelant</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml-schemas</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>retrofit-spring2</artifactId>
      <version>2.0.3-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-common</artifactId>
      <version>1.0.3-SNAPSHOT</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.facishare.paas</groupId>
          <artifactId>fs-paas-auth-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare.paas</groupId>
          <artifactId>fs-paas-auth-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.zhxing</groupId>
      <artifactId>retrofit-spring</artifactId>
      <version>1.3.2-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <artifactId>protostuff-api</artifactId>
      <groupId>io.protostuff</groupId>
      <version>1.8.0</version>
    </dependency>

    <dependency>
      <artifactId>protostuff-core</artifactId>
      <groupId>io.protostuff</groupId>
      <version>1.8.0</version>
    </dependency>

    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
      <version>3.22.1</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-webpage-customer-api</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-redisson-support</artifactId>
      <version>${fs-redisson-support.version}</version>
    </dependency>

    <dependency>
      <groupId>com.vladsch.flexmark</groupId>
      <artifactId>flexmark-all</artifactId>
      <version>0.62.2</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-eservice-rest-api</artifactId>
      <version>${fs-eservice-rest-api.version}</version>
    </dependency>

    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j</artifactId>
      <version>0.25.0</version>
    </dependency>
    <dependency>
      <groupId>dev.langchain4j</groupId>
      <artifactId>langchain4j-core</artifactId>
      <version>0.25.0</version>
    </dependency>
    <dependency>
      <groupId>com.knuddels</groupId>
      <artifactId>jtokkit</artifactId>
      <version>0.6.1</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-paas-ai-api</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp-sse</artifactId>
      <version>${okhttp3.version}</version>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>limit-support</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-audit-log</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>biz-log-client</artifactId>
      <version>3.2.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.erpdss</groupId>
      <artifactId>connector-spring-support</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.smsplatform</groupId>
      <artifactId>fs-sms-platform-outapi</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-common</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-auth-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare.paas</groupId>
      <artifactId>fs-paas-auth-client</artifactId>
      <!--      死活用不上父pom的版本 直接写死 -->
<!--      <version>2.6.0-SNAPSHOT</version>-->
    </dependency>

    <dependency>
      <groupId>com.jayway.jsonpath</groupId>
      <artifactId>json-path</artifactId>
      <version>2.8.0</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-email-proxy-rest-api</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
  </dependencies>

</project>
