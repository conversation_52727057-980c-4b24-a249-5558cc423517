package com.facishare.marketing.api.arg;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.vo.NoticeStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class KfCallbackArg implements Serializable {
    public static final String TAG = "OFFICIAL_WEBSITE_THIRD_TAG";

    @ApiModelProperty("消息主要内容")
    private String content;

    /** @see com.facishare.marketing.common.enums.ThirdKFCallbackEventEnum*/
    @ApiModelProperty("推送事件类型，但是53kd关键词推送的cmd在content中")
    private String cmd;

    @ApiModelProperty("验证码")
    private String token;

    @ApiModelProperty("消息id")
    private String msg_id;

    @ApiModelProperty("ea")
    private String ea;

    public static KfCallbackArg fromToKfCallbackArg(byte[] bytes) {
        KfCallbackArg kfCallbackArg = JSON.parseObject(new String(bytes), KfCallbackArg.class);
        return kfCallbackArg;
    }

}
