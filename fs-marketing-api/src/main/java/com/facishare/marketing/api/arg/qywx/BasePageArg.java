package com.facishare.marketing.api.arg.qywx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString(callSuper = true)
public class BasePageArg extends QYWXBaseArg {

    @ApiModelProperty(value = "当前页码", required = true)
    private Integer pageNum;
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "第一页请求时间戳")
    private Long time;

}
