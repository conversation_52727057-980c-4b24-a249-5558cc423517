package com.facishare.marketing.api.arg.marketingSpreadSource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 营销推广来源详情记录基础参数，主要是提交表单接口已经包含了很多营销推广来源详情记录的参数，这里要单独抽出来，让CustomizeFormDataEnrollArg继承
 * 营销推广来源详情记录全部参数查看 MarketingPromotionSourceArg
 */
@Data
public class MarketingPromotionSourceBaseArg implements Serializable {

    @ApiModelProperty(value = "企微推广员工ID", hidden = true)
    private String spreadQywxUserId;

    @ApiModelProperty(value = "会员ID", hidden = true)
    private String spreadMemberId;

    @ApiModelProperty(value = "营销用户ID", hidden = true)
    private String userMarketingId;

    @ApiModelProperty("来源URL")
    private String sourceUrl;

    @ApiModelProperty("落地页名称")
    private String landingPageName;

    @ApiModelProperty("落地页")
    private String landingPageUrl;

    @ApiModelProperty("落地类型")
    private String landingPageType;

    @ApiModelProperty("落地页平台")
    private String landingPagePlatform;

    @ApiModelProperty("转化页名称")
    private String convertPageName;

    @ApiModelProperty("转化页名称")
    private String convertPageUrl;

    @ApiModelProperty("推广的用户ID标识 可能是访客ID 也可能是微信的openId 主要用来换区营销用户ID的")
    private String spreadUserIdentifyId;

    @ApiModelProperty("推广的用户类型 ")
    private Integer spreadUserType;

    @ApiModelProperty("fsCTA 客户自定义参数")
    private String fsCTA;

    @ApiModelProperty("0:小程序 1:h5 2:web 3:app")
    private Integer client;

    @ApiModelProperty("操作系统")
    private String operateSystem;

    @ApiModelProperty("浏览器")
    private String browser;
    @ApiModelProperty("ip")
    private String ipAddr;

    @ApiModelProperty("来源网址类型")
    private String sourceUrlType;

    public boolean allFieldNull() {
        return  StringUtils.isBlank(spreadQywxUserId) && StringUtils.isBlank(spreadMemberId) && StringUtils.isBlank(spreadUserIdentifyId)
                && StringUtils.isBlank(sourceUrl) && StringUtils.isBlank(landingPageName)
                && StringUtils.isBlank(convertPageName) && StringUtils.isBlank(convertPageUrl);
    }

}
