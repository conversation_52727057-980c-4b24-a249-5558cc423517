package com.facishare.marketing.api.vo.advertiser;

import com.facishare.marketing.api.arg.BasePageArg;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import lombok.Data;

import java.util.Date;

@Data
public class QueryTencentAdGroupListVO extends BasePageArg {
    private String ea;
    private Integer status;
    private String campaignName;
    private String adGroupName;
    private Date startTime;
    private Date endTime;
    private String adAccountId;
    /**
     * @see TypeEnum
     */
    private String dataType;
}
