package com.facishare.marketing.api.data.usermarketingaccount;

import com.facishare.marketing.api.data.crmData.OwnerData;
import com.facishare.marketing.api.data.usermarketingaccounttag.TagData;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 28/03/2019
 */
@Data
public class UserMarketingAccountData implements Serializable {
    @ApiModelProperty(value = "营销用户id")
    private String userMarketingAccountId;
    @ApiModelProperty(value = "微信头像")
    private List<Map<String,Object>> weChatAvatar;
    @ApiModelProperty(value = "微信名称")
    private String weChatName;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "手机")
    private String phone;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "公司名")
    private String companyName;
    @ApiModelProperty(value = "负责人")
    private List<OwnerData> owners;
    @ApiModelProperty(value = "部门")
    private String department;
    @ApiModelProperty(value = "职务")
    private String position;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "标签列表")
    private TagNameList tagNameList;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    @ApiModelProperty(value = "修改时间")
    private Long updateTime;
    @ApiModelProperty("企业ea")
    private String ea;
    @ApiModelProperty("性别 0-无 1-男 2-女")
    private Integer sex;
    @ApiModelProperty("联系人名称")
    private String crmContactName;
    @ApiModelProperty("线索名称")
    private String crmLeadName;
    @ApiModelProperty("客户名称")
    private String crmCustomerName;
    @ApiModelProperty("合作伙伴名称")
    private String crmPartnerName;
    @ApiModelProperty("微信用户名称")
    private String crmWxUserName;
    @ApiModelProperty("自定义对象数据名称")
    private String customizeObjectDataName;
    @ApiModelProperty("地区")
    private String area;
    @ApiModelProperty("来源渠道")
    private String source;
    @ApiModelProperty("微信服务号ID")
    private List<WxServiceData> wxServiceDatas;
    @ApiModelProperty("联系人ID")
    private String crmContactId;
    @ApiModelProperty("销售线索ID")
    private String crmLeadId;
    @ApiModelProperty("客户ID")
    private String crmCustomerId;
    @ApiModelProperty("微信用户ID")
    private String crmWxUserId;
    @ApiModelProperty("自定义对象数据ID")
    private String customizeObjectDataId;
    @ApiModelProperty("合作伙伴ID")
    private String crmPartnerId;
    @ApiModelProperty("企业微信客户对象ID")
    private String crmWxWorkExternalUserId;
    @ApiModelProperty("企业微信客户外部联系人ID")
    private String externalUserId;
    @ApiModelProperty("企业微信客户对象名称")
    private String crmWxWorkExternalUserName;
    @ApiModelProperty("是否关注微信服务号")
    private boolean beWxApp;
    @ApiModelProperty("是否客户")
    private boolean beCustomer = false;
    @ApiModelProperty("是否合作伙伴")
    private boolean bePartner = false;
    @ApiModelProperty("是否联系人")
    private boolean beContact = false;
    @ApiModelProperty("是否线索")
    private boolean beLead = false;
    @ApiModelProperty("是否会员(0 非会员 1 会员)")
    private boolean beMember = false;
    @ApiModelProperty(value = "会员卡号")
    private String memberCardId;
    @ApiModelProperty(value = "等级名称")
    private String gradeName;
    @ApiModelProperty(value = "等级Id")
    private String gradeId;
    @ApiModelProperty(value = "成长值")
    private String growthValue;
    @ApiModelProperty(value = "积分")
    private String integralValue;

    @ApiModelProperty("New:联系人列表")
    private List<CrmObjectIdName> crmContactInfos;
    @ApiModelProperty("New:线索列表")
    private List<CrmObjectIdName> crmLeadInfos;
    @ApiModelProperty("New:客户列表")
    private List<CrmObjectIdName> crmCustomerInfos;
    @ApiModelProperty("New:微信用户列表")
    private List<CrmObjectIdName> crmWxUserInfos;
    @ApiModelProperty("New:企业微信客户列表")
    private List<CrmObjectIdName> crmWxWorkExternalInfos;
    @ApiModelProperty("New:会员列表")
    private List<CrmObjectIdName> crmMemberInfos;
    @ApiModelProperty("New:自定义对象列表")
    private List<CrmObjectIdName> crmCustomizeObjectInfos;
    @ApiModelProperty("New:用户头像Url")
    private String avatar;

    public void pushCrmContactInfo(String id, String name){
        if (crmContactInfos == null){
            crmContactInfos = new ArrayList<>();
        }
        crmContactInfos.add(new CrmObjectIdName(id, name));
    }

    public void pushCrmCustomerInfo(String id, String name){
        if (crmCustomerInfos == null){
            crmCustomerInfos = new ArrayList<>();
        }
        crmCustomerInfos.add(new CrmObjectIdName(id, name));
    }

    public void pushCrmLeadInfo(String id, String name){
        if (crmLeadInfos == null){
            crmLeadInfos = new ArrayList<>();
        }
        crmLeadInfos.add(new CrmObjectIdName(id, name));
    }

    public void pushCrmWxUserInfo(String id, String name){
        if (crmWxUserInfos == null){
            crmWxUserInfos = new ArrayList<>();
        }
        crmWxUserInfos.add(new CrmObjectIdName(id, name));
    }

    public void pushCrmWxWorkExternalUserInfos(String id, String name){
        if (crmWxWorkExternalInfos == null){
            crmWxWorkExternalInfos = new ArrayList<>();
        }
        crmWxWorkExternalInfos.add(new CrmObjectIdName(id, name));
    }

    public void pushCrmMemberInfos(String id, String name){
        if (crmMemberInfos == null){
            crmMemberInfos = new ArrayList<>();
        }
        crmMemberInfos.add(new CrmObjectIdName(id, name));
    }

    public void pushCrmCustomizeObjectInfo(String id, String name){
        if (crmCustomizeObjectInfos == null){
            crmCustomizeObjectInfos = new ArrayList<>();
        }
        crmCustomizeObjectInfos.add(new CrmObjectIdName(id, name));
    }


    public UserMarketingAccountData() {
    }
    public UserMarketingAccountData(String userMarketingAccountId) {
        this.userMarketingAccountId = userMarketingAccountId;
    }

    @Data
    public static class CrmObjectIdName implements Serializable{
        public CrmObjectIdName() {
        }

        public CrmObjectIdName(String id, String name) {
            this.id = id;
            this.name = name;
        }

        private String id;
        private String name;
    }
}
