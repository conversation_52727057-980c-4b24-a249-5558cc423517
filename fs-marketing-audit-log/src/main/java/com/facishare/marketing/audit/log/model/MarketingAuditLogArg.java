package com.facishare.marketing.audit.log.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketingAuditLogArg {
    private String appName; //服务名称
    private String profile; //容器环境
    private String podIp; //容器ip
    private Long createTime; //日志上报时间
    private String clientIp; //外部调用ip
    private String traceId; //分布式跟踪id
    private String ea; //租户ea
    private String ei; //租户ei信息
    private String userId; //用户id
    private String bizName; //服务处理bizname
    private String actionName;
    private String messageId; //MQ消息id
    private String message; //消息体
    private String extra; //更多辅助信息
    private String objectApiName; //对象api
    private String objectIds; //数据ids
    private Long cost; //处理总耗时
    private String status; // 状态：执行成功、执行失败
    private Long cost1; //扩展用
    private Long cost2;
    private Long cost3;
    private Long cost4;
    private Long cost5;
    private Long cost6;
    private Long cost7;
    private Long cost8;
    private Long cost9;
    private String extra1;
    private String extra2;
    private String extra3;
    private String extra4;
    private String extra5;
    private String extra6;
    private String extra7;
    private String extra8;
    private String extra9;
}
