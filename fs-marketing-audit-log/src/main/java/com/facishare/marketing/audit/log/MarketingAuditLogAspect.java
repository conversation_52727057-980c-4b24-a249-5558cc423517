package com.facishare.marketing.audit.log;

import com.facishare.marketing.audit.log.context.MarketingAuditLogContext;
import com.facishare.marketing.audit.log.model.MarketingAuditLogArg;
import com.github.trace.TraceContext;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class MarketingAuditLogAspect {
    private static final Logger log = LoggerFactory.getLogger(MarketingAuditLogAspect.class);
    private final SpelExpressionParser parser = new SpelExpressionParser();
    private final DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    String methodName;
    long startTime;

    @Pointcut(value = "execution(* *(..))")
    public void pointcut() {
    }

    @Autowired
    private MarketingAuditLogService marketingAuditLogService;


    @Around(value = "pointcut() && @annotation(marketingAuditLog)")
    public Object around(ProceedingJoinPoint joinPoint, MarketingAuditLog marketingAuditLog) throws Throwable {
        methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
        startTime = System.currentTimeMillis();
        Object proceed = null;
        MarketingAuditLogArg marketingAuditLogArg = MarketingAuditLogArg.builder().build();
        Throwable caughtException = null;

        try {
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            caughtException = e;
        } finally {
            try {
                long cost = System.currentTimeMillis() - startTime;
                Object[] args = joinPoint.getArgs();
                Class<EntityConverter> convertClass = (Class<EntityConverter>) marketingAuditLog.convertClass();
                Class<?> entityClass = marketingAuditLog.entityClass();
                if (args != null && args.length > 0 && convertClass != EntityConverter.class) {
                    for (Object arg : args) {
                        if (arg != null && arg.getClass() == entityClass) {
                            EntityConverter converter = convertClass.newInstance();
                            marketingAuditLogArg = converter.convert(arg);
                            break;
                        }
                    }
                }
                marketingAuditLogArg.setCost(cost);
                if (StringUtils.isNotBlank(marketingAuditLog.bizName())) {
                    marketingAuditLogArg.setBizName(marketingAuditLog.bizName());
                }
                marketingAuditLogArg = resolveExpress(marketingAuditLogArg, marketingAuditLog, joinPoint);
                if (marketingAuditLogArg != null) {
                    calCost(startTime, marketingAuditLogArg, marketingAuditLog);
                    if (caughtException != null) {
                        marketingAuditLogArg.setStatus("false");
                        if (StringUtils.isNotBlank(marketingAuditLogArg.getMessage())){
                            marketingAuditLogArg.setMessage(caughtException.getMessage());
                        }
                    }
                    marketingAuditLogService.sendAuditLog(marketingAuditLogArg, marketingAuditLog.topic());
                }
            } catch (Throwable e) {
                log.warn("Error while processing audit log. Method name: {}, traceId: {}, error: {}",
                        methodName, TraceContext.get().getTraceId(), e.getMessage());
            } finally {
                MarketingAuditLogContext.clearContext();
            }
        }

        if (caughtException != null) {
            throw caughtException;
        }

        return proceed;
    }

    private MarketingAuditLogArg resolveExpress(MarketingAuditLogArg marketingAuditLogArg, MarketingAuditLog auditLog, JoinPoint joinPoint) {
        String statusSpEL = auditLog.status();
        String bizNameSpEL = auditLog.bizName();
        String messageIdSpEL = auditLog.messageId();
        String messageSpEL = auditLog.message();
        String eaSpEL = auditLog.ea();
        String eiSpEL = auditLog.ei();
        String userIdSpEL = auditLog.userId();
        String objectApiNameSpEL = auditLog.objectApiName();
        String objectIdsSpEL = auditLog.objectIds();
        String conditionSpEL = auditLog.condition();
        String costSpEL = auditLog.cost();
        String cost1SpEL = auditLog.cost1();
        String cost2SpEL = auditLog.cost2();
        String cost3SpEL = auditLog.cost3();
        String cost4SpEL = auditLog.cost4();
        String cost5SpEL = auditLog.cost5();
        String cost6SpEL = auditLog.cost6();
        String cost7SpEL = auditLog.cost7();
        String cost8SpEL = auditLog.cost8();
        String cost9SpEL = auditLog.cost9();
        String extraSpEL = auditLog.extra();
        String extra1SpEL = auditLog.extra1();
        String extra2SpEL = auditLog.extra2();
        String extra3SpEL = auditLog.extra3();
        String extra4SpEL = auditLog.extra4();
        String extra5SpEL = auditLog.extra5();
        String extra6SpEL = auditLog.extra6();
        String extra7SpEL = auditLog.extra7();
        String extra8SpEL = auditLog.extra8();
        String extra9SpEL = auditLog.extra9();

        try {
            Object[] arguments = joinPoint.getArgs();
            Method method = getMethod(joinPoint);
            String[] params = discoverer.getParameterNames(method);
            StandardEvaluationContext context = MarketingAuditLogContext.getContext();
            if (params != null) {
                for (int len = 0; len < params.length; len++) {
                    context.setVariable(params[len], arguments[len]);
                }
            }

            if (StringUtils.isNotBlank(conditionSpEL)) {
                boolean conditionPassed = parseParamToBoolean(conditionSpEL, context);
                if (!conditionPassed) {
                    return null;
                }
            }

            if (StringUtils.isNotBlank(bizNameSpEL) && bizNameSpEL.startsWith("#")) {
                marketingAuditLogArg.setBizName(parseParamToString(bizNameSpEL, context));
            }
            if (StringUtils.isNotBlank(statusSpEL)) {
                marketingAuditLogArg.setStatus(parseParamToString(statusSpEL, context));
            }
            if (StringUtils.isNotBlank(messageIdSpEL)) {
                marketingAuditLogArg.setMessageId(parseParamToString(messageIdSpEL, context));
            }
            if (StringUtils.isNotBlank(messageSpEL)) {
                marketingAuditLogArg.setMessage(parseParamToString(messageSpEL, context));
            }
            if (StringUtils.isNotBlank(eaSpEL)) {
                marketingAuditLogArg.setEa(parseParamToString(eaSpEL, context));
            }
            if (StringUtils.isNotBlank(eiSpEL)) {
                marketingAuditLogArg.setEi(parseParamToString(eiSpEL, context));
            }
            if (StringUtils.isNotBlank(userIdSpEL)) {
                marketingAuditLogArg.setUserId(parseParamToString(userIdSpEL, context));
            }
            if (StringUtils.isNotBlank(objectApiNameSpEL)) {
                marketingAuditLogArg.setObjectApiName(parseParamToString(objectApiNameSpEL, context));
            }
            if (StringUtils.isNotBlank(objectIdsSpEL)) {
                marketingAuditLogArg.setObjectIds(parseParamToString(objectIdsSpEL, context));
            }
            if (StringUtils.isNotBlank(extraSpEL)) {
                marketingAuditLogArg.setExtra(parseParamToString(extraSpEL, context));
            }
            if (StringUtils.isNotBlank(extra1SpEL)) {
                marketingAuditLogArg.setExtra1(parseParamToString(extra1SpEL, context));
            }
            if (StringUtils.isNotBlank(extra2SpEL)) {
                marketingAuditLogArg.setExtra2(parseParamToString(extra2SpEL, context));
            }
            if (StringUtils.isNotBlank(extra3SpEL)) {
                marketingAuditLogArg.setExtra3(parseParamToString(extra3SpEL, context));
            }
            if (StringUtils.isNotBlank(extra4SpEL)) {
                marketingAuditLogArg.setExtra4(parseParamToString(extra4SpEL, context));
            }
            if (StringUtils.isNotBlank(extra5SpEL)) {
                marketingAuditLogArg.setExtra5(parseParamToString(extra5SpEL, context));
            }
            if (StringUtils.isNotBlank(extra6SpEL)) {
                marketingAuditLogArg.setExtra6(parseParamToString(extra6SpEL, context));
            }
            if (StringUtils.isNotBlank(extra7SpEL)) {
                marketingAuditLogArg.setExtra7(parseParamToString(extra7SpEL, context));
            }
            if (StringUtils.isNotBlank(extra8SpEL)) {
                marketingAuditLogArg.setExtra8(parseParamToString(extra8SpEL, context));
            }
            if (StringUtils.isNotBlank(extra9SpEL)) {
                marketingAuditLogArg.setExtra9(parseParamToString(extra9SpEL, context));
            }
            if (StringUtils.isNotBlank(costSpEL)) {
                marketingAuditLogArg.setCost(parseParamToLong(costSpEL, context));
            }
            if (StringUtils.isNotBlank(cost1SpEL)) {
                marketingAuditLogArg.setCost1(parseParamToLong(cost1SpEL, context));
            }
            if (StringUtils.isNotBlank(cost2SpEL)) {
                marketingAuditLogArg.setCost2(parseParamToLong(cost2SpEL, context));
            }
            if (StringUtils.isNotBlank(cost3SpEL)) {
                marketingAuditLogArg.setCost3(parseParamToLong(cost3SpEL, context));
            }
            if (StringUtils.isNotBlank(cost4SpEL)) {
                marketingAuditLogArg.setCost4(parseParamToLong(cost4SpEL, context));
            }
            if (StringUtils.isNotBlank(cost5SpEL)) {
                marketingAuditLogArg.setCost5(parseParamToLong(cost5SpEL, context));
            }
            if (StringUtils.isNotBlank(cost6SpEL)) {
                marketingAuditLogArg.setCost6(parseParamToLong(cost6SpEL, context));
            }
            if (StringUtils.isNotBlank(cost7SpEL)) {
                marketingAuditLogArg.setCost7(parseParamToLong(cost7SpEL, context));
            }
            if (StringUtils.isNotBlank(cost8SpEL)) {
                marketingAuditLogArg.setCost8(parseParamToLong(cost8SpEL, context));
            }
            if (StringUtils.isNotBlank(cost9SpEL)) {
                marketingAuditLogArg.setCost9(parseParamToLong(cost9SpEL, context));
            }
        } catch (Exception e) {
            log.error("MarketingAuditLogAspect resolveExpress error: ", e);
        }
        return marketingAuditLogArg;
    }

    private void calCost(long startTime, MarketingAuditLogArg marketingAuditLogArg, MarketingAuditLog auditLog) {
        if (!auditLog.calCost()) {
            return;
        }
        try {
            long lastNonNullTime = startTime;

            if (marketingAuditLogArg.getCost1() != null) {
                marketingAuditLogArg.setCost1(marketingAuditLogArg.getCost1() - lastNonNullTime);
                lastNonNullTime = marketingAuditLogArg.getCost1() + lastNonNullTime;
            }
            if (marketingAuditLogArg.getCost2() != null) {
                marketingAuditLogArg.setCost2(marketingAuditLogArg.getCost2() - lastNonNullTime);
                lastNonNullTime = marketingAuditLogArg.getCost2() + lastNonNullTime;
            }
            if (marketingAuditLogArg.getCost3() != null) {
                marketingAuditLogArg.setCost3(marketingAuditLogArg.getCost3() - lastNonNullTime);
                lastNonNullTime = marketingAuditLogArg.getCost3() + lastNonNullTime;
            }
            if (marketingAuditLogArg.getCost4() != null) {
                marketingAuditLogArg.setCost4(marketingAuditLogArg.getCost4() - lastNonNullTime);
                lastNonNullTime = marketingAuditLogArg.getCost4() + lastNonNullTime;
            }
            if (marketingAuditLogArg.getCost5() != null) {
                marketingAuditLogArg.setCost5(marketingAuditLogArg.getCost5() - lastNonNullTime);
            }
        } catch (Exception e) {
            log.warn("MarketingAuditLogAspect calCost error: ", e);
        }
    }

    private Method getMethod(JoinPoint joinPoint) {
        Method method = null;
        try {
            Signature signature = joinPoint.getSignature();
            MethodSignature ms = (MethodSignature) signature;
            Object target = joinPoint.getTarget();
            method = target.getClass().getMethod(ms.getName(), ms.getParameterTypes());
        } catch (NoSuchMethodException e) {
            log.error("OperationLogAspect getMethod error", e);
        }
        return method;
    }

    private String parseParamToString(String spel, StandardEvaluationContext context) {
        Expression paramExpression = parser.parseExpression(spel);
        return paramExpression.getValue(context, String.class);
    }

    private Long parseParamToLong(String spel, StandardEvaluationContext context) {
        Expression bizIdExpression = parser.parseExpression(spel);
        return bizIdExpression.getValue(context, Long.class);
    }

    private boolean parseParamToBoolean(String spel, StandardEvaluationContext context) {
        Expression conditionExpression = parser.parseExpression(spel);
        return Boolean.TRUE.equals(conditionExpression.getValue(context, Boolean.class));
    }

}
