package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.ListActivitiesArg;
import com.facishare.marketing.api.arg.conference.QueryActivityEnrollTimeArg;
import com.facishare.marketing.api.arg.conference.SignInArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityListArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityVO;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.ActivityResult;
import com.facishare.marketing.api.result.conference.CheckSignInStatusResult;
import com.facishare.marketing.api.arg.conference.CheckSignInStatusArg;
import com.facishare.marketing.api.result.conference.GetEnrollTimeResult;
import com.facishare.marketing.api.result.conference.SignInResult;
import com.facishare.marketing.api.result.permission.DataPermissionResult;
import com.facishare.marketing.api.service.kis.ActivityService;
import com.facishare.marketing.api.service.permission.DataPermissionService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.GetActivityInfoByIdArg;
import com.facishare.marketing.api.arg.conference.GetEnrollTimeArg;
import com.facishare.marketing.web.kis.controller.IActivityController;
import com.facishare.marketing.web.kis.interceptor.BrowserUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.IdentityInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.OuterUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @创建人 zhengliy
 * @创建时间 2019/2/22 16:20
 * @描述
 */
@Controller
@Slf4j
public class ActivityController implements IActivityController {

    @Autowired
    private ActivityService activityService;
    @Autowired
    private DataPermissionService dataPermissionService;

    @Override
    public Result listActivities(@RequestBody com.facishare.marketing.web.kis.arg.ListActivityArg arg) {
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_60));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_61));
        Preconditions.checkNotNull(arg.getState(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_62));
        ListActivitiesArg vo = BeanUtil.copy(arg, ListActivitiesArg.class);
        return activityService.webListActivities(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), UserInfoKeeper.getEi(), vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<ActivityResult> queryActivityInfoById(@RequestBody GetActivityInfoByIdArg arg) {
        return activityService.getActivityInfoById(arg.getId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<SignInResult> signIn(@RequestBody com.facishare.marketing.web.kis.arg.conference.SignInArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ActivityController.signIn param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        SignInArg signInArg = BeanUtil.copy(arg, SignInArg.class);
        if (identityCheckType == null) {
            log.warn("ActivityController.signIn error identityCheckType error identityCheckType is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            signInArg.setEnrollUserEa(UserInfoKeeper.getEa());
            signInArg.setEnrollUserFsUid(UserInfoKeeper.getFsUserId());
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            signInArg.setOpenId(OuterUserInfoKeeper.getOpenId());
            signInArg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            signInArg.setFingerPrint(BrowserUserInfoKeeper.getFingerPrint());
        }
        return activityService.signIn(signInArg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<CheckSignInStatusResult> checkSignInStatus(@RequestBody com.facishare.marketing.web.kis.arg.conference.CheckSignInStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ActivityController.checkSignInStatus param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        CheckSignInStatusArg checkSignInStatusArg = BeanUtil.copy(arg, CheckSignInStatusArg.class);
        if (identityCheckType == null) {
            log.warn("ActivityController.checkSignInStatus error identityCheckType error identityCheckType is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            checkSignInStatusArg.setEnrollUserEa(UserInfoKeeper.getEa());
            checkSignInStatusArg.setEnrollUserFsUid(UserInfoKeeper.getFsUserId());
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            checkSignInStatusArg.setOpenId(OuterUserInfoKeeper.getOpenId());
            checkSignInStatusArg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            checkSignInStatusArg.setFingerPrint(BrowserUserInfoKeeper.getFingerPrint());
        }
        return activityService.checkSignInStatus(checkSignInStatusArg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<PageResult<ActivityVO>> list(@RequestBody ActivityListArg arg) {
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_60));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_61));
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilter("is_mobile_display", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("0"));
        query.addFilter("life_status", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("normal"));
        //添加数据权限筛选条件
        Result<DataPermissionResult> dataPermissionResult = dataPermissionService.getDataPermission(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
        if (dataPermissionResult.isSuccess() && dataPermissionResult.getData() != null && dataPermissionResult.getData().isStatus()) {
            List<Integer> dataDepartments = dataPermissionResult.getData().getDataDepartments();
            if (CollectionUtils.isNotEmpty(dataDepartments)) {
                query.addFilter("data_own_organization", "IN", dataDepartments.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }
        filterData.setQuery(query);
        arg.setFilterData(filterData);
        arg.setFsEi(UserInfoKeeper.getEi());
        arg.setFsEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return activityService.listActivities(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetEnrollTimeResult> queryActivityEnrollEndTime(@RequestBody GetEnrollTimeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ActivityController.queryActivityEnrollEndTime param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryActivityEnrollTimeArg activityEnrollTimeArg = BeanUtil.copy(arg, QueryActivityEnrollTimeArg.class);
        return activityService.queryActivityEnrollEndTime(activityEnrollTimeArg);
    }
}