package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.CheckMemberSubmitResult;
import com.facishare.marketing.api.result.live.*;
import com.facishare.marketing.api.result.permission.DataPermissionResult;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.service.permission.DataPermissionService;
import com.facishare.marketing.api.vo.live.GetLiveViewUrlVO;
import com.facishare.marketing.api.vo.live.ListVO;
import com.facishare.marketing.api.vo.live.LiveMaterialsVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LiveRoleEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.PhoneNumberCheck;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.api.arg.CheckMemberSubmitArg;
import com.facishare.marketing.web.kis.arg.LiveArg;
import com.facishare.marketing.web.kis.arg.LiveMaterialsArg;
import com.facishare.marketing.web.kis.arg.live.*;
import com.facishare.marketing.web.kis.controller.ILiveController;
import com.facishare.marketing.web.kis.interceptor.BrowserUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.IdentityInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.OuterUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhengh on 2020/4/2.
 */
@Controller
@Slf4j
public class LiveController implements ILiveController{
    @Autowired
    private LiveService liveService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private DataPermissionService dataPermissionService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetViewUrlResult> getViewUrl(@RequestBody GetLiveViewUrlArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getLiveKey()) || StringUtils.isBlank(arg.getPhone())){
            log.info("LiveController.getViewUrl failed arg error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetLiveViewUrlVO vo = new GetLiveViewUrlVO();
        vo.setPhone(arg.getPhone());
        vo.setId(arg.getLiveKey());
        vo.setName(arg.getName());
        return liveService.getViewUrl(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<LiveLectureUrlResult> getLectureUrl(@RequestBody GetLectureViewUrlArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getLiveKey())){
            log.info("LiveController.getLectureUrl failed arg error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return liveService.getLectureUrl(arg.getLiveKey(), arg.getPassword(),arg.getType());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetLiveDetailResult> getDetail(@RequestBody GetLiveDetailArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())){
            log.info("LiveController.getDetail failed arg error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        }

       String wxAppId = null;
       String openId = null;
       if (identityCheckType == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType()){
            wxAppId = OuterUserInfoKeeper.getWxAppId();
            openId = OuterUserInfoKeeper.getOpenId();
        }
        return liveService.getDetail(null, arg.getId(), LiveRoleEnum.VIEW_ROLE.getRole(), identityCheckType, BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), wxAppId, openId);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetLiveDetailResult> getParentDetail(@RequestBody GetLiveDetailArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())){
            log.info("LiveController.getDetail failed arg error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        }

        String wxAppId = null;
        String openId = null;
        if (identityCheckType == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType()){
            wxAppId = OuterUserInfoKeeper.getWxAppId();
            openId = OuterUserInfoKeeper.getOpenId();
        }
        return liveService.getParentDetail(null, arg.getId(), LiveRoleEnum.VIEW_ROLE.getRole(), identityCheckType, BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), wxAppId, openId);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetLiveViewCheckInfoResult> getViewCheckInfo(@RequestBody GetLiveViewCheckInfoArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getLiveKey())){
            log.info("LiveController.getDetail failed arg error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return liveService.getViewCheckInfo(arg.getLiveKey());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<Integer> getLiveStatus(@RequestBody GetLiveStatusArg arg) {
        return liveService.getLiveStatus(arg.getId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<CheckUserHaveSubmitResult> checkXiaoetongSubmit(@RequestBody CheckXiaoetongSubmitArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return liveService.checkWxServiceUserHaveSubmit(arg.getId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            return liveService.checkH5UserHaveSubmit(arg.getId(), BrowserUserInfoKeeper.getFingerPrint(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), arg.isCheckMemberAndForm());
        }

        return Result.newSuccess();
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<CheckAndSyncUserToXiaoetongResult> checkAndSyncUserToXiaoetong(@RequestBody SyncSubmitUserToXiaoetongArg arg) {
        if (StringUtils.isEmpty(arg.getId()) || StringUtils.isEmpty(arg.getPhone())){
            throw new IllegalArgumentException();
        }

        return liveService.checkAndSyncUserToXiaoetong(arg.getId(), arg.getPhone(), arg.getXiaoetongViewUrl());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<XiaoetongCommonLoginResult> xiaoetongCommonLogin(@RequestBody XiaoetongCommonLoginArg arg) {
        if (StringUtils.isEmpty(arg.getAppId())|| StringUtils.isEmpty(arg.getXiaoetongViewUrl()) || StringUtils.isEmpty(arg.getPhone())
                || StringUtils.isEmpty(arg.getPhoneVerifyCode())){
            throw new IllegalArgumentException();
        }

        return liveService.xiaoetongCommonLogin(arg.getAppId(), arg.getPhone(), arg.getXiaoetongViewUrl(), arg.getPhoneVerifyCode());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetMarketingLiveByXiaoetongIdResult> getMarketingLiveByXiaoetongId(@RequestBody GetMarketingLiveByXiaoetongIdArg arg) {
        if (StringUtils.isEmpty(arg.getXiaoetongId())){
            throw new IllegalArgumentException();
        }

        return liveService.getMarketingLiveByXiaoetongId(arg.getXiaoetongId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result sendXiaoetongLoginSms(@RequestBody SendXiaoetongLoginSmsArg arg) {
        if (StringUtils.isEmpty(arg.getAppId()) || StringUtils.isEmpty(arg.getPhone())) {
            log.warn("LiveController.sendXiaoetongLoginSms param error arg:{}", arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (!PhoneNumberCheck.isPhoneLegal(arg.getPhone())) {
            log.info("LiveController.sendXiaoetongLoginSms failed phone number is illegal phone:{}", arg.getPhone());
            return new Result(SHErrorCode.PHONE_NUMBER_ILLEGAL);
        }
        return liveService.sendXiaoetongLoginSms(arg.getPhone(), arg.getAppId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<CheckAndSyncUserToXiaoetongResult> checkPolyvSubmit(@RequestBody SyncSubmitUserToXiaoetongArg arg) {
        if (StringUtils.isEmpty(arg.getId()) || StringUtils.isEmpty(arg.getPhone())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        
        return liveService.checkPolyvSubmit(arg.getId(),arg.getPhone());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Map<String, String> externalAuth(String channelId, String userid, Long ts, String token) {
        return liveService.externalAuth(channelId, userid, ts, token);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<ChannelsAccountResult> getAccountByMaterials(@RequestBody LiveMaterialsArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getObjectId())||arg.getObjectType()==null){
            log.info("LiveController.getLiveStatus failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        LiveMaterialsVO vo = BeanUtil.copy(arg, LiveMaterialsVO.class);
        return liveService.getAccountByMaterials(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<PageResult<ListResult>> list(@RequestBody LiveArg arg) {
        if (arg == null){
            log.info("LiveController.list failed arg == null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ListVO vo = BeanUtil.copy(arg, ListVO.class);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setEa(UserInfoKeeper.getEa());
        vo.setEi(UserInfoKeeper.getEi());
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilter("is_mobile_display", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("0"));
        query.addFilter("life_status", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("normal"));
        //添加数据权限筛选条件
        Result<DataPermissionResult> dataPermissionResult = dataPermissionService.getDataPermission(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
        if (dataPermissionResult.isSuccess() && dataPermissionResult.getData() != null && dataPermissionResult.getData().isStatus()) {
            List<Integer> dataDepartments = dataPermissionResult.getData().getDataDepartments();
            if (CollectionUtils.isNotEmpty(dataDepartments)) {
                query.addFilter("data_own_organization", "IN", dataDepartments.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }
        filterData.setQuery(query);
        vo.setFilterData(filterData);
        vo.setFsUserId(-10000);
        return liveService.list(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<PageResult<ListResult>> appList(@RequestBody LiveArg arg) {
        if (arg == null){
            log.info("LiveController.list failed arg == null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ListVO vo = BeanUtil.copy(arg, ListVO.class);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setEa(UserInfoKeeper.getEa());
        vo.setEi(UserInfoKeeper.getEi());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return liveService.appList(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<CheckMemberSubmitResult> checkMemberSubmit(@RequestBody CheckMemberSubmitArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getLiveId()) || StringUtils.isBlank(arg.getMemberId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return liveService.checkMemberSubmit(arg);
    }
}
