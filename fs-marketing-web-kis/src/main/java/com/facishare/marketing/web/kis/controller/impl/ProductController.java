package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.ListProductArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.result.ProductListResult;
import com.facishare.marketing.api.result.QueryProductDetailResult;
import com.facishare.marketing.api.service.kis.ProductService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.ProductArticleTypeEnum;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.QueryProductDetailArg;
import com.facishare.marketing.web.kis.interceptor.ErUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @创建人 zhengliy
 * @创建时间 2019/2/22 16:21
 * @描述
 */
@RestController
@RequestMapping("/web/product")
@FcpService("product")
@Slf4j
public class ProductController extends BaseController {
    /**
     * 查询“我的产品/ 公司产品”列表
     */

    @Autowired
    private ProductService productService;

    @RequestMapping(value = "/listProducts", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("listProducts")
    @GetOuterIdTrigger
    public Result<ProductListResult> listProducts(@RequestBody com.facishare.marketing.web.kis.arg.ListProductArg arg) {
        Preconditions.checkArgument(!(arg.getPageNo() == null || arg.getPageSize() == null || arg.getPageNo() < 0 || arg.getPageSize() <= 0));
        if (arg.getTime() == null) {
            arg.setTime(System.currentTimeMillis());
        }
        arg.setType(ProductArticleTypeEnum.CORPORATE.getType());
        ListProductArg vo = BeanUtil.copy(arg, ListProductArg.class);
        vo.setPageNum(arg.getPageNo());
//        if (isFromInner()) {
//            return productService.listProducts(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), UserInfoKeeper.getEi(), vo);
//        } else {
//            // 伙伴营销必传groupId
//            if (StringUtils.isBlank(arg.getGroupId())) {
//                return Result.newError(SHErrorCode.PARAMS_ERROR);
//            }
//            return productService.listProducts4Outer(ErUserInfoKeeper.getERUpstreamEa(), ErUserInfoKeeper.getEROuterTenantId(), ErUserInfoKeeper.getEROuterUid(), vo);
//        }
        return productService.listProducts(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), UserInfoKeeper.getEi(), vo);
    }

    @RequestMapping(value = "/queryProductDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("queryProductDetail")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryProductDetailResult> queryProductDetail(@RequestBody QueryProductDetailArg arg) {
        return productService.queryProductDetail(arg.getId());
    }

    @RequestMapping(value = "/listProductGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("listProductGroup")
    @GetOuterIdTrigger
    public Result<ObjectGroupListResult> listProductGroup(@RequestBody ListGroupArg arg) {
        if (isFromInner()) {
            return productService.listProductGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
        } else {
            // 伙伴营销
            return productService.listProductGroup4Outer(ErUserInfoKeeper.getERUpstreamEa(), ErUserInfoKeeper.getEROuterTenantId(), ErUserInfoKeeper.getEROuterUid(), arg);
        }
    }
}
