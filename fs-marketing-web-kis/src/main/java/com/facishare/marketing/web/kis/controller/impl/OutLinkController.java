package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.service.OutLinkService;
import com.facishare.marketing.api.vo.OutLinkVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.controller.IOutLinkController;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

@Controller
@Slf4j
public class OutLinkController implements IOutLinkController {

    @Autowired
    private OutLinkService outLinkService;

    /**
     * 根据ID获取外部链接详情
     */
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<OutLinkVO> getById(@RequestBody IdArg arg) {
        if (arg == null || arg.getId() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return outLinkService.getById(arg.getId());
    }

}
