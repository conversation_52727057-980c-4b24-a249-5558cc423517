package com.facishare.marketing.web.kis.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.marketing.common.contstant.CookieConstant;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.RequestSourceTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.RequestUtil;
import com.google.common.collect.ImmutableList;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * Created  By zhoux 2019/05/17
 **/
@Slf4j
public class HttpRequestInterceptor implements HandlerInterceptor {

    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;

    // 过滤接口
    private static List<String> FILTER_URI = ImmutableList.of("getIpAddr","officialWebsite","browserUser", "/web/live/getViewUrl", "/web/live/getLectureUrl", "/web/live/getViewCheckInfo", "/web/live/getLiveStatus", "/web/live/checkAndSyncUserToXiaoetong", "/web/live/xiaoetongCommonLogin", "/web/live/sendXiaoetongLoginSms",
            "/web/wxOfficialAccounts/getListByObjectInfo", "/web/wxOfficialAccounts/getH5AccessPermissionsSeeting", "/web/file/getFileBySpliceUrl", "/web/file/showQRCode", "/web/file/uploadNFileByObject", "/web/file/transferUrl", "/web/member/queryMiniAppInfoConfig", "/web/customizeContent/listActivityList",  "/web/customizeContent/listContentList","/web/hexagon/queryEnterpriseCommerceInfo",
            "/web/hexagon/getActivityCenterInfo", "/web/hexagon/getContentCenterInfo", "/web/live/getMarketingLiveByXiaoetongId","/web/weChatCoupon/detailWeChatCoupon","/web/weChatCoupon/queryUserCouponList","/web/weChatCoupon/getH5Sign", "/web/live/getMarketingLiveByXiaoetongId",
            "/web/live/externalAuth","/web/live/checkPolyvSubmit","/web/weChatServiceMarketing/selectPubPlatAuthComponent","/web/weChatServiceMarketing/getAuthorizedUserInformation","/web/weChatServiceMarketing/getPubPlatAuthComponentAppid","/web/weChatServiceMarketing/checkFocusOn","/web/weChatCoupon/receivePartnerCoupon",
            "/web/weChatCoupon/participateCoupon","/web/weChatCoupon/queryCouponActivityList","/web/weChatCoupon/queryCouponActivityDetail","/web/weChatCoupon/queryPartnerAppId","/web/live/getAccountByMaterials",
            "web/aiChat/getShareDetail");

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        String originService = RequestUtil.getOriginService(httpServletRequest);
        log.info("uri:{} originService:{}", httpServletRequest.getRequestURI(), originService);
        // 临时解决线上SSE接口variables不透传问题
        boolean isSSEReq = httpServletRequest.getRequestURI().contains("web/aiChat/sse/getChatCompleteResult");
        if (StringUtils.isNotBlank(originService) || (originService == null && isSSEReq)) {
            return cepPreHand(httpServletRequest, httpServletResponse, o);
        }
        return oldPreHand(httpServletRequest, httpServletResponse, o);
    }

    private boolean cepPreHand(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        RequestSourceInfoKeeper.setRequestSourceInfo(RequestSourceTypeEnum.FXIAOKE_HTTP.getType());
        String uri = httpServletRequest.getRequestURI();
//        log.info("HttpRequestInterceptor.cepPreHand uri:{}", uri);
        if (FILTER_URI.stream().anyMatch(uri::contains)) {
            return true;
        }
        String typeString = RequestUtil.getIdentityType(httpServletRequest);
        Integer type;
        if (typeString == null && httpServletRequest.getRequestURI().contains("web/aiChat/sse/getChatCompleteResult")) {
            typeString = "0";
        }
        if (!StringUtils.isNumeric(typeString)) {
            throw new HttpMessageNotReadableException("identityType is not number");
        }
        if (StringUtils.isNotBlank(typeString)) {
            type = Integer.valueOf(typeString);
        } else {
//            log.warn("HttpRequestInterceptor.cepPreHand typeString is null");
            type = IdentityCheckTypeEnum.FXIAOKE.getType();
        }
        IdentityInfoKeeper.setIdentityCheckType(type);
        if (type == IdentityCheckTypeEnum.FXIAOKE.getType()) {
            // 纷享身份鉴权
            Integer enterpriseId = RequestUtil.getTenantId(httpServletRequest);
            String enterpriseAccount = RequestUtil.getEa(httpServletRequest);
            Integer userId = RequestUtil.getUserId(httpServletRequest);
//            log.info("HttpRequestInterceptor ea={}, userId={}", enterpriseAccount, userId);
            UserInfoKeeper.setUserInfo(enterpriseAccount, enterpriseId, userId);
            return true;
        } else if (type == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType()) {
            // 公众号全员营销鉴权
            String ea = RequestUtil.getEa(httpServletRequest);
            String appId = RequestUtil.getAppId(httpServletRequest);
            String openId = RequestUtil.getOpenId(httpServletRequest);
            String wxAppId = RequestUtil.getWXAppId(httpServletRequest);
//            log.info("HttpRequestInterceptor ea:{} appId:{} openId:{} wxAppId:{}", ea, appId, openId, wxAppId);
            OuterUserInfoKeeper.setUserInfo(ea, openId, wxAppId, appId);
            return true;
        } else if (type == IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType()) {
            String fsMarketX = RequestUtil.getFSMarketX(httpServletRequest);
//            log.info("HttpRequestInterceptor fsMarketX:{}", fsMarketX);
            BrowserUserInfoKeeper.setUserInfo(fsMarketX, extractMemberCookie(httpServletRequest));
            return true;
        }
//        log.warn("HttpRequestInterceptor.cepPreHand userInfo error");
        throw new HttpMessageNotReadableException("identityType is wrong");
    }

    private boolean oldPreHand(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        // 设置请求来源
        RequestSourceInfoKeeper.setRequestSourceInfo(RequestSourceTypeEnum.FXIAOKE_HTTP.getType());
        String uri = httpServletRequest.getRequestURI();
        // 兼容header 传入身份
        String headerIdentityCheckType = httpServletRequest.getHeader("identityCheckType");
        String headerFsMarketX = httpServletRequest.getHeader("fsMarketX");
        if (StringUtils.isNotBlank(headerFsMarketX) && StringUtils.isNotBlank(headerIdentityCheckType)) {
            //兼容前端headerIdentityCheckType为null字符串的情况
            if (StringUtils.equals("null", headerIdentityCheckType)){
                log.info("HttpRequestInterceptor headerIdentityCheckType is null string");
                headerIdentityCheckType = "2";
            }
            Integer type = Integer.valueOf(headerIdentityCheckType);
            if (type.equals(IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType())) {
                IdentityInfoKeeper.setIdentityCheckType(IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType());
                BrowserUserInfoKeeper.setUserInfo(headerFsMarketX, extractMemberCookie(httpServletRequest));
                return true;
            }
        }

        if (FILTER_URI.stream().anyMatch(uri::contains)) {
            return true;
        }

        Cookie[] cookies = httpServletRequest.getCookies();
        if (cookies == null) {
            log.warn("HttpRequestInterceptor.preHandle cookies is null uri:{}", uri);
            throw new HttpMessageNotReadableException("cookies is null");
        }
        String typeString = doGetCookieByName(cookies, "identityCheckType");
        Integer type;
        if (!StringUtils.isNumeric(typeString)) {
            throw new HttpMessageNotReadableException("identityType is not number");
        }
        if (StringUtils.isNotBlank(typeString)) {
            type = Integer.valueOf(typeString);
        } else {
            // 默认使用纷享鉴权身份
            type = IdentityCheckTypeEnum.FXIAOKE.getType();
        }
        IdentityInfoKeeper.setIdentityCheckType(type);
        if (type == IdentityCheckTypeEnum.FXIAOKE.getType()) {
            String optionalFsCookie = doGetFsCookie(httpServletRequest);
            // 纷享身份鉴权
            CookieToAuth.Argument argument = new CookieToAuth.Argument();
            argument.setCookie(optionalFsCookie);
            argument.setIp(httpServletRequest.getRemoteAddr());
            CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
            ValidateStatus validateStatus = result.getValidateStatus();
            if (validateStatus.is(ValidateStatus.NORMAL)) {
                AuthXC authXC = result.getBody();
                String enterpriseAccount = authXC.getEnterpriseAccount();
                Integer enterpriseId = authXC.getEnterpriseId();
                Integer userId = authXC.getEmployeeId();
                UserInfoKeeper.setUserInfo(enterpriseAccount, enterpriseId, userId);
                return true;
            }
        } else if (type == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType()) {
            // 公众号全员营销鉴权
            // 将头部cookie信息取出
            String openId = doGetCookieByName(httpServletRequest.getCookies(), "openId");
            String ea = doGetCookieByName(httpServletRequest.getCookies(), "ea");
            String wxAppId = doGetCookieByName(httpServletRequest.getCookies(), "wxAppId");
            String appId = doGetCookieByName(httpServletRequest.getCookies(), "appId");
            if (StringUtils.isBlank(openId) || StringUtils.isBlank(ea) || StringUtils.isBlank(wxAppId) || StringUtils.isBlank(appId)) {
                log.warn("HttpRequestInterceptor.preHandle cookie error openId:{}, ea:{}, wxAppId:{}, appId:{}", openId, ea, wxAppId, appId);
                throw new HttpMessageNotReadableException("cookies error");
            }
            OuterUserInfoKeeper.setUserInfo(ea, openId, wxAppId, appId);
            return true;
        } else if (type == IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType()) {
            String fsMarketX = doGetCookieByName(httpServletRequest.getCookies(), "FSMarketX");
            if (StringUtils.isBlank(fsMarketX)) {
                log.warn("HttpRequestInterceptor.preHandle cookie error fsMarketX not found");
                throw new HttpMessageNotReadableException("cookies error");
            }

            BrowserUserInfoKeeper.setUserInfo(fsMarketX, extractMemberCookie(httpServletRequest));
            return true;
        } else {
            log.warn("HttpRequestInterceptor.type is error type:{}", type);
            throw new HttpMessageNotReadableException("cookies error");
        }
        throw new HttpMessageNotReadableException("identityType is wrong");
    }
    
    public static Map<String, String> extractMemberCookie(HttpServletRequest request){
        Map<String, String> map = new HashMap<>(4);
        if (request.getCookies() != null){
            for (Cookie cookie : request.getCookies()) {
                if (cookie.getName().startsWith(CookieConstant.MEMBER_COOKIE_PREFIX)){
                    map.put(cookie.getName().replace(CookieConstant.MEMBER_COOKIE_PREFIX, ""), cookie.getValue());
                }
            }
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null){
            while (headerNames.hasMoreElements()){
                String headerName = headerNames.nextElement();
                if (headerName != null && headerName.startsWith(CookieConstant.MEMBER_COOKIE_PREFIX)){
                    String value = request.getHeader(headerName);
                    if (value != null){
                        map.putIfAbsent(headerName.replace(CookieConstant.MEMBER_COOKIE_PREFIX, ""), value);
                    }
                }
            }
        }
        return map;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {
    }


    private String doGetFsCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            String cookieXC = doGetCookieByName(cookies, "FSAuthXC");
            if (StringUtils.isNotBlank(cookieXC)) {
                return cookieXC;
            }
            String cookieX = doGetCookieByName(cookies, "FSAuthX");
            if (StringUtils.isNotBlank(cookieX)) {
                return cookieX;
            }
        }
        return null;
    }


    private static String doGetCookieByName(Cookie[] cookies, String cookieName) {
        for (Cookie cookie : cookies) {
            if (cookieName.equalsIgnoreCase(cookie.getName())) {
                return cookie.getValue();
            }
        }
        return null;
    }

    public static String extractFingerPrint(HttpServletRequest httpServletRequest) {
        return doGetCookieByName(httpServletRequest.getCookies(), "FSMarketX");
    }
}
