package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.result.distribution.GetDistributionPlanInfoResult;
import com.facishare.marketing.api.result.distribution.ListDistributePlanResult;
import com.facishare.marketing.api.service.distribution.DistributePlanGradeService;
import com.facishare.marketing.api.service.distribution.DistributionPlanService;
import com.facishare.marketing.api.vo.ListDistributePlanVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.distribution.GetDistributionPlanInfoArg;
import com.facishare.marketing.web.kis.arg.distribution.ListDistributePlanArg;
import com.facishare.marketing.web.kis.controller.IDistributionPlanController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created  By zhoux 2019/05/09
 **/
@Slf4j
@Controller
public class DistributionPlanController implements IDistributionPlanController{

    @Autowired
    private DistributionPlanService distributionPlanService;

    @Autowired
    private DistributePlanGradeService planGradeService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetDistributionPlanInfoResult> getDistributionPlanInfo(@RequestBody GetDistributionPlanInfoArg arg) {
        try {
            return distributionPlanService.getDistributionInfo(arg.getId());
        } catch (Exception e) {
            log.error("DistributionPlanController.getDistributionPlanInfo arg:{},e", arg, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<ListDistributePlanResult> listDistributePlan(ListDistributePlanArg arg) {
        if (arg == null) {
            log.warn("DistributionPlanController listDistributePlan arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ListDistributePlanVO vo = BeanUtil.copy(arg, ListDistributePlanVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return planGradeService.listDistributePlan(vo);
    }
}
