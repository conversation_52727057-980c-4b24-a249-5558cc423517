package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.GetCampaignDataArg;
import com.facishare.marketing.api.GetSignInDetailArg;
import com.facishare.marketing.api.GetSignInSuccessSettingArg;
import com.facishare.marketing.api.arg.conference.CreateSignInQrCodeArg;
import com.facishare.marketing.api.arg.conference.QueryInviteCountInfoArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.HexagonQrCodeResult;
import com.facishare.marketing.api.result.conference.*;
import com.facishare.marketing.api.result.permission.DataPermissionResult;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.service.permission.DataPermissionService;
import com.facishare.marketing.api.vo.conference.*;
import com.facishare.marketing.common.enums.ActivityStatusEnum;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.RequestSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.conference.*;
import com.facishare.marketing.web.kis.controller.IConferenceController;
import com.facishare.marketing.web.kis.interceptor.*;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by ranluch on 2019/7/18.
 */
@Slf4j
@Controller
public class ConferenceController implements IConferenceController {
    @Autowired
    private ConferenceService conferenceService;
    @Autowired
    private DataPermissionService dataPermissionService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryConferenceDetailResult> queryConferenceDetail(@RequestBody QueryConferenceDetailArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Integer requestSourceType = RequestSourceInfoKeeper.getRequestSourceType();
        QueryConferenceDetailVO vo = BeanUtil.copy(arg, QueryConferenceDetailVO.class);
        vo.setCheckMarketingEvenAuth(false);
        if (requestSourceType.equals(RequestSourceTypeEnum.FXIAOKE_FCP.getType()) && arg.getMarketingEvenAuth() != null && arg.getMarketingEvenAuth()) {
            // 若是fcp请求需校验权限
            vo.setCheckMarketingEvenAuth(true);
            vo.setEa(UserInfoKeeper.getEa());
            vo.setFsUserId(UserInfoKeeper.getFsUserId());
        }
        return conferenceService.queryConferenceDetail(vo);
    }


    @Override
    public Result<PageResult<QueryConferenceListResult>> queryConferenceList(@RequestBody QueryConferenceListArg arg) {
        if (arg == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getSpreadSearch() == null){
            arg.setSpreadSearch(false);
        }
        QueryConferenceListVO vo = BeanUtil.copy(arg, QueryConferenceListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setNeedMarketingActivityResult(true);
        if (arg.getSpreadSearch()) {
            vo.setActivityStatus(Lists.newArrayList(ActivityStatusEnum.ENABLED.getStatus()));
        }
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilter("is_mobile_display", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("0"));
        query.addFilter("life_status", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("normal"));
        //添加数据权限筛选条件
        Result<DataPermissionResult> dataPermissionResult = dataPermissionService.getDataPermission(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
        if (dataPermissionResult.isSuccess() && dataPermissionResult.getData() != null && dataPermissionResult.getData().isStatus()) {
            List<Integer> dataDepartments = dataPermissionResult.getData().getDataDepartments();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataDepartments)) {
                query.addFilter("data_own_organization", "IN", dataDepartments.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }
        filterData.setQuery(query);
        vo.setFilterData(filterData);
        vo.setFsUserId(-10000);
        vo.setMenuId(arg.getMenuId());
        if (StringUtils.isBlank(vo.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.queryConferenceList(vo);
    }

    @Override
    public Result<PageResult<QueryConferenceListResult>> queryAppList(@RequestBody QueryConferenceListArg arg) {
        if (arg == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getSpreadSearch() == null){
            arg.setSpreadSearch(false);
        }
        QueryConferenceListVO vo = BeanUtil.copy(arg, QueryConferenceListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setNeedMarketingActivityResult(true);
        if (arg.getSpreadSearch()) {
            vo.setActivityStatus(Lists.newArrayList(ActivityStatusEnum.ENABLED.getStatus()));
        }
        vo.setMenuId(arg.getMenuId());
        if (StringUtils.isBlank(vo.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.queryConferenceAppList(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryInvitationResult> queryInvitationInfo(@RequestBody QueryInvitationArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryInvitationVO vo = BeanUtil.copy(arg, QueryInvitationVO.class);
        return conferenceService.queryInvitationInfo(vo);
    }

    @Override
    public Result<PageResult<QueryInviteListByUserResult>> queryInvitationList(@RequestBody QueryInvitationListArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryInvitationList arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<Integer> fsUserIds = arg.getFsUserIds();
        if (CollectionUtils.isEmpty(fsUserIds) && CollectionUtils.isEmpty(arg.getCircleIds())) {
            // 默认选择自己
            fsUserIds = Lists.newArrayList(UserInfoKeeper.getFsUserId());
        }
        QueryInviteListByUserVO vo = BeanUtil.copy(arg, QueryInviteListByUserVO.class);
        vo.setFsUserIds(fsUserIds);
        vo.setEa(UserInfoKeeper.getEa());
        return conferenceService.queryInviteListByUser(vo);
    }

    @Override
    public Result<List<QueryEnrollReviewResult>> queryEnrollReviewList(@RequestBody QueryEnrollReviewListArg arg) {
        return conferenceService.queryEnrollReviewList(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getConferenceId());
    }

    @Override
    public Result<QueryEnrollReviewDetailResult> queryEnrollReviewDetail(@RequestBody QueryEnrollReviewDetailArg arg) {
        return conferenceService.queryEnrollReviewDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getCampaignId());
    }

    @Override
    public Result<Boolean> updateEnrollReviewStatus(@RequestBody UpdateEnrollReviewStatusArg arg) {
        if (arg.isWrongParam()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ChangeConferenceParticipantsReviewStatusVO vo = new ChangeConferenceParticipantsReviewStatusVO();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        vo.setReviewStatus(arg.getReviewStatus());
        vo.setReviewFailedMsg(arg.getRejectReason());
        vo.setCampaignIds(Lists.newArrayList(arg.getCampaignId()));
        return conferenceService.changeConferenceParticipantsReviewStatus(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetConferenceStatisticDataResult> getConferenceStatisticData(@RequestBody GetConferenceStatisticDataArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getConferenceStatisticData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetConferenceStatisticDataVO vo = BeanUtil.copy(arg, GetConferenceStatisticDataVO.class);
        return conferenceService.getConferenceStatisticData(vo);
    }

    @Override
    public Result<PageResult<QueryConferenceParticipantsResult>> queryParticipants(@RequestBody QueryParticipantsArg arg) {
        if (arg.getConferenceId() == null) {
            log.info("ConferenceController.queryParticipants failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getFilterPhoneUser() == null) {
            arg.setFilterPhoneUser(false);
        }

        if (arg.getFilterErrorData() == null) {
            arg.setFilterErrorData(false);
        }

        QueryConferenceParticipantsVO vo = BeanUtil.copy(arg, QueryConferenceParticipantsVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        try {
            return conferenceService.queryConferenceParticipants(vo);
        } catch (Exception e) {
            log.error("ConferenceController.queryParticipants failed arg:{} e:{}", arg, e);
            return Result.newError(SHErrorCode.QUERY_CONFERENCE_PARTICIPANTS_FAILED);
        }
    }

    @Override
    public Result<QueryInviteCountInfoResult> queryInviteCountInfo(@RequestBody com.facishare.marketing.web.kis.arg.conference.QueryInviteCountInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryInviteCountInfo param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<Integer> fsUserIds = arg.getFsUserIds();
        if (CollectionUtils.isEmpty(fsUserIds) && CollectionUtils.isEmpty(arg.getCircleIds())) {
            // 默认选择自己
            fsUserIds = Lists.newArrayList(UserInfoKeeper.getFsUserId());
        }
        QueryInviteCountInfoArg vo = new QueryInviteCountInfoArg();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setFsUserIds(fsUserIds);
        vo.setCircleIds(arg.getCircleIds());
        vo.setConferenceId(arg.getConferenceId());
        return conferenceService.queryInviteCountInfo(vo);
    }

    @Override
    public Result<Boolean> queryTicketCheckPoint(@RequestBody QueryTicketCheckPointArg arg) {
        if (StringUtils.isBlank(arg.getConferenceId())) {
            log.warn("ConferenceController.queryTicketCheckPoint param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.queryTicketCheckPoint(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getConferenceId());
    }

    @Override
    public Result<Boolean> changeConferenceParticipantsSignStatus(@RequestBody ChangeConferenceParticipantsSignStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.changeConferenceParticipantsSignStatus param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ChangeConferenceParticipantsSignStatusVO vo = BeanUtil.copy(arg, ChangeConferenceParticipantsSignStatusVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.changeConferenceParticipantsSignStatus(vo);
    }

    @Override
    public Result<PersonalSettingDetailsResult> personalSettingDetails(@RequestBody PersonalSettingDetailsArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.personalSettingDetails param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.personalSettingDetails(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getConferenceId());
    }

    @Override
    public Result<QueryOwerViewConferenceStatisticsDataResult> queryOwerViewConferenceStatisticsData(@RequestBody QueryOwerViewConferenceStatisticsDataArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getConferenceId())){
            log.info("ConferenceController.queryOwerViewConferenceStatisticsData faile param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (CollectionUtils.isEmpty(arg.getDepartmentRange()) && CollectionUtils.isEmpty(arg.getEmployeeRange())){
            arg.setEmployeeRange(Lists.newArrayList(UserInfoKeeper.getFsUserId()));
        }

        QueryOwerViewConferenceStatisticsDataVO vo = BeanUtil.copy(arg, QueryOwerViewConferenceStatisticsDataVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());

        return conferenceService.queryOwerViewConferenceStatisticsData(vo);
    }

    @Override
    public Result<PageResult<QueryOwerViewConferenceEnrollResult>> queryOwerViewConferenceEnrollData(@RequestBody QueryOwerViewConferenceEnrollDataArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getConferenceId())){
            log.info("ConferenceController.queryOwerViewConferenceStatisticsData faile param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (CollectionUtils.isEmpty(arg.getDepartmentRange()) && CollectionUtils.isEmpty(arg.getEmployeeRange())){
            arg.setEmployeeRange(Lists.newArrayList(UserInfoKeeper.getFsUserId()));
        }

        QueryOwerViewConferenceEnrollDataVO vo = BeanUtil.copy(arg, QueryOwerViewConferenceEnrollDataVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());

        return conferenceService.queryOwerViewConferenceEnrollData(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetConferenceTimeStatusResult> getConferenceTimeStatus(@RequestBody GetConferenceTimeStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getConferenceTimeStatus param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetConferenceTimeStatusVO vo = BeanUtil.copy(arg, GetConferenceTimeStatusVO.class);
        return conferenceService.getConferenceTimeStatus(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetSignInSettingResult> getSignInSetting(@RequestBody GetSignInSettingArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getSignInSetting error param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetSignInSettingVO vo = BeanUtil.copy(arg, GetSignInSettingVO.class);
        return conferenceService.getSignInSetting(vo);
    }

    @Override
    public Result<GetInvitationCommonSettingResult> getInvitationCommonSetting(GetInvitationCommonSettingArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getInvitationCommonSetting error param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetInvitationCommonSettingVO vo = BeanUtil.copy(arg, GetInvitationCommonSettingVO.class);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.getInvitationCommonSetting(vo);
    }

    @Override
    public Result addInvitationUserByCrmObj(@RequestBody AddInvitationUserByCrmObjArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.addInvitationUserByCrmObj error param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AddInvitationUserByCrmObjVO vo = BeanUtil.copy(arg, AddInvitationUserByCrmObjVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.addInvitationUserByCrmObj(vo);
    }



    @RequestMapping(value = "/asynBatchAddInvitationUser", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result asynBatchAddInvitationUser(@RequestBody AsynBatchAddInvitationUserByCrmObArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.asynBatchAddInvitationUser error param arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        AsynBatchAddInvitationUserByCrmObjVO vo = BeanUtil.copy(arg, AsynBatchAddInvitationUserByCrmObjVO.class);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setEa(UserInfoKeeper.getEa());
        return conferenceService.asynBatchAddInvitationUser(vo);
    }

    @Override
    public Result changeInvitationStatus(@RequestBody ChangeInvitationStatusArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ChangeInvitationStatusVO vo = BeanUtil.copy(arg, ChangeInvitationStatusVO.class);
        return conferenceService.changeInvitationStatus(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetMarketingActivityChannelByIdResult> getMarketingActivityChannelById(@RequestBody GetMarketingActivityChannelByIdArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.getMarketingActivityChannelById(arg.getMarketingActivityId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result getCampaignData(@RequestBody GetCampaignDataArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getConferenceId()) || StringUtils.isBlank(arg.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Integer requestSourceType = RequestSourceInfoKeeper.getRequestSourceType();
        if (requestSourceType.equals(RequestSourceTypeEnum.FXIAOKE_HTTP.getType())) {
            arg.setFingerPrint(BrowserUserInfoKeeper.getFingerPrint());
        }
        if (IdentityInfoKeeper.getIdentityCheckType() != null && Objects.equals(IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType(),IdentityInfoKeeper.getIdentityCheckType())) {
            arg.setOpenid(OuterUserInfoKeeper.getOpenId());
            arg.setAppId(OuterUserInfoKeeper.getWxAppId());
        }
        return conferenceService.getCampaignData(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result getSignInSuccessSetting(@RequestBody GetSignInSuccessSettingArg arg) {
        if (StringUtils.isBlank(arg.getConferenceId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.getSignInSuccessSetting(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetSignInDetailResult> getSignInDetail(@RequestBody GetSignInDetailArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            log.warn("ActivityController.checkSignInStatus error identityCheckType error identityCheckType is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            arg.setEnrollUserEa(UserInfoKeeper.getEa());
            arg.setEnrollUserFsUid(UserInfoKeeper.getFsUserId());
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            arg.setOpenId(OuterUserInfoKeeper.getOpenId());
            arg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            arg.setFingerPrint(BrowserUserInfoKeeper.getFingerPrint());
        }
        return conferenceService.getSignInDetail(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetSimpleConferenceDetail> getSimpleDetail(@RequestBody ConferenceIdArg arg) {
        if (StringUtils.isBlank(arg.getConferenceId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return conferenceService.getSimpleDetail(arg.getConferenceId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<HexagonQrCodeResult> createSignInQrCode(@RequestBody CreateSignInQrCodeArg arg, HttpServletRequest request) {
        if (CollectionUtils.isEmpty(arg.getWxQrCodes()) && CollectionUtils.isEmpty(arg.getQywxQrCodes())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            log.warn("ConferenceController.checkSignInStatus error identityCheckType error identityCheckType is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            log.warn("ConferenceController.createSignInQrCode is not support fsUserId");
            return Result.newSuccess();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            arg.setOpenId(OuterUserInfoKeeper.getOpenId());
            arg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            arg.setFingerPrint(BrowserUserInfoKeeper.getFingerPrint());
        }
        String ipAddr = request.getRemoteAddr();
        arg.setIpAddr(ipAddr);
        return conferenceService.createSignInQrCode(arg);
    }

}
