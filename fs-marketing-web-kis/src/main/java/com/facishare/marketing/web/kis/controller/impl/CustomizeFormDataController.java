package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.qywx.customizeFormData.CustomizeFormDataShowSettingArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.GetAreaDataArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.HttpUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.AreaNameByKeywordArg;
import com.facishare.marketing.web.kis.arg.BatchQueryLocationInfoArg;
import com.facishare.marketing.web.kis.arg.GetLocationInfoByIpArg;
import com.facishare.marketing.web.kis.arg.ZoneByParentArg;
import com.facishare.marketing.web.kis.controller.ICustomizeFormDataController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created  By zhoux 2019/04/17
 **/
@Controller
@Slf4j
public class CustomizeFormDataController implements ICustomizeFormDataController {

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<CustomizeFormDataDetailResult> getCustomizeFormDataById(@RequestBody com.facishare.marketing.web.kis.arg.GetCustomizeFormDataByIdArg arg) {
        if (arg.isWrongParam()) {
            log.warn("CustomizeFormDataController.getCustomizeFormDataById param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        GetCustomizeFormDataByIdArg getCustomizeFormDataByIdArg = BeanUtil.copy(arg, GetCustomizeFormDataByIdArg.class);
        return customizeFormDataService.getCustomizeFormDataById(getCustomizeFormDataByIdArg);
    }

    @Override
    public Result<PageResult<QueryCustomizeFormDataResult>> queryCustomizeFormData(@RequestBody com.facishare.marketing.web.kis.arg.QueryCustomizeFormDataArg arg) {
        QueryCustomizeFormDataArg queryCustomizeFormDataArg = BeanUtil.copy(arg, QueryCustomizeFormDataArg.class);
        if (arg.isWrongParam()) {
            log.warn("CustomizeFormDataController.queryCustomizeFormData param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        queryCustomizeFormDataArg.setEa(UserInfoKeeper.getEa());
        queryCustomizeFormDataArg.setFsUserId(UserInfoKeeper.getFsUserId());
        return customizeFormDataService.queryCustomizeFormData(queryCustomizeFormDataArg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<AreaContainerResult> getAreaData(GetAreaDataArg arg) {
        return customizeFormDataService.getAreaData(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<AreaByKeywordResult> getAreaNameByKeyword(@RequestBody AreaNameByKeywordArg arg){
        return customizeFormDataService.getAreaNameByKeyword(UserInfoKeeper.getEa(), arg.getKeyword(), arg.getAreaType());
    }


    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<AreaByParentResult> getZoneByParent(@RequestBody ZoneByParentArg arg) {
        String ea = arg.getEa() == null ? UserInfoKeeper.getEa() : arg.getEa();
        return customizeFormDataService.getZoneByParent(ea, arg.getParentId(), arg.getCascadeLevel());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<LocationResult> batchQueryLocationInfo(@RequestBody BatchQueryLocationInfoArg arg) {
        return customizeFormDataService.batchQueryLocationInfo(UserInfoKeeper.getEa(), arg.getCodes());
    }

    @Override
    public Result<CustomizeFormDataShowSettingResult> showSetting(@RequestBody CustomizeFormDataShowSettingArg arg) {
        //默认返回成功
        CustomizeFormDataShowSettingResult result = new CustomizeFormDataShowSettingResult();
        return Result.newSuccess(result);
    }

    @Override
    public Result<ExecuteEnrollCustomizeFunctionResult> executeEnrollCustomizeFunction(@RequestBody com.facishare.marketing.web.kis.arg.ExecuteEnrollCustomizeFunctionArg arg) {
        if(arg.isWrongParam()) {
            log.warn("CustomizeFormDataController.executeEnrollCustomizeFunction param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ExecuteEnrollCustomizeFunctionArg executeEnrollCustomizeFunctionArg =  BeanUtil.copy(arg, ExecuteEnrollCustomizeFunctionArg.class);
        return customizeFormDataService.executeEnrollCustomizeFunction(executeEnrollCustomizeFunctionArg);
    }
    @Override
    public Result<PageResult<QueryFormUserDataResult>> queryFormUserDataForIds(@RequestBody queryFormUserDataForIdsArg arg) {
        if(arg==null || arg.getIds().size()==0 || !arg.isPageArgValid()) {
            log.warn("CustomizeFormDataController.queryFormUserDataForIds param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return customizeFormDataService.queryFormUserDataForIds(arg);
    }

    @Override
    public Result<PageResult<QueryFormUserDataResult>> queryFormUserDataForMarketingActivityIdAndSpreadFsUid(@RequestBody QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg arg) {
        if(arg==null || StringUtils.isBlank(arg.getMarketingActivityId()) || arg.getPageNum()==null || arg.getPageSize()==null) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg vo = BeanUtil.copy(arg, QueryFormUserDataForMarketingActivityIdAndSpreadFsUidArg.class);
        vo.setSpreadFsUid(UserInfoKeeper.getFsUserId());
        vo.setEa(UserInfoKeeper.getEa());
        return customizeFormDataService.queryFormUserDataForMarketingActivityIdAndSpreadFsUid(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<IP2locationData> getLocationInfoByIp(@RequestBody GetLocationInfoByIpArg arg, HttpServletRequest request, HttpServletResponse response) {
        return customizeFormDataService.getLocationInfoByIp(HttpUtil.getClientIpAddr(request), I18nUtil.getLanguage());
    }
}
