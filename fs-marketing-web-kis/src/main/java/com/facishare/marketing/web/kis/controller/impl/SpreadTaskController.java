package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.result.SpreadTaskNormalResult;
import com.facishare.marketing.api.result.kis.KisNoticeDetailResult;
import com.facishare.marketing.api.result.kis.QuerySpreadTaskListResult;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.GetNoticeDetailArg;
import com.facishare.marketing.web.kis.arg.GetSpreadTaskArg;
import com.facishare.marketing.web.kis.arg.QuerySpreadTaskListArg;
import com.facishare.marketing.web.kis.arg.SpreadTaskByNoticeArg;
import com.facishare.marketing.web.kis.controller.ISpreadTaskController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created  By zhoux 2019/02/25
 **/
@Controller
@Slf4j
public class SpreadTaskController implements ISpreadTaskController{

    @Autowired
    private SpreadTaskService spreadTaskService;

    @Autowired
    private NoticeService noticeService;

    @Override
    public Result<QuerySpreadTaskListResult> querSpreadTaskList(@RequestBody QuerySpreadTaskListArg arg) {
        try{
            return spreadTaskService.querySpreadTaskList(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), false, arg.getUpstreamEa());
        }catch (Exception e){
            log.error("SpreadTaskController.querSpreadTaskList error ea:{} userId:{}", UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
            return Result.newError(SHErrorCode.QUERY_SPREAD_TASK_FAILED);
        }
    }

    @Override
    public Result<Void> finishSpreadTaskByNotice(@RequestBody SpreadTaskByNoticeArg arg) {
        try{
            return spreadTaskService.spreadTaskByNotice(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getNoticeId());
        }catch (Exception e){
            log.error("SpreadTaskController.spreadTaskByNotice failed ea:{} userId:{} noticeId:{}", UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getNoticeId());
            return Result.newError(SHErrorCode.UPDATE_SPREAD_TASK_STATUS_FAILED);
        }
    }

    @Override
    public Result<KisNoticeDetailResult> getNoticeDetail(@RequestBody GetNoticeDetailArg arg) {
        Preconditions.checkNotNull(arg.getNoticeId(), "noticeId is null");
        try {
            return spreadTaskService.getNoticeDetail(arg.getNoticeId());
        }catch (Exception e){
            log.error("SpreadTaskController.getNoticeDetail failed ea:{} userId:{} noticeId:{}", UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getNoticeId());
            return Result.newError(SHErrorCode.KIS_QUERY_SPREAD_NOTICE_FAILED);
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<Boolean> spreadTaskIsRevocation(@RequestBody IdArg arg) {
        Result<Boolean> noticeDetail = spreadTaskService.spreadTaskIsRevocation(arg.getId());
        if (null == noticeDetail) {
            return Result.newError(SHErrorCode.KIS_QUERY_SPREAD_STATUS_FAILED);
        }
        return noticeDetail;
    }

    @Override
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<SpreadTaskNormalResult> spreadTaskIsNormal(@RequestBody GetSpreadTaskArg arg) {
        if(StringUtils.isBlank(arg.getObjectId()) && arg.getObjectType() == null && StringUtils.isBlank(arg.getMarketingActivityId()) && StringUtils.isBlank(arg.getMarketingEventId())) {
            log.warn("SpreadTaskController.arg.getId() param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return spreadTaskService.spreadTaskIsNormal(arg.getObjectId(),arg.getObjectType(),arg.getMarketingActivityId(),arg.getMarketingEventId(), arg.getEa());
    }
}
