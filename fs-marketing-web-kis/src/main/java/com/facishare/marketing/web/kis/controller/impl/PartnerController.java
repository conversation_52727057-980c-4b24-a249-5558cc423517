package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.marketing.api.service.PartnerService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.controller.IPartnerController;
import com.facishare.marketing.web.kis.interceptor.ErUserInfoKeeper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

@Controller
public class PartnerController extends BaseController implements IPartnerController {

    @Autowired
    private PartnerService partnerService;

    @Override
    @FcpMethod("checkAccessible")
    @RequestMapping(value = "/checkAccessible", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    @GetOuterIdTrigger
    public Result<Void> checkAccessible() {
        if (isFromInner()) {
           return Result.newError(SHErrorCode.ILLEGAL_REQUEST);
        }
        String upstreamEa = ErUserInfoKeeper.getERUpstreamEa();
        long outerUid = Long.parseLong(Objects.requireNonNull(ErUserInfoKeeper.getEROuterUid()));
        long outerTenantId = Long.parseLong(Objects.requireNonNull(ErUserInfoKeeper.getEROuterTenantId()));
        return partnerService.checkAppAccessible(upstreamEa, outerTenantId, outerUid);
    }
}
