package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.HexagonhomepageDetailArg;
import com.facishare.marketing.api.arg.hexagon.CreateHexagonWxQrCodeArg;
import com.facishare.marketing.api.arg.hexagon.GetFilePreviewUrlByObjectArg;
import com.facishare.marketing.api.arg.hexagon.GetSiteByEaArg;
import com.facishare.marketing.api.arg.hexagon.SimpleHexagonListArg;
import com.facishare.marketing.api.result.HexagonQrCodeResult;
import com.facishare.marketing.api.result.OfficialWebsiteWxQrCodeResult;
import com.facishare.marketing.api.result.hexagon.*;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.vo.GetMarketingContentSiteVO;
import com.facishare.marketing.api.vo.SimpleHexagonVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.RequestSourceTypeEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonPreviewEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.GetActivityCenterInfoArg;
import com.facishare.marketing.web.kis.arg.GetContentCenterInfoArg;
import com.facishare.marketing.web.kis.arg.MktParamArg;
import com.facishare.marketing.web.kis.arg.hexagon.GetHexagonFilePreviewUrlArg;
import com.facishare.marketing.web.kis.arg.hexagon.GetHomepageBySiteIdArg;
import com.facishare.marketing.web.kis.arg.hexagon.GetPageDetailArg;
import com.facishare.marketing.web.kis.arg.hexagon.QueryEnterpriseCommerceInfoArg;
import com.facishare.marketing.web.kis.controller.IHexagonController;
import com.facishare.marketing.web.kis.interceptor.*;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Controller
@Slf4j
public class HexagonController implements IHexagonController {

    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private MemberService memberService;

    @Override
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetPageDetailResult> getHomepageDetailBySiteId(@RequestBody GetHomepageBySiteIdArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getSiteId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (arg.getCheckMemberAuthority() == null) {
            arg.setCheckMemberAuthority(true);
        }

        if (null == arg.getType()) {
            arg.setType(1);
        }

        if (!Objects.equals(HexagonPreviewEnum.TEMPLATE_SITE.getType(),arg.getType()) && BooleanUtils.isTrue(arg.getCheckMemberAuthority()) && isUserNotAccessibleToObject(ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getSiteId())){
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }

        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(arg.getType());
        if (null == hexagonPreviewEnum) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        }

        String wxAppId = null;
        String openId = null;
        if (identityCheckType == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType()){
            wxAppId = OuterUserInfoKeeper.getWxAppId();
            openId = OuterUserInfoKeeper.getOpenId();
        }
        try {
            HexagonhomepageDetailArg detailArg = BeanUtil.copy(arg, HexagonhomepageDetailArg.class);
            return hexagonService.getHomepageDetailBySiteId(detailArg);
        } catch (Exception e) {
            log.error("HexagonController.getHomepageDetailBySiteId exception:", e);
            return new Result(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetPageDetailResult> getPageDetail(@RequestBody GetPageDetailArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (arg.getCheckMemberAuthority() == null) {
            arg.setCheckMemberAuthority(true);
        }

        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(arg.getType());
        if (null == hexagonPreviewEnum) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (arg.getType() != null && HexagonPreviewEnum.TEMPLATE_SITE.getType() != arg.getType()
                && BooleanUtils.isTrue(arg.getCheckMemberAuthority()) && isUserNotAccessibleToObject(ObjectTypeEnum.HEXAGON_PAGE.getType(), arg.getId())){
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }

        if (null == arg.getType()) {
            arg.setType(1);
        }


        try {
            return hexagonService.getPageDetail(arg.getType(), arg.getId());
        } catch (Exception e) {
            log.error("HexagonController.getPageDetail exception:", e);
            return new Result(SHErrorCode.SYSTEM_ERROR);
        }
    }
    
    private boolean isUserNotAccessibleToObject(Integer objectType, String objectId){
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (RequestSourceInfoKeeper.getRequestSourceType().equals(RequestSourceTypeEnum.FXIAOKE_FCP.getType())){
            return false;
        }
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return BooleanUtils.isFalse(memberService.checkWxServiceUserHaveMemberAuth(objectType, objectId, OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), true).isSuccess());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            return BooleanUtils.isFalse(memberService.checkH5UserHaveMemberAuth(objectType, objectId, BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), true).isSuccess());
        }
        return false;
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<HexagonFilePreviewResult> getFilePreviewUrl(@RequestBody GetHexagonFilePreviewUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("HexagonController.getFilePreviewUrl param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        try {
            if (StringUtils.isNotBlank(arg.getSiteId())) {
                return hexagonService.getFilePreviewUrl(arg.getSiteId(), arg.getNpath(), arg.getFileName());
            } else if (StringUtils.isNotBlank(arg.getObjectId()) && arg.getObjectType() != null) {
                GetFilePreviewUrlByObjectArg getFilePreviewUrlByObjectArg = BeanUtil.copy(arg, GetFilePreviewUrlByObjectArg.class);
                return hexagonService.getFilePreviewUrlByObject(getFilePreviewUrlByObjectArg);
            }
        } catch (Exception e) {
            log.error("HexagonController.getFilePreviewUrl exception:", e);
            return new Result(SHErrorCode.SYSTEM_ERROR);
        }
        return new Result(SHErrorCode.PARAMS_ERROR);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<PageResult<GetSiteByEaUnitResult>> getMarketingContentSite(@RequestBody GetSiteByEaArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getPageNum(), arg.getPageSize())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (null != arg.getStatusFitter()) {
            HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatusFitter());
            if (null == hexagonStatusEnum || hexagonStatusEnum == HexagonStatusEnum.DELETED || hexagonStatusEnum == HexagonStatusEnum.NOTVISIBLE) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }
        GetMarketingContentSiteVO vo = BeanUtil.copy(arg, GetMarketingContentSiteVO.class);
        return hexagonService.getMarketingContentSite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<List<QueryEnterpriseCommerceInfoResult>> queryEnterpriseCommerceInfo(@RequestBody QueryEnterpriseCommerceInfoArg arg) {
        if (arg.getObjectId() == null || arg.getObjectType() == null){
            log.info("HexagonController.queryEnterpriseCommerceInfo failed param error arg:{}", arg);
            return Result.newSuccess(Lists.newArrayList());
        }

        try {
            return hexagonService.queryEnterpriseCommerceInfo(arg.getObjectId(), arg.getObjectType(), arg.getKeyword());
        }catch (Exception e){
            log.warn("queryEnterpriseCommerceInfo failed arg:{} e:",arg ,e);
            return Result.newSuccess(Lists.newArrayList());
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetActivityCenterInfoResult> getActivityCenterInfo(@RequestBody GetActivityCenterInfoArg arg) {
        if (arg.getObjectId() == null || arg.getObjectType() == null){
            log.info("HexagonController.getActivityCenterInfo failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.getActivityCenterInfoByObjectId(arg.getObjectId(), arg.getObjectType());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetContentCenterInfoResult> getContentCenterInfo(@RequestBody GetContentCenterInfoArg arg) {
        if (arg.getObjectId() == null || arg.getObjectType() == null){
            log.info("HexagonController.getContentCenterInfo failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return hexagonService.getContentCenterInfoByObjectId(arg.getObjectId(), arg.getObjectType());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<PageResult<GetSiteByEaUnitResult>> getSiteByEa(@RequestBody GetSiteByEaArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getPageNum(), arg.getPageSize())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (null != arg.getStatusFitter()) {
            HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatusFitter());
            if (null == hexagonStatusEnum || hexagonStatusEnum == HexagonStatusEnum.DELETED || hexagonStatusEnum == HexagonStatusEnum.NOTVISIBLE) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }

        return hexagonService.getSiteByEa(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getPageSize(), arg.getPageNum(), arg.getTime(), arg.getSearchFitter(), arg.getStatusFitter(), arg.getExcludeSystemSite());
    }


    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<HexagonQrCodeResult> createHexagonWxQrCode(@RequestBody CreateHexagonWxQrCodeArg arg, HttpServletRequest request) {
        if ((CollectionUtils.isEmpty(arg.getWxQrCodes()) && CollectionUtils.isEmpty(arg.getQywxQrCodes()))
                || StringUtils.isEmpty(arg.getUserMarketingId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ipAddr = request.getRemoteAddr();
        arg.setIpAddr(ipAddr);
        return hexagonService.createHexagonQrCode(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/genMktParam", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> genMktParam(@RequestBody MktParamArg arg){
        if (null == arg){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return hexagonService.genMktParam(GsonUtil.toJson(arg));
    }


    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/simpleHexagonList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<SimpleHexagonVO>> simpleHexagonList(@RequestBody SimpleHexagonListArg arg) {
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return hexagonService.simpleHexagonList(arg);
    }
}
