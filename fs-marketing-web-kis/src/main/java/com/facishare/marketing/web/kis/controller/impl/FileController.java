package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.mankeep.api.outService.service.OutFileService;
import com.facishare.mankeep.api.vo.ShowQRCodeVO;
import com.facishare.marketing.api.arg.GetDownLoadUrlArg;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.SendFileMailArg;
import com.facishare.marketing.api.arg.file.BatchGetUrlByPathArg;
import com.facishare.marketing.api.arg.file.UploadFileArg;
import com.facishare.marketing.api.result.FilePreviewResult;
import com.facishare.marketing.api.result.GetDownLoadResult;
import com.facishare.marketing.api.result.UploadFileResult;
import com.facishare.marketing.api.result.file.BatchGetUrlByPathResult;
import com.facishare.marketing.api.result.file.CreateTNFileFromAFileArgResult;
import com.facishare.marketing.api.result.file.GenerateUploadFileOmitResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileByGroupResult;
import com.facishare.marketing.api.result.video.QueryVideoPlayUrlByIdResult;
import com.facishare.marketing.api.service.FileService;
import com.facishare.marketing.api.service.VideoService;
import com.facishare.marketing.api.service.fileLibrary.FileLibraryService;
import com.facishare.marketing.api.vo.GetFileBySpliceUrlVO;
import com.facishare.marketing.common.enums.FileTypeEnum;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.RequestSourceTypeEnum;
import com.facishare.marketing.common.filters.IPLimiterFilter;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.Base64Util;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.HttpUtil;
import com.facishare.marketing.common.util.URLValidator;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.CreateTNFileFromAFileArg;
import com.facishare.marketing.web.kis.arg.GenerateUploadFileOmitArg;
import com.facishare.marketing.web.kis.controller.IFileController;
import com.facishare.marketing.web.kis.interceptor.IdentityInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.RequestSourceInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.facishare.uc.api.service.CaptchaService;
import com.google.common.base.Preconditions;

import java.io.ByteArrayInputStream;
import java.io.OutputStream;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.Arrays;

@Controller
@Slf4j
public class FileController implements IFileController {

    @Autowired
    private FileService fileService;
    @Autowired
    private OutFileService outFileService;

    @Autowired
    private FileLibraryService fileLibraryService;

    @Autowired
    private VideoService videoService;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private IPLimiterFilter ipLimiterFilter;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<UploadFileResult> uploadFile(MultipartFile file, Integer type, Boolean needApath, Boolean needPermanent) {
        try {
            Preconditions.checkArgument(file != null);
            Preconditions.checkArgument(type != null);
            Result checkResult = checkFileFormat(type, file);
            if (!checkResult.isSuccess()){
                return new Result<>(SHErrorCode.getByCode(checkResult.getErrCode()));
            }

            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setExt(getUploadFileExt(file));
            uploadFileArg.setFileBytes(file.getBytes());
            uploadFileArg.setFileName(file.getOriginalFilename());
            uploadFileArg.setNeedApath(needApath);
            uploadFileArg.setNeedPermanent(needPermanent);

            return fileService.uploadFile(uploadFileArg);
        } catch (IOException e) {
            log.warn("exception:",  e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<UploadFileResult> uploadToCFile(MultipartFile file, Integer type, Integer needCpath, String ea, String objectId, Integer objectType) {
        try {
            Preconditions.checkArgument(file != null);
            Preconditions.checkArgument(type != null);
            Result checkResult = checkFileFormat(type, file);
            if (!checkResult.isSuccess()){
                return new Result<>(SHErrorCode.getByCode(checkResult.getErrCode()));
            }

            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setExt(getUploadFileExt(file));
            uploadFileArg.setFileBytes(file.getBytes());
            uploadFileArg.setFileName(file.getOriginalFilename());
            uploadFileArg.setNeedCpath(needCpath);
            uploadFileArg.setObjectType(objectType);
            uploadFileArg.setObjectId(objectId);
            uploadFileArg.setEa(ea);
            return fileService.uploadToCFile(uploadFileArg);
        } catch (IOException e) {
            log.warn("exception:",  e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<BatchGetUrlByPathResult> batchGetUrlByPath(@RequestBody BatchGetUrlByPathArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getPathList())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Integer requestSourceType = RequestSourceInfoKeeper.getRequestSourceType();
        if (requestSourceType.equals(RequestSourceTypeEnum.FXIAOKE_FCP.getType())) {
            arg.setEa(UserInfoKeeper.getEa());
        } else {
            Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
            if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
                arg.setEa(UserInfoKeeper.getEa());
            }
        }

        return fileService.batchGetUrlByPath(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public byte[] transferUrl(String path) {
        try {
            if (StringUtils.isBlank(path)) {
                log.warn("FileController.transferUrl param error");
                return null;
            }

            return fileService.transferUrl(path);
        } catch (Exception e) {
            log.error("FileController.transferUrl error, path={}", path);
            return null;
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @RequestMapping(value = "redirectDownload", method = RequestMethod.GET)
    public void redirectDownload(HttpServletResponse response, @RequestParam("url") String path) {
        if (path.startsWith("//") && !path.contains("http") && !path.contains("https")) {
            path = "https" + path;
        }

        try {
            log.info("origin path = {}", path);
            path = URLDecoder.decode(path, "UTF-8");
            log.info("decoded path = {}", path);
        } catch (Exception e) {
            log.warn("exception:",  e);
        }

        String lastCha = "\"";
        String lastCha2 = "\"/";
        if (StringUtils.isNotBlank(path)) {
            if (path.endsWith(lastCha)) {
                path = path.substring(0, path.length() - 1);
            } else if (path.endsWith(lastCha2)) {
                path = path.substring(0, path.length() - 2);
            }
        }

        if (path.contains("qpic")) {
            if (path.contains("&tp=webp&wxfrom=5")) {
                path = path.replaceAll("&tp=webp&wxfrom=5", "");
            }
            if (path.contains("wx_fmt=png")) {
                path = path.replaceAll("wx_fmt=png", "wx_fmt=jpg");
            }
        }

        if (!URLValidator.validateURL(path)) {
            HttpUtil.writeErrorInfo(response);
            return;
        }

        InputStream inputStream = null;
        OutputStream os = null;
        try {
//            URL url = new URL(path);
//            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//            conn.setRequestMethod("GET");
//            conn.connect();
//
//            for (int i = 0;; i++) {
//                String mine = conn.getHeaderField(i);
//                if (mine == null) break;
//                response.setHeader(conn.getHeaderFieldKey(i), mine);
//            }
//
//            os = response.getOutputStream();
//            inputStream = conn.getInputStream();
            byte[] bytes = fileService.redirectDownload(path);
            if (null == bytes) {
                HttpUtil.writeErrorInfo(response);
                return;
            }
            os = response.getOutputStream();
            inputStream = new ByteArrayInputStream(bytes);
            int len = 0;
            byte[] bis = new byte[1024];
            while ((len = inputStream.read(bis)) != -1) {
                os.write(bis, 0, len);
            }

            inputStream.close();

        } catch (Exception e) {
            log.error("Error: getImageFromNetByUrl! url:{}", path, e);
        } finally {
            try {
                if (null != os) {
                    os.close();
                }
            } catch (Exception e) {
                log.error("Error: getImageFromNetByUrl! url:{}", path, e);
            }
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @RequestMapping(value = "/getFileBySpliceUrl", method = RequestMethod.GET, produces = "image/jpg")
    public void getFileBySpliceUrl(HttpServletRequest request, HttpServletResponse response) {
        String path = request.getParameter("path");
        String ea = request.getParameter("ea");
        String param = request.getQueryString();
        try {
            if (StringUtils.isBlank(path)) {
                log.warn("FileController.getFileBySpliceUrl param error url:{}", request.getRequestURI());
                return;
            }
            GetFileBySpliceUrlVO vo = new GetFileBySpliceUrlVO();
            vo.setPath(path);
            int cutNum = 2;
            if (StringUtils.isNotBlank(ea)) {
                vo.setEa(ea);
                cutNum = 3;
            }

            if (StringUtils.isNotBlank(param)) {
                String[] paramSplit = param.split("&", cutNum);
                if (paramSplit.length >= cutNum) {
                    vo.setParam("&" + paramSplit[cutNum - 1]);
                } else {
                    vo.setParam(null);
                }
            }
            Result<String> result = fileService.getFileUrlBySpliceUrl(vo);
            if (result.getData() != null){
                response.sendRedirect(result.getData());
            }
        } catch (Exception e) {
            log.error("FileController.getFileBySpliceUrl error, path={}", path);
        }
    }

    @Override
    public byte[] showQRCode(@RequestParam("scene") String scene, @RequestParam("page") String page, @RequestParam("width") Integer width, String type) {
        try {
            log.info("showQRCode page:{}", page);
            ShowQRCodeVO vo = new ShowQRCodeVO();
            vo.setScene(scene);

            if (StringUtils.isEmpty(type)) {
                byte[] pageBytes = Base64Util.stringToBytes(page);
                page = new String(pageBytes, "utf-8");
            } else if (type.equals("notice")) {
                page = URLDecoder.decode(page, "UTF-8");
            }

            if (StringUtils.isEmpty(page)) {
                log.info("show QR code, page is empty! page:{}", page);
                return null;
            }

            vo.setPage(page);
            vo.setWidth(width);

            log.info("showQRCode vo:{}", vo);

            return outFileService.showQRCode(vo);
        } catch (Exception e) {
            log.error("Error: showQRCode! scene:{}, page:{}, width:{}", scene, page, width, e);
            return null;
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<UploadFileResult> uploadNFileByObject(MultipartFile file, String objectId, Integer objectType) {
        try {
            if(StringUtils.isBlank(objectId) || objectType == null) {
                log.warn("FileController.uploadNFileByObject objectInfo is null");
                return new Result<>(SHErrorCode.PARAMS_ERROR);
            }
            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setExt(getUploadFileExt(file));
            uploadFileArg.setFileBytes(file.getBytes());
            uploadFileArg.setFileName(file.getOriginalFilename());
            uploadFileArg.setObjectId(objectId);
            uploadFileArg.setObjectType(objectType);
            return fileService.uploadNFileByObject(uploadFileArg);
        } catch (IOException e) {
            log.warn("exception:",  e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<ListFileByGroupResult> getFileDetailById(@RequestBody IdArg arg) {
        if(arg==null||StringUtils.isBlank(arg.getId())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return fileLibraryService.getFileDetailById(arg.getId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryVideoPlayUrlByIdResult> getVideoDetailById(@RequestBody IdArg arg) {
        if(arg==null||StringUtils.isBlank(arg.getId())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return videoService.queryVideoPlayUrlById(arg.getId());
    }

    private Result checkFileFormat(Integer type, MultipartFile file){

        String originalFilename = file.getOriginalFilename();
        String[] strings = originalFilename.split("\\.");
        // check file's ext
        String ext = strings[strings.length - 1].toLowerCase();
        if (strings.length == 0) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if (type == FileTypeEnum.PICTURE.getType()) {
            String[] picExtList = {"jpeg", "jpg", "png", "gif", "bmp"};
            if (!Arrays.asList(picExtList).contains(ext)) {
                log.error("originalFilename : " + originalFilename);
                return new Result<>(SHErrorCode.FILE_FORMAT_ERROR);
            }
        }

        return new Result<>(SHErrorCode.SUCCESS);
    }

    private String getUploadFileExt(MultipartFile file){
        String originalFilename = file.getOriginalFilename();
        String[] strings = originalFilename.split("\\.");
        // check file's ext
        String ext = strings[strings.length - 1].toLowerCase();
        return ext;
    }

    @RequestMapping(value = "createTNFileFromAFile", method = RequestMethod.POST)
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<CreateTNFileFromAFileArgResult> createTNFileFromAFile(@RequestBody CreateTNFileFromAFileArg arg){
        Preconditions.checkArgument(arg.getApath() != null);
        return fileService.createTNFileFromAFile(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getApath());
    }

    @RequestMapping(value = "generateUploadFileOmit", method = RequestMethod.POST)
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @Override
    public Result<GenerateUploadFileOmitResult> generateUploadFileOmit(@RequestBody GenerateUploadFileOmitArg arg) {
        if (arg.isWrongParam()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return fileService.generateUploadFileOmit(arg.getEa(), arg.getResourceType(), arg.getFilename(), arg.getExtension(), arg.getFileSize());
    }
    @ApiOperation(value = "邮件发送，带图形验证码")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<Void> sendFileMail(HttpServletRequest httpServletRequest, @RequestBody SendFileMailArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.sendFileMail param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getValidateCode()!=null && arg.getValidateCode()==2) {
            return fileService.sendFileMail(arg);
        }
        // 配合 IpLimiterFilter 配置使用:
        // 1. 调用获取接口是否异常告警
        // 2. 超出限制,返回图形验证码
        // 3. 校验图形验证码
        try {
//            if (ipLimiterFilter.alarm(httpServletRequest)) {
            if (org.apache.commons.lang.StringUtils.isEmpty(arg.getEpxId()) || org.apache.commons.lang.StringUtils.isEmpty(arg.getCode())) {
                return Result.newError(SHErrorCode.PHONE_NUMBER_CAPTCHA);
            }
            if (!captchaService.verify(arg.getEpxId(), arg.getCode())) {
                return Result.newError(SHErrorCode.PHONE_NUMBER_CAPTCHA_ERROR);
            }
//            }
        } catch (Exception e) {
            log.warn("captcha fail e:", e);
        }

        return fileService.sendFileMail(arg);
    }


    @ApiOperation(value = "获取文件浏览地址")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<FilePreviewResult> getPreviewUrlV2(@RequestBody com.facishare.marketing.api.arg.GetPreviewUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.getPreviewUrlV2 param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fileService.getPreviewUrlV2(arg);
    }

    @ApiOperation(value = "获取文件下载地址")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetDownLoadResult> getDownLoadUrlV2(@RequestBody GetDownLoadUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.getPreviewUrlV2 param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fileService.getDownLoadUrlV2(arg);
    }

}
