package com.facishare.marketing.web.kis.controller.outer;

import com.facishare.marketing.api.arg.UserAgentArg;
import com.facishare.marketing.api.service.BrowserUserService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created  By zhoux 2019/12/04
 **/
@RestController
@RequestMapping("/web/browserUser")
@CrossOrigin
@Slf4j
public class BrowserUserController {

    @Autowired
    private BrowserUserService browserUserService;


    @RequestMapping(value = "getIdentity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取浏览器身份")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<String> getIdentity(@RequestBody UserAgentArg arg){
        return browserUserService.getIdentity(arg.getUserAgent());
    }

}
