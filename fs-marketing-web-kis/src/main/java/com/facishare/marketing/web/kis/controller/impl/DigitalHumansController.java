package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.digitalHumans.DigitalHumansArg;
import com.facishare.marketing.api.arg.digitalHumans.SignatureArg;
import com.facishare.marketing.api.result.DigitalHumansResult;
import com.facishare.marketing.api.service.digitalHumans.DigitalHumansService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/web/digitalHumans")
@FcpService("digitalHumans")
@Slf4j
public class DigitalHumansController extends BaseController{
    @Autowired
    private DigitalHumansService digitalHumansService;


    @ApiOperation(value = "获取签名", tags = "获取签名")
    @RequestMapping(value = "/getSignature", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("getSignature")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<String> getSignature(@RequestBody SignatureArg arg) {
        if (StringUtils.isBlank(arg.getCardUid())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return digitalHumansService.getSignature(arg);
    }


    @ApiOperation(value = "获取员工数字人信息", tags = "获取员工数字人信息")
    @RequestMapping(value = "/getUserDigitalHumans", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("getUserDigitalHumans")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<DigitalHumansResult> getUserDigitalHumans(@RequestBody DigitalHumansArg arg) {
        if (StringUtils.isBlank(arg.getCardUid())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return digitalHumansService.getUserDigitalHumans(arg);
    }
}
