package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.result.bd.GetBdSiteListResult;
import com.facishare.marketing.api.service.bd.BdSiteService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.bd.GetBdSiteListArg;
import com.facishare.marketing.web.kis.controller.IBdSiteController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

@Controller
@Slf4j
public class BdSiteController implements IBdSiteController {

    @Autowired
    private BdSiteService bdSiteService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<PageResult<GetBdSiteListResult>> getList(@RequestBody GetBdSiteListArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getPageNum(), arg.getPageSize())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return bdSiteService.getList(arg.getPageSize(), arg.getPageNum(), arg.getTime(), arg.getSearchFitter());
    }

}
