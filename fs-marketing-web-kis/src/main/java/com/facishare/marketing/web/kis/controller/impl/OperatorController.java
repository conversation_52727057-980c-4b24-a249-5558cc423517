package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.mankeep.api.result.distribution.QueryDistributorAssociationResult;
import com.facishare.mankeep.api.vo.distribution.QueryDistributorAssociationVO;
import com.facishare.mankeep.common.enums.DistributorStatusEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorByOperatorResult;
import com.facishare.marketing.api.result.distribution.QueryOperatorClueResult;
import com.facishare.marketing.api.result.distribution.QueryOperatorInfoResult;
import com.facishare.marketing.api.result.distribution.QueryPlanListResult;
import com.facishare.marketing.api.service.distribution.OperatorService;
import com.facishare.marketing.api.vo.QueryDistributorByOperatorVO;
import com.facishare.marketing.api.vo.QueryOperatorClueVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.arg.distribution.QueryDistributorByOperatorArg;
import com.facishare.marketing.web.kis.arg.distribution.QueryOperatorClueArg;
import com.facishare.marketing.web.kis.arg.distribution.QueryOperatorInfoArg;
import com.facishare.marketing.web.kis.arg.distribution.QueryPlanListArg;
import com.facishare.marketing.web.kis.controller.IOperatorController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Created by zhengh on 2019/4/11.
 */
@Controller
@Slf4j
public class OperatorController implements IOperatorController{
    @Autowired
    private OperatorService operatorService;
    @Autowired
    private com.facishare.mankeep.api.service.OperatorService outerOperatorService;

    @Override
    public Result<QueryOperatorInfoResult> queryOperatorInfo(@RequestBody QueryOperatorInfoArg arg) {
        try{
            return operatorService.queryOperatorInfo(arg.getOperatorId(), arg.getPlanId());
        }catch (Exception e){
            log.error("OperatorController.queryOperatorInfo failed ea:{}, userId:{}",UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
            return Result.newError(SHErrorCode.QUERY_OPERATOR_ERROR);
        }
    }

    @Override
    public Result<PageResult<QueryDistributorByOperatorResult>> queryDistributorByOperator(@RequestBody QueryDistributorByOperatorArg arg) {
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_60));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_61));
        Preconditions.checkNotNull(arg.getOperatorId(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_OPERATORCONTROLLER_54));
        Preconditions.checkNotNull(arg.getPlanId(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_OPERATORCONTROLLER_55));

        try{
            QueryDistributorByOperatorVO vo = BeanUtil.copy(arg, QueryDistributorByOperatorVO.class);
            return operatorService.queryDistributorByOperator(vo);
        }catch (Exception e){
            log.error("OperatorController.queryDistributorByOperator failed ea:{}, userId:{}, operatorId:{} planId:{}",
                    UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getOperatorId(), arg.getPlanId());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<PageResult<QueryOperatorClueResult>> queryOperatorClue(@RequestBody QueryOperatorClueArg arg) {
        if (arg.getPageNum() == null || arg.getPageSize() == null || arg.getOperatorId() == null || arg.getPlanId() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try{
            QueryOperatorClueVO vo = BeanUtil.copy(arg, QueryOperatorClueVO.class);
            return operatorService.queryOperatorClue(vo);
        }catch (Exception e){
            log.error("OperatorController.queryOperatorClue failed ea:{}, userId:{}, operatorId:{} planId:{}",
                    UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getOperatorId(), arg.getPlanId());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<List<QueryPlanListResult>> queryPlanList(@RequestBody QueryPlanListArg arg) {
        if (UserInfoKeeper.getEa() == null || UserInfoKeeper.getFsUserId() == null) {
            log.warn("OperatorController.queryPlanList parms error");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try{
            return operatorService.queryPlanList(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
        }catch (Exception e){
            log.error("OperatorController.queryPlanList failed ea:{}", UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
            return Result.newError(SHErrorCode.QUERY_PLAN_LIST_ERROR);
        }
    }

    @Override
    public ModelResult<PageObject<QueryDistributorAssociationResult>> queryDistributorAssociationByOperatorId(@RequestBody QueryDistributorByOperatorArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getOperatorId())) {
            log.warn("OperatorController.queryDistributorAssociationByOperatorId arg error arg:{}", arg);
            return new ModelResult(com.facishare.mankeep.common.result.SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getTime() == null) {
            arg.setTime(System.currentTimeMillis());
        }
        if (DistributorStatusEnum.ALL.getType() == arg.getStatus()) {
            arg.setStatus(null);
        }
        try {
            QueryDistributorAssociationVO vo = com.facishare.mankeep.common.util.BeanUtil.copy(arg, QueryDistributorAssociationVO.class);
            return outerOperatorService.queryDistributorAssociation(vo);

        } catch (Exception e) {
            log.error("OperatorController.queryDistributorAssociationByDistributor error arg:{}, e:{}", arg, e);
            return new ModelResult<>(com.facishare.mankeep.common.result.SHErrorCode.SYSTEM_ERROR);
        }
    }
}