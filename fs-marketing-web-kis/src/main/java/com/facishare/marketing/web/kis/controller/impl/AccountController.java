package com.facishare.marketing.web.kis.controller.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.AccountIsApplyForKISArg;
import com.facishare.marketing.api.arg.FsViewProfileDTO;
import com.facishare.marketing.api.arg.QueryI18nArg;
import com.facishare.marketing.api.arg.TenantBrandColorArg;
import com.facishare.marketing.api.arg.qywx.card.QueryCardArg;
import com.facishare.marketing.api.result.EnterpriseSettingsResult;
import com.facishare.marketing.api.result.account.AccountIsApplyForKISResult;
import com.facishare.marketing.api.result.account.GetDownstreamEmployeeInfoResult;
import com.facishare.marketing.api.result.account.GetFsUserInfoResult;
import com.facishare.marketing.api.result.account.GetQywxBaseInfoFromWxResult;
import com.facishare.marketing.api.result.qywx.ResetAppUserDataResult;
import com.facishare.marketing.api.result.qywx.card.CardInfoResult;
import com.facishare.marketing.api.service.AccountService;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.kis.SpreadWorkService;
import com.facishare.marketing.api.service.qywx.CardService;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.account.ApplyBindStatusForKISEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.GetQywxBaseInfoFromWxArg;
import com.facishare.marketing.web.kis.arg.QueryEmployeeByOfficeAccountOpenIdArg;
import com.facishare.marketing.web.kis.controller.IAccountController;
import com.facishare.marketing.web.kis.interceptor.ErUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.facishare.marketing.web.kis.utils.RequestUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.reader.UnicodeReader;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;

/**
 * @ClassName AccountController
 * @Description
 * <AUTHOR>
 * @Date 2019/2/26 10:37 AM
 */
@Controller
@Slf4j
public class AccountController extends BaseController implements IAccountController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private CardService cardService;

    @Autowired
    private SettingService settingService;

    @Autowired
    private MergeJedisCmd jedisCmd;

    private Gson gs = new Gson();

    @Autowired
    private SpreadWorkService spreadWorkService;

    @ReloadableProperty("open_marketing_tag_objects")
    private String openMarketingTagObjects;
    /**
     * 灰度企业获取api
     * **/
    private static final String MARKETING_GRAY_EA_LISt = "MARKETING_GRAY_EA_LISt_";
    private static final int MARKETING_GRAY_EA_LISt_EXPIRED_TIME = 3600 * 8;

    private FsViewProfileDTO getConfigProfile() {
        FsViewProfileDTO fsViewProfileDTO = getGrayEaList();
        if(fsViewProfileDTO != null){
            log.info("AccountController -> getApiNameList from redis cache, FsViewProfileDTO:{}", fsViewProfileDTO);
            return fsViewProfileDTO;
        }else {
            //拉取配置文件并格式转换
            IConfig config = ConfigFactory.getInstance().getConfig("fs-web-view-profile");
            String yText = config.getString();
            Map<String, Object> map = yamlHandler(yText);
            fsViewProfileDTO = BeanUtil.copyByFastJson(map.get("openMarketingTagObjects"), FsViewProfileDTO.class);
            setGrayEaList(fsViewProfileDTO);
        }
        return fsViewProfileDTO;
    }

    private Map<String, Object> yamlHandler(String text){
        //返回的结果
        Map<String, Object> result = new LinkedHashMap<>();
        try {
            //读取方式
            UnicodeReader reader = new UnicodeReader(new ByteArrayInputStream(text.getBytes()) {
            });
            //单文件处理
            Yaml yaml = new Yaml();
            Object object = yaml.load(reader);
            if (object instanceof Map) {
                result = (Map) object;
            }

            reader.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return result;
    }
    public boolean setGrayEaList(FsViewProfileDTO fsViewProfileDTO) {
        String key = MARKETING_GRAY_EA_LISt;
        String value = gs.toJson(fsViewProfileDTO);;
        String result = jedisCmd.setex(key, MARKETING_GRAY_EA_LISt_EXPIRED_TIME, value);
        return !Strings.isNullOrEmpty(result);
    }
    public FsViewProfileDTO getGrayEaList() {
        String key = MARKETING_GRAY_EA_LISt;
        String value = jedisCmd.get(key);
        if (value == null) {
            return null;
        }
        return gs.fromJson(value, FsViewProfileDTO.class);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @GetOuterIdTrigger
    public Result<AccountIsApplyForKISResult> isApply() {
        try {
            boolean isFromInner = isFromInner();
            if (isFromInner && !RequestUtil.fsIdentityRequest()) {
                log.warn("AccountController.isApply identityCheckType error");
                return new Result(SHErrorCode.IDENTITY_INVAILED);
            }
            if (isFromInner) {
                Result<String> versionRet = spreadWorkService.checkMarketingLicense(UserInfoKeeper.getEa());
                if (versionRet == null || versionRet.getData() == null) {
                    log.info("AccountController.isApply ea:{} not open marketing", UserInfoKeeper.getEa());
                    AccountIsApplyForKISResult result = new AccountIsApplyForKISResult();
                    result.setStatus(ApplyBindStatusForKISEnum.WITHOUT_MARKETING_LICENSE.getType());
                    return Result.newSuccess(result);
                }
            }
            AccountIsApplyForKISArg arg = new AccountIsApplyForKISArg();
            if (isFromInner()) {
                arg.setEa(UserInfoKeeper.getEa());
                arg.setFsUserId(UserInfoKeeper.getFsUserId());
            } else {
                arg.setUpstreamEa(ErUserInfoKeeper.getERUpstreamEa());
                arg.setOuterUid(ErUserInfoKeeper.getEROuterUid());
                arg.setOuterTenantId(ErUserInfoKeeper.getEROuterTenantId());
            }
            return accountService.isApplyForKIS(arg);
        } catch (Exception e) {
            log.error("AccountController.isApply exception:", e);
            return new Result(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @GetOuterIdTrigger
    public Result<GetFsUserInfoResult> getFsUserInfo() {
        try {
//            if (!RequestUtil.fsIdentityRequest()) {
//                log.warn("AccountController.getFsUserInfo identityCheckType error");
//                return new Result(SHErrorCode.SUCCESS, new GetFsUserInfoResult());
//            }
            return accountService.getFsUserInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), null);
        } catch (Exception e) {
            log.error("AccountController.getFsUserInfo exception:", e);
            return new Result(SHErrorCode.SYSTEM_ERROR);
        }
    }

    // 为什么新开一个接口，主要是因为getFsUserInfo太基础了，很多地方都有调用，担心该坏
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @GetOuterIdTrigger
    public Result<GetFsUserInfoResult> getFsUserInfoByPartner() {
        try {
            return accountService.getFsUserInfo(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), null);
        } catch (Exception e) {
            log.error("AccountController.getFsUserInfo ea: {} fsUserId: {}", UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), e);
            return new Result(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<ResetAppUserDataResult> resetAppUserData() {
        return accountService.resetAppUserData(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<GetQywxBaseInfoFromWxResult> getQywxBaseInfoFromWx(@RequestBody GetQywxBaseInfoFromWxArg arg) {
        if (arg.isWrongParam()) {
            log.warn("AccountController.getQywxBaseInfoFromWx param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.getQywxBaseInfoFromWx(arg.getTargetUid());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<CardInfoResult> queryBaseCardInfo(@RequestBody QueryCardArg arg) {
        return cardService.queryBaseCardInfo(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<EnterpriseSettingsResult> getEnterpriseSettings() {
        return settingService.getEnterpriseSettings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @Override
    @GetOuterIdTrigger
    public Result<List<String>> getApiNameList() {
        String ea = UserInfoKeeper.getEa();
       // FsViewProfileDTO fsViewProfileDTO = getConfigProfile();
        FsViewProfileDTO fsViewProfileDTO = JSON.parseObject(openMarketingTagObjects, FsViewProfileDTO.class);
        if (fsViewProfileDTO != null && StringUtils.isNotEmpty(fsViewProfileDTO.getRule())) {
            String[] split = fsViewProfileDTO.getRule().split(";");
            if(Arrays.asList(split).contains(ea)){
                return Result.newSuccess(fsViewProfileDTO.getV());
            }
        }
        return Result.newSuccess(new ArrayList<>());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<String> queryTenantBrandColor(@RequestBody TenantBrandColorArg arg) {
        //先取参数了的ea信息,没有再看身份上是否携带
        if(!arg.validParam()){
            if(StringUtils.isBlank(UserInfoKeeper.getEa())){
                return new Result<>(SHErrorCode.PARAMS_ERROR);
            }
            arg.setEa(UserInfoKeeper.getEa());
        }
        return settingService.queryTenantBrandColor(arg);
    }

    @Override
    @GetOuterIdTrigger
    public Result<GetDownstreamEmployeeInfoResult> getDownstreamEmployeeInfo() {
        if (StringUtils.isBlank(ErUserInfoKeeper.getERUpstreamEa())||StringUtils.isBlank(ErUserInfoKeeper.getEROuterTenantId())||StringUtils.isBlank( ErUserInfoKeeper.getEROuterUid())) {
            return new Result<>(SHErrorCode.IDENTITY_INVAILED);
        }
        return accountService.getDownstreamEmployeeInfo(ErUserInfoKeeper.getERUpstreamEa(), ErUserInfoKeeper.getEROuterTenantId(), ErUserInfoKeeper.getEROuterUid());

    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @FilterLog
    public Result<LinkedTreeMap<String,Object>> queryI18n(@RequestBody QueryI18nArg arg) {
        //先取参数了的ea信息,没有再看身份上是否携带
        if(StringUtils.isBlank(arg.getEa()) && StringUtils.isBlank(UserInfoKeeper.getEa())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if(!arg.validParam()){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if(StringUtils.isBlank(arg.getEa())){
            arg.setEa(UserInfoKeeper.getEa());
        }
        return settingService.queryI18n(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetFsUserInfoResult> queryEmployeeByOfficeAccountOpenId(@RequestBody QueryEmployeeByOfficeAccountOpenIdArg arg) {
        if (StringUtils.isEmpty(arg.getOfficeAccountAppId()) || StringUtils.isEmpty(arg.getOfficeAccountOpenId())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return accountService.queryEmployeeByOfficeAccountOpenId(arg.getOfficeAccountAppId(), arg.getOfficeAccountOpenId());
    }

    @Override
    public Result<Integer> publicEmployeeHasCrmAccount() {
        if (isFromInner()) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        String ea = ErUserInfoKeeper.getERUpstreamEa();
        String outerUid = ErUserInfoKeeper.getEROuterUid();
        String outerTenantId = ErUserInfoKeeper.getEROuterTenantId();
        return accountService.checkEnterpriseRelationHasCrmAccount(ea, outerUid, outerTenantId);
    }
}
