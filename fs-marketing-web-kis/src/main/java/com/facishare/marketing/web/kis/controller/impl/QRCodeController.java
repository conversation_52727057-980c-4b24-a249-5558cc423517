package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.result.qr.CreateQRCodeResult;
import com.facishare.marketing.api.result.qr.QueryQRCodeResult;
import com.facishare.marketing.api.service.qr.QRCodeService;
import com.facishare.marketing.api.vo.PartnerChannelQrCodeVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.qr.*;
import com.facishare.marketing.web.kis.controller.IQRCodeController;
import com.facishare.marketing.web.kis.interceptor.ErUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Controller
@Slf4j
public class QRCodeController implements IQRCodeController {

    @Autowired
    private QRCodeService qrCodeService;

    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<CreateQRCodeResult> createQRCode(@RequestBody CreateQRCodeArg arg) {
//        if (arg.isPartner()) {
//            return qrCodeService.createQRCode(ErUserInfoKeeper.getERUpstreamEa(), null, arg.getType(), arg.getValue(), arg.getLengthOfSide(), null);
//        } else {
//            String ea = StringUtils.isBlank(UserInfoKeeper.getEa()) ? arg.getEa() : UserInfoKeeper.getEa();
//            Integer fsUserId = UserInfoKeeper.getFsUserId() == null ? arg.getUserId() : UserInfoKeeper.getFsUserId();
//            return qrCodeService.createQRCode(ea, fsUserId, arg.getType(), arg.getValue(), arg.getLengthOfSide(), null);
//        }
        String ea = StringUtils.isBlank(UserInfoKeeper.getEa()) ? arg.getEa() : UserInfoKeeper.getEa();
        Integer fsUserId = UserInfoKeeper.getFsUserId() == null ? arg.getUserId() : UserInfoKeeper.getFsUserId();
        return qrCodeService.createQRCode(ea, fsUserId, arg.getType(), arg.getValue(), arg.getLengthOfSide(), null);

    }

    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryQRCodeResult> queryQRCode(@RequestBody QueryQRCodeArg arg) {
        return qrCodeService.queryQRCode(arg.getId(), arg.getAuthCode());
    }
    
    @Override
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<String> createWxTemplateQrCodeByChannelQrCode(@RequestBody CreateWxTemplateQrCodeByChannelQrCodeArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getWxAppId()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getChannelQrCodeId()));
        //Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getMarketingActivityId()));
        Preconditions.checkArgument(arg.getSpreadFsUserId() != null);
//        if (arg.isPartner()) {
//            if (StringUtils.isEmpty(ErUserInfoKeeper.getEROuterTenantId()) || StringUtils.isEmpty(ErUserInfoKeeper.getEROuterUid())) {
//                log.warn("EROuterTenantId, EROuterUid is empty,{},{}",ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid());
//                return Result.newError(SHErrorCode.PARAMS_ERROR);
//            }
//            String erUpstreamEa = ErUserInfoKeeper.getERUpstreamEa();
//            if (Strings.isNullOrEmpty(erUpstreamEa)) {
//                erUpstreamEa = arg.getEa();
//            }
//            if (Strings.isNullOrEmpty(erUpstreamEa)) {
//                log.warn("erUpstreamEa is empty");
//                return Result.newError(SHErrorCode.PARAMS_ERROR);
//            }
//            PartnerChannelQrCodeVO VO = BeanUtil.copy(arg, PartnerChannelQrCodeVO.class);
//            VO.setEa(erUpstreamEa);
//            VO.setEROuterTenantId(ErUserInfoKeeper.getEROuterTenantId());
//            VO.setEROuterUid(ErUserInfoKeeper.getEROuterUid());
//            return qrCodeService.createWxTemplateQrCodeByPartnerChannelQrCode(VO);
//        }
        String ea = StringUtils.isBlank(UserInfoKeeper.getEa()) ? arg.getEa() : UserInfoKeeper.getEa();
        return qrCodeService.createWxTemplateQrCodeByChannelQrCode(ea, arg.getWxAppId(), arg.getChannelQrCodeId(), arg.getSpreadFsUserId(), arg.getMarketingEventId(), arg.getMarketingActivityId());
    }

    @Override
    public Result<CreateQRCodeResult> createQRCodeWithoutIdentity(@RequestBody CreateQRCodeWithoutIdentityArg arg) {
        return qrCodeService.createQRCodeWithoutIdentity(arg.getObjectType(), arg.getObjectId(), arg.getType(), arg.getValue(), arg.getLengthOfSide());
    }

    @Override
    public Result<CreateQRCodeResult> createSpreadUserQywxQRCode(@RequestBody CreateSpreadQRCodeArg arg) {
        String ea = StringUtils.isBlank(UserInfoKeeper.getEa()) ? arg.getEa() : UserInfoKeeper.getEa();
        Integer fsUserId = UserInfoKeeper.getFsUserId() == null ? arg.getUserId() : UserInfoKeeper.getFsUserId();
        return qrCodeService.createSpreadUserQywxQRCode(ea, fsUserId,arg.getId());
    }
}
