package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.result.MemberSpreadDetail;
import com.facishare.marketing.api.result.MemberSpreadResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.member.BuildMemberInfoByFormResult;
import com.facishare.marketing.api.result.member.MemberEnrollResult;
import com.facishare.marketing.api.result.memberCenter.MiniAppInfoConfigResult;
import com.facishare.marketing.api.result.memberCenter.QueryMemberContentResult;
import com.facishare.marketing.api.result.memberCenter.QueryMemberInfoResult;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.vo.MemberAccessibleWebsiteVo;
import com.facishare.marketing.api.vo.MemberConfigVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.enums.RequestSourceTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.TextUtil;
import com.facishare.marketing.common.util.UrlUtils;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.CheckUserInMarketingEventArg;
import com.facishare.marketing.web.kis.arg.ObjectInfoArg;
import com.facishare.marketing.web.kis.controller.IMemberController;
import com.facishare.marketing.web.kis.interceptor.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Slf4j
public class MemberController implements IMemberController {

    @Autowired
    private MemberService memberService;
    
    @Override
    @CrossOrigin
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<String> checkIsMember(@RequestBody ObjectInfoArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            //return memberService.checkWxServiceUserHaveMemberAuth(arg.getObjectType(), arg.getObjectId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), false);
            return memberService.checkWxServiceUserHaveMemberAuthReview(arg.getObjectType(), arg.getObjectId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), false);
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            //return memberService.checkH5UserHaveMemberAuth(arg.getObjectType(), arg.getObjectId(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), false);
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                return memberService.checkPartnerUserHaveMemberAuthReview(arg.getObjectType(), arg.getObjectId(), ErUserInfoKeeper.getERUpstreamEa(),ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid(),false);
            }
            return memberService.checkH5UserHaveMemberAuthReview(null,arg.getObjectType(), arg.getObjectId(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), false);
        }
        return Result.newSuccess();
    }
    
    @Override
    @CrossOrigin
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<Boolean> checkUserInMarketingEvent(@RequestBody CheckUserInMarketingEventArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            Result<MemberEnrollResult> memberEnrollResultResult = memberService.checkWxServiceUserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), arg.getMarketingEventId());
            if (memberEnrollResultResult.isSuccess()) {
                if (memberEnrollResultResult.getData() == null) {
                    return Result.newSuccess(false);
                }
                return Result.newSuccess(memberEnrollResultResult.getData().getMemberEnroll());
            }
            return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                Result<MemberEnrollResult> memberEnrollResultResult = memberService.checkPartnerUserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), ErUserInfoKeeper.getERUpstreamEa(), ErUserInfoKeeper.getEROuterTenantId(), ErUserInfoKeeper.getEROuterUid(), arg.getMarketingEventId());
                if (memberEnrollResultResult.isSuccess()) {
                    if (memberEnrollResultResult.getData() == null) {
                        return Result.newSuccess(false);
                    }
                    return Result.newSuccess(memberEnrollResultResult.getData().getMemberEnroll());
                }
                return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
            }
            Result<MemberEnrollResult> memberEnrollResultResult = memberService.checkH5UserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), BrowserUserInfoKeeper.getFingerPrint(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), arg.getMarketingEventId());
            if (memberEnrollResultResult.isSuccess()) {
                if (memberEnrollResultResult.getData() == null) {
                    return Result.newSuccess(false);
                }
                return Result.newSuccess(memberEnrollResultResult.getData().getMemberEnroll());
            }
            return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            return Result.newSuccess(false);
        }
        return Result.newSuccess(false);
    }

    @Override
    @CrossOrigin
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<MemberEnrollResult> checkUserInMarketingEventV2(@RequestBody CheckUserInMarketingEventArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return Result.newSuccess(memberService.checkWxServiceUserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), arg.getMarketingEventId()).getData());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                return Result.newSuccess(memberService.checkPartnerUserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), ErUserInfoKeeper.getERUpstreamEa(),ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid(), arg.getMarketingEventId()).getData());
            }
            return Result.newSuccess(memberService.checkH5UserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), BrowserUserInfoKeeper.getFingerPrint(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), arg.getMarketingEventId()).getData());
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            MemberEnrollResult result = new MemberEnrollResult();
            result.setMemberEnroll(false);
            return Result.newSuccess(result);
        }
        MemberEnrollResult result = new MemberEnrollResult();
        result.setMemberEnroll(false);
        return Result.newSuccess(result);
    }

    @Override
    @CrossOrigin
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<MemberEnrollResult> checkUserInMarketingEventV3(@RequestBody CheckUserInMarketingEventArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return Result.newSuccess(memberService.checkWxServiceUserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), arg.getMarketingEventId()).getData());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                return Result.newSuccess(memberService.checkPartnerUserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), ErUserInfoKeeper.getERUpstreamEa(),ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid(), arg.getMarketingEventId()).getData());
            }
            return Result.newSuccess(memberService.checkH5UserInMarketingEvent(arg.getObjectType(), arg.getObjectId(), BrowserUserInfoKeeper.getFingerPrint(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), arg.getMarketingEventId()).getData());
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            MemberEnrollResult result = new MemberEnrollResult();
            result.setMemberEnroll(false);
            return Result.newSuccess(result);
        }
        MemberEnrollResult result = new MemberEnrollResult();
        result.setMemberEnroll(false);
        return Result.newSuccess(result);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<MemberConfigVO> getMemberConfig(@RequestBody ObjectInfoArg arg) {
        return memberService.getMemberConfig(arg.getObjectType(), arg.getObjectId());
    }
    
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    @GetOuterIdTrigger
    public Result<String> memberEnroll(@RequestBody MemberEnrollArg arg, HttpServletRequest httpServletRequest) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        // 替换channelValue中的++ 为 %，适配小程序不能有%
        arg.setChannelValue(TextUtil.replaceText(arg.getChannelValue(), "++", "%"));
        arg.setChannelValue(UrlUtils.urlDecode(arg.getChannelValue()));
        if (httpServletRequest != null) {
            String ipAddress = httpServletRequest.getRemoteAddr();
            arg.setIpAddr(ipAddress);
        }
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            Result<MemberEnrollResult> memberEnrollResultResult = memberService.wxServiceMemberEnroll(OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), arg);
            if (!memberEnrollResultResult.isSuccess()) {
                return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
            }
            if (memberEnrollResultResult.getData() == null) {
                return Result.newSuccess(null);
            }
            return Result.newSuccess(memberEnrollResultResult.getData().getLeadId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            arg.setFingerPrintId(BrowserUserInfoKeeper.getFingerPrint());
            Result<MemberEnrollResult> memberEnrollResult;
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                arg.setOuterUid(Integer.valueOf(ErUserInfoKeeper.getEROuterUid()));
                arg.setOuterTenantId(ErUserInfoKeeper.getEROuterTenantId());
                memberEnrollResult = memberService.partnerMemberEnroll(ErUserInfoKeeper.getERUpstreamEa(),ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid(), arg);
            } else {
                memberEnrollResult = memberService.h5MemberEnroll(BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), arg);
            }
            if (!memberEnrollResult.isSuccess()) {
                return Result.newError(memberEnrollResult.getErrCode(), memberEnrollResult.getErrMsg());
            }
            if (memberEnrollResult.getData() == null) {
                return Result.newSuccess(null);
            }
            return Result.newSuccess(memberEnrollResult.getData().getLeadId());
        }
        return Result.newSuccess(null);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<MemberEnrollResult> memberEnrollV2(@RequestBody MemberEnrollArg arg, HttpServletRequest httpServletRequest) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        // 替换channelValue中的++ 为 %，适配小程序不能有%
        arg.setChannelValue(TextUtil.replaceText(arg.getChannelValue(), "++", "%"));
        arg.setChannelValue(UrlUtils.urlDecode(arg.getChannelValue()));
        if (httpServletRequest != null) {
            String ipAddress = httpServletRequest.getRemoteAddr();
            arg.setIpAddr(ipAddress);
        }
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return memberService.wxServiceMemberEnroll(OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId(), arg);
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            arg.setFingerPrintId(BrowserUserInfoKeeper.getFingerPrint());
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                arg.setOuterUid(Integer.valueOf(ErUserInfoKeeper.getEROuterUid()));
                arg.setOuterTenantId(ErUserInfoKeeper.getEROuterTenantId());
                return memberService.partnerMemberEnroll(ErUserInfoKeeper.getERUpstreamEa(),ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid(), arg);
            }
            return memberService.h5MemberEnroll(BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), arg);
        }
        return Result.newSuccess(null);
    }
    
    @Override
    @CrossOrigin
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    public Result<List<MemberAccessibleWebsiteVo>> listMemberAccessibleWebsite(@RequestBody EaArg arg) {
        return memberService.listMemberAccessibleWebsite(arg.getEa());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    @GetOuterIdTrigger
    public Result<QueryMemberInfoResult> queryMemberInfo(@RequestBody ObjectInfoArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return memberService.queryWxServiceMemberInfo(OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            //兼容处理外商协会 CRM会员中心,立即报名
            if (StringUtils.isNotBlank(ErUserInfoKeeper.getERUpstreamEa()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterTenantId()) && StringUtils.isNotBlank(ErUserInfoKeeper.getEROuterUid())) {
                return memberService.queryPartnerMemberInfo(arg.getObjectId(),arg.getObjectType(), ErUserInfoKeeper.getERUpstreamEa(),ErUserInfoKeeper.getEROuterTenantId(),ErUserInfoKeeper.getEROuterUid());
            }
            return memberService.queryH5MemberInfo(arg.getObjectId(), arg.getObjectType(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap());
        }
        return Result.newSuccess(null);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<PageResult<QueryMemberContentResult>> queryWxServiceMemberConference(@RequestBody QueryWxServiceMemberConferenceArg arg) {
        arg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
        arg.setWxOpenId(OuterUserInfoKeeper.getOpenId());
        return memberService.queryWxServiceMemberConference(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.NOT_NEED_IDENTITY})
    @CrossOrigin
    public Result<MiniAppInfoConfigResult> queryMiniAppInfoConfig(@RequestBody ObjectInfoArg arg) {
        return memberService.queryMiniAppInfoConfig(arg.getObjectId(), arg.getObjectType());
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<BuildMemberInfoByFormResult> buildMemberInfoByForm(@RequestBody ObjectInfoArg arg) {
        Integer requestSourceType = RequestSourceInfoKeeper.getRequestSourceType();
        if (requestSourceType.equals(RequestSourceTypeEnum.FXIAOKE_FCP.getType())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            log.warn("MemberController.buildMemberInfoByForm identityCheckType is null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            return memberService.buildWxMemberInfoByForm(arg.getObjectId(), OuterUserInfoKeeper.getWxAppId(), OuterUserInfoKeeper.getOpenId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            return memberService.buildH5MemberInfoByForm(arg.getObjectId(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap());
        }
        log.warn("MemberController.buildMemberInfoByForm identityCheckType error ");
        return Result.newError(SHErrorCode.PARAMS_ERROR);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<MemberSpreadResult> memberSpread(@RequestBody MemberSpreadArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        }
        fillMemberBaseArg(identityCheckType, arg);
        return memberService.memberSpread(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<MemberSpreadResult> createMarketingActivity(@RequestBody MemberSpreadArg arg) {
        return memberService.createMarketingActivity(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<PageResult<MemberSpreadDetail>> memberSpreadList(@RequestBody MemberSpreadListArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        }
        fillMemberBaseArg(identityCheckType, arg);
        return memberService.memberSpreadList(arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<com.facishare.marketing.api.result.PageResult<UserMarketingActionResult>> pageUserMarketingActionStatistic(@RequestBody MemberUserMarketingActionArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            throw new IllegalArgumentException();
        }
        fillMemberBaseArg(identityCheckType, arg);
        return memberService.pageUserMarketingActionStatistic(arg);
    }

    private void fillMemberBaseArg(Integer identityCheckType, MemberBaseArg arg) {
        if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            arg.setIdentityCheckType(identityCheckType);
            arg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
            arg.setOpenId(OuterUserInfoKeeper.getOpenId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            arg.setIdentityCheckType(identityCheckType);
            arg.setMemberCookieMap(BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap());
        }
    }
}
