package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.AddArticleArg;
import com.facishare.marketing.api.arg.InitWebCrawlerArticleArg;
import com.facishare.marketing.api.result.AddWebCrawlerArticleResult;
import com.facishare.marketing.api.result.InitWebCrawlerArticleResult;
import com.facishare.marketing.common.enums.ArticleTypeEnum;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.ListArticleArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.result.QueryArticleDetailResult;
import com.facishare.marketing.api.service.kis.ArticleService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.WebCrawlerUtil;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.QueryArticleDetailArg;
import com.facishare.marketing.web.kis.interceptor.ErUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.validator.routines.UrlValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @创建人 zhengliy
 * @创建时间 2019/2/22 16:19
 * @描述
 */
@RestController
@RequestMapping("/web/article")
@FcpService("article")
@Slf4j
public class ArticleController extends BaseController {

    @Autowired
    private ArticleService articleService;
    @Autowired
    private com.facishare.marketing.api.service.ArticleService kisArticleService;

    @RequestMapping(value = "/listArticles", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("listArticles")
    @GetOuterIdTrigger
    public Result listArticles(@RequestBody com.facishare.marketing.web.kis.arg.ListArticleArg arg) {
        Preconditions.checkNotNull(arg, I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_134));
        Preconditions.checkArgument(!(arg.getPageNum() == null || arg.getPageSize() == null || arg.getPageNum() < 0 || arg.getPageSize() <= 0),
            "IArticleController.listArticles failed pageNum or pageSize is null");
        com.facishare.marketing.api.arg.ListArticleArg vo = BeanUtil.copy(arg, ListArticleArg.class);
        return articleService.listArticles(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), UserInfoKeeper.getEi(), vo);
//        if (isFromInner()) {
//            return articleService.listArticles(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), UserInfoKeeper.getEi(), vo);
//        } else {
//            // 伙伴营销
//            return articleService.listArticles4Outer(ErUserInfoKeeper.getERUpstreamEa(), ErUserInfoKeeper.getEROuterTenantId(), ErUserInfoKeeper.getEROuterUid(), vo);
//        }
    }

    @RequestMapping(value = "/queryArticleDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("queryArticleDetail")
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryArticleDetailResult> queryArticleDetail(@RequestBody QueryArticleDetailArg arg) {
        return articleService.queryArticleDetail(arg.getId());
    }

    @RequestMapping(value = "/listArticleGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @FcpMethod("listArticleGroup")
    @GetOuterIdTrigger
    public Result<ObjectGroupListResult> listArticleGroup(ListGroupArg arg) {
        if (isFromInner()) {
            return articleService.listArticleGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
        } else {
            return articleService.listArticleGroup4Outer(ErUserInfoKeeper.getERUpstreamEa(), ErUserInfoKeeper.getEROuterTenantId(), ErUserInfoKeeper.getEROuterUid(), arg);
        }
    }


    @FcpMethod("initWebCrawlerArticle")
    @GetOuterIdTrigger
    @RequestMapping(value = "initWebCrawlerArticle", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<InitWebCrawlerArticleResult> initWebCrawlerArticle(@RequestBody InitWebCrawlerArticleArg arg) {
        if (arg==null || StringUtils.isBlank(arg.getUrl()) || !new UrlValidator().isValid(arg.getUrl())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isBlank(UserInfoKeeper.getEa()) || UserInfoKeeper.getFsUserId() == null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return kisArticleService.initWebCrawlerArticle(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getUrl(),arg.getScopeType());
    }

    @FcpMethod("addWebCrawlerArticle")
    @GetOuterIdTrigger
    @RequestMapping(value = "addWebCrawlerArticle", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<AddWebCrawlerArticleResult> addWebCrawlerArticle(@RequestBody AddArticleArg arg) {
        Preconditions.checkNotNull(arg, "ArticleController.addAppCrawlerArticle failed arg is null");
        if (StringUtils.isBlank(UserInfoKeeper.getEa()) || UserInfoKeeper.getFsUserId() == null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Preconditions.checkNotNull(arg.getArticleType(), "ArticleController.addAppCrawlerArticle failed type is null");
        Preconditions.checkArgument(arg.getArticleType() == ArticleTypeEnum.REPRINT.getType(), "ArticleController.addAppCrawlerArticle failed type not is REPRINT");
        Preconditions.checkArgument(!(StringUtils.isBlank(arg.getUrl()) || !WebCrawlerUtil.isUrl(arg.getUrl())), "ArticleController.addAppCrawlerArticle failed url is null");
        Preconditions.checkArgument(WebCrawlerUtil.isWXUrl(arg.getUrl()), "ArticleAppController.addAppCrawlerArticle failed url is not WXUrl");
        AddArticleArg vo = BeanUtil.copy(arg, AddArticleArg.class);
        return kisArticleService.addWebCrawlerArticle(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

}