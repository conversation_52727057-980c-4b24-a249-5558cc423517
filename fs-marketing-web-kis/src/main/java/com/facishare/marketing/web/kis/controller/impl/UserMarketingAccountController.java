package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.UserMarketingDetailsByAssociationIdArg;
import com.facishare.marketing.api.arg.usermarketingaccount.*;
import com.facishare.marketing.api.arg.usermarketingaccounttag.AddFirstTagAndSubTagsArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.ListTagModelArg;
import com.facishare.marketing.api.result.CheckUserMarketingAccountResult;
import com.facishare.marketing.api.result.CustomizeFormDataEnrollResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountDetailsResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.GetAllTagGroupsResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.LikeTagByNameResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.ListTagModelResult;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.StringReplaceUtils;
import com.facishare.marketing.common.util.TextUtil;
import com.facishare.marketing.common.util.UrlUtils;
import com.facishare.marketing.outapi.arg.result.AssociateCrmLeadModel.AssociateCrmLeadArg;
import com.facishare.marketing.outapi.arg.result.AssociateCrmLeadModel.AssociateCrmLeadResult;
import com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.CheckUserMarketingAccountArg;
import com.facishare.marketing.web.kis.arg.PageUserMarketingActionStatisticArg;
import com.facishare.marketing.web.kis.controller.IUserMarketingAccountController;
import com.facishare.marketing.web.kis.interceptor.BrowserUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.IdentityInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.OuterUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.Cookie;
import java.util.List;
import java.util.Optional;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-08-05
 */
@Slf4j
@Controller
public class UserMarketingAccountController implements IUserMarketingAccountController {
    @Autowired
    private UserMarketingTagService userMarketingTagService;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private CrmLeadMarketingAccountAssociationService crmLeadMarketingAccountAssociationService;

    @Autowired
    private MemberService memberService;

    @Override
    @GetOuterIdTrigger
    public Result<UserMarketingAccountDetailsResult> getUserMarketingAccountDetails(@RequestBody UserMarketingAccountDetailsArg arg) {
        return userMarketingAccountService.getUserMarketingAccountDetails(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getUserMarketingId());
    }

    @Override
    @GetOuterIdTrigger
    public Result<PageResult<UserMarketingActionResult>> pageUserMarketingActionStatistic(@RequestBody PageUserMarketingActionStatisticArg arg) {
        com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg pageUserMarketingActionStatisticArg = new com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg();
        pageUserMarketingActionStatisticArg.setPageSize(arg.getPageSize());
        pageUserMarketingActionStatisticArg.setPageNo(arg.getPageNum());
        pageUserMarketingActionStatisticArg.setUserMarketingId(arg.getUserMarketingId());
        pageUserMarketingActionStatisticArg.setMarketingActivityId(arg.getMarketingActivityId());
       return userMarketingAccountService.pageUserMarketingActionStatistic(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), pageUserMarketingActionStatisticArg);
    }

    @Override
    public Result<TagNameList> getTagsByUserMarketingAccount(@RequestBody GetTagsByUserMarketingArg arg) {
        return userMarketingAccountService.getTagsByUserMarketingId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getUserMarketingId());
    }

    @Override
    public Result<Boolean> batchDeleteUserMarketingAccountsFromTags(@RequestBody BatchDeleteTagsFromUserMarketingArg arg) {
        return userMarketingAccountService
            .batchDeleteTagsFromUserMarketings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTargetObjectApiName(), arg.getUserMarketingIds(), arg.getTagNameList());
    }

    @Override
    public Result<Boolean> batchMergeUserMarketingAccountsFromTags(@RequestBody BatchMergeUserMarketingAccountsFromTagsArg arg) {
        return userMarketingAccountService.batchMergeUserMarketingAccountsFromTags(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @Override
    public Result<TagNameList> getTagsByUserMarketingId(@RequestBody GetTagsByUserMarketingArg arg) {
        Result<TagNameList> result = userMarketingAccountService.getTagsByUserMarketingId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getUserMarketingId());
        if (result.isSuccess() && CollectionUtils.isEmpty(result.getData())) {
            Result.newSuccess(Lists.newArrayList());
        }
        return result;
    }

    @Override
    public Result<Boolean> batchAddTags(@RequestBody AddTagsToUserMarketingAccountsTArg arg) {
        return userMarketingAccountService
            .batchAddTagsToUserMarketings(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getTargetObjectApiNames(), arg.getUserMarketingIds(), arg.getTagNameList());
    }

    @Deprecated
    @Override
    public Result<List<LikeTagByNameResult>> listAllTag() {
        return userMarketingTagService.listAllTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @Override
    public Result<ListTagModelResult> listTagModel(ListTagModelArg arg) {
        String ea = UserInfoKeeper.getEa();
        Integer fsUserId = UserInfoKeeper.getFsUserId();
        return userMarketingTagService.listTagModel(ea, fsUserId, arg);
    }

    @Deprecated
    @Override
    public Result<List<GetAllTagGroupsResult>> getAllTagGroups() {
        return userMarketingTagService.getAllTagGroups(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @Override
    public Result<AssociateCrmLeadResult> associateCrmLead(@RequestBody AssociateCrmLeadArg associateCrmLeadArg) {
        return crmLeadMarketingAccountAssociationService.associateCrmLead(associateCrmLeadArg);
    }

    @Override
    public Result<UserMarketingAccountDetailsResult> getUserMarketingDetailsByAssociationId(@RequestBody UserMarketingDetailsByAssociationIdArg userMarketingDetailsByAssociationIdArg) {
        Result<UserMarketingAccountDetailsResult> result = userMarketingAccountService
            .getUserMarketingDetailsByAssociationId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), userMarketingDetailsByAssociationIdArg);
        if (result.isSuccess() && result.getData() != null && CollectionUtils.isEmpty(result.getData().getTagNameList())) {
            result.getData().setTagNameList(new TagNameList());
        }
        return result;
    }

    @Override
    public Result<CheckUserMarketingAccountResult> checkUserMarketingAccount(@RequestBody CheckUserMarketingAccountArg checkUserMarketingAccountArg) {
        return userMarketingAccountService
            .checkUserMarketingAccount(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), checkUserMarketingAccountArg.getUid(), checkUserMarketingAccountArg.getNeedCheckApiName());
    }

    @Override
    public Result<NestedId> addFirstTag(AddFirstTagAndSubTagsArg arg) {
        return userMarketingTagService.addFirstTag(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<String> getMarketingAccount(@RequestBody GetMarketingAccountArg arg) {
        Integer identityCheckType = IdentityInfoKeeper.getIdentityCheckType();
        if (identityCheckType == null) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        } else if (IdentityCheckTypeEnum.FXIAOKE.getType() == identityCheckType) {
            // 纷享身份,员工时无需记录来源营销用户
            return Result.newSuccess();
        } else if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == identityCheckType) {
            // 公众号营销
            GetMarketingAccountArg accountArg = new GetMarketingAccountArg();
            accountArg.setObjectId(arg.getObjectId());
            accountArg.setObjectType(arg.getObjectType());
            accountArg.setOpenid(OuterUserInfoKeeper.getOpenId());
            accountArg.setAppId(OuterUserInfoKeeper.getWxAppId());
            return userMarketingAccountService.getMarketingAccount(accountArg);
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == identityCheckType) {
            // 无身份
            Result<String> memberId = memberService.checkH5UserHaveMemberAuthReview(null, arg.getObjectType(), arg.getObjectId(), BrowserUserInfoKeeper.getAllEnterpriseMemberCookieMap(), false);
            GetMarketingAccountArg accountArg = new GetMarketingAccountArg();
            accountArg.setObjectId(arg.getObjectId());
            accountArg.setObjectType(arg.getObjectType());
            accountArg.setMemberId(memberId.getData());
            accountArg.setBrowserId(BrowserUserInfoKeeper.getFingerPrint());
            return userMarketingAccountService.getMarketingAccount(accountArg);
        }
        log.warn("UserMarketingAccountController.getMarketingAccount error identityCheckType error identityCheckType:{}", identityCheckType);
        return new Result<>(SHErrorCode.SYSTEM_ERROR);
    }
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<String> getMarketingAccountByTargetId(@RequestBody MarketingAccountByTargetArg arg) {
        if (StringUtils.isBlank(arg.getTargetId()) || arg.getTargetType() == null) {
            log.warn("UserMarketingAccountController.getTagsByUserMarketingAccount param error args{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if(StringUtils.isBlank(arg.getEa())){
            arg.setEa(UserInfoKeeper.getEa());
        }
        if (StringUtils.isBlank(arg.getEa()) ) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountService.getMarketingAccountByTargetId(arg);
    }
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    @CrossOrigin
    public Result<String> bindBrowserAndOpenId(@RequestBody BindBrowserAndOpenIdArg arg) {
        arg.setOpenId(OuterUserInfoKeeper.getOpenId());
        arg.setWxAppId(OuterUserInfoKeeper.getWxAppId());
        return userMarketingAccountService.bindBrowserAndOpenId(arg);
    }
}
