package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.marketingactivity.GetMarketingActivityArg;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.GetOuterIdTrigger;
import com.facishare.marketing.web.kis.arg.GetMarketingActivityDescriptionArg;
import com.facishare.marketing.api.arg.kis.GetActivityDetailByObjectArg;
import com.facishare.marketing.api.result.kis.GetActivityDetailByObjectResult;
import com.facishare.marketing.api.service.kis.KisMarketingActivityService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.GetMultipleDetailArg;
import com.facishare.marketing.web.kis.controller.IMarketingActivityController;
import com.facishare.marketing.web.kis.interceptor.ErUserInfoKeeper;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created  By zhoux 2019/03/05
 **/
@Controller
@Slf4j
public class MarketingActivityController implements IMarketingActivityController {

    @Autowired
    private KisMarketingActivityService kisMarketingActivityService;
    @Autowired
    private MarketingActivityService marketingActivityService;

    @Override
    public Result<GetActivityDetailByObjectResult> getActivityDetailByObject(@RequestBody com.facishare.marketing.web.kis.arg.GetActivityDetailByObjectArg arg) {
        GetActivityDetailByObjectArg vo = BeanUtil.copy(arg, GetActivityDetailByObjectArg.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUid(UserInfoKeeper.getFsUserId());
        return kisMarketingActivityService.getActivityDetailByObject(vo);
    }

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<String> getMarketingActivityDescription(@RequestBody GetMarketingActivityDescriptionArg arg) {
        return marketingActivityService.getMarketingActivityDescription(arg.getMarketingActivityId(), arg.isPartner());
    }

    @Override
    @GetOuterIdTrigger
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<GetMarketingActivityResult> getMultipleActivityDetail(@RequestBody GetMultipleDetailArg arg) {
        Preconditions.checkNotNull(arg.getMarketingActivityId(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_MARKETINGACTIVITYCONTROLLER_97));
        GetMarketingActivityArg activityArg = new GetMarketingActivityArg();
        activityArg.setId(arg.getMarketingActivityId());
        String ea = UserInfoKeeper.getEa();
//        if (!arg.isPartner()) {
//            ea = UserInfoKeeper.getEa();
//        } else {
//            ea = ErUserInfoKeeper.getERUpstreamEa();
//        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.HEADLINES_INVALID_CORE_USER);
        }
        return marketingActivityService.getMarketingActivity(ea,-10000,activityArg);
    }
}