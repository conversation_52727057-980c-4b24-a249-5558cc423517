package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.mankeep.api.result.distribution.QueryDistributorAssociationResult;
import com.facishare.mankeep.api.result.distribution.QueryMessageDistributeResult;
import com.facishare.mankeep.api.service.DistributionService;
import com.facishare.mankeep.api.vo.distribution.QueryDistributeInfoVO;
import com.facishare.mankeep.api.vo.distribution.QueryDistributorAssociationVO;
import com.facishare.mankeep.common.enums.DistributorStatusEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.marketing.api.result.distribution.QueryDistributeClueResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorApplyResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorInfoResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorRecruitResult;
import com.facishare.marketing.api.service.distribution.ClueService;
import com.facishare.marketing.api.service.distribution.DistributorService;
import com.facishare.marketing.api.vo.ConfirmDistributorVO;
import com.facishare.marketing.api.vo.QueryDistributeClueVO;
import com.facishare.marketing.api.vo.QueryDistributorApplyVO;
import com.facishare.marketing.api.vo.QueryDistributorRecruitVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.distribution.*;
import com.facishare.marketing.web.kis.controller.IDistributorController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by zhengh on 2019/4/16.
 */
@Slf4j
@Controller
public class DistributorController implements IDistributorController{
    @Autowired
    private DistributorService distributorService;
    @Autowired
    private DistributionService outerDistributionService;
    @Autowired
    private com.facishare.mankeep.api.service.OperatorService outerOperatorService;
    @Autowired
    private ClueService clueService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryDistributorInfoResult> queryDistributeInfoByOperator(@RequestBody QueryDistributorInfoArg arg) {
        if (arg.getDistributorId() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try{
            return distributorService.queryDistributeInfoByOperator(arg.getOperatorId(), arg.getDistributorId());
        }catch (Exception e){
            log.error("DistributorController.queryDistributeInfo failed arg:{}", arg);
            return Result.newError(SHErrorCode.QUERY_DISTRIBUTOR_INFO_ERROR);
        }
    }

    @Override
    public Result confirmDistributor(@RequestBody ConfirmDistributorArg arg) {
        if (arg == null || arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try{
            ConfirmDistributorVO vo = BeanUtil.copy(arg, ConfirmDistributorVO.class);
            vo.setFsEa(UserInfoKeeper.getEa());
            vo.setFsUserId(UserInfoKeeper.getFsUserId());
            return distributorService.confirmDistributor(vo);
        }catch (Exception e){
            log.error("DistributorController.confirmDistributor failed arg:{}, exception: {}", arg, e.fillInStackTrace());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<PageResult<QueryDistributorRecruitResult>> queryDistributorRecruit(@RequestBody QueryDistributorRecruitArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getDistributorId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try {
            QueryDistributorRecruitVO vo = BeanUtil.copy(arg, QueryDistributorRecruitVO.class);
            vo.setFsEa(UserInfoKeeper.getEa());
            vo.setFsUserId(UserInfoKeeper.getFsUserId());
            return distributorService.queryDistributorRecruit(vo);
        } catch (Exception e) {
            log.info("DistributorController.queryDistributorRecruit failed arg:{}, exception: {}", arg, e.fillInStackTrace());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<QueryDistributorApplyResult> queryDistributorApplyInfo(@RequestBody QueryDistributorApplyArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getDistributorApplicationId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try {
            QueryDistributorApplyVO vo = BeanUtil.copy(arg, QueryDistributorApplyVO.class);
            vo.setFsEa(UserInfoKeeper.getEa());
            vo.setFsUid(UserInfoKeeper.getFsUserId());
            return distributorService.queryDistributorApplyInfo(vo);
        } catch (Exception e) {
            log.info("DistributorController.queryDistributorRecruit failed arg:{}, exception: {}", arg, e.fillInStackTrace());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public ModelResult<QueryMessageDistributeResult> queryApplyDistributorMessageInfo(@RequestBody OperatorMessageArg arg) {
        try {
            if (arg == null) {
                log.info("DistributionController.queryMessageInfoByDistributorId failed arg is null");
                return new ModelResult<>(com.facishare.mankeep.common.result.SHErrorCode.PARAMS_ERROR);
            }

            if (StringUtils.isBlank(arg.getDistributorApplicationId())) {
                log.info("DistributionController.queryMessageInfoByDistributorId failed distributorApplicationId is null");
                return new ModelResult<>(com.facishare.mankeep.common.result.SHErrorCode.PARAMS_ERROR);
            }

            QueryDistributeInfoVO vo = com.facishare.mankeep.common.util.BeanUtil.copy(arg, QueryDistributeInfoVO.class);
            return outerDistributionService.queryApplyDistributorMessageInfo(vo);
        } catch (Exception e) {
            log.error("DistributionController.queryMessageInfoByDistributorId! arg:{}", e);
            return new ModelResult<>(com.facishare.mankeep.common.result.SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<PageObject<QueryDistributorAssociationResult>> queryDistributorAssociation(@RequestBody QueryDistributorAssociationArg arg) {
        if (arg.isWrongParam()) {
            log.warn("DistributionController.queryDistributorAssociation arg error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getTime() == null) {
            arg.setTime(System.currentTimeMillis());
        }
        if (DistributorStatusEnum.ALL.getType() == arg.getStatus()) {
            arg.setStatus(null);
        }
        try {
            QueryDistributorAssociationVO vo = com.facishare.mankeep.common.util.BeanUtil.copy(arg, QueryDistributorAssociationVO.class);
            ModelResult<PageObject<QueryDistributorAssociationResult>> modelResult = outerOperatorService.queryDistributorAssociation(vo);
            if (!modelResult.isSuccess()){
                return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
            }
            return Result.newSuccess(modelResult.getData());
        } catch (Exception e) {
            log.error("DistributionController.queryDistributorAssociation error arg:{}, e:{}", arg, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<PageResult<QueryDistributeClueResult>> queryDistributeClue(@RequestBody QueryDistributeClueArg arg) {
        Preconditions.checkNotNull(arg.getDistributorId(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_DISTRIBUTORCONTROLLER_71));
        Preconditions.checkNotNull(arg.getType(), I18nUtil.get(I18nKeyEnum.MARK_IMPL_CLUECONTROLLER_58));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_IMPL_CLUECONTROLLER_59));
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_IMPL_CLUECONTROLLER_60));

        if (arg.isWrongParam()){
            log.info("ClueController.queryDistributeClue failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.QUERY_CLUE_LIST_ERROR);
        }

        try{
            QueryDistributeClueVO vo = BeanUtil.copy(arg, QueryDistributeClueVO.class);
            return clueService.queryDistributeClue(vo);
        }catch (Exception e){
            log.info("ClueController.queryDistributeClue failed  arg:{}", arg);
            return Result.newError(SHErrorCode.QUERY_CLUE_LIST_ERROR);
        }
    }
}