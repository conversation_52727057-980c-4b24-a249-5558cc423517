package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.distribution.ConfirmClueValidArg;
import com.facishare.marketing.api.result.distribution.QueryClueDetailResult;
import com.facishare.marketing.api.result.distribution.QueryDistributeClueResult;
import com.facishare.marketing.api.service.distribution.ClueService;
import com.facishare.marketing.api.vo.QueryDistributeClueVO;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.arg.distribution.QueryClueDetailArg;
import com.facishare.marketing.web.kis.arg.distribution.QueryDistributeClueArg;
import com.facishare.marketing.web.kis.controller.IClueController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by zhengh on 2019/4/12.
 */
@Slf4j
@Controller
public class ClueController implements IClueController{
    @Autowired
    private ClueService clueService;

    /**
     * 查询线索详情
     * @param arg
     * @return
     */
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<QueryClueDetailResult> queryDetail(@RequestBody QueryClueDetailArg arg) {
        Preconditions.checkNotNull(arg.getId(), I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUECONTROLLER_127));
        try{
            return clueService.queryDetail(arg.getId());
        }catch (Exception e){
            log.error("ClueController.queryDetail failed clueId:{}", arg.getId());
            return Result.newError(SHErrorCode.QUERY_CLUE_DETAIL_ERROR);
        }
    }

    /**
     * 查询分销人员的线索列表
     * @param arg
     * @return
     */
    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.ALL})
    public Result<PageResult<QueryDistributeClueResult>> queryDistributeClue(@RequestBody QueryDistributeClueArg arg) {
        Preconditions.checkNotNull(arg.getDistributorId(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_DISTRIBUTORCONTROLLER_71));
        Preconditions.checkNotNull(arg.getType(), I18nUtil.get(I18nKeyEnum.MARK_IMPL_CLUECONTROLLER_58));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_IMPL_CLUECONTROLLER_59));
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_IMPL_CLUECONTROLLER_60));

        if (arg.isWrongParam()){
            log.info("ClueController.queryDistributeClue failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.QUERY_CLUE_LIST_ERROR);
        }

        try{
            QueryDistributeClueVO vo = BeanUtil.copy(arg, QueryDistributeClueVO.class);
            return clueService.queryDistributeClue(vo);
        }catch (Exception e){
            log.info("ClueController.queryDistributeClue failed  arg:{}", arg);
            return Result.newError(SHErrorCode.QUERY_CLUE_LIST_ERROR);
        }
    }

    @Override
    public Result confirmClueValid(com.facishare.marketing.web.kis.arg.distribution.ConfirmClueValidArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ClueController.confirmClueValid arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ConfirmClueValidArg confirmClueValidArg = BeanUtil.copy(arg, ConfirmClueValidArg.class);
        confirmClueValidArg.setEa(UserInfoKeeper.getEa());
        confirmClueValidArg.setFsUid(UserInfoKeeper.getFsUserId());
        return clueService.confirmClueValid(confirmClueValidArg);
    }
}