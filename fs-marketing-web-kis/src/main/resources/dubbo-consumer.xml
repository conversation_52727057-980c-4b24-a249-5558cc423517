<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       ">

  <dubbo:application name="${dubbo.application.name}"/>
  <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}" timeout="120000"/>
  <dubbo:consumer check="false" filter="tracerpc" timeout="7000"/>
  <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" protocol="dubbo"/>
  <dubbo:reference id="openAppAdminService" interface="com.facishare.open.app.center.api.service.OpenAppAdminService" version="1.3"/>

  <dubbo:reference id="productService" interface="com.facishare.marketing.api.service.ProductService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="objectGroupService" interface="com.facishare.marketing.api.service.ObjectGroupService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="articleService" interface="com.facishare.marketing.api.service.ArticleService" version="${dubbo.provider.version}">
    <dubbo:method name="addWebCrawlerArticle" retries="0" timeout="500000"/>
  </dubbo:reference>

  <dubbo:reference id="materialSearchService" interface="com.facishare.marketing.api.service.MaterialSearchService" version="${dubbo.provider.version}">
    <dubbo:method name="search" retries="0" timeout="15000"/>
  </dubbo:reference>

  <dubbo:reference id="fileService" interface="com.facishare.marketing.api.service.FileService" timeout="60000" version="${dubbo.provider.version}"/>
  <dubbo:reference id="fileLibraryService" interface="com.facishare.marketing.api.service.fileLibrary.FileLibraryService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="videoService" interface="com.facishare.marketing.api.service.VideoService" timeout="15000" version="${dubbo.provider.version}"/>

  <dubbo:reference id="activityService" interface="com.facishare.marketing.api.service.ActivityService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="materielService" interface="com.facishare.marketing.api.service.distribution.MaterielService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="settingService" interface="com.facishare.marketing.api.service.SettingService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="noticeService" interface="com.facishare.marketing.api.service.NoticeService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="systemNoticeService" interface="com.facishare.marketing.api.service.SystemNoticeService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="userService" interface="com.facishare.marketing.api.service.UserService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="objectFieldService" interface="com.facishare.marketing.api.service.ObjectFieldService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseStatisticService" interface="com.facishare.marketing.api.service.EnterpriseStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="distributorService" interface="com.facishare.marketing.api.service.distribution.DistributorService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseObjectStatisticService" interface="com.facishare.marketing.api.service.EnterpriseObjectStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseEmployeeStatisticService" interface="com.facishare.marketing.api.service.EnterpriseEmployeeStatisticService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="activityTemplateService" interface="com.facishare.marketing.api.service.ActivityTemplateService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="formTemplateService" interface="com.facishare.marketing.api.service.FormTemplateService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="objectDescriptionService" interface="com.facishare.marketing.api.service.ObjectDescriptionService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="formDataService" interface="com.facishare.marketing.api.service.FormDataService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CrmService" id="crmService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.FsBindService" id="fsBindService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.OperatorService" id="operatorService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.DistributionIndexService" id="distributionIndexService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="outerServiceWechatService" interface="com.facishare.wechat.union.core.api.service.OuterServiceWechatService" version="1.0"/>

  <dubbo:reference id="groupSpaceService" interface="com.facishare.mankeep.api.service.GroupSpaceService"  version="${dubbo.provider.version}"/>

  <dubbo:reference id="clueService" interface="com.facishare.marketing.api.service.distribution.ClueService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="memberService" interface="com.facishare.marketing.api.service.MemberService" version="${dubbo.provider.version}">
    <dubbo:method name="h5MemberEnroll" retries="0" timeout="15000"/>
    <dubbo:method name="wxServiceMemberEnroll" retries="0" timeout="15000"/>
  </dubbo:reference>

  <dubbo:reference check="false" id="outEnterpriseSocialGroupService" interface="com.facishare.mankeep.api.outService.service.OutEnterpriseSocialGroupService" timeout="3000" version="1.0"/>

   <dubbo:reference id="applyService" interface="com.facishare.marketing.api.service.sms.ApplyService" version="${dubbo.provider.version}">
    <dubbo:method name="applySignature" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference id="payService" interface="com.facishare.marketing.api.service.sms.PayService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="quotaService" interface="com.facishare.marketing.api.service.sms.QuotaService" version="${dubbo.provider.version}">
    <dubbo:method name="calcForSpendingQuota" retries="0" timeout="60000"/>
  </dubbo:reference>
  <dubbo:reference id="sendService" interface="com.facishare.marketing.api.service.sms.SendService" version="${dubbo.provider.version}">
      <dubbo:method name="sendGroupSms" retries="0"/>
  </dubbo:reference>
  <dubbo:reference id="outCardService" interface="com.facishare.mankeep.api.outService.service.OutCardService"  version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseFeedService" interface="com.facishare.marketing.api.service.enterpriseFeed.EnterpriseFeedService" version="${dubbo.provider.version}"/>

  <dubbo:reference check="false" id="outEnterpriseDefaultCardService" interface="com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService" timeout="10000" version="1.0"/>

  <dubbo:reference check="false" id="weChatServiceMarketingActivityService" interface="com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService" timeout="30000" version="1.0"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.EnterpriseSpreadStatisticService" id="enterpriseSpreadStatisticService" version="${dubbo.provider.version}">
    <dubbo:method name="addMarketingActivityKis" timeout="10000" retries="0"/>
    <dubbo:method name="getClueListByMarketingActivityId" timeout="15000" retries="0"/>
    <dubbo:method name="listMarketingActivity" timeout="10000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.ProductService" id="kisProductService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.ArticleService" id="kisArticleService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.ActivityService" id="kisActivityService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingReportService" id="marketingReportService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.kis.SpreadTaskService" id="spreadTaskService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.kis.AppVersionService" id="appVersionService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.AccountService" id="accountService" version="${dubbo.provider.version}">
    <dubbo:method name="resetAppUserData" timeout="60000" retries="0"/>
    <dubbo:method name="isApplyForKIS" timeout="60000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.kis.SpreadWorkService" id="spreadWorkService" timeout="30000" retries="0" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.kis.KisPermissionService" id="kisPermissionService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.EmployeeSpreadStatisticService" id="employeeSpreadStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.KisMarketingActivityService" id="kisMarketingReportService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.marketingactivity.MarketingActivityService" id="marketingActivityService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="marketingUserGroupService" interface="com.facishare.marketing.api.service.MarketingUserGroupService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.kis.KisActionService" id="kisActionService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.MiniappService" id="miniappService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.kis.RedDotConfigService" id="redDotConfigService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.GuideTaskService" id="guideTaskService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeFormDataService" id="customizeFormDataService" version="${dubbo.provider.version}">
    <dubbo:method name="wxCustomizeFormDataEnroll" timeout="30000" retries="0"/>
    <dubbo:method name="noIdentityFormDataEnroll" timeout="30000" retries="0"/>
    <dubbo:method name="fsCustomizeFormDataEnroll" timeout="30000" retries="0"/>
    <dubbo:method name="exportEnrollsData" timeout="30000" retries="0"/>
    <dubbo:method name="queryFormUserDataForIds" timeout="30000"  retries="0"/>
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAppCardNavbarService" id="customizeMiniAppCardNavbarService"  protocol="dubbo" version="1.0"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.DistributionPlanService" id="distributionPlanService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.MomentPosterService" id="momentPosterService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.WxOfficialAccountsService" id="wxOfficialAccountsService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXContactService" id="qywxContactService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="distributePlanGradeService" interface="com.facishare.marketing.api.service.distribution.DistributePlanGradeService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="wxUserMkActionService" interface="com.facishare.marketing.api.service.WxUserMkActionService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="conferenceService" interface="com.facishare.marketing.api.service.conference.ConferenceService" version="${dubbo.provider.version}">
    <dubbo:method name="addInvitationUserByCrmObj" timeout="10000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference id="qrPosterCodeService" interface="com.facishare.marketing.api.service.qr.QRCodeService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="qrPosterService" interface="com.facishare.marketing.api.service.qr.QRPosterService" version="${dubbo.provider.version}" timeout="15000"/>

  <dubbo:reference id="userMarketingTagService" interface="com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="userMarketingAccountService" interface="com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService" timeout="10000" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService" id="crmLeadMarketingAccountAssociationService" protocol="dubbo" version="${dubbo.provider.version}1.0"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.CustomerService" id="customerService" protocol="dubbo" version="${dubbo.provider.version}"/>

  <dubbo:reference id="marketingEventService" interface="com.facishare.marketing.api.service.MarketingEventService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="customizeTicketService" interface="com.facishare.marketing.api.service.CustomizeTicketService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="wxTicketService" interface="com.facishare.marketing.api.service.WxTicketService" protocol="dubbo" version="1.0"/>

  <dubbo:reference id="hexagonService" interface="com.facishare.marketing.api.service.hexagon.HexagonService" version="${dubbo.provider.version}" timeout="15000"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.BrowserUserService" id="browserUserService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="bdSiteService" interface="com.facishare.marketing.api.service.bd.BdSiteService" version="${dubbo.provider.version}" timeout="15000"/>

  <dubbo:reference id="officialWebsiteService" interface="com.facishare.marketing.api.service.OfficialWebsiteService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.counselor.CounselorService" id="counselorService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="liveService" interface="com.facishare.marketing.api.service.live.LiveService"  version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.OperatorService" id="outerOperatorService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.DistributionService" id="outerDistributionService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QyweixinAccountBindService" id="qyweixinAccountBindService" version="${dubbo.provider.version}"/>
  <dubbo:reference check="false" id="outFileService" interface="com.facishare.mankeep.api.outService.service.OutFileService" timeout="15000" version="1.0"/>
  <dubbo:reference id="taskCenterService" interface="com.facishare.marketing.api.service.TaskCenterService" version="${dubbo.provider.version}">
    <dubbo:method name="pageTaskList" timeout="60000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.BoardService" id="boardService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.StatisticService" id="statisticService" timeout="15000" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAuthorizeService" id="customizeMiniAuthorizeService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeContentService" id="customizeContentService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="wxCouponPayService" interface="com.facishare.marketing.api.service.wxcoupon.WxCouponPayService" version="${dubbo.provider.version}" timeout="15000"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.pay.FsPayService" id="fsPayService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ContentMarketingEventService" id="contentMarketingEventService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.OutLinkService" id="outLinkService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.CardService" id="cardService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService" id="photoLibraryService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.open.material.MaterialShowSettingService" id="materialShowSettingService" protocol="dubbo" version="1.0"/>
  <dubbo:reference id="dataPermissionService" interface="com.facishare.marketing.api.service.permission.DataPermissionService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ai.AiChatService" id="aiChatService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ai.ObjectQueryProxyService" id="objectQueryProxyService" protocol="dubbo" version="1.0"/>
  <dubbo:reference id="authenticationService" interface="com.facishare.marketing.api.service.AuthenticationService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ObjectSloganRelationService" id="objectSloganRelationService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXSettingService" id="qywxSettingService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.digitalHumans.DigitalHumansService" id="digitalHumansService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAppNavbarService" id="customizeMiniAppNavbarService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.MiniAppProductService" id="miniAppProductService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.UserProfileRepositoryService" id="userProfileRepositoryService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.appMenu.AppMenuTemplateService" id="appMenuTemplateService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.PartnerService" id="partnerService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.userrelation.UserRelationService" id="userRelationService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ImageCaptchaService" id="imageCaptchaService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.CtaService" id="kisCtaService" protocol="dubbo"  version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXInnerService" id="qywxInnerService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxUserService" id="qywxUserService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.emailMaterial.EmailMaterialService" id="emailMaterialService" protocol="dubbo" version="${dubbo.provider.version}"/>
</beans>