/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.service.MarketingFlowService;
import com.facishare.marketing.task.dao.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.task.data.MarketingFlowId;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@JobHander(value = "StartUserGroupGrowFlowJob")
@Service
@Slf4j
public class StartUserGroupGrowFlowJob extends IJobHandler {
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @Autowired
    private MarketingFlowService marketingFlowService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            log.info("StartUserGroupGrowFlowJob start");
            startUserGroupGrowFlow();
            return new ReturnT(200, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }

    private void startUserGroupGrowFlow(){
        List<MarketingFlowId> userGroupGrowFlows = marketingFlowAdditionalConfigDao.listStartByUserGroupGrowFlows();
        for (MarketingFlowId userGroupGrowFlow : userGroupGrowFlows) {
            try {
                String ea = eieaConverter.enterpriseIdToAccount(userGroupGrowFlow.getEi());
                UserInfoKeeper.setEa(ea);
            } catch (Exception e) {
                log.warn("Error at enterpriseIdToAccount params:{}", userGroupGrowFlow, e);
            }
            try {
                marketingFlowService.startByUserGroupGrowFlow(userGroupGrowFlow.getEi(), userGroupGrowFlow.getBpmFlowId(), userGroupGrowFlow.getWorkflowId());
            }catch (Exception e){
                log.warn("Error at start userGroup grow flow, marketingFlowId:{}", userGroupGrowFlow, e);
            }
        }
    }
}
