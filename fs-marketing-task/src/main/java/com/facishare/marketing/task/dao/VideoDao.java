package com.facishare.marketing.task.dao;

import com.facishare.marketing.task.data.VideoEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2021/3/3.
 */
public interface VideoDao {
    @Select("SELECT * FROM video WHERE status = 0 and video_type = 1 AND create_time > #{checkpoint}")
    List<VideoEntity> getAllTranscodingResouce(@Param("checkpoint")Date checkpoint);

    @Select("SELECT * FROM video WHERE token=#{token}")
    VideoEntity getResourceByToken(@Param("token")String token);

    @Update("<script>"
            + "UPDATE video\n"
            +   "<set>"
            +      "<if test = \"ldUrl != null\">"
            +          "ld_url = #{ldUrl},"
            +      "</if>\n"
            +      "<if test = \"hdUrl != null\">"
            +          "hd_url = #{hdUrl},"
            +      "</if>\n"
            +      "<if test = \"image4H5 != null\">"
            +          "image4_h5 = #{image4H5},"
            +      "</if>\n"
            +      "<if test = \"image4Web != null\">"
            +          "image4_web = #{image4Web},"
            +      "</if>\n"
            +      "<if test = \"videoName != null\">"
            +          "video_name = #{videoName},"
            +      "</if>\n"
            +      "<if test = \"videoSize != null\">"
            +          "video_size = #{videoSize},"
            +      "</if>\n"
            +      "<if test = \"videoTime != null\">"
            +          "video_time = #{videoTime},"
            +      "</if>\n"
            +      "<if test = \"status != null\">"
            +          "status = #{status},"
            +      "</if>\n"
            +      "update_time = now()\n"
            +   "</set>"
            +   "WHERE token=#{token}"
            + "</script>")
    void updateTrasResult(@Param("entity") VideoEntity entity);

    @Update("UPDATE video SET status=#{status}, update_time=now() WHERE token=#{token}")
    void updateStatus(@Param("status")Integer status, @Param("token")String token);

    @Update("UPDATE video SET hd_url=#{entity.hdUrl}, ld_url=#{entity.ldUrl}, transcode_url = #{entity.transcodeUrl}, image4_h5=#{entity.image4H5}, image4_web=#{entity.image4Web}, file_id=#{entity.fileId}, video_size=#{entity.videoSize}, video_time=#{entity.videoTime}, status=#{entity.status}, update_time=#{entity.updateTime} WHERE id=#{entity.id}")
    int updateTransitVideo(@Param("entity") VideoEntity entity);
}
