package com.facishare.marketing.task.entity;

import com.facishare.marketing.common.enums.SendNoticeTypeEnum;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created By tianh, 2018/6/28.
 **/
@Entity
@Data
public class NoticeEntity implements Serializable {
    /** id **/
    private String id;
    /** 通知标题 **/
    private String title;
    /** 通知内容 **/
    private String description;
    /** 推广内容类型 1：文章 2：图片 **/
    private Integer contentType;
    /** 推广内容id **/
    private String content;
    /** 发送类型： 1：立即发送 2：定时发送 **/
    private Integer sendType;
    /** 发送时间 **/
    private Date sendTime;
    /** 定时发送时间 **/
    private Date timingTime;
    /** 发送状态 1：未发送 2：发送成功 3：发送失败 **/
    private Integer status;
    /** 推广时间 start **/
    private Date startTime;
    /** 推广时间 start **/
    private Date endTime;
    /** 创建时间 **/
    private Date createTime;
    /** 更新时间 **/
    private Date updateTime;
    /** 发送范围 **/
    private String sendScope;
    /** 操作人的纷享企业账号 **/
    private String fsEa;
    /** @see SendNoticeTypeEnum **/
    private Integer type;
    /** 操作人的纷享用户id **/
    private Integer fsUserId;
    /** 操作人姓名 **/
    private String username;
    /** 营销活动id **/
    private String marketingActivityId;
    /** 封面Apath **/
    private String coverApath;
    /** 图片或海报多张素材 **/
    private String materialInfoList;
}
