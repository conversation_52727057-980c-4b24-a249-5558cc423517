/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.task.job;

import com.facishare.marketing.api.arg.qywx.SendResultScheduleArg;
import com.facishare.marketing.api.service.QywxGroupMessageService;
import com.facishare.marketing.task.dao.TriggerTaskInstanceDao;
import com.facishare.marketing.task.interceptor.UserInfoKeeper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@JobHander(value = "SopQywxMsgResultJob")
@Service
@Slf4j
public class SopQywxMsgResultJob extends IJobHandler {

    @Autowired
    private QywxGroupMessageService qywxGroupMessageService;
    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;

    @Override
    public ReturnT execute(TriggerParam params) throws Exception {
        try {
            for (int offset = 0, limit = 1000; offset < 200000; offset += limit) {
                List<SendResultScheduleArg> sendResultArgs = triggerTaskInstanceDao.newNeedQueryResultQywxMsgSopTask(offset, limit);
                if (CollectionUtils.isEmpty(sendResultArgs)) {
                    break;
                }
                for (SendResultScheduleArg sendResultArg : sendResultArgs) {
                    UserInfoKeeper.setEa(sendResultArg.getEa());
                    qywxGroupMessageService.handlerSopQywxMsgTaskResult(sendResultArg);
                }
            }
            for (int offset = 0, limit = 1000; offset < 200000; offset += limit) {
                List<SendResultScheduleArg> groupSendResultArgs = triggerTaskInstanceDao.newNeedQueryResultQywxMsgGroupSopTask(offset, limit);
                if (CollectionUtils.isEmpty(groupSendResultArgs)) {
                    break;
                }
                for (SendResultScheduleArg groupSendResultArg : groupSendResultArgs) {
                    UserInfoKeeper.setEa(groupSendResultArg.getEa());
                    qywxGroupMessageService.handSopQywxGroupMsgTaskResult(groupSendResultArg);
                }
            }
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
        return new ReturnT(200, "定时任务调用成功");
    }
}
