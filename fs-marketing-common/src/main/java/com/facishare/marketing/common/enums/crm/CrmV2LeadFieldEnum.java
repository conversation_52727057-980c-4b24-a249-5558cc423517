package com.facishare.marketing.common.enums.crm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@AllArgsConstructor
public enum CrmV2LeadFieldEnum {

    ID("_id", "_id", true),
    ObjectDescribeID("object_describe_id", "object_describe_id", true),
    ApiName("object_describe_api_name", "object_describe_api_name", true),
    CustomerID("CustomerID", "account_id", false),
    CustomerName("CustomerName", "account_id__r", false),
    OpportunityID("OpportunityID", "opportunity_id", false),
    OpportunityName("OpportunityName", "opportunity_id__r", false),
    SalesClueName("SalesClueName", "name", false),
    MarketingEventID("MarketingEventID", "marketing_event_id", false),
    MarketingEventName("MarketingEventName", "marketing_event_id__r", false),
    ContactID("ContactID", "contact_id", false),
    ContactName("ContactName", "contact_id__r", false),
    SalesCluePoolID("SalesCluePoolID", "leads_pool_id", false),
    SalesCluePoolName("SalesCluePoolName", "leads_pool_id__r", false),
    Company("Company", "company", false),
    Remark("Remark", "remark", false),
    Source("Source", "source", false),
    Department("Department", "department", false),
    Position("Position", "job_title", false),
    ContactWay("ContactWay", "tel", false),
    Mobile("Mobile", "mobile", false),
    URL("URL", "url", false),
    Email("Email", "email", false),
    Address("Address", "address", false),
    DealResult("DealResult", "completed_result", true),
    DoneTime("DoneTime", "completed_time", true),
    Status("Status", "leads_status", true),
    OwnerID("OwnerID", "owner", true),
    Circles("Circles", "owner_department", true),
    AssignerID("AssignerID", "assigner_id", true),
    AssignTime("AssignTime", "assigned_time", true),
    IsOverTime("IsOverTime", "is_overtime", true),
    LastFolowerID("LastFolowerID", "last_follower", true),
    LastFollowTime("LastFollowTime", "last_follow_time", true),
    TransformTime("TransformTime", "transform_time", true),
    PicturePath("PicturePath", "picture_path", false),
    CreatorID("CreatorID", "created_by", true),
    CreateTime("CreateTime", "create_time", true),
    UpdateTime("UpdateTime", "last_modified_time", true),
    UpdatorID("UpdatorID", "last_modified_by", true),
    IsDeleted("IsDeleted", "is_deleted", true),
    LockStatus("LockStatus", "lock_status", true),
    OwnerChangeTime("OwnerChangeTime", "owner_change_time", true),
    PartnerID("PartnerID", "partner_id", false),
    PartnerName("PartnerName", "partner_id__r", false),
    OutEI("OutEI", "out_tenant_id", false),
    OutOwnerID("OutOwnerID", "out_owner", false),
    OutResources("OutResources", "out_resources", false),
    RecordType("RecordType", "record_type", true),
    CloseReason("CloseReason", "close_reason", false),
    NewOpportunityID("NewOpportunityID", "new_opportunity_id", false),
    NewOpportunityName("NewOpportunityName", "new_opportunity_id__r", false),
    DataOwnDepartment("DataOwnDepartment", "data_own_department", false),
    MarketingEventId("marketing_event_id", "marketing_event_id", true),
    SubMarketingEventId("sub_marketing_event_id", "sub_marketing_event_id", true),
    DataOwnDepartmentName("DataOwnDepartmentName", "data_own_department__r", false),
    DataOwnOrganizationName("data_own_organization", "data_own_organization", false),
    PromotionChannel("promotion_channel", "promotion_channel", true),
    PromotionChannel__o("promotion_channel__o", "promotion_channel__o", true),
    LifeStatus("life_status", "life_status", true),
    MarketingPromotionSourceId("marketing_promotion_source_id", "marketing_promotion_source_id", true);

    private String oldFieldName;
    private String newFieldName;
    private Boolean needFilter;

    public static String getNewFieldName(String oldFieldName) {
        for (CrmV2LeadFieldEnum code : values()) {
            if (code.getOldFieldName().equals(oldFieldName)) {
                return code.getNewFieldName();
            }
        }

        return null;
    }

    public static Boolean needFilter(String newFieldName) {
        for (CrmV2LeadFieldEnum code : values()) {
            if (code.getNewFieldName().equals(newFieldName)) {
                return code.getNeedFilter();
            }
        }

        return null;
    }

}
