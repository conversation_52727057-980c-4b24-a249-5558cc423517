package com.facishare.marketing.web.cepauth;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.GetCepAuthByRequetURIArg;
import com.facishare.marketing.api.arg.GetCepAuthByTokenArg;
import com.facishare.marketing.api.arg.GetCepEaRuleByRequetURIArg;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.result.GetCepAuthByTokenResult;
import com.facishare.marketing.api.result.GetCepEaRuleByRequetURIResult;
import com.facishare.marketing.api.result.fsBind.GetFsBindInfoResult;
import com.facishare.marketing.api.service.FsBindService;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.manager.TokenManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.facishare.marketing.web.cepauth.AuthRuleEnum.DISTRIBUTOR_QUERY_GRADE;

@RestController
@RequestMapping("cepAuth")
@Slf4j
@Api(description = "cep信息解析")
public class CepAuthController {
    private EIEAConverter eieaConverter;
    @Autowired
    private TokenManager tokenManager;
    @Autowired
    private FsBindService fsBindService;
    @ReloadableProperty("cep_default_ea")
    private String cepDefaultEa;
    @ReloadableProperty("cep_qywx_skip_urls")
    private String qywxSkipUrls;

    @RequestMapping(value = "isQywxSkipUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Boolean> isQywxSkipUrl(@RequestBody GetCepEaRuleByRequetURIArg arg) {
        boolean flag = false;
        if (StringUtils.isNotBlank(qywxSkipUrls)) {
            String[] urls = qywxSkipUrls.split(",");
            if (Lists.newArrayList(urls).stream().anyMatch(arg.getRequestURI()::contains)) {
                flag = true;
            }
        }
        return Result.newSuccess(flag);
    }

    @RequestMapping(value = "getCepAuthByToken", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetCepAuthByTokenResult> getCepAuthByToken(@RequestBody GetCepAuthByTokenArg arg) {
        GetCepAuthByTokenResult cepAuthData = new GetCepAuthByTokenResult();
        Result<QYWXBaseArg> baseArgModelResult = tokenManager.token2QYWXBaseArg(arg.getToken());
        if (baseArgModelResult.isSuccess() && baseArgModelResult.getData() != null) {
            QYWXBaseArg baseArg = baseArgModelResult.getData();
            if (StringUtils.isNotBlank(baseArg.getUid()) && (StringUtils.isBlank(baseArg.getFsEa()) || baseArg.getFsUserId() == null)) {
                //处理第一次授权绑定员工信息后, token 里并未带ea , fsUserId 信息
                Result<GetFsBindInfoResult> result = fsBindService.getBindInfoByUid(baseArg.getUid());
                if (result.isSuccess() && result.getData() != null) {
                    baseArg.setFsEa(result.getData().getFsEa());
                    baseArg.setFsUserId(result.getData().getFsUserId());
                }
            }
            if (StringUtils.isBlank(baseArg.getFsEa())) {
                WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(baseArg.getAppId());
                if (wxAppInfoEnum != null) {
                    baseArg.setFsEa(cepDefaultEa);
                }
                //处理非员工身份查找ea
                Result<String> result = fsBindService.getFsEaByAppId(baseArg.getAppId());
                if (result.isSuccess() && result.getData() != null) {
                    baseArg.setFsEa(result.getData());
                }
            }
            //异常兜底
            if (StringUtils.isBlank(baseArg.getFsEa())) {
                baseArg.setFsEa(cepDefaultEa);
            }
            cepAuthData = new GetCepAuthByTokenResult(baseArg.getFsEa(), baseArg.getFsUserId());
        }
        return Result.newSuccess(cepAuthData);
    }

    @RequestMapping(value = "getDefaultCepAuth", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetCepAuthByTokenResult> getDefaultCepAuth() {
        GetCepAuthByTokenResult result = new GetCepAuthByTokenResult(cepDefaultEa, null);
        return Result.newSuccess(result);
    }

    @RequestMapping(value = "getCepEaRuleByRequetURI", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetCepEaRuleByRequetURIResult> getCepEaRuleByRequetURI(@RequestBody GetCepEaRuleByRequetURIArg arg) {
        GetCepEaRuleByRequetURIResult result = null;
        String requestURI = arg.getRequestURI();

        AuthRuleEnum authRuleEnum = AuthRuleEnum.getAuthRuleEnum(requestURI);
        if (authRuleEnum == null){
            result =  new GetCepEaRuleByRequetURIResult(AuthRuleValueEnum.DEFAULT.getRule(), null, 0, cepDefaultEa);
            return Result.newSuccess(result);
        }
        result = new GetCepEaRuleByRequetURIResult(authRuleEnum.getRule(), authRuleEnum.getKey(), authRuleEnum.getType(), authRuleEnum.getValue());
        if (result.getRule() == AuthRuleValueEnum.DEFAULT.getRule()){
            result.setValue(cepDefaultEa);
        }

        return Result.newSuccess(result);
    }

}
