package com.facishare.marketing.web.controller;

import com.facishare.mankeep.api.outService.arg.enterpriseSocialGroup.UpdateGroupAdminArg;
import com.facishare.mankeep.api.outService.service.OutEnterpriseSocialGroupService;
import com.facishare.mankeep.api.result.group.DistributionMaterialInfoResult;
import com.facishare.mankeep.api.result.group.EnterpriseGroupMemberInfoResult;
import com.facishare.mankeep.api.result.group.EnterpriseGroupObjectStatisticResult;
import com.facishare.mankeep.api.result.group.EnterpriseGroupStatisticResult;
import com.facishare.mankeep.api.result.group.EnterpriseGroupUserActiveStatisticResult;
import com.facishare.mankeep.api.result.group.GroupFeedDetailResult;
import com.facishare.mankeep.api.result.group.QueryEaGroupsResult;
import com.facishare.mankeep.api.service.GroupSpaceService;
import com.facishare.mankeep.api.vo.group.EnterpriseGroupMemberInfoVO;
import com.facishare.mankeep.api.vo.group.EnterpriseGroupObjectStatisticVO;
import com.facishare.mankeep.api.vo.group.EnterpriseGroupStatisticVO;
import com.facishare.mankeep.api.vo.group.EnterpriseGroupUserActiveStatisticVO;
import com.facishare.mankeep.api.vo.group.ListEaGroupFeedsVO;
import com.facishare.mankeep.api.vo.group.QueryEaGroupsVO;
import com.facishare.mankeep.api.vo.groupSpace.DistributionMaterialByObjectTypeVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.marketing.api.result.GetEnterpriseSocialGroupGrayResult;
import com.facishare.marketing.api.service.EnterpriseSocialGroupService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.group.EnterpriseGroupMemberInfoArg;
import com.facishare.marketing.web.arg.group.EnterpriseGroupObjectStatisticArg;
import com.facishare.marketing.web.arg.group.EnterpriseGroupStatisticArg;
import com.facishare.marketing.web.arg.group.EnterpriseGroupUserActiveStatisticArg;
import com.facishare.marketing.web.arg.group.ListEaGroupFeedsArg;
import com.facishare.marketing.web.arg.group.QueryEaGroupsArg;
import com.facishare.marketing.web.enums.CheckIndentityAuthScopeEnum;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by ranluch on 2018/10/29.
 */
@RestController
@RequestMapping("enterpriseSocialGroup")
@Api(tags = "enterpriseSocialGroup", description = "社群营销相关")
public class EnterpriseSocialGroupController {
    @Autowired
    private GroupSpaceService groupSpaceService;

    @Autowired
    private OutEnterpriseSocialGroupService outEnterpriseSocialGroupService;

    @Autowired
    private EnterpriseSocialGroupService enterpriseSocialGroupService;


    @ApiOperation(value = "社群列表灰度")
    @CheckIdentityTrigger
    @RequestMapping(value = "gray", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetEnterpriseSocialGroupGrayResult> getEnterpriseSocialGroupGray() {
        return enterpriseSocialGroupService.gray(UserInfoKeeper.getEa());
    }


    @ApiOperation(value = "获取群组列表")
    @CheckIdentityTrigger
    @RequestMapping(value = "queryEnterpriseGroups", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageObject<QueryEaGroupsResult>> queryEnterpriseGroups(@RequestBody QueryEaGroupsArg arg) {
        Preconditions.checkNotNull(arg, "queryEnterpriseGroups arg is null");
        Preconditions.checkArgument(!(arg.getPageNum() == null || arg.getPageSize() == null || arg.getPageNum() <= 0 || arg.getPageSize() <= 0),
            "EnterpriseSocialGroupController.queryEnterpriseGroups failed,ea or pageNum or pageSize is null");
        QueryEaGroupsVO vo = BeanUtil.copy(arg, QueryEaGroupsVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        ModelResult<PageObject<QueryEaGroupsResult>> modelResult = groupSpaceService.queryEnterpriseGroups(vo);
        if (modelResult.getErrCode() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, modelResult.getData());
        }
        return new Result<>(SHErrorCode.UNKNOWN);
    }

    @ApiOperation(value = "获取群动态列表")
    @CheckIdentityTrigger
    @RequestMapping(value = "listEnterpriseGroupFeeds", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageObject<GroupFeedDetailResult>> listEnterpriseGroupFeeds(@RequestBody ListEaGroupFeedsArg arg) {
        Preconditions.checkNotNull(arg, "listEnterpriseGroupFeeds arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null || arg.getPageNum() == null || arg.getPageSize() == null || arg.getPageNum() < 0 || arg.getPageSize() <= 0),
            "EnterpriseSocialGroupController.listEnterpriseGroupFeeds failed, ea or groupId or pageNum or pageSize is null");
        ListEaGroupFeedsVO vo = BeanUtil.copy(arg, ListEaGroupFeedsVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        ModelResult<PageObject<GroupFeedDetailResult>> modelResult = groupSpaceService.listEnterpriseGroupFeeds(vo);
        if (modelResult.getErrCode() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, modelResult.getData());
        }
        return new Result<>(SHErrorCode.UNKNOWN);
    }

    @ApiOperation(value = "营销数据-数据统计")
    @CheckIdentityTrigger
    @RequestMapping(value = "queryEnterpriseGroupStatistic", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EnterpriseGroupStatisticResult> queryEnterpriseGroupStatistic(@RequestBody EnterpriseGroupStatisticArg arg) {
        Preconditions.checkNotNull(arg, "queryEnterpriseGroupStatistic arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null),
            "EnterpriseSocialGroupController.queryEnterpriseGroupStatistic failed, ea or groupId is null");

        EnterpriseGroupStatisticVO vo = BeanUtil.copy(arg, EnterpriseGroupStatisticVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        ModelResult<EnterpriseGroupStatisticResult> modelResult = groupSpaceService.queryEnterpriseGroupStatistic(vo);
        if (modelResult.getErrCode() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, modelResult.getData());
        }
        return new Result<>(SHErrorCode.UNKNOWN);
    }

    @ApiOperation(value = "营销数据-热门内容")
    @CheckIdentityTrigger
    @RequestMapping(value = "queryEnterpriseGroupObjectStatistic", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EnterpriseGroupObjectStatisticResult> queryEnterpriseGroupObjectStatistic(@RequestBody EnterpriseGroupObjectStatisticArg arg) {
        Preconditions.checkNotNull(arg, "queryEnterpriseGroupObjectStatistic arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null),
            "EnterpriseSocialGroupController.queryEnterpriseGroupObjectStatistic failed, ea or groupId is null");
        Preconditions.checkArgument(!(arg.getDay() == null),
                "EnterpriseSocialGroupController.queryEnterpriseGroupObjectStatistic failed, day is null");
        EnterpriseGroupObjectStatisticVO vo = BeanUtil.copy(arg, EnterpriseGroupObjectStatisticVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        ModelResult<EnterpriseGroupObjectStatisticResult> modelResult = groupSpaceService.queryEnterpriseGroupObjectStatistic(vo);
        if (modelResult.getErrCode() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, modelResult.getData());
        }
        return new Result<>(SHErrorCode.UNKNOWN);
    }

    @ApiOperation(value = "营销数据-群成员互动排行")
    @CheckIdentityTrigger
    @RequestMapping(value = "queryEnterpriseGroupUserActiveStatistic", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EnterpriseGroupUserActiveStatisticResult> queryEnterpriseGroupUserActiveStatistic(@RequestBody EnterpriseGroupUserActiveStatisticArg arg) {
        Preconditions.checkNotNull(arg, "queryEnterpriseGroupUserActiveStatistic arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null),
            "EnterpriseSocialGroupController.queryEnterpriseGroupUserActiveStatistic failed, ea or groupId is null");

        EnterpriseGroupUserActiveStatisticVO vo = BeanUtil.copy(arg, EnterpriseGroupUserActiveStatisticVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        ModelResult<EnterpriseGroupUserActiveStatisticResult> modelResult = groupSpaceService.queryEnterpriseGroupUserActiveStatistic(vo);
        if (modelResult.getErrCode() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, modelResult.getData());
        }
        return new Result<>(SHErrorCode.UNKNOWN);
    }

    @ApiOperation(value = "社群营销-群资料")
    @CheckIdentityTrigger
    @RequestMapping(value = "listEnterpriseGroupMemberInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageObject<EnterpriseGroupMemberInfoResult>> listEnterpriseGroupMemberInfo(@RequestBody EnterpriseGroupMemberInfoArg arg) {
        Preconditions.checkNotNull(arg, "listEnterpriseGroupMemberInfo arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null),
            "EnterpriseSocialGroupController.listEnterpriseGroupMemberInfo failed, ea or groupId is null");

        EnterpriseGroupMemberInfoVO vo = BeanUtil.copy(arg, EnterpriseGroupMemberInfoVO.class);
        //vo.setEa(UserInfoKeeper.getEa());
        ModelResult<PageObject<EnterpriseGroupMemberInfoResult>> modelResult = groupSpaceService.listEnterpriseGroupMemberInfo(vo);
        if (modelResult.getErrCode() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, modelResult.getData());
        }
        return new Result<>(SHErrorCode.UNKNOWN);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.ONLY_APP_ADMIN)
    @RequestMapping(value = "updateGroupAdmin",method = RequestMethod.POST)
    @ApiOperation(value = "重置群管理员",tags = "1.5.5")
    public Result updateGroupAdmin(@RequestBody com.facishare.marketing.web.arg.group.UpdateGroupAdminArg arg){
        Preconditions.checkNotNull(arg, "updateGroupAdmin arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null),
            "EnterpriseSocialGroupController.updateGroupAdmin failed, ea or groupId is null");

        UpdateGroupAdminArg updateGroupAdminArg = BeanUtil.copy(arg, UpdateGroupAdminArg.class);
        updateGroupAdminArg.setEa(UserInfoKeeper.getEa());

        ModelResult modelResult = outEnterpriseSocialGroupService.updateGroupAdmin(updateGroupAdminArg);
        if (modelResult.isSuccess()) {
            return Result.newSuccess();
        }

        return new Result(modelResult.getErrCode(), modelResult.getErrMsg());
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "queryDistributionMaterialByObjectType",method = RequestMethod.POST)
    @ApiOperation(value = "查询群资料",tags = "1.5.5")
    public Result<PageObject<DistributionMaterialInfoResult>> queryDistributionMaterialByObjectType(@RequestBody com.facishare.marketing.web.arg.distribution.DistributionMaterialByObjectTypeArg arg){
        Preconditions.checkNotNull(arg, "queryDistributionMaterialByObjectType arg is null");
        Preconditions.checkArgument(!(arg.getGroupId() == null),
                "EnterpriseSocialGroupController.queryDistributionMaterialByObjectType failed, ea or groupId is null");

        Preconditions.checkArgument(!(arg.getObjectType() == null),
                "EnterpriseSocialGroupController.queryDistributionMaterialByObjectType failed, ea or objectType is null");

        DistributionMaterialByObjectTypeVO distributionMaterialByObjectTypeVO = BeanUtil.copy(arg, DistributionMaterialByObjectTypeVO.class);
        distributionMaterialByObjectTypeVO.setFsEa(UserInfoKeeper.getEa());

        ModelResult<PageObject<DistributionMaterialInfoResult>> modelResult = groupSpaceService.queryDistributionMaterialByObjectType(distributionMaterialByObjectTypeVO);
        if (modelResult.isSuccess()) {
            return Result.newSuccess(modelResult.getData());
        }

        return new Result<>(SHErrorCode.UNKNOWN);
    }

}
