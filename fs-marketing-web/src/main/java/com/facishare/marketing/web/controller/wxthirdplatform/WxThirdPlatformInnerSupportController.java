package com.facishare.marketing.web.controller.wxthirdplatform;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.DispatchRequestArg;
import com.facishare.marketing.api.result.DispatchRequestResult;
import com.facishare.marketing.api.result.QueryWxMiniListResult;
import com.facishare.marketing.api.result.WxCodeTemplateResult;
import com.facishare.marketing.api.result.WxTemplateVersionResult;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdPlatformInnerSupportService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.google.common.base.Preconditions;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 第三方授权相关的接口
 */
@Slf4j
@RestController
@RequestMapping("/wxThirdPlatformInnerSupport")
public class WxThirdPlatformInnerSupportController {
    @Autowired
    private WxThirdPlatformInnerSupportService wxThirdPlatformInnerSupportService;

    @GetMapping("getWxAppIdByPlatformIdAndEa")
    public Result<String> getWxAppIdByPlatformIdAndEa(@RequestParam("fsPlatformId") String fsPlatformId, @RequestParam("ea") String ea, HttpServletRequest request){
        onlyPermitInnerVisit(request);
        return wxThirdPlatformInnerSupportService.getWxAppIdByFSPlatformIdAndEa(fsPlatformId, ea);
    }

    @GetMapping("getEaByFSPlatformIdAndWxAppId")
    public Result<String> getEaByFSPlatformIdAndWxAppId(@RequestParam("fsPlatformId") String fsPlatformId, @RequestParam("wxAppId") String wxAppId, HttpServletRequest request){
        onlyPermitInnerVisit(request);
        return wxThirdPlatformInnerSupportService.getEaByFSPlatformIdAndWxAppId(fsPlatformId, wxAppId);
    }

    @PostMapping("dispatchRequest")
    public Result<DispatchRequestResult> dispatchRequest(@RequestParam("fsPlatformId") String fsPlatformId, @RequestParam("wxAppId") String wxAppId, @RequestBody DispatchRequestArg dispatchRequestArg, HttpServletRequest request){
        onlyPermitInnerVisit(request);
        return wxThirdPlatformInnerSupportService.dispatchRequest(fsPlatformId, wxAppId, dispatchRequestArg);
    }

    @GetMapping("getWxMiniAppList")
    public Result<List<QueryWxMiniListResult>> getWxMiniAppList(@RequestParam("fsPlatformId") String fsPlatformId, @RequestParam("keywordType") String keywordType, @RequestParam("keyword") String keyword, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        if ("null".equals(keywordType)) {
            keywordType = null;
        }
        if ("null".equals(keyword)) {
            keyword = null;
        }
        return wxThirdPlatformInnerSupportService.getWxMiniAppList(fsPlatformId, keywordType, keyword);
    }

    @PostMapping("batchCommitCodeAndSubmitAudit")
    public Result<String> batchCommitCodeAndSubmitAudit(@RequestParam String appIdListStr, @RequestParam String platformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        if (StringUtils.isEmpty(appIdListStr) || "null".equals(appIdListStr) || StringUtils.isEmpty(platformId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> appIds = JSON.parseArray(appIdListStr, String.class);
        if (appIds.isEmpty()) {
            return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTCONTROLLER_73));
        }
        return wxThirdPlatformInnerSupportService.batchCommitCodeAndSubmitAudit(appIds, platformId);
    }

    @PostMapping("batchReleaseCode")
    public Result<String> batchReleaseCode(@RequestParam String appIdListStr, @RequestParam String platformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        if (StringUtils.isEmpty(appIdListStr) || "null".equals(appIdListStr) || StringUtils.isEmpty(platformId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> appIds = JSON.parseArray(appIdListStr, String.class);
        if (appIds.isEmpty()) {
            return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTCONTROLLER_73));
        }
        return wxThirdPlatformInnerSupportService.batchReleaseCode(appIds, platformId);
    }

    @PostMapping("batchUndoCodeAudit")
    public Result<String> batchUndoCodeAudit(@RequestParam String appIdListStr, @RequestParam String platformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        if (StringUtils.isEmpty(appIdListStr) || "null".equals(appIdListStr) || StringUtils.isEmpty(platformId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> appIds = JSON.parseArray(appIdListStr, String.class);
        if (appIds.isEmpty()) {
            return Result.newSuccess(I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTCONTROLLER_73));
        }
        return wxThirdPlatformInnerSupportService.batchUndoCodeAudit(appIds, platformId);
    }

    @GetMapping("getWxMiniAppNewVersion")
    public Result<WxCodeTemplateResult> getWxMiniAppNewVersion(@RequestParam("platformId") String platformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        return wxThirdPlatformInnerSupportService.getWxMiniAppNewVersion(platformId);
    }

    @GetMapping("getVersionList")
    public Result<List<WxTemplateVersionResult>> getVersionList(@RequestParam("platformId") String platformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        if (!request.getRequestURL().toString().contains("fs-marketing-web.foneshare.k8s1.foneshare.cn") && !request.getRequestURL().toString().contains("fs-marketing.fstest.k8s1.firstshare.cn")) {
            return Result.newError(SHErrorCode.UNKNOWN, new ArrayList<>());
        }
        return wxThirdPlatformInnerSupportService.getVersionList(platformId);
    }

    @PostMapping("updateShowVersion")
    public Result<String> updateShowVersion(@RequestParam String showCodeTemplateId, @RequestParam String showCodeVersion, @RequestParam String showCodeDescription, @RequestParam String platformId, HttpServletRequest request) {
        onlyPermitInnerVisit(request);
        if (!request.getRequestURL().toString().contains("fs-marketing-web.foneshare.k8s1.foneshare.cn") && !request.getRequestURL().toString().contains("fs-marketing.fstest.k8s1.firstshare.cn")) {
            return Result.newError(SHErrorCode.UNKNOWN, I18nUtil.get(I18nKeyEnum.MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTCONTROLLER_123));
        }
        return wxThirdPlatformInnerSupportService.updateShowVersion(showCodeTemplateId, showCodeVersion, showCodeDescription, platformId);
    }

    private void onlyPermitInnerVisit(HttpServletRequest request){
        Preconditions.checkArgument(request.getRequestURI().contains("/inner/"));
    }
}