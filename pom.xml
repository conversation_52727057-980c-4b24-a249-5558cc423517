<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <modules>
    <module>fs-marketing-common</module>
    <module>fs-marketing-api</module>
    <module>fs-marketing-outapi</module>
    <module>fs-marketing-provider</module>
    <module>fs-marketing-web</module>
    <module>fs-marketing-task</module>
    <module>fs-marketing-web-kis</module>
    <module>fs-marketing-qywx</module>
    <module>fs-marketing-audit-log</module>
  </modules>

  <parent>
    <groupId>com.fxiaoke.common</groupId>
    <artifactId>fxiaoke-parent-pom</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <groupId>com.facishare.marketing</groupId>
  <artifactId>fs-marketing</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <properties>
    <project.version>1.0-SNAPSHOT</project.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <mysql.version>5.1.36</mysql.version>
    <!--<netty.version>4.1.5.Final</netty.version>-->
    <commons-dbcp.version>1.4</commons-dbcp.version>
    <commons-collections4.version>4.1</commons-collections4.version>
<!--    <common-mq.version>1.0.3-SNAPSHOT</common-mq.version>-->
    <open-message.version>1.0.0-SNAPSHOT</open-message.version>
    <skip_maven_deploy>false</skip_maven_deploy>
    <webapp.contextPath>start</webapp.contextPath>
    <fs-open-common-result.version>0.0.6</fs-open-common-result.version>
    <fs-open-common-storage.version>0.0.5</fs-open-common-storage.version>
    <fs-open-common-utils.version>0.0.4-SNAPSHOT</fs-open-common-utils.version>
    <fs-crm-rest-api.version>2.0.26-SNAPSHOT</fs-crm-rest-api.version>
  </properties>

  <dependencies>

    <!-- SLF4J -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>log4j-over-slf4j</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>logconfig-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-hosts-record</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.2</version>
    </dependency>

    <!-- Utils -->
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-util</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>javassist-3.14.0-GA</artifactId>
          <groupId>org.ow2.util.bundles</groupId>
        </exclusion>
        <exclusion>
          <groupId>commons-collections</groupId>
          <artifactId>commons-collections</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>${commons-collections4.version}</version>
    </dependency>
    <!-- GSon -->
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>retrofit-spring2</artifactId>
      <version>2.0.3-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>metrics-oss</artifactId>
    </dependency>

    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-core</artifactId>
      <version>${powermock.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <version>${powermock.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito</artifactId>
      <version>${powermock.version}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>
  <dependencyManagement>
    <dependencies>
      <!-- MySQL -->
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
      </dependency>

      <dependency>
        <groupId>commons-dbcp</groupId>
        <artifactId>commons-dbcp</artifactId>
        <version>${commons-dbcp.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>${h2.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-enterprise-id-account-converter</artifactId>
        <version>1.1-SNAPSHOT</version>
      </dependency>

      <!-- warehouse -->
      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-fsi-proxy</artifactId>
        <version>3.0.0-SNAPSHOT</version>
      </dependency>

      <!-- http -->
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.9</version>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.11</version>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpmime</artifactId>
        <version>4.5.9</version>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpasyncclient</artifactId>
        <version>4.1.4</version>
      </dependency>

      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-warehouse-api</artifactId>
        <version>1.1-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-fsc-api</artifactId>
        <version>1.0-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>commons-fileupload</groupId>
        <artifactId>commons-fileupload</artifactId>
        <version>1.3.3</version>
      </dependency>

      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-common-mq</artifactId>
        <version>${common-mq.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>config-core</artifactId>
            <groupId>com.github.colin-lee</groupId>
          </exclusion>
          <exclusion>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <!-- 消息服务 -->
      <dependency>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-open-msg-api</artifactId>
        <version>${open-message.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-result</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-common-storage</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>*</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
          </exclusion>
          <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>*</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>*</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <!--企信-->
      <dependency>
        <groupId>com.facishare</groupId>
        <artifactId>fs-qixin-api</artifactId>
        <version>0.1.0-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-open-msg-common</artifactId>
        <version>0.0.4-SNAPSHOT</version>
      </dependency>

      <!--微信服务号连接-->
      <dependency>
        <groupId>com.facishare.open</groupId>
        <artifactId>fs-wechat-union-core-api</artifactId>
        <version>2.0.0-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <artifactId>fs-fcp-biz-server</artifactId>
            <groupId>com.facishare</groupId>
          </exclusion>
          <exclusion>
            <artifactId>bcprov-jdk16</artifactId>
            <groupId>org.bouncycastle</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <artifactId>protostuff-api</artifactId>
        <groupId>io.protostuff</groupId>
        <version>1.8.0</version>
      </dependency>

      <dependency>
        <artifactId>protostuff-core</artifactId>
        <groupId>io.protostuff</groupId>
        <version>1.8.0</version>
      </dependency>

      <dependency>
        <groupId>com.fxiaoke</groupId>
        <artifactId>fs-crm-rest-api</artifactId>
        <version>${fs-crm-rest-api.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.20</version>
        <configuration>
          <includes>
            <include>**/test/**/*Test.java</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.2</version>
        <executions>
          <execution>
            <id>default-prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>default-report</id>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
          <execution>
            <id>default-check</id>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <rules>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>